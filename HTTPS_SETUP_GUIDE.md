# 🔒 HTTPS-Only Setup for star.omnilyzer.ai

## 🎯 **Goal: HTTPS-Only Access**
Configure `https://star.omnilyzer.ai` with proper SSL certificate and redirect all HTTP traffic to HTTPS.

---

## 🚀 **Quick Setup (Automated)**

Run the automated setup script:
```bash
./setup-https.sh
```

---

## 🔧 **Manual Setup (Step by Step)**

If you prefer manual control or the automated script fails:

### **Step 1: Install Nginx Configuration**
```bash
sudo cp star.nginx.conf /etc/nginx/sites-available/star.conf
```

### **Step 2: Enable the Site**
```bash
sudo ln -sf /etc/nginx/sites-available/star.conf /etc/nginx/sites-enabled/star.conf
```

### **Step 3: Test Configuration**
```bash
sudo nginx -t
```

### **Step 4: Get SSL Certificate**
```bash
sudo certbot certonly --nginx -d star.omnilyzer.ai --non-interactive --agree-tos --email <EMAIL>
```

**Alternative if the above fails:**
```bash
sudo certbot certonly --webroot -w /var/www/star/frontend/dist -d star.omnilyzer.ai --email <EMAIL> --agree-tos --non-interactive
```

### **Step 5: Reload Nginx**
```bash
sudo systemctl reload nginx
```

### **Step 6: Verify HTTPS**
```bash
# Test HTTPS (should return 200)
curl -I https://star.omnilyzer.ai

# Test HTTP redirect (should return 301/302)
curl -I http://star.omnilyzer.ai

# Test health endpoint
curl https://star.omnilyzer.ai/health
```

---

## 🔍 **Current Issue Diagnosis**

**Problem Identified:**
- DNS resolves correctly: `star.omnilyzer.ai` → `46.62.161.155` ✅
- Server is serving certificate for `dev.trusthansen.dk` instead of `star.omnilyzer.ai` ❌
- Need to obtain proper SSL certificate for `star.omnilyzer.ai`

**SSL Certificate Status:**
```bash
# Current certificate is for: dev.trusthansen.dk
# Need certificate for: star.omnilyzer.ai
```

---

## 🛠️ **Troubleshooting**

### **If Certificate Generation Fails:**

1. **Check DNS propagation:**
   ```bash
   nslookup star.omnilyzer.ai
   dig star.omnilyzer.ai
   ```

2. **Verify domain ownership:**
   ```bash
   curl -I http://star.omnilyzer.ai/.well-known/acme-challenge/test
   ```

3. **Manual certificate request:**
   ```bash
   sudo certbot certonly --manual -d star.omnilyzer.ai
   ```

### **If Nginx Fails to Start:**

1. **Check configuration:**
   ```bash
   sudo nginx -t
   ```

2. **Check logs:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

3. **Check port conflicts:**
   ```bash
   sudo netstat -tlnp | grep :443
   ```

### **If HTTPS Still Not Working:**

1. **Check certificate files:**
   ```bash
   sudo ls -la /etc/letsencrypt/live/star.omnilyzer.ai/
   ```

2. **Test certificate:**
   ```bash
   echo | openssl s_client -connect star.omnilyzer.ai:443 -servername star.omnilyzer.ai
   ```

3. **Check nginx is using correct certificate:**
   ```bash
   sudo nginx -T | grep star.omnilyzer.ai -A 10
   ```

---

## 🔒 **Security Features Enabled**

Once HTTPS is working, these security features will be active:

### **SSL/TLS Security:**
- ✅ **TLS 1.2 & 1.3** only
- ✅ **HSTS** (HTTP Strict Transport Security)
- ✅ **SSL Stapling** for performance
- ✅ **Strong cipher suites**

### **HTTP Security Headers:**
- ✅ **CSP** (Content Security Policy) optimized for WebGL
- ✅ **X-Frame-Options** (clickjacking protection)
- ✅ **X-Content-Type-Options** (MIME sniffing protection)
- ✅ **X-XSS-Protection** (XSS filtering)
- ✅ **Referrer-Policy** (privacy protection)

### **Performance Features:**
- ✅ **Gzip compression** for all text assets
- ✅ **Static asset caching** (1 year for immutable assets)
- ✅ **No-cache headers** for HTML (ensures updates)

---

## 🎮 **Expected Result**

After successful setup:

- **✅ https://star.omnilyzer.ai** - Game loads with HTTPS
- **✅ http://star.omnilyzer.ai** - Redirects to HTTPS (301/302)
- **✅ https://star.omnilyzer.ai/health** - Returns "Galactic Genesis Game - Online"

### **Game Features Available:**
- 🌌 **Enhanced Galaxy Map** with hover overlays
- 🎮 **Rotation Controls** (start/stop, speed)
- 🪐 **Visible Planets** around stars
- 🏛️ **Colony Management**
- 🔬 **Technology Tree**
- 💰 **Galactic Market**
- 🚀 **Fleet Management**

---

## 📊 **Monitoring**

### **Log Files:**
- **Access:** `/var/log/nginx/star-access.log`
- **Errors:** `/var/log/nginx/star-error.log`

### **Certificate Renewal:**
```bash
# Check certificate expiry
sudo certbot certificates

# Test renewal
sudo certbot renew --dry-run

# Auto-renewal is typically configured via cron
```

---

## 🚀 **Next Steps**

1. **Run the setup:** `./setup-https.sh` or follow manual steps
2. **Test HTTPS:** Visit `https://star.omnilyzer.ai`
3. **Verify redirect:** Test `http://star.omnilyzer.ai` redirects to HTTPS
4. **Play the game:** Explore the galaxy! 🌌

---

**🔒 HTTPS-only configuration ensures your galactic empire is secure! 🛡️**
