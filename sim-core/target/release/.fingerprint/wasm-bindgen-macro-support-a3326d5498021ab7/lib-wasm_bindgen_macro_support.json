{"rustc": 12013579709055016942, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 15199102770819487777, "path": 15465991858858153760, "deps": [[373107762698212489, "proc_macro2", false, 5065444893535814789], [3354082262598541841, "wasm_bindgen_shared", false, 1243148933038835228], [10395631535039749755, "wasm_bindgen_backend", false, 6236051039689558488], [17332570067994900305, "syn", false, 8895287586083852451], [17990358020177143287, "quote", false, 18191256471438870979]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-support-a3326d5498021ab7/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}