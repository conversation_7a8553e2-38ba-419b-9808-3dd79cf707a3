{"rustc": 12013579709055016942, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 14724531817405131023, "deps": [[373107762698212489, "proc_macro2", false, 5065444893535814789], [1988483478007900009, "unicode_ident", false, 3814634352728208932], [17990358020177143287, "quote", false, 18191256471438870979]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-7307142f21012cb1/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}