{"rustc": 12013579709055016942, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 6456097124152657218, "path": 15401241361751905603, "deps": [[3354082262598541841, "wasm_bindgen_shared", false, 2346325872690336839], [3722963349756955755, "once_cell", false, 10283372761709719362], [4773143178277693085, "build_script_build", false, 12877826868597660889], [7843059260364151289, "cfg_if", false, 14213450703244138676], [14156967978702956262, "rustversion", false, 3446770132260372015], [14863139193646289797, "wasm_bindgen_macro", false, 8866297560226440079]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/wasm-bindgen-0b81a401a389c44e/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}