use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use std::collections::HashMap;

#[derive(Serialize, Deserialize)]
pub struct OrderEnvelope {
    pub kind: String,
    pub payload: serde_json::Value,
}

#[derive(Serialize, Deserialize)]
pub struct Delta {
    pub applied: bool,
    pub notes: String,
}

#[derive(Serialize, Deserialize)]
pub struct Fleet {
    pub id: String,
    pub empire_id: String,
    pub supply: i32,
    pub stance: String, // "aggressive", "defensive", "balanced"
}

#[derive(Serialize, Deserialize)]
pub struct CombatResult {
    pub attacker_supply_loss: i32,
    pub target_supply_loss: i32,
    pub attacker_destroyed: bool,
    pub target_destroyed: bool,
    pub notes: String,
}

fn resolve_combat(attacker: &Fleet, target: &Fleet) -> CombatResult {
    // Base damage calculation
    let mut attacker_damage = 20;
    let mut target_damage = 15;

    // Stance modifiers
    match attacker.stance.as_str() {
        "aggressive" => {
            attacker_damage += 10; // More damage dealt
            target_damage += 5;    // More damage taken
        }
        "defensive" => {
            attacker_damage -= 5;  // Less damage dealt
            target_damage -= 10;   // Less damage taken
        }
        _ => {} // balanced - no modifiers
    }

    match target.stance.as_str() {
        "aggressive" => {
            target_damage += 10;   // More damage dealt
            attacker_damage += 5;  // More damage taken
        }
        "defensive" => {
            target_damage -= 5;    // Less damage dealt
            attacker_damage -= 10; // Less damage taken
        }
        _ => {} // balanced - no modifiers
    }

    // Supply affects combat effectiveness
    let attacker_effectiveness = (attacker.supply as f32 / 100.0).min(1.0);
    let target_effectiveness = (target.supply as f32 / 100.0).min(1.0);

    let final_attacker_damage = (attacker_damage as f32 * attacker_effectiveness) as i32;
    let final_target_damage = (target_damage as f32 * target_effectiveness) as i32;

    let attacker_new_supply = (attacker.supply - final_target_damage).max(0);
    let target_new_supply = (target.supply - final_attacker_damage).max(0);

    CombatResult {
        attacker_supply_loss: attacker.supply - attacker_new_supply,
        target_supply_loss: target.supply - target_new_supply,
        attacker_destroyed: attacker_new_supply <= 0,
        target_destroyed: target_new_supply <= 0,
        notes: format!("Combat: {} vs {} stance", attacker.stance, target.stance),
    }
}

#[wasm_bindgen]
pub fn resolve_battle(attacker_json: &str, target_json: &str) -> String {
    let attacker: Result<Fleet, _> = serde_json::from_str(attacker_json);
    let target: Result<Fleet, _> = serde_json::from_str(target_json);

    match (attacker, target) {
        (Ok(attacker), Ok(target)) => {
            let result = resolve_combat(&attacker, &target);
            serde_json::to_string(&result).unwrap()
        }
        _ => {
            serde_json::to_string(&CombatResult {
                attacker_supply_loss: 0,
                target_supply_loss: 0,
                attacker_destroyed: false,
                target_destroyed: false,
                notes: "Invalid fleet data".into(),
            }).unwrap()
        }
    }
}

#[wasm_bindgen]
pub fn apply(order_json: &str) -> String {
    let env: Result<OrderEnvelope, _> = serde_json::from_str(order_json);
    match env {
        Ok(env) => {
            match env.kind.as_str() {
                "move" => {
                    serde_json::to_string(&Delta{ applied: true, notes: "moved one step".into() }).unwrap()
                }
                "resupply" => {
                    serde_json::to_string(&Delta{ applied: true, notes: "resupplied".into() }).unwrap()
                }
                "attack" => {
                    serde_json::to_string(&Delta{ applied: true, notes: "battle resolved".into() }).unwrap()
                }
                _ => {
                    serde_json::to_string(&Delta{ applied: false, notes: "unsupported kind".into() }).unwrap()
                }
            }
        }
        Err(_) => serde_json::to_string(&Delta{ applied: false, notes: "invalid envelope".into() }).unwrap()
    }
}

