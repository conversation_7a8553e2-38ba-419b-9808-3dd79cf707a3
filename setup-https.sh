#!/bin/bash

# HTTPS-Only Setup for star.omnilyzer.ai
# This script sets up SSL certificate and HTTPS-only configuration

set -e

echo "🔒 Setting up HTTPS-only for star.omnilyzer.ai..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Copy nginx configuration
print_status "Installing nginx configuration..."
sudo cp star.nginx.conf /etc/nginx/sites-available/star.conf
if [ $? -eq 0 ]; then
    print_success "Nginx configuration installed"
else
    print_error "Failed to install nginx configuration"
    exit 1
fi

# Step 2: Enable the site
print_status "Enabling star.omnilyzer.ai site..."
sudo ln -sf /etc/nginx/sites-available/star.conf /etc/nginx/sites-enabled/star.conf
if [ $? -eq 0 ]; then
    print_success "Site enabled"
else
    print_warning "Site may already be enabled"
fi

# Step 3: Test nginx configuration
print_status "Testing nginx configuration..."
sudo nginx -t
if [ $? -eq 0 ]; then
    print_success "Nginx configuration is valid"
else
    print_error "Nginx configuration test failed"
    exit 1
fi

# Step 4: Check if SSL certificate already exists
if [ -f "/etc/letsencrypt/live/star.omnilyzer.ai/fullchain.pem" ]; then
    print_success "SSL certificate already exists for star.omnilyzer.ai"
else
    print_status "Obtaining SSL certificate for star.omnilyzer.ai..."
    
    # Get SSL certificate
    sudo certbot certonly --nginx -d star.omnilyzer.ai --non-interactive --agree-tos --email <EMAIL>
    
    if [ $? -eq 0 ]; then
        print_success "SSL certificate obtained successfully"
    else
        print_error "Failed to obtain SSL certificate"
        print_warning "You may need to run manually:"
        print_warning "sudo certbot certonly --nginx -d star.omnilyzer.ai"
        exit 1
    fi
fi

# Step 5: Reload nginx
print_status "Reloading nginx..."
sudo systemctl reload nginx
if [ $? -eq 0 ]; then
    print_success "Nginx reloaded successfully"
else
    print_error "Failed to reload nginx"
    exit 1
fi

# Step 6: Test HTTPS
print_status "Testing HTTPS connection..."
sleep 2

HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://star.omnilyzer.ai || echo "000")
if [ "$HTTPS_STATUS" = "200" ]; then
    print_success "HTTPS site is working! (Status: $HTTPS_STATUS)"
else
    print_warning "HTTPS test returned status: $HTTPS_STATUS"
    print_warning "This might be normal if the certificate is still propagating"
fi

# Step 7: Test HTTP redirect
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://star.omnilyzer.ai || echo "000")
if [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    print_success "HTTP to HTTPS redirect working (Status: $HTTP_STATUS)"
else
    print_warning "HTTP redirect test returned status: $HTTP_STATUS"
fi

echo ""
echo "🌟 =================================="
echo "🌟 HTTPS SETUP SUMMARY"
echo "🌟 =================================="
echo ""
print_success "✅ Nginx configuration installed"
print_success "✅ Site enabled"
print_success "✅ SSL certificate configured"
print_success "✅ HTTPS-only mode active"
echo ""
echo -e "${GREEN}🎮 Galactic Genesis is now available at:${NC}"
echo -e "${BLUE}   https://star.omnilyzer.ai${NC}"
echo ""
echo -e "${YELLOW}🔒 Security Features:${NC}"
echo "   • HTTPS-only (HTTP redirects to HTTPS)"
echo "   • Security headers enabled"
echo "   • CSP optimized for WebGL games"
echo "   • Asset caching and compression"
echo ""
echo -e "${GREEN}🚀 HTTPS setup completed successfully!${NC}"
