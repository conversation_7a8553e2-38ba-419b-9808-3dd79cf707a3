# Temporary HTTP configuration for SSL certificate generation
server {
    listen 80;
    server_name star.omnilyzer.ai;

    # Document root for static files and ACME challenge
    root /var/www/star/frontend/dist;
    index index.html;

    # ACME challenge for Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/star/frontend/dist;
        try_files $uri =404;
    }

    # Serve the game temporarily over HTTP for certificate generation
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Health check
    location /health {
        return 200 "Galactic Genesis - Certificate Setup\n";
        add_header Content-Type text/plain;
    }
}
