import Fastify from 'fastify';
import { Pool } from 'pg';

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433'),
  database: process.env.DB_NAME || 'galactic_genesis',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
});

export async function buildServer() {
  const app = Fastify({ logger: true });

  // CORS support
  await app.register(import('@fastify/cors'), {
    origin: true,
    credentials: false,
  });

  // Health check
  app.get('/health', async () => {
    return { ok: true, service: 'materials-svc' };
  });

  // Material Types API
  app.get('/v1/materials', async () => {
    const result = await pool.query(`
      SELECT * FROM material_types 
      ORDER BY category, name
    `);
    return { materials: result.rows };
  });

  app.get<{ Params: { id: string } }>('/v1/materials/:id', async (request, reply) => {
    const { id } = request.params;
    const result = await pool.query('SELECT * FROM material_types WHERE id = $1', [id]);
    
    if (result.rows.length === 0) {
      reply.status(404);
      return { error: 'Material not found' };
    }
    
    return { material: result.rows[0] };
  });

  // Material Deposits API
  app.get<{ 
    Querystring: { 
      body_type?: string; 
      body_id?: string; 
      material_id?: string;
      discovered?: string;
    } 
  }>('/v1/material-deposits', async (request) => {
    const { body_type, body_id, material_id, discovered } = request.query;
    
    let query = `
      SELECT bmd.*, mt.name as material_name, mt.category as material_category
      FROM body_material_deposits bmd
      JOIN material_types mt ON mt.id = bmd.material_id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (body_type) {
      query += ` AND bmd.body_type = $${++paramCount}`;
      params.push(body_type);
    }
    if (body_id) {
      query += ` AND bmd.body_id = $${++paramCount}`;
      params.push(parseInt(body_id));
    }
    if (material_id) {
      query += ` AND bmd.material_id = $${++paramCount}`;
      params.push(material_id);
    }
    if (discovered !== undefined) {
      query += ` AND bmd.discovered = $${++paramCount}`;
      params.push(discovered === 'true');
    }

    query += ` ORDER BY bmd.richness DESC`;
    
    const result = await pool.query(query, params);
    return { deposits: result.rows };
  });

  // Space Stations API
  app.get<{ 
    Querystring: { 
      system_id?: string; 
      empire_id?: string; 
      station_type?: string;
      operational?: string;
    } 
  }>('/v1/stations', async (request) => {
    const { system_id, empire_id, station_type, operational } = request.query;
    
    let query = `
      SELECT ss.*, st.name as station_type_name, st.category as station_category
      FROM space_stations ss
      JOIN station_types st ON st.id = ss.station_type
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (system_id) {
      query += ` AND ss.system_id = $${++paramCount}`;
      params.push(system_id);
    }
    if (empire_id) {
      query += ` AND ss.empire_id = $${++paramCount}`;
      params.push(empire_id);
    }
    if (station_type) {
      query += ` AND ss.station_type = $${++paramCount}`;
      params.push(station_type);
    }
    if (operational !== undefined) {
      query += ` AND ss.operational = $${++paramCount}`;
      params.push(operational === 'true');
    }

    query += ` ORDER BY ss.created_at DESC`;
    
    const result = await pool.query(query, params);
    return { stations: result.rows };
  });

  app.get<{ Params: { id: string } }>('/v1/stations/:id', async (request, reply) => {
    const { id } = request.params;
    const result = await pool.query(`
      SELECT ss.*, st.name as station_type_name, st.category as station_category
      FROM space_stations ss
      JOIN station_types st ON st.id = ss.station_type
      WHERE ss.id = $1
    `, [id]);
    
    if (result.rows.length === 0) {
      reply.status(404);
      return { error: 'Station not found' };
    }
    
    return { station: result.rows[0] };
  });

  // Station Materials API
  app.get<{ Params: { station_id: string } }>('/v1/stations/:station_id/materials', async (request) => {
    const { station_id } = request.params;
    
    const result = await pool.query(`
      SELECT sm.*, mt.name as material_name, mt.category as material_category
      FROM station_materials sm
      JOIN material_types mt ON mt.id = sm.material_id
      WHERE sm.station_id = $1
      ORDER BY sm.quantity DESC
    `, [station_id]);
    
    return { materials: result.rows };
  });

  // Mining Operations API
  app.get<{ 
    Querystring: { 
      empire_id?: string; 
      status?: string;
    } 
  }>('/v1/mining-operations', async (request) => {
    const { empire_id, status } = request.query;
    
    let query = `
      SELECT mo.*, bmd.material_id, mt.name as material_name
      FROM mining_operations mo
      JOIN body_material_deposits bmd ON bmd.id = mo.deposit_id
      JOIN material_types mt ON mt.id = bmd.material_id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (empire_id) {
      query += ` AND mo.empire_id = $${++paramCount}`;
      params.push(empire_id);
    }
    if (status) {
      query += ` AND mo.status = $${++paramCount}`;
      params.push(status);
    }

    query += ` ORDER BY mo.created_at DESC`;
    
    const result = await pool.query(query, params);
    return { operations: result.rows };
  });

  // Station Types API
  app.get('/v1/station-types', async () => {
    const result = await pool.query(`
      SELECT * FROM station_types 
      ORDER BY category, name
    `);
    return { station_types: result.rows };
  });

  // Processing Recipes API
  app.get('/v1/processing-recipes', async () => {
    const result = await pool.query(`
      SELECT mp.*, 
             imt.name as input_material_name,
             omt.name as output_material_name
      FROM material_processing mp
      JOIN material_types imt ON imt.id = mp.input_material_id
      JOIN material_types omt ON omt.id = mp.output_material_id
      ORDER BY mp.facility_type, mp.id
    `);
    return { recipes: result.rows };
  });

  // Material Market Data API
  app.get<{ 
    Querystring: { 
      system_id?: string; 
      material_id?: string;
    } 
  }>('/v1/material-market-data', async (request) => {
    const { system_id, material_id } = request.query;
    
    let query = `
      SELECT mmd.*, mt.name as material_name, s.name as system_name
      FROM material_market_data mmd
      JOIN material_types mt ON mt.id = mmd.material_id
      JOIN systems s ON s.id = mmd.system_id
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (system_id) {
      query += ` AND mmd.system_id = $${++paramCount}`;
      params.push(system_id);
    }
    if (material_id) {
      query += ` AND mmd.material_id = $${++paramCount}`;
      params.push(material_id);
    }

    query += ` ORDER BY mmd.last_updated DESC`;
    
    const result = await pool.query(query, params);
    return { market_data: result.rows };
  });

  // Survey Orders API
  app.post<{
    Body: {
      empire_id: string;
      target_body_type: 'planet' | 'moon' | 'asteroid';
      target_body_id: number;
      system_id: string;
      survey_type: 'basic' | 'detailed' | 'deep_scan';
      fleet_id?: string;
    }
  }>('/v1/survey-orders', async (request, reply) => {
    const { empire_id, target_body_type, target_body_id, system_id, survey_type, fleet_id } = request.body;

    try {
      const result = await pool.query(`
        INSERT INTO survey_orders (id, empire_id, target_body_type, target_body_id, system_id, survey_type, fleet_id)
        VALUES (gen_random_uuid()::text, $1, $2, $3, $4, $5, $6)
        RETURNING *
      `, [empire_id, target_body_type, target_body_id, system_id, survey_type, fleet_id]);

      return { survey_order: result.rows[0] };
    } catch (error) {
      reply.status(400);
      return { error: 'Failed to create survey order' };
    }
  });

  // Station Construction Orders API
  app.post<{
    Body: {
      empire_id: string;
      system_id: string;
      station_type_id: string;
      orbiting_body_type?: string;
      orbiting_body_id?: number;
      orbital_distance_km?: number;
    }
  }>('/v1/station-construction-orders', async (request, reply) => {
    const { empire_id, system_id, station_type_id, orbiting_body_type, orbiting_body_id, orbital_distance_km } = request.body;

    try {
      // Get construction requirements
      const stationTypeResult = await pool.query(
        'SELECT base_construction_cost FROM station_types WHERE id = $1',
        [station_type_id]
      );

      if (stationTypeResult.rows.length === 0) {
        reply.status(400);
        return { error: 'Invalid station type' };
      }

      const result = await pool.query(`
        INSERT INTO station_construction_orders 
        (id, empire_id, system_id, station_type_id, orbiting_body_type, orbiting_body_id, orbital_distance_km, materials_required)
        VALUES (gen_random_uuid()::text, $1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `, [empire_id, system_id, station_type_id, orbiting_body_type, orbiting_body_id, orbital_distance_km, stationTypeResult.rows[0].base_construction_cost]);

      return { construction_order: result.rows[0] };
    } catch (error) {
      reply.status(400);
      return { error: 'Failed to create construction order' };
    }
  });

  return app;
}

// Start server if this file is run directly
if (require.main === module) {
  const start = async () => {
    try {
      const server = await buildServer();
      const port = parseInt(process.env.PORT || '8084');
      await server.listen({ port, host: '0.0.0.0' });
      console.log(`Materials service listening on port ${port}`);
    } catch (err) {
      console.error('Error starting server:', err);
      process.exit(1);
    }
  };
  start();
}
