{"name": "materials-svc", "version": "1.0.0", "description": "Materials and Trade Stations Service for Galactic Genesis", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "test": "jest"}, "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "pg": "^8.11.3"}, "devDependencies": {"@types/node": "^20.8.7", "@types/pg": "^8.10.7", "tsx": "^3.14.0", "typescript": "^5.2.2"}, "keywords": ["galactic-genesis", "materials", "mining", "trade-stations"], "author": "Galactic Genesis Team", "license": "MIT"}