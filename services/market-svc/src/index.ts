import Fastify from 'fastify';
import { Pool } from 'pg';
import { nanoid } from 'nanoid';

// Types
interface MarketOrder {
  id: string;
  empire_id: string;
  order_type: 'buy' | 'sell';
  resource_type: string;
  quantity: number;
  price_per_unit: number;
  total_value: number;
  filled_quantity: number;
  status: 'active' | 'completed' | 'cancelled' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

interface TradeTransaction {
  id: string;
  buyer_empire_id: string;
  seller_empire_id: string;
  buy_order_id: string;
  sell_order_id: string;
  resource_type: string;
  quantity: number;
  price_per_unit: number;
  total_value: number;
  executed_at: string;
}

interface MarketPrice {
  resource_type: string;
  current_price: number;
  price_trend: 'rising' | 'falling' | 'stable';
  daily_volume: number;
  last_updated: string;
}

interface TradeRoute {
  id: string;
  empire_id: string;
  origin_system_id: string;
  destination_system_id: string;
  resource_type: string;
  quantity_per_turn: number;
  profit_per_turn: number;
  established_at: string;
  status: 'active' | 'disrupted' | 'inactive';
}

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433'),
  database: process.env.DB_NAME || 'gg',
  user: process.env.DB_USER || 'gg',
  password: process.env.DB_PASSWORD || 'ggpassword',
});

export async function buildServer() {
  const app = Fastify({ logger: true });

  // CORS support
  await app.register(import('@fastify/cors'), {
    origin: true,
    credentials: false,
  });

  // Health check
  app.get('/health', async () => {
    return { ok: true, service: 'market-svc' };
  });

  // Get market prices for all resources
  app.get('/v1/market/prices', async () => {
    const result = await pool.query(`
      SELECT * FROM market_prices 
      ORDER BY resource_type
    `);
    
    return { prices: result.rows };
  });

  // Get market orders (with optional filtering)
  app.get<{ 
    Querystring: { 
      empire_id?: string; 
      resource_type?: string; 
      order_type?: string;
      status?: string;
      limit?: string;
    } 
  }>('/v1/market/orders', async (request) => {
    const { empire_id, resource_type, order_type, status, limit = '100' } = request.query;
    
    let query = 'SELECT * FROM market_orders WHERE 1=1';
    const params: any[] = [];
    let paramCount = 0;

    if (empire_id) {
      query += ` AND empire_id = $${++paramCount}`;
      params.push(empire_id);
    }
    if (resource_type) {
      query += ` AND resource_type = $${++paramCount}`;
      params.push(resource_type);
    }
    if (order_type) {
      query += ` AND order_type = $${++paramCount}`;
      params.push(order_type);
    }
    if (status) {
      query += ` AND status = $${++paramCount}`;
      params.push(status);
    }

    query += ` ORDER BY created_at DESC LIMIT $${++paramCount}`;
    params.push(parseInt(limit));

    const result = await pool.query(query, params);
    return { orders: result.rows };
  });

  // Create a market order
  app.post<{ 
    Body: { 
      empire_id: string;
      order_type: 'buy' | 'sell';
      resource_type: string;
      quantity: number;
      price_per_unit: number;
      expires_hours?: number;
    } 
  }>('/v1/market/orders', async (request, reply) => {
    const { empire_id, order_type, resource_type, quantity, price_per_unit, expires_hours } = request.body;

    // Validation
    if (quantity <= 0 || price_per_unit <= 0) {
      reply.status(400);
      return { error: 'Quantity and price must be positive' };
    }

    // Check if empire has sufficient resources for sell orders or credits for buy orders
    if (order_type === 'sell') {
      const resourceCheck = await pool.query(`
        SELECT amount FROM empire_resources 
        WHERE empire_id = $1 AND resource_type = $2
      `, [empire_id, resource_type]);

      if (resourceCheck.rows.length === 0 || resourceCheck.rows[0].amount < quantity) {
        reply.status(400);
        return { error: 'Insufficient resources to create sell order' };
      }
    } else if (order_type === 'buy') {
      const creditsCheck = await pool.query(`
        SELECT amount FROM empire_resources 
        WHERE empire_id = $1 AND resource_type = 'credits'
      `, [empire_id]);

      const totalCost = quantity * price_per_unit;
      if (creditsCheck.rows.length === 0 || creditsCheck.rows[0].amount < totalCost) {
        reply.status(400);
        return { error: 'Insufficient credits to create buy order' };
      }
    }

    const orderId = nanoid();
    const expiresAt = expires_hours ? 
      new Date(Date.now() + expires_hours * 60 * 60 * 1000) : null;

    // Create the order
    const result = await pool.query(`
      INSERT INTO market_orders (
        id, empire_id, order_type, resource_type, quantity, 
        price_per_unit, expires_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [orderId, empire_id, order_type, resource_type, quantity, price_per_unit, expiresAt]);

    // Reserve resources for sell orders or credits for buy orders
    if (order_type === 'sell') {
      await pool.query(`
        UPDATE empire_resources 
        SET amount = amount - $1
        WHERE empire_id = $2 AND resource_type = $3
      `, [quantity, empire_id, resource_type]);
    } else {
      const totalCost = quantity * price_per_unit;
      await pool.query(`
        UPDATE empire_resources 
        SET amount = amount - $1
        WHERE empire_id = $2 AND resource_type = 'credits'
      `, [totalCost, empire_id]);
    }

    return { order: result.rows[0] };
  });

  // Cancel a market order
  app.delete<{ Params: { order_id: string } }>('/v1/market/orders/:order_id', async (request, reply) => {
    const { order_id } = request.params;

    // Get order details
    const orderResult = await pool.query(`
      SELECT * FROM market_orders WHERE id = $1 AND status = 'active'
    `, [order_id]);

    if (orderResult.rows.length === 0) {
      reply.status(404);
      return { error: 'Order not found or not active' };
    }

    const order = orderResult.rows[0];
    const unfilledQuantity = order.quantity - order.filled_quantity;

    // Cancel the order
    await pool.query(`
      UPDATE market_orders 
      SET status = 'cancelled', updated_at = now()
      WHERE id = $1
    `, [order_id]);

    // Refund reserved resources
    if (order.order_type === 'sell') {
      await pool.query(`
        UPDATE empire_resources 
        SET amount = amount + $1
        WHERE empire_id = $2 AND resource_type = $3
      `, [unfilledQuantity, order.empire_id, order.resource_type]);
    } else {
      const refundAmount = unfilledQuantity * order.price_per_unit;
      await pool.query(`
        UPDATE empire_resources 
        SET amount = amount + $1
        WHERE empire_id = $2 AND resource_type = 'credits'
      `, [refundAmount, order.empire_id]);
    }

    return { cancelled: true, refunded: unfilledQuantity };
  });

  // Get trade history
  app.get<{ 
    Querystring: { 
      empire_id?: string; 
      resource_type?: string;
      limit?: string;
    } 
  }>('/v1/market/transactions', async (request) => {
    const { empire_id, resource_type, limit = '50' } = request.query;
    
    let query = 'SELECT * FROM trade_transactions WHERE 1=1';
    const params: any[] = [];
    let paramCount = 0;

    if (empire_id) {
      query += ` AND (buyer_empire_id = $${++paramCount} OR seller_empire_id = $${paramCount})`;
      params.push(empire_id);
    }
    if (resource_type) {
      query += ` AND resource_type = $${++paramCount}`;
      params.push(resource_type);
    }

    query += ` ORDER BY executed_at DESC LIMIT $${++paramCount}`;
    params.push(parseInt(limit));

    const result = await pool.query(query, params);
    return { transactions: result.rows };
  });

  // Execute trade matching (called by turn processor or manually)
  app.post('/v1/market/execute-trades', async () => {
    const result = await pool.query('SELECT * FROM execute_trade_matching()');
    
    // Update market prices after trades
    await pool.query('SELECT update_market_prices()');
    
    return { 
      executed_trades: result.rows.length,
      transactions: result.rows 
    };
  });

  // Get trade routes for an empire
  app.get<{ Params: { empire_id: string } }>('/v1/empires/:empire_id/trade-routes', async (request) => {
    const { empire_id } = request.params;
    
    const result = await pool.query(`
      SELECT * FROM trade_routes 
      WHERE empire_id = $1 
      ORDER BY established_at DESC
    `, [empire_id]);
    
    return { trade_routes: result.rows };
  });

  // Create a trade route
  app.post<{ 
    Params: { empire_id: string };
    Body: {
      origin_system_id: string;
      destination_system_id: string;
      resource_type: string;
      quantity_per_turn: number;
    }
  }>('/v1/empires/:empire_id/trade-routes', async (request, reply) => {
    const { empire_id } = request.params;
    const { origin_system_id, destination_system_id, resource_type, quantity_per_turn } = request.body;

    if (quantity_per_turn <= 0) {
      reply.status(400);
      return { error: 'Quantity per turn must be positive' };
    }

    const routeId = nanoid();
    
    const result = await pool.query(`
      INSERT INTO trade_routes (
        id, empire_id, origin_system_id, destination_system_id,
        resource_type, quantity_per_turn
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [routeId, empire_id, origin_system_id, destination_system_id, resource_type, quantity_per_turn]);

    return { trade_route: result.rows[0] };
  });

  // Get market statistics
  app.get('/v1/market/stats', async () => {
    const [pricesResult, volumeResult, ordersResult] = await Promise.all([
      pool.query('SELECT * FROM market_prices ORDER BY resource_type'),
      pool.query(`
        SELECT resource_type, COUNT(*) as transaction_count, SUM(total_value) as total_value
        FROM trade_transactions 
        WHERE executed_at >= current_date - interval '7 days'
        GROUP BY resource_type
      `),
      pool.query(`
        SELECT order_type, status, COUNT(*) as count
        FROM market_orders 
        GROUP BY order_type, status
      `)
    ]);

    return {
      prices: pricesResult.rows,
      weekly_volume: volumeResult.rows,
      order_stats: ordersResult.rows
    };
  });

  return app;
}

// Start server
async function start() {
  try {
    const app = await buildServer();
    const port = parseInt(process.env.PORT || '3006');
    
    await app.listen({ port, host: '0.0.0.0' });
    console.log(`Market service listening on port ${port}`);
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  start();
}
