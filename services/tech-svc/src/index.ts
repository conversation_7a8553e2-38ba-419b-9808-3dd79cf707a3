import Fastify from 'fastify';
import { Pool } from 'pg';
import { nanoid } from 'nanoid';

// Types
interface TechNode {
  id: string;
  name: string;
  branch: string;
  tier: number;
  base_cost_knw: number;
  effects: Record<string, any>;
  description: string;
  icon: string;
  created_at: string;
}

interface TechProgress {
  empire_id: string;
  tech_id: string;
  invested_knw: number;
  unlocked: boolean;
  unlocked_at?: string;
}

interface ResearchQueueItem {
  id: string;
  empire_id: string;
  tech_id: string;
  priority: number;
  created_at: string;
}

interface TechTreeNode extends TechNode {
  prerequisites: string[];
  unlocks: string[];
  progress?: TechProgress;
  can_research: boolean;
}

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433'),
  database: process.env.DB_NAME || 'gg',
  user: process.env.DB_USER || 'gg',
  password: process.env.DB_PASSWORD || 'ggpassword',
});

export async function buildServer() {
  const app = Fastify({ logger: true });

  // CORS support
  await app.register(import('@fastify/cors'), {
    origin: true,
    credentials: false,
  });

  // Health check
  app.get('/health', async () => {
    return { ok: true, service: 'tech-svc' };
  });

  // Get technology tree for an empire
  app.get<{ Params: { empire_id: string } }>('/v1/empires/:empire_id/tech-tree', async (request) => {
    const { empire_id } = request.params;
    
    // Get all tech nodes with progress and dependencies
    const techQuery = `
      SELECT 
        tn.*,
        tp.invested_knw,
        tp.unlocked,
        tp.unlocked_at,
        COALESCE(
          json_agg(
            DISTINCT te_from.from_tech
          ) FILTER (WHERE te_from.from_tech IS NOT NULL), 
          '[]'
        ) as prerequisites,
        COALESCE(
          json_agg(
            DISTINCT te_to.to_tech
          ) FILTER (WHERE te_to.to_tech IS NOT NULL), 
          '[]'
        ) as unlocks
      FROM tech_nodes tn
      LEFT JOIN tech_progress tp ON tn.id = tp.tech_id AND tp.empire_id = $1
      LEFT JOIN tech_edges te_from ON tn.id = te_from.to_tech
      LEFT JOIN tech_edges te_to ON tn.id = te_to.from_tech
      GROUP BY tn.id, tp.invested_knw, tp.unlocked, tp.unlocked_at
      ORDER BY tn.branch, tn.tier, tn.name
    `;

    const result = await pool.query(techQuery, [empire_id]);
    
    // Process results to determine research availability
    const techNodes: TechTreeNode[] = result.rows.map(row => {
      const prerequisites = row.prerequisites || [];
      const unlocks = row.unlocks || [];
      
      // Check if all prerequisites are unlocked
      const canResearch = prerequisites.length === 0 || 
        prerequisites.every((prereqId: string) => {
          const prereq = result.rows.find(r => r.id === prereqId);
          return prereq && prereq.unlocked;
        });

      return {
        id: row.id,
        name: row.name,
        branch: row.branch,
        tier: row.tier,
        base_cost_knw: row.base_cost_knw,
        effects: row.effects,
        description: row.description,
        icon: row.icon,
        created_at: row.created_at,
        prerequisites,
        unlocks,
        progress: {
          empire_id,
          tech_id: row.id,
          invested_knw: row.invested_knw || 0,
          unlocked: row.unlocked || false,
          unlocked_at: row.unlocked_at
        },
        can_research: canResearch && !row.unlocked
      };
    });

    return { tech_tree: techNodes };
  });

  // Get research queue for an empire
  app.get<{ Params: { empire_id: string } }>('/v1/empires/:empire_id/research-queue', async (request) => {
    const { empire_id } = request.params;
    
    const result = await pool.query(`
      SELECT rq.*, tn.name, tn.base_cost_knw, tp.invested_knw
      FROM research_queue rq
      JOIN tech_nodes tn ON rq.tech_id = tn.id
      LEFT JOIN tech_progress tp ON rq.tech_id = tp.tech_id AND rq.empire_id = tp.empire_id
      WHERE rq.empire_id = $1
      ORDER BY rq.priority ASC
    `, [empire_id]);
    
    return { research_queue: result.rows };
  });

  // Add technology to research queue
  app.post<{ 
    Params: { empire_id: string };
    Body: { tech_id: string; priority?: number } 
  }>('/v1/empires/:empire_id/research-queue', async (request, reply) => {
    const { empire_id } = request.params;
    const { tech_id, priority = 1 } = request.body;

    // Check if tech exists and can be researched
    const techCheck = await pool.query(`
      SELECT tn.*, tp.unlocked
      FROM tech_nodes tn
      LEFT JOIN tech_progress tp ON tn.id = tp.tech_id AND tp.empire_id = $1
      WHERE tn.id = $2
    `, [empire_id, tech_id]);

    if (techCheck.rows.length === 0) {
      reply.status(404);
      return { error: 'Technology not found' };
    }

    if (techCheck.rows[0].unlocked) {
      reply.status(400);
      return { error: 'Technology already unlocked' };
    }

    // Check prerequisites
    const prereqCheck = await pool.query(`
      SELECT te.from_tech, tp.unlocked
      FROM tech_edges te
      LEFT JOIN tech_progress tp ON te.from_tech = tp.tech_id AND tp.empire_id = $1
      WHERE te.to_tech = $2
    `, [empire_id, tech_id]);

    const unlockedPrereqs = prereqCheck.rows.filter(row => row.unlocked).length;
    if (unlockedPrereqs < prereqCheck.rows.length) {
      reply.status(400);
      return { error: 'Prerequisites not met' };
    }

    // Add to research queue
    const queueId = nanoid();
    const result = await pool.query(`
      INSERT INTO research_queue (id, empire_id, tech_id, priority)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [queueId, empire_id, tech_id, priority]);

    return { research_item: result.rows[0] };
  });

  // Remove technology from research queue
  app.delete<{ 
    Params: { empire_id: string; queue_id: string } 
  }>('/v1/empires/:empire_id/research-queue/:queue_id', async (request, reply) => {
    const { empire_id, queue_id } = request.params;

    const result = await pool.query(`
      DELETE FROM research_queue 
      WHERE id = $1 AND empire_id = $2
      RETURNING *
    `, [queue_id, empire_id]);

    if (result.rows.length === 0) {
      reply.status(404);
      return { error: 'Research queue item not found' };
    }

    return { deleted: result.rows[0] };
  });

  // Invest research points in a technology
  app.post<{ 
    Params: { empire_id: string };
    Body: { tech_id: string; research_points: number } 
  }>('/v1/empires/:empire_id/research', async (request, reply) => {
    const { empire_id } = request.params;
    const { tech_id, research_points } = request.body;

    if (research_points <= 0) {
      reply.status(400);
      return { error: 'Research points must be positive' };
    }

    // Get current progress and tech info
    const progressResult = await pool.query(`
      SELECT tp.*, tn.base_cost_knw, tn.name
      FROM tech_progress tp
      JOIN tech_nodes tn ON tp.tech_id = tn.id
      WHERE tp.empire_id = $1 AND tp.tech_id = $2
    `, [empire_id, tech_id]);

    if (progressResult.rows.length === 0) {
      reply.status(404);
      return { error: 'Technology not found' };
    }

    const progress = progressResult.rows[0];
    if (progress.unlocked) {
      reply.status(400);
      return { error: 'Technology already unlocked' };
    }

    // Calculate new investment
    const newInvestment = progress.invested_knw + research_points;
    const isUnlocked = newInvestment >= progress.base_cost_knw;

    // Update progress
    const updateResult = await pool.query(`
      UPDATE tech_progress 
      SET invested_knw = $1, unlocked = $2, unlocked_at = $3
      WHERE empire_id = $4 AND tech_id = $5
      RETURNING *
    `, [
      newInvestment,
      isUnlocked,
      isUnlocked ? new Date() : null,
      empire_id,
      tech_id
    ]);

    // If unlocked, remove from research queue and apply bonuses
    if (isUnlocked) {
      await pool.query(`
        DELETE FROM research_queue
        WHERE empire_id = $1 AND tech_id = $2
      `, [empire_id, tech_id]);

      // Apply technology bonuses
      await pool.query(`
        SELECT apply_technology_bonuses($1, $2)
      `, [empire_id, tech_id]);
    }

    return {
      progress: updateResult.rows[0],
      unlocked: isUnlocked,
      overflow: isUnlocked ? Math.max(0, newInvestment - progress.base_cost_knw) : 0
    };
  });

  // Get all technology branches
  app.get('/v1/tech-branches', async () => {
    const result = await pool.query(`
      SELECT branch, COUNT(*) as tech_count, MIN(tier) as min_tier, MAX(tier) as max_tier
      FROM tech_nodes 
      GROUP BY branch 
      ORDER BY branch
    `);
    
    return { branches: result.rows };
  });

  // Get technologies by branch
  app.get<{ Params: { branch: string } }>('/v1/tech-branches/:branch', async (request) => {
    const { branch } = request.params;

    const result = await pool.query(`
      SELECT * FROM tech_nodes
      WHERE branch = $1
      ORDER BY tier, name
    `, [branch]);

    return { technologies: result.rows };
  });

  // Process research for all empires (called by turn processor)
  app.post('/v1/process-research', async (request, reply) => {
    try {
      // Get all empires with research queues
      const empiresResult = await pool.query(`
        SELECT DISTINCT empire_id FROM research_queue
      `);

      const results = [];

      for (const empire of empiresResult.rows) {
        const empireId = empire.empire_id;

        // Get empire's research resource
        const resourceResult = await pool.query(`
          SELECT amount FROM empire_resources
          WHERE empire_id = $1 AND resource_type = 'research'
        `, [empireId]);

        if (resourceResult.rows.length === 0) continue;

        let availableResearch = Number(resourceResult.rows[0].amount);
        if (availableResearch <= 0) continue;

        // Get research queue ordered by priority
        const queueResult = await pool.query(`
          SELECT rq.*, tn.base_cost_knw, tp.invested_knw
          FROM research_queue rq
          JOIN tech_nodes tn ON rq.tech_id = tn.id
          LEFT JOIN tech_progress tp ON rq.tech_id = tp.tech_id AND rq.empire_id = tp.empire_id
          WHERE rq.empire_id = $1
          ORDER BY rq.priority ASC
        `, [empireId]);

        let totalSpent = 0;
        const completedTechs = [];

        for (const queueItem of queueResult.rows) {
          if (availableResearch <= 0) break;

          const currentInvestment = queueItem.invested_knw || 0;
          const requiredPoints = queueItem.base_cost_knw - currentInvestment;
          const pointsToSpend = Math.min(availableResearch, requiredPoints);

          if (pointsToSpend > 0) {
            const newInvestment = currentInvestment + pointsToSpend;
            const isCompleted = newInvestment >= queueItem.base_cost_knw;

            // Update tech progress
            await pool.query(`
              UPDATE tech_progress
              SET invested_knw = $1, unlocked = $2, unlocked_at = $3
              WHERE empire_id = $4 AND tech_id = $5
            `, [
              newInvestment,
              isCompleted,
              isCompleted ? new Date() : null,
              empireId,
              queueItem.tech_id
            ]);

            availableResearch -= pointsToSpend;
            totalSpent += pointsToSpend;

            if (isCompleted) {
              // Remove from research queue
              await pool.query(`
                DELETE FROM research_queue
                WHERE empire_id = $1 AND tech_id = $2
              `, [empireId, queueItem.tech_id]);

              // Apply technology bonuses
              await pool.query(`
                SELECT apply_technology_bonuses($1, $2)
              `, [empireId, queueItem.tech_id]);

              completedTechs.push({
                tech_id: queueItem.tech_id,
                name: queueItem.name || queueItem.tech_id
              });
            }
          }
        }

        // Update empire's research resource
        if (totalSpent > 0) {
          await pool.query(`
            UPDATE empire_resources
            SET amount = amount - $1
            WHERE empire_id = $2 AND resource_type = 'research'
          `, [totalSpent, empireId]);
        }

        results.push({
          empire_id: empireId,
          research_spent: totalSpent,
          completed_technologies: completedTechs
        });
      }

      return { results };
    } catch (error) {
      console.error('Error processing research:', error);
      reply.status(500);
      return { error: 'Failed to process research' };
    }
  });

  return app;
}

// Start server
async function start() {
  try {
    const app = await buildServer();
    const port = parseInt(process.env.PORT || '3004');
    
    await app.listen({ port, host: '0.0.0.0' });
    console.log(`Technology service listening on port ${port}`);
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  start();
}
