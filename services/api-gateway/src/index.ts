import Fastify from 'fastify';
import { Pool } from 'pg';

const ENV = (globalThis as any).process?.env ?? {};
const port = Number(ENV.PORT || 19081);
const ORDERS_SVC_URL = ENV.ORDERS_SVC_URL || 'http://localhost:8081';
const FLEETS_SVC_URL = ENV.FLEETS_SVC_URL || 'http://localhost:8082';
const COLONIES_SVC_URL = ENV.COLONIES_SVC_URL || 'http://localhost:8083';
const TECH_SVC_URL = ENV.TECH_SVC_URL || 'http://localhost:8084';
const MARKET_SVC_URL = ENV.MARKET_SVC_URL || 'http://localhost:8085';
const MATERIALS_SVC_URL = ENV.MATERIALS_SVC_URL || 'http://localhost:8086';
const EVENTS_WS_URL = ENV.EVENTS_WS_URL || 'ws://localhost:8090';

// Database connection for stellar data
const pool = new Pool({
  host: ENV.PGHOST || 'localhost',
  port: Number(ENV.PGPORT) || 5433,
  database: ENV.PGDATABASE || 'gg',
  user: ENV.PGUSER || 'gg',
  password: ENV.PGPASSWORD || 'ggpassword',
});

export async function buildServer() {
  const app = Fastify({ logger: true });

  // Add CORS support for frontend
  app.addHook('onRequest', async (request, reply) => {
    reply.header('Access-Control-Allow-Origin', '*');
    reply.header('Access-Control-Allow-Methods', 'GET, POST, PATCH, PUT, DELETE, OPTIONS');
    reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Idempotency-Key');
    reply.header('Access-Control-Allow-Credentials', 'false');
  });

  // Handle preflight requests
  app.options('*', async (request, reply) => {
    reply.status(200).send();
  });

  // Register Fastify websocket plugin (ESM-friendly dynamic import)
  const fastifyWs = (await import('@fastify/websocket')).default as any;
  app.register(fastifyWs);

  app.get('/v1/health', async () => {
    return { ok: true };
  });
  // Liveness and aggregated readiness
  app.get('/healthz', async () => ({ status: 'ok' }));
  app.get('/readyz', async (_req, rep) => {
    // Derive HTTP base for event dispatcher from WS URL
    const evtHttp = EVENTS_WS_URL.startsWith('ws')
      ? EVENTS_WS_URL.replace(/^wss?:\/\//, (m: string) =>
          m === 'wss://' ? 'https://' : 'http://',
        )
      : EVENTS_WS_URL;

    const targets = [
      { name: 'orders', url: `${ORDERS_SVC_URL}/readyz` },
      { name: 'fleets', url: `${FLEETS_SVC_URL}/readyz` },
      { name: 'events', url: `${evtHttp}/readyz` },
    ];

    const results = await Promise.all(
      targets.map(async (t) => {
        try {
          const res = await fetch(t.url);
          return { name: t.name, status: res.status };
        } catch {
          return { name: t.name, status: 0 };
        }
      }),
    );

    const allReady = results.every((r) => r.status === 200);
    const details = Object.fromEntries(results.map((r) => [r.name, r.status]));
    return rep.status(allReady ? 200 : 503).send({ ready: allReady, details });
  });

  // Friendly root index to help manual testing
  app.get('/', async (_req, rep) => {
    return rep.send({
      ok: true,
      service: 'api-gateway',
      hint: 'Use the /v1/* endpoints below',
      endpoints: {
        health: '/v1/health',
        turn: 'GET /v1/turn',
        fleets_list: 'GET /v1/fleets',
        fleets_create: 'POST /v1/fleets',
        fleets_update: 'PATCH /v1/fleets/:id',
        orders_submit: 'POST /v1/orders',
        orders_list: 'GET /v1/orders',
        orders_get: 'GET /v1/orders/:id',
        stream: 'WS /v1/stream',
      },
    });
  });

  // Proxy: POST /v1/orders -> orders-svc (validate body before forwarding)
  app.post<{ Body: { kind: string; payload: Record<string, unknown> } }>(
    '/v1/orders',
    {
      schema: {
        body: {
          type: 'object',
          properties: {
            kind: { type: 'string' },
            payload: { type: 'object' },
          },
          required: ['kind', 'payload'],
          additionalProperties: true,
        },
      },
    },
    async (req, rep) => {
      const url = `${ORDERS_SVC_URL}/v1/orders`;
      const headers: Record<string, string> = {
        'content-type': 'application/json',
      };
      // Pass through idempotency and auth headers if present
      const idem = req.headers['idempotency-key'];
      if (typeof idem === 'string') headers['idempotency-key'] = idem;
      const auth = req.headers['authorization'];
      if (typeof auth === 'string') headers['authorization'] = auth;

      const body = JSON.stringify(req.body ?? {});
      const res = await fetch(url, { method: 'POST', headers, body });
      const text = await res.text();

      // Try to return JSON if possible
      try {
        const json = JSON.parse(text);
        return rep.status(res.status).send(json);
      } catch {
        return rep.status(res.status).send(text);
      }
    },
  );

  // Proxy: GET /v1/fleets -> fleets-svc
  // Proxy: GET /v1/systems -> fleets-svc
  app.get('/v1/systems', async (_req, rep) => {
    const res = await fetch(`${FLEETS_SVC_URL}/v1/systems`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  app.get('/v1/fleets', async (_req, rep) => {
    const res = await fetch(`${FLEETS_SVC_URL}/v1/fleets`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });
  // Proxy: GET /v1/systems/:id/neighbors -> fleets-svc
  app.get('/v1/systems/:id/neighbors', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${FLEETS_SVC_URL}/v1/systems/${id}/neighbors`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/empires/:id/fleets -> fleets-svc
  app.get('/v1/empires/:id/fleets', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${FLEETS_SVC_URL}/v1/empires/${id}/fleets`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });


  // Proxy: GET /v1/fleets/:id -> fleets-svc
  app.get('/v1/fleets/:id', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${FLEETS_SVC_URL}/v1/fleets/${id}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/fleets -> fleets-svc
  app.post('/v1/fleets', async (req, rep) => {
    const res = await fetch(`${FLEETS_SVC_URL}/v1/fleets`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: PATCH /v1/fleets/:id -> fleets-svc
  app.patch('/v1/fleets/:id', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${FLEETS_SVC_URL}/v1/fleets/${id}`, {
      method: 'PATCH',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/orders -> orders-svc
  app.get('/v1/orders', async (_req, rep) => {
    const res = await fetch(`${ORDERS_SVC_URL}/v1/orders`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/orders/:id -> orders-svc
  app.get('/v1/orders/:id', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${ORDERS_SVC_URL}/v1/orders/${id}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });
  // Proxy: GET /v1/battles -> orders-svc
  app.get('/v1/battles', async (_req, rep) => {
    const res = await fetch(`${ORDERS_SVC_URL}/v1/battles`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Colonies service routes
  // Proxy: GET /v1/colonies -> colonies-svc
  app.get('/v1/colonies', async (req, rep) => {
    const queryString = req.url.split('?')[1] || '';
    const res = await fetch(`${COLONIES_SVC_URL}/v1/colonies?${queryString}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/colonies -> colonies-svc
  app.post('/v1/colonies', async (req, rep) => {
    const res = await fetch(`${COLONIES_SVC_URL}/v1/colonies`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/colonies/:id -> colonies-svc
  app.get('/v1/colonies/:id', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${COLONIES_SVC_URL}/v1/colonies/${id}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/colonies/:id/improvements -> colonies-svc
  app.get('/v1/colonies/:id/improvements', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${COLONIES_SVC_URL}/v1/colonies/${id}/improvements`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/colonies/:id/improvements -> colonies-svc
  app.post('/v1/colonies/:id/improvements', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${COLONIES_SVC_URL}/v1/colonies/${id}/improvements`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/empires/:empire_id/resources -> colonies-svc
  app.get('/v1/empires/:empire_id/resources', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${COLONIES_SVC_URL}/v1/empires/${empire_id}/resources`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/planet-types -> colonies-svc
  app.get('/v1/planet-types', async (_req, rep) => {
    const res = await fetch(`${COLONIES_SVC_URL}/v1/planet-types`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/improvement-types -> colonies-svc
  app.get('/v1/improvement-types', async (_req, rep) => {
    const res = await fetch(`${COLONIES_SVC_URL}/v1/improvement-types`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/empires/:empire_id/improvement-types -> colonies-svc
  app.get('/v1/empires/:empire_id/improvement-types', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${COLONIES_SVC_URL}/v1/empires/${empire_id}/improvement-types`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/empires/:empire_id/tech-bonuses -> colonies-svc
  app.get('/v1/empires/:empire_id/tech-bonuses', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${COLONIES_SVC_URL}/v1/empires/${empire_id}/tech-bonuses`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Technology service routes
  // Proxy: GET /v1/empires/:empire_id/tech-tree -> tech-svc
  app.get('/v1/empires/:empire_id/tech-tree', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${TECH_SVC_URL}/v1/empires/${empire_id}/tech-tree`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/empires/:empire_id/research-queue -> tech-svc
  app.get('/v1/empires/:empire_id/research-queue', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${TECH_SVC_URL}/v1/empires/${empire_id}/research-queue`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/empires/:empire_id/research-queue -> tech-svc
  app.post('/v1/empires/:empire_id/research-queue', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${TECH_SVC_URL}/v1/empires/${empire_id}/research-queue`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: DELETE /v1/empires/:empire_id/research-queue/:queue_id -> tech-svc
  app.delete('/v1/empires/:empire_id/research-queue/:queue_id', async (req, rep) => {
    const { empire_id, queue_id } = req.params as any as { empire_id: string; queue_id: string };
    const res = await fetch(`${TECH_SVC_URL}/v1/empires/${empire_id}/research-queue/${queue_id}`, {
      method: 'DELETE',
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/empires/:empire_id/research -> tech-svc
  app.post('/v1/empires/:empire_id/research', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${TECH_SVC_URL}/v1/empires/${empire_id}/research`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/tech-branches -> tech-svc
  app.get('/v1/tech-branches', async (_req, rep) => {
    const res = await fetch(`${TECH_SVC_URL}/v1/tech-branches`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/tech-branches/:branch -> tech-svc
  app.get('/v1/tech-branches/:branch', async (req, rep) => {
    const { branch } = req.params as any as { branch: string };
    const res = await fetch(`${TECH_SVC_URL}/v1/tech-branches/${branch}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/process-research -> tech-svc
  app.post('/v1/process-research', async (_req, rep) => {
    const res = await fetch(`${TECH_SVC_URL}/v1/process-research`, {
      method: 'POST',
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Market service routes
  // Proxy: GET /v1/market/prices -> market-svc
  app.get('/v1/market/prices', async (_req, rep) => {
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/prices`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/market/orders -> market-svc
  app.get('/v1/market/orders', async (req, rep) => {
    const queryString = req.url.split('?')[1] || '';
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/orders?${queryString}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/market/orders -> market-svc
  app.post('/v1/market/orders', async (req, rep) => {
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/orders`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: DELETE /v1/market/orders/:order_id -> market-svc
  app.delete('/v1/market/orders/:order_id', async (req, rep) => {
    const { order_id } = req.params as any as { order_id: string };
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/orders/${order_id}`, {
      method: 'DELETE',
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/market/transactions -> market-svc
  app.get('/v1/market/transactions', async (req, rep) => {
    const queryString = req.url.split('?')[1] || '';
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/transactions?${queryString}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/market/execute-trades -> market-svc
  app.post('/v1/market/execute-trades', async (_req, rep) => {
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/execute-trades`, {
      method: 'POST',
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/empires/:empire_id/trade-routes -> market-svc
  app.get('/v1/empires/:empire_id/trade-routes', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${MARKET_SVC_URL}/v1/empires/${empire_id}/trade-routes`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/empires/:empire_id/trade-routes -> market-svc
  app.post('/v1/empires/:empire_id/trade-routes', async (req, rep) => {
    const { empire_id } = req.params as any as { empire_id: string };
    const res = await fetch(`${MARKET_SVC_URL}/v1/empires/${empire_id}/trade-routes`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/market/stats -> market-svc
  app.get('/v1/market/stats', async (_req, rep) => {
    const res = await fetch(`${MARKET_SVC_URL}/v1/market/stats`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Materials service routes
  // Proxy: GET /v1/materials -> materials-svc
  app.get('/v1/materials', async (_req, rep) => {
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/materials`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/materials/:id -> materials-svc
  app.get('/v1/materials/:id', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/materials/${id}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/material-deposits -> materials-svc
  app.get('/v1/material-deposits', async (req, rep) => {
    const queryString = new URLSearchParams(req.query as any).toString();
    const url = `${MATERIALS_SVC_URL}/v1/material-deposits${queryString ? '?' + queryString : ''}`;
    const res = await fetch(url);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/stations -> materials-svc
  app.get('/v1/stations', async (req, rep) => {
    const queryString = new URLSearchParams(req.query as any).toString();
    const url = `${MATERIALS_SVC_URL}/v1/stations${queryString ? '?' + queryString : ''}`;
    const res = await fetch(url);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/stations/:id -> materials-svc
  app.get('/v1/stations/:id', async (req, rep) => {
    const { id } = req.params as any as { id: string };
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/stations/${id}`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/stations/:station_id/materials -> materials-svc
  app.get('/v1/stations/:station_id/materials', async (req, rep) => {
    const { station_id } = req.params as any as { station_id: string };
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/stations/${station_id}/materials`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/mining-operations -> materials-svc
  app.get('/v1/mining-operations', async (req, rep) => {
    const queryString = new URLSearchParams(req.query as any).toString();
    const url = `${MATERIALS_SVC_URL}/v1/mining-operations${queryString ? '?' + queryString : ''}`;
    const res = await fetch(url);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/station-types -> materials-svc
  app.get('/v1/station-types', async (_req, rep) => {
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/station-types`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: GET /v1/processing-recipes -> materials-svc
  app.get('/v1/processing-recipes', async (_req, rep) => {
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/processing-recipes`);
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/survey-orders -> materials-svc
  app.post('/v1/survey-orders', async (req, rep) => {
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/survey-orders`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

  // Proxy: POST /v1/station-construction-orders -> materials-svc
  app.post('/v1/station-construction-orders', async (req, rep) => {
    const res = await fetch(`${MATERIALS_SVC_URL}/v1/station-construction-orders`, {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify((req as any).body ?? {}),
    });
    const text = await res.text();
    try {
      return rep.status(res.status).send(JSON.parse(text));
    } catch {
      return rep.status(res.status).send(text);
    }
  });

	  // Proxy: GET /v1/turn -> orders-svc
	  app.get('/v1/turn', async (_req, rep) => {
	    const res = await fetch(`${ORDERS_SVC_URL}/v1/turn`);
	    const text = await res.text();
	    try {
	      return rep.status(res.status).send(JSON.parse(text));
	    } catch {
	      return rep.status(res.status).send(text);
	    }
	  });

  // Stellar database endpoints
  app.get('/v1/stellar/stars', async (req, rep) => {
    try {
      const query = req.query as any;
      const { max_distance = 1000, limit = 5000 } = query;

      const result = await pool.query(`
        SELECT
          star_id,
          src,
          src_key,
          gaia_source_id,
          name,
          catalog_name,
          ra_deg,
          dec_deg,
          distance_ly,
          x_pc,
          y_pc,
          z_pc,
          spectral_type,
          stellar_color,
          mass_solar,
          radius_solar,
          teff_k,
          luminosity_solar,
          mag_v,
          absolute_magnitude,
          color_bv,
          proper_name,
          hipparcos_id,
          henry_draper_id,
          gliese_id,
          hz_inner_au,
          hz_outer_au,
          discovery_status,
          is_colonizable,
          data_quality_score,
          (SELECT COUNT(*)::integer FROM planets WHERE star_id = s.star_id) as planet_count
        FROM stars s
        WHERE distance_ly <= $1 OR distance_ly = 0
        ORDER BY distance_ly
        LIMIT $2
      `, [max_distance, limit]);

      return rep.send({
        stars: result.rows,
        total: result.rows.length,
        max_distance: parseFloat(max_distance)
      });
    } catch (error) {
      app.log.error({ error }, 'Error fetching stars');
      return rep.status(500).send({ error: 'Failed to fetch stars' });
    }
  });

  app.get('/v1/stellar/stars/:star_id', async (req, rep) => {
    try {
      const { star_id } = req.params as any;

      const [starResult, planetsResult, moonsResult] = await Promise.all([
        pool.query(`
          SELECT
            star_id, name, catalog_name, ra_deg, dec_deg, distance_ly,
            x_pc, y_pc, z_pc,
            spectral_type, mass_solar, radius_solar, teff_k, luminosity_solar,
            mag_v, absolute_magnitude, color_bv, stellar_color,
            proper_name, hipparcos_id, henry_draper_id, gliese_id,
            hz_inner_au, hz_outer_au, discovery_status, is_colonizable,
            data_quality_score
          FROM stars WHERE star_id = $1
        `, [star_id]),
        pool.query(`
          SELECT
            planet_id, name, mass_earth, radius_earth, sma_au, period_days,
            composition, habitability_score, in_habitable_zone, mineral_richness,
            energy_potential, discovery_year, is_colonized, exploration_status,
            atmosphere, has_atmosphere, surface_temp_k, eq_temp_k,
            raw_materials, material_category, dominant_color, color_hex, albedo,
            visual_texture, atmospheric_effects
          FROM planets WHERE star_id = $1 ORDER BY sma_au
        `, [star_id]),
        pool.query(`
          SELECT
            m.moon_id, m.planet_id, m.name, m.distance_km, m.orbital_period_days,
            m.diameter_km, m.mass_earth, m.radius_earth, m.surface_composition,
            m.raw_materials, m.material_category, m.dominant_color, m.color_hex,
            m.albedo, m.visual_texture, m.discovery_year, m.moon_type,
            m.mineral_richness, m.exploration_status
          FROM moons m
          JOIN planets p ON m.planet_id = p.planet_id
          WHERE p.star_id = $1
          ORDER BY m.planet_id, m.distance_km
        `, [star_id])
      ]);

      if (starResult.rows.length === 0) {
        return rep.status(404).send({ error: 'Star not found' });
      }

      const star = starResult.rows[0];
      const planets = planetsResult.rows;
      const moons = moonsResult.rows;

      // Convert moon decimal fields from strings to numbers
      const processedMoons = moons.map(moon => ({
        ...moon,
        orbital_period_days: moon.orbital_period_days ? parseFloat(moon.orbital_period_days) : null,
        diameter_km: moon.diameter_km ? parseFloat(moon.diameter_km) : null,
        albedo: moon.albedo ? parseFloat(moon.albedo) : null,
        mass_earth: moon.mass_earth ? parseFloat(moon.mass_earth) : null,
        radius_earth: moon.radius_earth ? parseFloat(moon.radius_earth) : null,
        mineral_richness: moon.mineral_richness ? parseFloat(moon.mineral_richness) : null
      }));

      // Group moons by planet_id
      const moonsByPlanet = processedMoons.reduce((acc, moon) => {
        if (!acc[moon.planet_id]) {
          acc[moon.planet_id] = [];
        }
        acc[moon.planet_id].push(moon);
        return acc;
      }, {});

      // Add moons to each planet
      planets.forEach(planet => {
        planet.moons = moonsByPlanet[planet.planet_id] || [];
      });

      star.planets = planets;

      return rep.send(star);
    } catch (error) {
      app.log.error({ error }, 'Error fetching star details');
      return rep.status(500).send({ error: 'Failed to fetch star details' });
    }
  });

  app.get('/v1/stellar/statistics', async (req, rep) => {
    try {
      const result = await pool.query(`
        SELECT
          (SELECT COUNT(*) FROM stars) as total_stars,
          (SELECT COUNT(*) FROM planets) as total_planets,
          (SELECT COUNT(*) FROM planets WHERE in_habitable_zone = true) as habitable_planets,
          (SELECT COUNT(*) FROM planets WHERE is_colonized = true) as colonized_planets,
          (SELECT AVG(distance_ly) FROM stars WHERE distance_ly > 0) as avg_star_distance,
          (SELECT COUNT(*) FROM stars WHERE spectral_type LIKE 'M%') as m_dwarf_count,
          (SELECT COUNT(*) FROM stars WHERE spectral_type LIKE 'G%') as g_dwarf_count,
          (SELECT COUNT(*) FROM planets WHERE composition = 'rocky') as rocky_planets,
          (SELECT COUNT(*) FROM planets WHERE planet_status = 'confirmed') as confirmed_planets,
          (SELECT COUNT(*) FROM planets WHERE planet_status = 'procedural') as procedural_planets
      `);

      const stats = result.rows[0];

      // Convert string numbers to proper types
      Object.keys(stats).forEach(key => {
        if (stats[key] !== null) {
          if (key.includes('avg')) {
            stats[key] = parseFloat(stats[key]);
          } else {
            stats[key] = parseInt(stats[key]);
          }
        }
      });

      return rep.send(stats);
    } catch (error) {
      app.log.error({ error }, 'Error fetching statistics');
      return rep.status(500).send({ error: 'Failed to fetch statistics' });
    }
  });

  // Planets endpoint
  app.get('/v1/stellar/planets', async (req, rep) => {
    try {
      const { star_id, status, limit = 50 } = req.query as any;

      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (star_id) {
        whereClause += ' AND p.star_id = $' + (params.length + 1);
        params.push(star_id);
      }

      if (status) {
        whereClause += ' AND p.planet_status = $' + (params.length + 1);
        params.push(status);
      }

      whereClause += ' ORDER BY p.name LIMIT $' + (params.length + 1);
      params.push(limit);

      const result = await pool.query(`
        SELECT
          p.planet_id,
          p.name,
          p.host_star_name,
          p.mass_earth,
          p.radius_earth,
          p.sma_au,
          p.period_days,
          p.eccentricity,
          p.eq_temp_k,
          p.composition,
          p.discovery_method,
          p.discovery_year,
          p.in_habitable_zone,
          p.planet_status,
          p.src,
          s.name as star_name,
          s.distance_ly
        FROM planets p
        LEFT JOIN stars s ON p.star_id = s.star_id
        ${whereClause}
      `, params);

      return rep.send({
        planets: result.rows,
        total: result.rows.length
      });
    } catch (error) {
      app.log.error({ error }, 'Error fetching planets');
      return rep.status(500).send({ error: 'Failed to fetch planets' });
    }
  });

  // WS proxy: GET /v1/stream -> event-dispatcher WS (simple pipe)
  app.get(
    '/v1/stream',
    { websocket: true } as any,
    async (connection: any /* fastify ws plugin shape */) => {
      const { socket } = connection;
      // Lazy import ws to avoid ESM/CJS type clashing
      const WSMod: any = await import('ws' as any);
      const upstream = new WSMod.WebSocket(EVENTS_WS_URL);

      // Pipe upstream -> client
      upstream.on('message', (data: any) => {
        try {
          socket.send(data);
        } catch (err) {
          app.log.debug({ err }, 'ws downstream send failed');
        }
      });
      upstream.on('close', () => {
        try {
          socket.close();
        } catch (err) {
          app.log.debug({ err }, 'ws downstream close');
        }
      });
      upstream.on('error', () => {
        try {
          socket.close();
        } catch (err) {
          app.log.debug({ err }, 'ws downstream error-close');
        }
      });

      // Pipe client -> upstream (if needed later)
      socket.on('message', (data: any) => {
        try {
          upstream.send(data);
        } catch (err) {
          app.log.debug({ err }, 'ws upstream send failed');
        }
      });
      socket.on('close', () => {
        try {
          upstream.close();
        } catch (err) {
          app.log.debug({ err }, 'ws upstream close');
        }
      });
      socket.on('error', () => {
        try {
          upstream.close();
        } catch (err) {
          app.log.debug({ err }, 'ws upstream error-close');
        }
      });
    },
  );

  return app;
}

async function start() {
  const app = await buildServer();
  await app.listen({ port, host: '0.0.0.0' });
  console.log(`api-gateway listening on :${port}`);
}

if (
  (globalThis as any).process &&
  import.meta.url === `file://${(globalThis as any).process.argv[1]}`
) {
  start().catch((err) => {
    console.error(err);
    (globalThis as any).process.exit(1);
  });
}
