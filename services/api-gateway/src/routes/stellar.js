const express = require('express');
const { Pool } = require('pg');

const router = express.Router();

// Database connection
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5433,
  database: process.env.PGDATABASE || 'gg',
  user: process.env.PGUSER || 'gg',
  password: process.env.PGPASSWORD || 'ggpassword',
});

/**
 * GET /v1/stellar/stars
 * Get all stars within specified distance
 */
router.get('/stars', async (req, res) => {
  try {
    const { max_distance = 20, limit = 1000 } = req.query;
    
    const query = `
      SELECT
        s.star_id,
        s.src,
        s.name,
        s.catalog_name,
        s.ra_deg,
        s.dec_deg,
        s.distance_ly,
        s.spectral_type,
        s.stellar_color,
        s.mass_solar,
        s.radius_solar,
        s.teff_k,
        s.luminosity_solar,
        s.mag_v,
        s.hz_inner_au,
        s.hz_outer_au,
        s.discovery_status,
        s.is_colonizable,
        s.gaia_source_id,
        COUNT(p.planet_id) as planet_count
      FROM stars s
      LEFT JOIN planets p ON s.star_id = p.star_id
      WHERE s.distance_ly <= $1
      GROUP BY s.star_id, s.src, s.name, s.catalog_name, s.ra_deg, s.dec_deg, s.distance_ly,
               s.spectral_type, s.stellar_color, s.mass_solar, s.radius_solar, s.teff_k, s.luminosity_solar,
               s.mag_v, s.hz_inner_au, s.hz_outer_au, s.discovery_status, s.is_colonizable, s.gaia_source_id
      ORDER BY s.distance_ly
      LIMIT $2
    `;
    
    const result = await pool.query(query, [max_distance, limit]);
    
    res.json({
      stars: result.rows,
      total: result.rows.length,
      max_distance: parseFloat(max_distance)
    });
  } catch (error) {
    console.error('Error fetching stars:', error);
    res.status(500).json({ error: 'Failed to fetch stars' });
  }
});

/**
 * GET /v1/stellar/stars/:star_id
 * Get detailed information about a specific star
 */
router.get('/stars/:star_id', async (req, res) => {
  try {
    const { star_id } = req.params;
    
    const starQuery = `
      SELECT 
        star_id,
        src,
        name,
        catalog_name,
        ra_deg,
        dec_deg,
        distance_ly,
        parallax_mas,
        pm_ra_masyr,
        pm_dec_masyr,
        mag_g,
        mag_v,
        mag_b,
        color_bv,
        spectral_type,
        mass_solar,
        radius_solar,
        teff_k,
        luminosity_solar,
        metallicity_fe_h,
        age_gyr,
        hz_inner_au,
        hz_outer_au,
        discovery_status,
        is_colonizable,
        created_at,
        updated_at
      FROM stars 
      WHERE star_id = $1
    `;
    
    const planetsQuery = `
      SELECT 
        planet_id,
        name,
        mass_earth,
        radius_earth,
        sma_au,
        period_days,
        eccentricity,
        inclination_deg,
        eq_temp_k,
        surface_temp_k,
        atmosphere,
        composition,
        density_gcc,
        habitability_score,
        in_habitable_zone,
        has_atmosphere,
        has_water,
        mineral_richness,
        energy_potential,
        rare_elements,
        discovery_method,
        discovery_year,
        discovery_facility,
        is_colonized,
        is_exploitable,
        exploration_status
      FROM planets 
      WHERE star_id = $1
      ORDER BY sma_au
    `;
    
    const neighborsQuery = `
      SELECT 
        sn.neighbor_star_id,
        s.name as neighbor_name,
        sn.distance_ly,
        sn.travel_time_days,
        sn.hyperlane_type,
        sn.is_discovered
      FROM star_neighbors sn
      JOIN stars s ON s.star_id = sn.neighbor_star_id
      WHERE sn.star_id = $1
      ORDER BY sn.distance_ly
    `;
    
    const [starResult, planetsResult, neighborsResult] = await Promise.all([
      pool.query(starQuery, [star_id]),
      pool.query(planetsQuery, [star_id]),
      pool.query(neighborsQuery, [star_id])
    ]);
    
    if (starResult.rows.length === 0) {
      return res.status(404).json({ error: 'Star not found' });
    }
    
    const star = starResult.rows[0];
    star.planets = planetsResult.rows;
    star.neighbors = neighborsResult.rows;
    
    res.json(star);
  } catch (error) {
    console.error('Error fetching star details:', error);
    res.status(500).json({ error: 'Failed to fetch star details' });
  }
});

/**
 * GET /v1/stellar/planets
 * Get all planets with optional filters
 */
router.get('/planets', async (req, res) => {
  try {
    const { 
      habitable_only = false, 
      composition, 
      min_habitability = 0,
      max_distance = 20,
      limit = 1000 
    } = req.query;
    
    let whereConditions = ['s.distance_ly <= $1'];
    let params = [max_distance];
    let paramIndex = 2;
    
    if (habitable_only === 'true') {
      whereConditions.push('p.in_habitable_zone = true');
    }
    
    if (composition) {
      whereConditions.push(`p.composition = $${paramIndex}`);
      params.push(composition);
      paramIndex++;
    }
    
    if (min_habitability > 0) {
      whereConditions.push(`p.habitability_score >= $${paramIndex}`);
      params.push(min_habitability);
      paramIndex++;
    }
    
    const query = `
      SELECT 
        p.planet_id,
        p.name as planet_name,
        s.name as star_name,
        s.distance_ly as star_distance_ly,
        s.spectral_type,
        p.mass_earth,
        p.radius_earth,
        p.sma_au,
        p.period_days,
        p.eq_temp_k,
        p.composition,
        p.habitability_score,
        p.in_habitable_zone,
        p.mineral_richness,
        p.energy_potential,
        p.discovery_year,
        p.discovery_method,
        p.is_colonized,
        p.exploration_status
      FROM planets p
      JOIN stars s ON s.star_id = p.star_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY s.distance_ly, p.sma_au
      LIMIT $${paramIndex}
    `;
    
    params.push(limit);
    
    const result = await pool.query(query, params);
    
    res.json({
      planets: result.rows,
      total: result.rows.length,
      filters: {
        habitable_only: habitable_only === 'true',
        composition,
        min_habitability: parseFloat(min_habitability),
        max_distance: parseFloat(max_distance)
      }
    });
  } catch (error) {
    console.error('Error fetching planets:', error);
    res.status(500).json({ error: 'Failed to fetch planets' });
  }
});

/**
 * GET /v1/stellar/search
 * Search stars and planets by name
 */
router.get('/search', async (req, res) => {
  try {
    const { q, type = 'both', limit = 50 } = req.query;
    
    if (!q || q.length < 2) {
      return res.status(400).json({ error: 'Query must be at least 2 characters' });
    }
    
    const searchTerm = `%${q}%`;
    let results = { stars: [], planets: [] };
    
    if (type === 'stars' || type === 'both') {
      const starQuery = `
        SELECT 
          star_id,
          name,
          catalog_name,
          distance_ly,
          spectral_type,
          'star' as result_type
        FROM stars 
        WHERE name ILIKE $1 OR catalog_name ILIKE $1
        ORDER BY distance_ly
        LIMIT $2
      `;
      
      const starResult = await pool.query(starQuery, [searchTerm, limit]);
      results.stars = starResult.rows;
    }
    
    if (type === 'planets' || type === 'both') {
      const planetQuery = `
        SELECT 
          p.planet_id,
          p.name as planet_name,
          s.name as star_name,
          s.distance_ly,
          p.composition,
          p.habitability_score,
          'planet' as result_type
        FROM planets p
        JOIN stars s ON s.star_id = p.star_id
        WHERE p.name ILIKE $1
        ORDER BY s.distance_ly
        LIMIT $2
      `;
      
      const planetResult = await pool.query(planetQuery, [searchTerm, limit]);
      results.planets = planetResult.rows;
    }
    
    res.json({
      query: q,
      results,
      total: results.stars.length + results.planets.length
    });
  } catch (error) {
    console.error('Error searching stellar objects:', error);
    res.status(500).json({ error: 'Failed to search stellar objects' });
  }
});

/**
 * GET /v1/stellar/statistics
 * Get statistical overview of the stellar database
 */
router.get('/statistics', async (req, res) => {
  try {
    const statsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM stars) as total_stars,
        (SELECT COUNT(*) FROM planets) as total_planets,
        (SELECT COUNT(*) FROM planets WHERE in_habitable_zone = true) as habitable_planets,
        (SELECT COUNT(*) FROM planets WHERE is_colonized = true) as colonized_planets,
        (SELECT COUNT(*) FROM star_neighbors) as hyperlane_connections,
        (SELECT AVG(distance_ly) FROM stars WHERE distance_ly > 0) as avg_star_distance,
        (SELECT MAX(distance_ly) FROM stars) as max_star_distance,
        (SELECT COUNT(*) FROM stars WHERE spectral_type LIKE 'M%') as m_dwarf_count,
        (SELECT COUNT(*) FROM stars WHERE spectral_type LIKE 'G%') as g_dwarf_count,
        (SELECT COUNT(*) FROM stars WHERE spectral_type LIKE 'K%') as k_dwarf_count,
        (SELECT COUNT(*) FROM planets WHERE composition = 'rocky') as rocky_planets,
        (SELECT COUNT(*) FROM planets WHERE composition = 'gas_giant') as gas_giants,
        (SELECT AVG(habitability_score) FROM planets WHERE habitability_score > 0) as avg_habitability
    `;
    
    const result = await pool.query(statsQuery);
    const stats = result.rows[0];
    
    // Convert string numbers to integers/floats
    Object.keys(stats).forEach(key => {
      if (stats[key] !== null) {
        if (key.includes('avg') || key.includes('habitability')) {
          stats[key] = parseFloat(stats[key]);
        } else {
          stats[key] = parseInt(stats[key]);
        }
      }
    });
    
    res.json(stats);
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
});

module.exports = router;
