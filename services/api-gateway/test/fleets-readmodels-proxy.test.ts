import { describe, it, expect, vi, beforeEach } from 'vitest';
import { buildServer } from '../src/index';

const mockFetch = vi.fn();
vi.stubGlobal('fetch', mockFetch as any);

describe('Fleets read model proxies', () => {
  beforeEach(() => mockFetch.mockReset());

  it('proxies /v1/systems/:id/neighbors', async () => {
    mockFetch.mockResolvedValueOnce({ status: 200, text: async () => JSON.stringify({ neighbors: [{ id: 'sys-2' }] }) } as any);
    const app = await buildServer();
    const res = await app.inject({ method: 'GET', url: '/v1/systems/sys-1/neighbors' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ neighbors: [{ id: 'sys-2' }] });
  });

  it('proxies /v1/empires/:id/fleets', async () => {
    mockFetch.mockResolvedValueOnce({ status: 200, text: async () => JSON.stringify({ fleets: [{ id: 'fleet-1' }] }) } as any);
    const app = await buildServer();
    const res = await app.inject({ method: 'GET', url: '/v1/empires/emp-1/fleets' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ fleets: [{ id: 'fleet-1' }] });
  });
});

