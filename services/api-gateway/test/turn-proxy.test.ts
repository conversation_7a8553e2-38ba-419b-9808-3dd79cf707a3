import { describe, it, expect, vi, beforeEach } from 'vitest';
import { buildServer } from '../src/index';

// Mock global fetch
const mockFetch = vi.fn();
vi.stubGlobal('fetch', mockFetch as any);

describe('GET /v1/turn proxy', () => {
  beforeEach(() => {
    mockFetch.mockReset();
  });

  it('forwards to orders-svc and returns JSON', async () => {
    const app = await buildServer();

    mockFetch.mockResolvedValueOnce({
      status: 200,
      text: async () => JSON.stringify({ turn: 1 }),
    } as any);

    const res = await app.inject({ method: 'GET', url: '/v1/turn' });

    expect(mockFetch).toHaveBeenCalledTimes(1);
    const [url] = mockFetch.mock.calls[0];
    expect(url).toMatch(/\/v1\/turn$/);

    expect(res.statusCode).toBe(200);
    const json = res.json();
    expect(json).toHaveProperty('turn');
    expect(typeof json.turn === 'number').toBe(true);

    await app.close();
  });
});
