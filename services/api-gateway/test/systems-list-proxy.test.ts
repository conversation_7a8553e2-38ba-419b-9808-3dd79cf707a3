import { describe, it, expect, vi, beforeEach } from 'vitest';
import { buildServer } from '../src/index';

const mockFetch = vi.fn();
vi.stubGlobal('fetch', mockFetch as any);

describe('GET /v1/systems proxy', () => {
  beforeEach(() => mockFetch.mockReset());

  it('proxies JSON response from fleets-svc', async () => {
    mockFetch.mockResolvedValueOnce({ status: 200, text: async () => JSON.stringify({ systems: [] }) } as any);
    const app = await buildServer();
    const res = await app.inject({ method: 'GET', url: '/v1/systems' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ systems: [] });
  });
});

