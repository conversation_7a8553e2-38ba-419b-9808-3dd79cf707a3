{"name": "api-gateway", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "tsc -p tsconfig.json", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@fastify/websocket": "^8.3.1", "@types/pg": "^8.15.5", "fastify": "^4.28.1", "pg": "^8.16.3", "ws": "^8.18.3"}, "devDependencies": {"tsx": "^4.16.2", "typescript": "^5.5.4", "vitest": "^2.0.5"}}