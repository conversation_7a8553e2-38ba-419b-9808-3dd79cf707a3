import { describe, it, expect, vi, beforeEach } from 'vitest';

// Provide a per-test client factory that the pg mock will use
let getClient: () => any = () => ({})
vi.mock('pg', () => ({
  default: {
    Pool: class {
      async connect() { return getClient(); }
    }
  }
}));

describe('attack order worker path', () => {
  const publish = vi.fn();
  const pub = { publish } as any;

  beforeEach(() => { publish.mockReset(); })

  it('applies a simple attack and emits battle events', async () => {
    // first select finds an attack order
    const mkClient = () => ({
      async query(sql: string, params?: any[]) {
        if (/^BEGIN$/.test(sql)) return {} as any;
        if (/^COMMIT$/.test(sql)) return {} as any;
        if (/^ROLLBACK$/.test(sql)) return {} as any;
        if (/^select id, kind, payload from orders/.test(sql)) {
          return { rows: [{ id: 'oA', kind: 'attack', payload: { fleetId: 'fleet-1', targetFleetId: 'fleet-2' } }] } as any;
        }
        if (/from fleets where id = \$1 for update/.test(sql)) {
          const id = params?.[0];
          if (id === 'fleet-1') return { rows: [{ id: 'fleet-1', empire_id: 'emp-1', system_id: 'sys-2', supply: 100 }] } as any;
          if (id === 'fleet-2') return { rows: [{ id: 'fleet-2', empire_id: 'emp-2', system_id: 'sys-2', supply: 100 }] } as any;
          return { rows: [] } as any;
        }
        if (/^update fleets set supply/.test(sql)) return { rows: [] } as any;
        if (/^insert into battles/.test(sql)) return { rows: [] } as any;
        if (/^update orders set status =/.test(sql)) return { rows: [] } as any;
        throw new Error('unexpected sql: ' + sql);
      },
      release() {}
    });
    getClient = mkClient;
    const { startApplyWorker } = await import('../src/index');
    const stop = startApplyWorker(pub, 5);
    await new Promise((r) => setTimeout(r, 25));
    stop();

    expect(publish).toHaveBeenCalledWith('battle.started', expect.objectContaining({ attackerId: 'fleet-1', targetId: 'fleet-2' }));
    expect(publish).toHaveBeenCalledWith('battle.resolved', expect.objectContaining({ attackerId: 'fleet-1', targetId: 'fleet-2' }));
  });
});

