import { describe, it, expect } from 'vitest';
import { buildServer } from '../src/index';

// unit-ish test for adjacency logic using TEST_MODE=true (DB checks are skipped)
// We validate the shape of responses when adjacency path is bypassed.
// For full adjacency, an integration test with PG is appropriate.

describe('orders-svc move adjacency (shape under test mode)', () => {
  it('accepts move payload shape and returns 202 in TEST_MODE', async () => {
    process.env.NODE_ENV = 'test';
    const app = await buildServer();
    const res = await app.inject({
      method: 'POST',
      url: '/v1/orders',
      payload: { kind: 'move', payload: { fleetId: 'f1', toSystemId: 'sys-2' } },
      headers: { 'content-type': 'application/json' },
    });
    expect(res.statusCode).toBe(202);
    const body = res.json();
    expect(body).toHaveProperty('orderId');
    expect(body).toHaveProperty('target_turn');
  });
});

