{"name": "orders-svc", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "tsc -p tsconfig.json", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"fastify": "^4.28.1", "nanoid": "^5.0.7", "nats": "^2.29.3", "pg": "^8.12.0"}, "devDependencies": {"tsx": "^4.16.2", "typescript": "^5.5.4", "vitest": "^2.0.5"}}