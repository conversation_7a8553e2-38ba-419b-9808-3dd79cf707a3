import { connect, StringCodec } from 'nats';
export function createConsolePublisher() {
    return {
        async publish(topic, payload) {
            // Dev stub: log only
            // eslint-disable-next-line no-console
            console.log(`[pub] ${topic}`, payload);
        },
    };
}
export async function createNatsPublisher(url) {
    const nc = await connect({ servers: url });
    const sc = StringCodec();
    return {
        async publish(topic, payload) {
            const data = sc.encode(JSON.stringify(payload));
            nc.publish(topic, data);
        },
        async close() {
            await nc.drain();
        },
    };
}
