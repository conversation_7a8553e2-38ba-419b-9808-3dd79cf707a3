let currentTurn = 1;
let lastTickTs = 0;
let timer = null;
export function getCurrentTurn() {
    return currentTurn;
}
export function getLastTickTs() {
    return lastTickTs;
}
export async function advanceTurn(publisher) {
    currentTurn += 1;
    lastTickTs = Date.now();
    try {
        if (publisher)
            await publisher.publish('turn.tick', { turn: currentTurn, ts: lastTickTs });
    }
    catch {
        // ignore publish errors in dev
    }
    return currentTurn;
}
export function startTurnClock(publisher, intervalMs = Number(process.env.TICK_MS || 5000)) {
    // In tests, do not start interval to keep tests deterministic.
    if (process.env.NODE_ENV === 'test')
        return () => { };
    if (timer)
        clearInterval(timer);
    lastTickTs = Date.now();
    timer = setInterval(() => {
        void advanceTurn(publisher);
    }, intervalMs);
    return () => {
        if (timer)
            clearInterval(timer);
        timer = null;
    };
}
