import Fastify from 'fastify';
import { nanoid } from 'nanoid';
import pg from 'pg';
const { Pool } = pg;
const port = Number(process.env.PORT || 8081);
const TEST_MODE = process.env.NODE_ENV === 'test';
import { createConsolePublisher, createNatsPublisher } from './publisher.js';
import { startDevTick } from './dev-tick.js';
import { getCurrentTurn, startTurnClock } from './turn.js';
function createMockSim() {
    return {
        async apply(order) {
            if (order.kind === 'move')
                return { applied: true, notes: 'moved one step (mock)' };
            if (order.kind === 'resupply')
                return { applied: true, notes: 'resupplied (mock)' };
            if (order.kind === 'attack')
                return { applied: true, notes: 'battle resolved (mock)' };
            return { applied: false, notes: 'unsupported kind (mock)' };
        },
        resolve_battle: (attacker<PERSON><PERSON>, targetJson) => {
            // Simple mock combat resolution
            const attacker = JSON.parse(attackerJson);
            const target = JSON.parse(targetJson);
            return JSON.stringify({
                attacker_supply_loss: 20,
                target_supply_loss: 30,
                attacker_destroyed: false,
                target_destroyed: target.supply <= 30,
                notes: "Mock combat resolution"
            });
        },
    };
}
import { loadSimFromEnv } from './sim-loader.js';
export async function buildServer(pub, sim) {
    const app = Fastify({ logger: true });
    // DB pool (internal docker network defaults)
    const pool = TEST_MODE
        ? null
        : new Pool({
            host: process.env.PGHOST || 'postgres',
            port: Number(process.env.PGPORT || 5432),
            user: process.env.PGUSER || 'gg',
            password: process.env.PGPASSWORD || 'ggpassword',
            database: process.env.PGDATABASE || 'gg',
        });
    let publisher;
    if (pub) {
        publisher = pub;
    }
    else if (process.env.NATS_URL) {
        publisher = await createNatsPublisher(process.env.NATS_URL);
    }
    else {
        publisher = createConsolePublisher();
    }
    // expose publisher for dev-tick
    app.publisher = publisher;
    const simCore = sim ?? (await loadSimFromEnv().catch(() => createMockSim()));
    // expose simCore for worker
    app.simCore = simCore;
    app.post('/v1/orders', {
        schema: {
            body: {
                type: 'object',
                properties: {
                    kind: { type: 'string' },
                    payload: { type: 'object' },
                },
                required: ['kind', 'payload'],
                additionalProperties: true,
            },
            response: {
                202: {
                    type: 'object',
                    properties: {
                        orderId: { type: 'string' },
                        target_turn: { type: 'integer' },
                        idemKey: { anyOf: [{ type: 'string' }, { type: 'array' }, { type: 'null' }] },
                        delta: {
                            type: 'object',
                            properties: {
                                applied: { type: 'boolean' },
                                notes: { type: 'string' },
                            },
                            required: ['applied', 'notes'],
                        },
                    },
                    required: ['orderId', 'target_turn'],
                    additionalProperties: true,
                },
            },
        },
    }, async (req, rep) => {
        // Validate move payload early
        if (req.body.kind === 'move') {
            const fid = req.body.payload?.fleetId || req.body.payload?.fleet_id;
            const to = req.body.payload?.toSystemId || req.body.payload?.to_system_id;
            if (!fid)
                return rep.status(400).send({ error: 'invalid_request', message: 'fleetId is required' });
            if (!to)
                return rep
                    .status(400)
                    .send({ error: 'invalid_request', message: 'toSystemId is required' });
            // Validate both entities exist
            if (!TEST_MODE && pool) {
                try {
                    const [[f], [s]] = await Promise.all([
                        pool.query('select 1 from fleets where id = $1', [fid]).then((r) => r.rows),
                        pool.query('select 1 from systems where id = $1', [to]).then((r) => r.rows),
                    ]);
                    if (!f)
                        return rep
                            .status(400)
                            .send({ error: 'invalid_request', field: 'fleetId', message: 'fleet not found' });
                    if (!s)
                        return rep.status(400).send({
                            error: 'invalid_request',
                            field: 'toSystemId',
                            message: 'toSystemId not found',
                        });
                }
                catch (err) {
                    app.log.error({ err }, 'validation query failed');
                    return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
                }
            }
            // Validate adjacency between current fleet system and target
            if (!TEST_MODE && pool) {
                try {
                    // find fleet's current system
                    const { rows: frows } = await pool.query('select system_id from fleets where id = $1', [fid]);
                    const cur = frows[0]?.system_id;
                    if (!cur)
                        return rep.status(400).send({ error: 'invalid_request', field: 'fleetId', message: 'fleet not found' });
                    const { rows: link } = await pool.query('select 1 from system_links where a = $1 and b = $2', [cur, to]);
                    if (!link[0])
                        return rep.status(400).send({ error: 'invalid_request', field: 'toSystemId', message: 'not adjacent' });
                }
                catch (err) {
                    app.log.error({ err }, 'adjacency validation failed');
                    return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
                }
            }
        }
        else if (req.body.kind === 'attack') {
            const p = (req.body.payload || {});
            const attackerId = p.fleetId || p.attackerId || p.fleet_id;
            const targetId = p.targetFleetId || p.target_id;
            if (typeof attackerId !== 'string' || attackerId.length < 1)
                return rep.status(400).send({ error: 'invalid_request', field: 'fleetId', message: 'fleetId is required' });
            if (typeof targetId !== 'string' || targetId.length < 1)
                return rep.status(400).send({ error: 'invalid_request', field: 'targetFleetId', message: 'targetFleetId is required' });
            if (!TEST_MODE && pool) {
                try {
                    const [[a], [t]] = await Promise.all([
                        pool.query('select system_id, empire_id from fleets where id = $1', [attackerId]).then((r) => r.rows),
                        pool.query('select system_id, empire_id from fleets where id = $1', [targetId]).then((r) => r.rows),
                    ]);
                    if (!a)
                        return rep.status(400).send({ error: 'invalid_request', field: 'fleetId', message: 'attacker not found' });
                    if (!t)
                        return rep.status(400).send({ error: 'invalid_request', field: 'targetFleetId', message: 'target not found' });
                    if (a.system_id !== t.system_id)
                        return rep.status(400).send({ error: 'invalid_request', message: 'fleets not co-located' });
                    if (a.empire_id === t.empire_id)
                        return rep.status(400).send({ error: 'invalid_request', message: 'cannot attack same empire' });
                }
                catch (err) {
                    app.log.error({ err }, 'attack validation failed');
                    return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
                }
            }
        }
        else if (req.body.kind === 'resupply') {
            const p = (req.body.payload || {});
            const fid = p.fleetId || p.fleet_id;
            const amt = p.amount;
            if (typeof fid !== 'string' || fid.length < 1) {
                return rep
                    .status(400)
                    .send({ error: 'invalid_request', field: 'fleetId', message: 'fleetId required' });
            }
            if (typeof amt !== 'number' || !Number.isInteger(amt) || amt <= 0) {
                return rep.status(400).send({
                    error: 'invalid_request',
                    field: 'amount',
                    message: 'amount must be positive integer',
                });
            }
            if (!TEST_MODE && pool) {
                try {
                    const { rows } = await pool.query('select 1 from fleets where id = $1', [fid]);
                    if (!rows[0])
                        return rep
                            .status(400)
                            .send({ error: 'invalid_request', field: 'fleetId', message: 'fleet not found' });
                }
                catch (err) {
                    app.log.error({ err }, 'validation query failed');
                    return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
                }
            }
        }
        // Minimal idempotency: echo Idempotency-Key if provided
        const idemKey = req.headers['idempotency-key'];
        // Idempotency stub: if same key seen before in this process, reuse orderId
        const memory = app.__idem || (app.__idem = new Map());
        let orderId;
        if (typeof idemKey === 'string' && memory.has(idemKey)) {
            orderId = memory.get(idemKey);
        }
        else {
            orderId = nanoid();
            if (typeof idemKey === 'string')
                memory.set(idemKey, orderId);
        }
        const target_turn = getCurrentTurn();
        // Call Sim Core (mock or wasm-bridged) for deterministic delta
        const delta = await simCore.apply({ kind: req.body.kind, payload: req.body.payload });
        // Persist order (minimal fields). Default empire for demo.
        const empireId = process.env.DEFAULT_EMPIRE_ID || 'emp-1';
        const idem = typeof idemKey === 'string' ? idemKey : null;
        const upsertSql = `
        insert into orders (id, empire_id, kind, payload, target_turn, idem_key, status)
        values ($1, $2, $3, $4::jsonb, $5, $6, $7)
        on conflict (idem_key) do update set id = excluded.id -- ensure same orderId returned on repeat
        returning id, target_turn, status
      `;
        const upsertParams = [
            orderId,
            empireId,
            req.body.kind,
            JSON.stringify(req.body.payload ?? {}),
            target_turn,
            idem,
            'accepted',
        ];
        let row;
        if (TEST_MODE || !pool) {
            row = { id: orderId, target_turn, status: 'accepted' };
        }
        else {
            try {
                const { rows } = await pool.query(upsertSql, upsertParams);
                row = rows[0];
            }
            catch (err) {
                app.log.error({ err }, 'orders upsert failed');
                return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
            }
        }
        // Publish receipt stub (to be wired to NATS later)
        await publisher.publish('order.receipt', {
            orderId: row.id,
            status: row.status,
            target_turn: row.target_turn,
            delta,
        });
        return rep
            .status(202)
            .send({ orderId: row.id, target_turn: row.target_turn, idemKey, delta });
    });
    // Expose current turn
    app.get('/v1/turn', async () => ({ turn: getCurrentTurn() }));
    // Fetch order by id
    app.get('/v1/orders/:id', async (req, rep) => {
        const id = req.params.id;
        if (TEST_MODE || !pool)
            return rep.status(404).send({ error: 'not_found' });
        try {
            const { rows } = await pool.query('select id, empire_id, kind, payload, target_turn, idem_key, status, created_at from orders where id = $1', [id]);
            if (!rows[0])
                return rep.status(404).send({ error: 'not_found' });
            return rep.send(rows[0]);
        }
        catch (err) {
            app.log.error({ err }, 'orders fetch failed');
            return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
        }
    });
    // List recent battles (from battles table)
    app.get('/v1/battles', async (_req, rep) => {
        if (TEST_MODE || !pool)
            return rep.send({ battles: [] });
        try {
            const { rows } = await pool.query(`select id, order_id, system_id, attacker_fleet_id, target_fleet_id, attacker_empire_id, target_empire_id,
                attacker_supply_after, target_supply_after, destroyed, created_at
           from battles
           order by created_at desc
           limit 50`);
            return rep.send({ battles: rows });
        }
        catch (err) {
            app.log.error({ err }, 'battles list failed');
            return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
        }
    });
    // Liveness and readiness
    app.get('/healthz', async () => ({ status: 'ok' }));
    app.get('/readyz', async () => ({ ready: true }));
    // List orders (basic)
    app.get('/v1/orders', async (_req, rep) => {
        if (TEST_MODE || !pool)
            return rep.send({ orders: [] });
        try {
            const { rows } = await pool.query('select id, empire_id, kind, payload, target_turn, idem_key, status, created_at from orders order by created_at desc limit 50');
            return rep.send({ orders: rows });
        }
        catch (err) {
            app.log.error({ err }, 'orders list failed');
            return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
        }
    });
    return app;
}
// Simple worker: pick an accepted 'move' order, apply movement with supply cost, then mark applied
export function startApplyWorker(publisher, simCore, intervalMs = Number(process.env.APPLY_MS || 2000)) {
    const pool = new Pool({
        host: process.env.PGHOST || 'postgres',
        port: Number(process.env.PGPORT || 5432),
        user: process.env.PGUSER || 'gg',
        password: process.env.PGPASSWORD || 'ggpassword',
        database: process.env.PGDATABASE || 'gg',
    });
    const COST_PER_MOVE = 10;
    const t = setInterval(async () => {
        console.log(`[Worker] Checking for orders at turn ${getCurrentTurn()}`);
        const client = await pool.connect();
        try {
            await client.query('BEGIN');
            // Find one accepted order (move, resupply, or attack)
            const sel = await client.query("select id, kind, payload from orders where status = 'accepted' and target_turn <= $1 and kind in ('move','resupply','attack') order by created_at asc limit 1 for update skip locked", [getCurrentTurn()]);
            const row = sel.rows[0];
            if (!row) {
                await client.query('COMMIT');
                return;
            }
            const orderId = row.id;
            const kind = row.kind;
            const p = row.payload || {};
            if (kind === 'move') {
                const fleetId = (p.fleetId || p.fleet_id);
                const toSystemId = (p.toSystemId || p.to_system_id);
                if (!fleetId || !toSystemId) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                    await client.query('COMMIT');
                    return;
                }
                // Move the fleet to the target system
                const f = await client.query('select id, system_id, supply from fleets where id = $1 for update', [fleetId]);
                if (!f.rows[0]) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                    await client.query('COMMIT');
                    return;
                }
                // Enforce supply cost
                if ((f.rows[0].supply ?? 0) < COST_PER_MOVE) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'rejected']);
                    await client.query('COMMIT');
                    try {
                        await publisher.publish('order.rejected', { orderId, reason: 'insufficient_supply' });
                    }
                    catch {
                        /* ignore publish error */
                    }
                    return;
                }
                // Apply move and decrement supply
                await client.query('update fleets set system_id = $2, supply = supply - $3 where id = $1', [
                    fleetId,
                    toSystemId,
                    COST_PER_MOVE,
                ]);
                await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                await client.query('COMMIT');
                try {
                    const fromSystemId = f.rows[0].system_id;
                    await publisher.publish('fleet.moved', {
                        fleetId,
                        from: fromSystemId,
                        to: toSystemId,
                        orderId,
                    });
                    await publisher.publish('order.applied', { orderId, status: 'applied' });
                }
                catch {
                    /* ignore publish error */
                }
            }
            else if (kind === 'resupply') {
                const fleetId = (p.fleetId || p.fleet_id);
                const amount = Number(p.amount || 0);
                if (!fleetId || amount <= 0 || !Number.isInteger(amount)) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'rejected']);
                    await client.query('COMMIT');
                    return;
                }
                // Lock fleet and update supply with cap
                const f = await client.query('select id, supply from fleets where id = $1 for update', [fleetId]);
                if (!f.rows[0]) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                    await client.query('COMMIT');
                    return;
                }
                const currentSupply = Number(f.rows[0].supply || 0);
                const MAX_SUPPLY = 200; // soft cap for MVP
                const newSupply = Math.min(MAX_SUPPLY, currentSupply + amount);
                await client.query('update fleets set supply = $2 where id = $1', [fleetId, newSupply]);
                await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                await client.query('COMMIT');
                try {
                    await publisher.publish('fleet.resupplied', { fleetId, amount, newSupply, orderId });
                    await publisher.publish('order.applied', { orderId, status: 'applied' });
                }
                catch {
                    /* ignore publish error */
                }
            }
            else if (kind === 'attack') {
                const attackerId = (p.fleetId || p.attackerId || p.fleet_id);
                const targetId = (p.targetFleetId || p.target_id);
                if (!attackerId || !targetId) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                    await client.query('COMMIT');
                    return;
                }
                // Lock both fleets and simulate a simple outcome
                const fA = await client.query('select id, empire_id, system_id, supply from fleets where id = $1 for update', [attackerId]);
                const fT = await client.query('select id, empire_id, system_id, supply from fleets where id = $1 for update', [targetId]);
                if (!fA.rows[0] || !fT.rows[0]) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                    await client.query('COMMIT');
                    return;
                }
                // Co-location and not same empire assumed validated at intake; recheck lightly
                if (fA.rows[0].system_id !== fT.rows[0].system_id || fA.rows[0].empire_id === fT.rows[0].empire_id) {
                    await client.query('update orders set status = $2 where id = $1', [orderId, 'rejected']);
                    await client.query('COMMIT');
                    try {
                        await publisher.publish('order.rejected', { orderId, reason: 'invalid_attack' });
                    }
                    catch { }
                    return;
                }
                // Use Sim Core for combat resolution
                const attackerFleet = {
                    id: attackerId,
                    empire_id: fA.rows[0].empire_id,
                    supply: Number(fA.rows[0].supply || 0),
                    stance: 'balanced' // Default stance, could be enhanced later
                };
                const targetFleet = {
                    id: targetId,
                    empire_id: fT.rows[0].empire_id,
                    supply: Number(fT.rows[0].supply || 0),
                    stance: 'balanced' // Default stance, could be enhanced later
                };
                const combatResult = simCore.resolve_battle
                    ? JSON.parse(simCore.resolve_battle(JSON.stringify(attackerFleet), JSON.stringify(targetFleet)))
                    : {
                        attacker_supply_loss: 20,
                        target_supply_loss: 30,
                        attacker_destroyed: false,
                        target_destroyed: targetFleet.supply <= 30,
                        notes: "Fallback combat resolution"
                    };
                const aSupply = Math.max(0, attackerFleet.supply - combatResult.attacker_supply_loss);
                const tSupply = Math.max(0, targetFleet.supply - combatResult.target_supply_loss);
                await client.query('update fleets set supply = $2 where id = $1', [attackerId, aSupply]);
                await client.query('update fleets set supply = $2 where id = $1', [targetId, tSupply]);
                // Insert battle record
                await client.query(`insert into battles (id, order_id, system_id, attacker_fleet_id, target_fleet_id, attacker_empire_id, target_empire_id, attacker_supply_after, target_supply_after, destroyed)
           values ($1,$1,$2,$3,$4,$5,$6,$7,$8,$9)
           on conflict (id) do nothing`, [
                    orderId,
                    fA.rows[0].system_id,
                    attackerId,
                    targetId,
                    fA.rows[0].empire_id,
                    fT.rows[0].empire_id,
                    aSupply,
                    tSupply,
                    tSupply === 0,
                ]);
                await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                await client.query('COMMIT');
                try {
                    await publisher.publish('battle.started', { attackerId, targetId, systemId: fA.rows[0].system_id, orderId });
                    await publisher.publish('battle.resolved', { attackerId, targetId, attackerSupply: aSupply, targetSupply: tSupply, destroyed: tSupply === 0, orderId });
                    await publisher.publish('order.applied', { orderId, status: 'applied' });
                }
                catch {
                    /* ignore publish error */
                }
            }
            else {
                // Unknown kind - mark applied to avoid blocking
                await client.query('update orders set status = $2 where id = $1', [orderId, 'applied']);
                await client.query('COMMIT');
            }
        }
        catch (error) {
            console.error('[Worker] Error processing orders:', error);
            await client.query('ROLLBACK').catch(() => { });
        }
        finally {
            client.release();
        }
    }, intervalMs);
    return () => clearInterval(t);
}
async function start() {
    const app = await buildServer();
    const pub = app.publisher ?? { publish: async () => { } };
    const simCore = app.simCore ?? createMockSim();
    const stopTick = startDevTick(pub);
    // Start turn clock (dev)
    startTurnClock(pub);
    // Start a tiny background worker to apply orders
    const worker = startApplyWorker(pub, simCore);
    await app.listen({ port, host: '0.0.0.0' });
    console.log(`orders-svc listening on :${port}`);
    // graceful shutdown
    process.on('SIGINT', () => {
        try {
            stopTick();
            worker();
        }
        catch {
            /* ignore */
        }
        process.exit(0);
    });
    process.on('SIGTERM', () => {
        try {
            stopTick();
            worker();
        }
        catch {
            /* ignore */
        }
        process.exit(0);
    });
}
if (import.meta.url === `file://${process.argv[1]}`) {
    start().catch((err) => {
        console.error(err);
        process.exit(1);
    });
}
