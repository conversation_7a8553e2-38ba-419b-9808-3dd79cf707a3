export async function createWasmSim(load) {
    const mod = await load();
    return {
        async apply(order) {
            const out = mod.apply(JSON.stringify(order));
            try {
                return JSON.parse(out);
            }
            catch {
                return { applied: false, notes: 'invalid delta' };
            }
        },
        resolve_battle: mod.resolve_battle,
    };
}
