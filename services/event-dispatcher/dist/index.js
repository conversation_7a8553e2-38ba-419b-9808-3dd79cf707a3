import { connect, StringCodec } from 'nats';
import { WebSocketServer } from 'ws';
import http from 'node:http';
const NATS_URL = process.env.NATS_URL || 'nats://localhost:4222';
const WS_PORT = Number(process.env.WS_PORT || 8090);
export async function createDispatcher(natsUrl = NATS_URL, wsPort = WS_PORT) {
    const nc = await connect({ servers: natsUrl });
    const sc = StringCodec();
    const natsConnected = true; // set false on stop; in future, wire reconnection listeners
    // HTTP server for health and WS upgrade
    const server = http.createServer((req, res) => {
        if (!req.url)
            return res.end('');
        if (req.method === 'GET' && (req.url === '/healthz' || req.url.startsWith('/healthz'))) {
            res.statusCode = 200;
            res.setHeader('content-type', 'text/plain');
            return res.end('ok');
        }
        if (req.method === 'GET' && (req.url === '/readyz' || req.url.startsWith('/readyz'))) {
            res.statusCode = natsConnected ? 200 : 503;
            res.setHeader('content-type', 'text/plain');
            return res.end(natsConnected ? 'ready' : 'not_ready');
        }
        res.statusCode = 404;
        return res.end('not_found');
    });
    const wss = new WebSocketServer({ server });
    const clients = new Set();
    const clientCtx = new Map();
    wss.on('connection', (ws, req) => {
        clients.add(ws);
        // Parse optional ?topics=a,b,c for simple filtering (backward compatible: no filter = all)
        const url = new URL(req?.url || '/', 'http://localhost');
        const topicsParam = url.searchParams.get('topics');
        const topics = topicsParam ? new Set(topicsParam.split(',').map((s) => s.trim()).filter(Boolean)) : undefined;
        clientCtx.set(ws, { ws, topics });
        ws.on('close', () => {
            clients.delete(ws);
            clientCtx.delete(ws);
        });
    });
    let subs = [];
    const topics = ['order.receipt', 'fleet.moved', 'fleet.resupplied', 'battle.started', 'battle.resolved'];
    return {
        async start() {
            await new Promise((resolve, reject) => {
                server.listen(wsPort, '0.0.0.0', () => resolve());
                server.on('error', reject);
            });
            subs = topics.map((t) => nc.subscribe(t));
            (async () => {
                for await (const s of subs) {
                    (async () => {
                        for await (const m of s) {
                            const text = sc.decode(m.data);
                            // Attempt to parse JSON to inspect topic; fall back to broadcast on parse error
                            let parsed = null;
                            try {
                                parsed = JSON.parse(text);
                            }
                            catch { }
                            const topic = (parsed && (parsed.topic || parsed.type));
                            for (const ws of clients) {
                                try {
                                    const ctx = clientCtx.get(ws);
                                    const allow = !ctx?.topics || !topic || ctx.topics.has(topic);
                                    if (allow)
                                        ws.send(text);
                                }
                                catch {
                                    // ignore WS send errors (client may have disconnected)
                                }
                            }
                        }
                    })();
                }
            })();
        },
        async stop() {
            subs.forEach((s) => s.unsubscribe());
            wss.close();
            await new Promise((resolve) => server.close(() => resolve()));
            await nc.drain();
        },
    };
}
if (import.meta.url === `file://${process.argv[1]}`) {
    createDispatcher()
        .then((d) => d.start())
        .catch((err) => {
        console.error(err);
        process.exit(1);
    });
}
