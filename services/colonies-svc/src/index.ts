import Fastify from 'fastify';
import { Pool } from 'pg';
import { nanoid } from 'nanoid';

// Types
interface Colony {
  id: string;
  system_id: string;
  empire_id: string;
  name: string;
  planet_type: string;
  pop: number;
  habitability: number;
  yields: Record<string, number>;
  max_pop: number;
  created_at: string;
}

interface Improvement {
  id: string;
  colony_id: string;
  type: string;
  level: number;
  yields: Record<string, number>;
  maintenance_cost: Record<string, number>;
  created_at: string;
}

interface EmpireResource {
  empire_id: string;
  resource_type: string;
  amount: number;
  production_rate: number;
  storage_cap: number;
  last_updated: string;
}

interface PlanetType {
  type: string;
  base_habitability: number;
  base_yields: Record<string, number>;
  description: string;
}

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'gg',
  user: process.env.DB_USER || 'gg',
  password: process.env.DB_PASSWORD || 'gg',
});

export async function buildServer() {
  const app = Fastify({ logger: true });

  // CORS support
  await app.register(import('@fastify/cors'), {
    origin: true,
    credentials: false,
  });

  // Health check
  app.get('/health', async () => {
    return { ok: true, service: 'colonies-svc' };
  });

  // Get all colonies
  app.get<{ Querystring: { empire_id?: string; system_id?: string } }>('/v1/colonies', async (request) => {
    const { empire_id, system_id } = request.query;
    
    let query = 'SELECT * FROM colonies';
    const params: any[] = [];
    const conditions: string[] = [];

    if (empire_id) {
      conditions.push('empire_id = $' + (params.length + 1));
      params.push(empire_id);
    }

    if (system_id) {
      conditions.push('system_id = $' + (params.length + 1));
      params.push(system_id);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY created_at DESC';

    const result = await pool.query(query, params);
    return { colonies: result.rows };
  });

  // Get colony by ID
  app.get<{ Params: { id: string } }>('/v1/colonies/:id', async (request, reply) => {
    const { id } = request.params;
    
    const result = await pool.query('SELECT * FROM colonies WHERE id = $1', [id]);
    
    if (result.rows.length === 0) {
      reply.status(404);
      return { error: 'Colony not found' };
    }

    return { colony: result.rows[0] };
  });

  // Create new colony
  app.post<{ 
    Body: { 
      system_id: string; 
      empire_id: string; 
      name: string; 
      planet_type?: string; 
    } 
  }>('/v1/colonies', async (request, reply) => {
    const { system_id, empire_id, name, planet_type = 'terrestrial' } = request.body;

    // Check if colony already exists in this system for this empire
    const existing = await pool.query(
      'SELECT id FROM colonies WHERE system_id = $1 AND empire_id = $2',
      [system_id, empire_id]
    );

    if (existing.rows.length > 0) {
      reply.status(400);
      return { error: 'Colony already exists in this system' };
    }

    // Get planet type info
    const planetInfo = await pool.query(
      'SELECT * FROM planet_types WHERE type = $1',
      [planet_type]
    );

    if (planetInfo.rows.length === 0) {
      reply.status(400);
      return { error: 'Invalid planet type' };
    }

    const planet = planetInfo.rows[0] as PlanetType;
    const colonyId = nanoid();

    // Create colony
    const result = await pool.query(`
      INSERT INTO colonies (id, system_id, empire_id, name, planet_type, pop, habitability, yields, max_pop)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [
      colonyId,
      system_id,
      empire_id,
      name,
      planet_type,
      1, // starting population
      planet.base_habitability,
      JSON.stringify(planet.base_yields),
      10 // starting max population
    ]);

    // Update empire resource production
    await updateEmpireProduction(empire_id);

    return { colony: result.rows[0] };
  });

  // Get colony improvements
  app.get<{ Params: { id: string } }>('/v1/colonies/:id/improvements', async (request, reply) => {
    const { id } = request.params;
    
    const result = await pool.query('SELECT * FROM improvements WHERE colony_id = $1', [id]);
    return { improvements: result.rows };
  });

  // Build improvement
  app.post<{
    Params: { id: string };
    Body: { type: string; level?: number }
  }>('/v1/colonies/:id/improvements', async (request, reply) => {
    const { id } = request.params;
    const { type, level = 1 } = request.body;

    // Get colony info first
    const colonyResult = await pool.query('SELECT empire_id FROM colonies WHERE id = $1', [id]);
    if (colonyResult.rows.length === 0) {
      reply.status(404);
      return { error: 'Colony not found' };
    }
    const empireId = colonyResult.rows[0].empire_id;

    // Check if improvement type is available to this empire (considering tech requirements)
    const availabilityCheck = await pool.query(`
      SELECT * FROM get_available_improvements($1) WHERE type = $2
    `, [empireId, type]);

    if (availabilityCheck.rows.length === 0) {
      reply.status(400);
      return { error: 'Invalid improvement type' };
    }

    const improvementType = availabilityCheck.rows[0];
    if (!improvementType.is_available) {
      reply.status(400);
      return { error: 'Technology requirement not met for this improvement' };
    }

    const improvementId = nanoid();

    // Create improvement
    const result = await pool.query(`
      INSERT INTO improvements (id, colony_id, type, level, yields, maintenance_cost)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      improvementId,
      id,
      type,
      level,
      JSON.stringify(improvementType.yields),
      JSON.stringify(improvementType.maintenance)
    ]);

    // Update empire production
    await updateEmpireProduction(empireId);

    return { improvement: result.rows[0] };
  });

  // Get empire resources
  app.get<{ Params: { empire_id: string } }>('/v1/empires/:empire_id/resources', async (request) => {
    const { empire_id } = request.params;
    
    const result = await pool.query(
      'SELECT * FROM empire_resources WHERE empire_id = $1 ORDER BY resource_type',
      [empire_id]
    );
    
    return { resources: result.rows };
  });

  // Get planet types
  app.get('/v1/planet-types', async () => {
    const result = await pool.query('SELECT * FROM planet_types ORDER BY type');
    return { planet_types: result.rows };
  });

  // Get improvement types
  app.get('/v1/improvement-types', async () => {
    const result = await pool.query('SELECT * FROM improvement_types ORDER BY type');
    return { improvement_types: result.rows };
  });

  // Get available improvement types for a specific empire (considering tech requirements)
  app.get<{ Params: { empire_id: string } }>('/v1/empires/:empire_id/improvement-types', async (request) => {
    const { empire_id } = request.params;

    const result = await pool.query(`
      SELECT * FROM get_available_improvements($1)
    `, [empire_id]);

    return { improvement_types: result.rows };
  });

  // Get empire technology bonuses
  app.get<{ Params: { empire_id: string } }>('/v1/empires/:empire_id/tech-bonuses', async (request) => {
    const { empire_id } = request.params;

    const result = await pool.query(`
      SELECT * FROM calculate_empire_tech_bonuses($1)
    `, [empire_id]);

    return { bonuses: result.rows };
  });

  return app;
}

// Helper function to recalculate empire resource production
async function updateEmpireProduction(empireId: string) {
  // Calculate total production from all colonies and improvements
  const productionQuery = `
    SELECT
      resource_type,
      SUM(amount) as total_production
    FROM (
      -- Base colony yields
      SELECT
        empire_id,
        jsonb_each_text(yields) as yield_data
      FROM colonies
      WHERE empire_id = $1

      UNION ALL

      -- Improvement yields
      SELECT
        c.empire_id,
        jsonb_each_text(i.yields) as yield_data
      FROM improvements i
      JOIN colonies c ON i.colony_id = c.id
      WHERE c.empire_id = $1
    ) as all_yields
    CROSS JOIN LATERAL (
      SELECT
        (yield_data).key as resource_type,
        ((yield_data).value)::numeric as amount
    ) as parsed_yields
    GROUP BY resource_type
  `;

  const productionResult = await pool.query(productionQuery, [empireId]);

  // Get technology bonuses
  const bonusQuery = `
    SELECT * FROM calculate_empire_tech_bonuses($1)
  `;
  const bonusResult = await pool.query(bonusQuery, [empireId]);

  // Create a map of bonuses for easy lookup
  const bonuses: Record<string, number> = {};
  for (const bonus of bonusResult.rows) {
    bonuses[bonus.bonus_type] = Number(bonus.total_bonus);
  }

  // Update empire resources with new production rates (including tech bonuses)
  for (const row of productionResult.rows) {
    let finalProduction = Number(row.total_production);

    // Apply technology bonuses
    const bonusKey = `${row.resource_type}_production`;
    if (bonuses[bonusKey]) {
      finalProduction = finalProduction * (1 + bonuses[bonusKey]);
    }

    await pool.query(`
      UPDATE empire_resources
      SET production_rate = $1, last_updated = now()
      WHERE empire_id = $2 AND resource_type = $3
    `, [finalProduction, empireId, row.resource_type]);
  }
}

// Start server
async function start() {
  try {
    const app = await buildServer();
    const port = parseInt(process.env.PORT || '3003');
    
    await app.listen({ port, host: '0.0.0.0' });
    console.log(`Colonies service listening on port ${port}`);
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  start();
}
