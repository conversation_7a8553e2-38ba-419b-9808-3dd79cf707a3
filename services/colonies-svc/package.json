{"name": "colonies-svc", "version": "0.1.0", "description": "Colony and resource management service for Galactic Genesis", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch"}, "dependencies": {"@fastify/cors": "^9.0.1", "fastify": "^4.24.3", "pg": "^8.11.3", "nanoid": "^5.0.4"}, "devDependencies": {"@types/node": "^20.8.7", "@types/pg": "^8.10.7", "tsx": "^4.1.2", "typescript": "^5.2.2", "vitest": "^0.34.6"}, "type": "module"}