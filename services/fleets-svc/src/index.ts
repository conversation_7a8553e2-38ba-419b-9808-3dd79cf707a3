import Fastify from 'fastify';
import { randomUUID } from 'node:crypto';

const TEST_MODE = process.env.NODE_ENV === 'test';

export async function buildServer() {
  const app = Fastify({ logger: true });

  // DB pool (internal-only; compose network)
  let pool: any = null;
  if (!TEST_MODE) {
    const pgMod: any = await import('pg');
    const Pool = pgMod.default?.Pool || pgMod.Pool;
    pool = new Pool({
      host: process.env.PGHOST || 'postgres',
      port: Number(process.env.PGPORT || 5432),
      user: process.env.PGUSER || 'gg',
      password: process.env.PGPASSWORD || 'ggpassword',
      database: process.env.PGDATABASE || 'gg',
    });
  }

  // List fleets with a simple join to systems (optional fields)
  app.get('/v1/fleets', async (_req, rep) => {
    try {
      if (TEST_MODE || !pool) {
        return rep.send({ fleets: [] });
      }
      const { rows } = await pool.query(
        `select f.id, f.empire_id, f.system_id, f.stance, f.supply,
                s.name as system_name
           from fleets f
           left join systems s on s.id = f.system_id
           order by f.id asc`,
      );
      return rep.send({ fleets: rows });
    } catch (err: any) {
      app.log.error({ err }, 'fleets query failed');
      return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
    }
  });
  // Systems list with neighbor_count
  app.get('/v1/systems', async (_req, rep) => {
    try {
      if (TEST_MODE || !pool) {
        return rep.send({ systems: [] });
      }
      const { rows } = await pool.query(
        `select s.id, s.name, s.position_xyz, s.owner_empire_id,
                (select count(1) from system_links l where l.a = s.id) as neighbor_count
           from systems s
           order by s.id`,
      );
      return rep.send({ systems: rows });
    } catch (err: any) {
      app.log.error({ err }, 'systems list failed');
      return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
    }
  });

  // Systems neighbors: GET /v1/systems/:id/neighbors
  app.get<{ Params: { id: string } }>('/v1/systems/:id/neighbors', async (req, rep) => {
    const { id } = req.params;
    try {
      if (TEST_MODE || !pool) {
        return rep.send({ neighbors: [] });
      }
      const { rows } = await pool.query(
        `select s2.id, s2.name, s2.position_xyz
           from system_links l
           join systems s2 on s2.id = l.b
          where l.a = $1
          order by s2.id`,
        [id],
      );
      return rep.send({ neighbors: rows });
    } catch (err: any) {
      app.log.error({ err }, 'neighbors query failed');
      return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
    }
  });

  // Fleets by empire: GET /v1/empires/:id/fleets
  app.get<{ Params: { id: string } }>('/v1/empires/:id/fleets', async (req, rep) => {
    const { id } = req.params;
    try {
      if (TEST_MODE || !pool) {
        return rep.send({ fleets: [] });
      }
      const { rows } = await pool.query(
        `select f.id, f.empire_id, f.system_id, f.stance, f.supply,
                s.name as system_name
           from fleets f
           left join systems s on s.id = f.system_id
          where f.empire_id = $1
          order by f.id asc`,
        [id],
      );
      return rep.send({ fleets: rows });
    } catch (err: any) {
      app.log.error({ err }, 'fleets by empire query failed');
      return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
    }
  });


  // Get fleet by id
  app.get<{ Params: { id: string } }>('/v1/fleets/:id', async (req, rep) => {
    const { id } = req.params;
    try {
      if (TEST_MODE || !pool) {
        return rep.send({
          id,
          empire_id: 'emp-1',
          system_id: 'sys-1',
          stance: 'neutral',
          supply: 100,
        });
      }
      const { rows } = await pool.query(
        `select f.id, f.empire_id, f.system_id, f.stance, f.supply,
                s.name as system_name
           from fleets f
           left join systems s on s.id = f.system_id
          where f.id = $1`,
        [id],
      );
      if (!rows[0]) return rep.status(404).send({ error: 'not_found' });
      return rep.send(rows[0]);
    } catch (err: any) {
      app.log.error({ err }, 'fleet get failed');
      return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
    }
  });

  // Health
  app.get('/v1/health', async () => ({ ok: true }));

  // Liveness and readiness
  app.get('/healthz', async () => ({ status: 'ok' }));
  app.get('/readyz', async () => ({ ready: true }));

  // Create a fleet
  app.post<{
    Body: { id?: string; empire_id: string; system_id: string; stance?: string; supply?: number };
  }>(
    '/v1/fleets',
    {
      schema: {
        body: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            empire_id: { type: 'string' },
            system_id: { type: 'string' },
            stance: { type: 'string' },
            supply: { type: 'integer' },
          },
          required: ['empire_id', 'system_id'],
          additionalProperties: false,
        },
      },
    },
    async (req, rep) => {
      const id = req.body.id || randomUUID();
      const { empire_id, system_id } = req.body;
      const stance = req.body.stance ?? 'neutral';
      const supply = typeof req.body.supply === 'number' ? req.body.supply : 100;
      try {
        // Validate system exists when DB available
        if (!TEST_MODE && pool) {
          const sys = await pool.query('select 1 from systems where id = $1', [system_id]);
          if (!sys.rows[0])
            return rep
              .status(400)
              .send({ error: 'invalid_system', message: 'system_id not found' });
        }

        if (TEST_MODE || !pool) {
          return rep.status(201).send({ id, empire_id, system_id, stance, supply });
        }

        const { rows } = await pool.query(
          `insert into fleets (id, empire_id, system_id, stance, supply)
           values ($1, $2, $3, $4, $5)
           returning id, empire_id, system_id, stance, supply`,
          [id, empire_id, system_id, stance, supply],
        );
        return rep.status(201).send(rows[0]);
      } catch (err: any) {
        app.log.error({ err }, 'fleet create failed');
        // unique violation
        if (err && err.code === '23505')
          return rep.status(409).send({ error: 'conflict', message: 'id already exists' });
        return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
      }
    },
  );

  // Update a fleet (stance/supply)
  app.patch<{ Params: { id: string }; Body: { stance?: string; supply?: number } }>(
    '/v1/fleets/:id',
    {
      schema: {
        params: { type: 'object', properties: { id: { type: 'string' } }, required: ['id'] },
        body: {
          type: 'object',
          properties: {
            stance: { type: 'string' },
            supply: { type: 'integer', minimum: 0 },
          },
          additionalProperties: false,
        },
      },
    },
    async (req, rep) => {
      const { id } = req.params;
      const stance = req.body.stance ?? null;
      const supply = typeof req.body.supply === 'number' ? req.body.supply : null;
      try {
        if (TEST_MODE || !pool) {
          const base = {
            id,
            empire_id: 'emp-1',
            system_id: 'sys-1',
            stance: 'neutral',
            supply: 100,
          } as any;
          if (stance !== null) base.stance = stance;
          if (supply !== null) base.supply = supply;
          return rep.send(base);
        }
        const { rows } = await pool.query(
          `update fleets
             set stance = coalesce($2, stance),
                 supply = coalesce($3, supply)
           where id = $1
           returning id, empire_id, system_id, stance, supply`,
          [id, stance, supply],
        );
        if (!rows[0]) return rep.status(404).send({ error: 'not_found' });
        return rep.send(rows[0]);
      } catch (err: any) {
        app.log.error({ err }, 'fleet update failed');
        return rep.status(500).send({ error: 'db_error', message: err?.message || 'unknown' });
      }
    },
  );

  return app;
}

async function start() {
  const port = Number(process.env.PORT || 8082);
  const app = await buildServer();
  await app.listen({ port, host: '0.0.0.0' });
  console.log(`fleets-svc listening on :${port}`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  start().catch((err) => {
    console.error(err);
    process.exit(1);
  });
}
