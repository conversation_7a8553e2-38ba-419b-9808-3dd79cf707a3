import { describe, it, expect } from 'vitest';
import { buildServer } from '../src/index';

describe('systems list read-model', () => {
  it('GET /v1/systems returns empty list in TEST_MODE', async () => {
    const app = await buildServer();
    const res = await app.inject({ method: 'GET', url: '/v1/systems' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ systems: [] });
  });
});

