import { describe, it, expect } from 'vitest';
import { buildServer } from '../src/index';

describe('fleets read-model endpoints', () => {
  it('GET /v1/systems/:id/neighbors returns empty list in TEST_MODE', async () => {
    const app = await buildServer();
    const res = await app.inject({ method: 'GET', url: '/v1/systems/sys-1/neighbors' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ neighbors: [] });
  });

  it('GET /v1/empires/:id/fleets returns empty list in TEST_MODE', async () => {
    const app = await buildServer();
    const res = await app.inject({ method: 'GET', url: '/v1/empires/emp-1/fleets' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ fleets: [] });
  });
});

