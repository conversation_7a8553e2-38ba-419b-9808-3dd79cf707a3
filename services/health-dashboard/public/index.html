<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galactic Genesis - Health Dashboard</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e2e8f0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .status-healthy {
            background: linear-gradient(135deg, #10b981, #059669);
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        }
        
        .status-unhealthy {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }
        
        .status-degraded {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
        }
        
        .status-unknown {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            box-shadow: 0 0 20px rgba(107, 114, 128, 0.3);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 8px;
            padding: 16px;
            transition: all 0.2s;
        }
        
        .metric-card:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.15);
        }
        
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid #ef4444;
            color: #fecaca;
        }
        
        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid #f59e0b;
            color: #fed7aa;
        }
        
        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border-left: 4px solid #3b82f6;
            color: #bfdbfe;
        }
        
        .loading-spinner {
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-left: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;
        
        // Utility functions
        const formatBytes = (bytes) => {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };
        
        const formatUptime = (seconds) => {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            if (days > 0) return `${days}d ${hours}h ${minutes}m`;
            if (hours > 0) return `${hours}h ${minutes}m`;
            return `${minutes}m`;
        };
        
        const getStatusColor = (status) => {
            switch (status) {
                case 'healthy': return 'status-healthy';
                case 'unhealthy': return 'status-unhealthy';
                case 'degraded': return 'status-degraded';
                default: return 'status-unknown';
            }
        };
        
        const getStatusIcon = (status) => {
            switch (status) {
                case 'healthy': return 'fas fa-check-circle';
                case 'unhealthy': return 'fas fa-times-circle';
                case 'degraded': return 'fas fa-exclamation-triangle';
                default: return 'fas fa-question-circle';
            }
        };
        
        // Service Widget Component
        const ServiceWidget = ({ service, onRestart, onStart, onStop, isRestarting }) => {
            return (
                <div data-testid="service-widget" className="glass-card p-4">
                    <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                            <div data-testid="service-status" className={`w-3 h-3 rounded-full ${getStatusColor(service.status)} ${service.status === 'healthy' ? 'pulse-animation' : ''}`}></div>
                            <h3 data-testid="service-name" className="text-lg font-semibold">{service.name}</h3>
                        </div>
                        <i className={`${getStatusIcon(service.status)} text-xl`}></i>
                    </div>
                    
                    <div className="space-y-2 text-sm text-gray-300 mb-4">
                        <div className="flex justify-between">
                            <span>Status:</span>
                            <span className={`font-medium ${service.status === 'healthy' ? 'text-green-400' : service.status === 'unhealthy' ? 'text-red-400' : 'text-yellow-400'}`}>
                                {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span>Response Time:</span>
                            <span>{service.responseTime}ms</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Last Checked:</span>
                            <span>{new Date(service.lastChecked).toLocaleTimeString()}</span>
                        </div>
                        {service.details?.version && (
                            <div className="flex justify-between">
                                <span>Version:</span>
                                <span>{service.details.version}</span>
                            </div>
                        )}
                    </div>
                    
                    <div data-testid="service-controls" className="flex space-x-2">
                        <button
                            data-testid="start-button"
                            className="btn-success flex-1 flex items-center justify-center space-x-2"
                            onClick={() => onStart(service.name)}
                            disabled={isRestarting}
                            aria-label={`Start ${service.name} service`}
                        >
                            <i className="fas fa-play"></i>
                            <span>Start</span>
                        </button>
                        <button
                            data-testid="restart-button"
                            className="btn-primary flex-1 flex items-center justify-center space-x-2"
                            onClick={() => onRestart(service.name)}
                            disabled={isRestarting}
                            aria-label={`Restart ${service.name} service`}
                        >
                            {isRestarting ? (
                                <div className="loading-spinner"></div>
                            ) : (
                                <i className="fas fa-redo"></i>
                            )}
                            <span>Restart</span>
                        </button>
                        <button
                            data-testid="stop-button"
                            className="btn-danger flex-1 flex items-center justify-center space-x-2"
                            onClick={() => onStop(service.name)}
                            disabled={isRestarting}
                            aria-label={`Stop ${service.name} service`}
                        >
                            <i className="fas fa-stop"></i>
                            <span>Stop</span>
                        </button>
                    </div>
                </div>
            );
        };
        
        // System Metrics Component
        const SystemMetrics = ({ system }) => {
            return (
                <div data-testid="system-metrics" className="glass-card p-6">
                    <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
                        <i className="fas fa-server text-blue-400"></i>
                        <span>System Metrics</span>
                    </h2>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div data-testid="cpu-usage" className="metric-card">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm text-gray-400">CPU Usage</span>
                                <i className="fas fa-microchip text-blue-400"></i>
                            </div>
                            <div className="text-2xl font-bold">{system.cpu.usage}%</div>
                            <div className="text-xs text-gray-500">{system.cpu.cores} cores</div>
                        </div>

                        <div data-testid="memory-usage" className="metric-card">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm text-gray-400">Memory</span>
                                <i className="fas fa-memory text-green-400"></i>
                            </div>
                            <div className="text-2xl font-bold">{system.memory.usage}%</div>
                            <div className="text-xs text-gray-500">{formatBytes(system.memory.used)} / {formatBytes(system.memory.total)}</div>
                        </div>

                        <div data-testid="disk-usage" className="metric-card">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm text-gray-400">Disk Usage</span>
                                <i className="fas fa-hdd text-yellow-400"></i>
                            </div>
                            <div className="text-2xl font-bold">{system.disk.usage}%</div>
                            <div className="text-xs text-gray-500">{formatBytes(system.disk.used)} / {formatBytes(system.disk.total)}</div>
                        </div>
                        
                        <div data-testid="network-stats" className="metric-card">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm text-gray-400">Uptime</span>
                                <i className="fas fa-clock text-purple-400"></i>
                            </div>
                            <div className="text-2xl font-bold">{formatUptime(system.uptime)}</div>
                            <div className="text-xs text-gray-500">Load: {system.loadAverage[0]?.toFixed(2) || '0.00'}</div>
                        </div>
                    </div>
                </div>
            );
        };

        // Port Monitor Component
        const PortMonitor = ({ portData, onKillPort }) => {
            if (!portData) {
                return (
                    <div className="glass-card p-6">
                        <div className="flex items-center justify-center">
                            <div className="loading-spinner"></div>
                            <span className="ml-2">Loading port data...</span>
                        </div>
                    </div>
                );
            }

            const getPortStatusColor = (port) => {
                if (port.state === 'LISTEN') {
                    return port.isExpected ? 'bg-green-500' : 'bg-yellow-500';
                }
                return port.isExpected ? 'bg-red-500' : 'bg-gray-500';
            };

            const getPortStatusText = (port) => {
                if (port.state === 'LISTEN') {
                    return port.isExpected ? 'Running' : 'Unexpected';
                }
                return port.isExpected ? 'Missing' : 'Closed';
            };

            return (
                <div className="glass-card p-6">
                    <div className="flex items-center justify-between mb-6">
                        <h2 className="text-xl font-bold flex items-center space-x-2">
                            <i className="fas fa-network-wired text-purple-400"></i>
                            <span>Port Monitor</span>
                        </h2>
                        <div className="text-sm text-gray-400">
                            Last scan: {new Date(portData.timestamp).toLocaleTimeString()}
                        </div>
                    </div>

                    {/* Summary */}
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                        <div className="metric-card">
                            <div className="text-2xl font-bold text-blue-400">{portData.summary.total}</div>
                            <div className="text-xs text-gray-400">Total Ports</div>
                        </div>
                        <div className="metric-card">
                            <div className="text-2xl font-bold text-green-400">{portData.summary.listening}</div>
                            <div className="text-xs text-gray-400">Listening</div>
                        </div>
                        <div className="metric-card">
                            <div className="text-2xl font-bold text-green-400">{portData.summary.expected}</div>
                            <div className="text-xs text-gray-400">Expected</div>
                        </div>
                        <div className="metric-card">
                            <div className="text-2xl font-bold text-yellow-400">{portData.summary.unexpected}</div>
                            <div className="text-xs text-gray-400">Unexpected</div>
                        </div>
                        <div className="metric-card">
                            <div className="text-2xl font-bold text-red-400">{portData.summary.missing}</div>
                            <div className="text-xs text-gray-400">Missing</div>
                        </div>
                    </div>

                    {/* Detailed Port Table */}
                    <div className="mt-6">
                        <h3 className="text-lg font-semibold mb-4 text-gray-200">Detailed Port Overview</h3>
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b border-gray-700">
                                        <th className="text-left py-2 px-3 text-gray-300">Port</th>
                                        <th className="text-left py-2 px-3 text-gray-300">Protocol</th>
                                        <th className="text-left py-2 px-3 text-gray-300">State</th>
                                        <th className="text-left py-2 px-3 text-gray-300">Service</th>
                                        <th className="text-left py-2 px-3 text-gray-300">Process</th>
                                        <th className="text-left py-2 px-3 text-gray-300">Expected</th>
                                        <th className="text-left py-2 px-3 text-gray-300">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="max-h-96 overflow-y-auto">
                                    {portData.ports.map((port, index) => (
                                        <tr key={`${port.port}-${port.protocol}-${index}`} className="border-b border-gray-800 hover:bg-gray-800">
                                            <td className="py-2 px-3">
                                                <div className="flex items-center space-x-2">
                                                    <div className={`w-2 h-2 rounded-full ${getPortStatusColor(port)}`}></div>
                                                    <span className="font-mono font-medium">{port.port}</span>
                                                </div>
                                            </td>
                                            <td className="py-2 px-3 text-gray-400 uppercase font-mono">{port.protocol}</td>
                                            <td className="py-2 px-3">
                                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                                    port.state === 'LISTEN' ? 'bg-green-900 text-green-200' :
                                                    port.state === 'CLOSED' ? 'bg-red-900 text-red-200' :
                                                    'bg-gray-900 text-gray-200'
                                                }`}>
                                                    {port.state}
                                                </span>
                                            </td>
                                            <td className="py-2 px-3">
                                                <div>
                                                    <div className="font-medium text-gray-200">{port.service || 'Unknown'}</div>
                                                    {port.description && (
                                                        <div className="text-xs text-gray-500">{port.description}</div>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="py-2 px-3 text-gray-400">
                                                {port.pid ? (
                                                    <div className="font-mono text-xs">
                                                        <div>PID: {port.pid}</div>
                                                        {port.processName && <div>{port.processName}</div>}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-600">-</span>
                                                )}
                                            </td>
                                            <td className="py-2 px-3">
                                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                                    port.isExpected ? 'bg-blue-900 text-blue-200' : 'bg-yellow-900 text-yellow-200'
                                                }`}>
                                                    {port.isExpected ? 'Expected' : 'Unexpected'}
                                                </span>
                                            </td>
                                            <td className="py-2 px-3">
                                                {port.state === 'LISTEN' && port.pid && (
                                                    <button
                                                        className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1 rounded transition-colors"
                                                        onClick={() => onKillPort(port.port)}
                                                        title={`Kill process on port ${port.port}`}
                                                    >
                                                        Kill
                                                    </button>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Port Summary Cards */}
                        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="bg-gray-800 p-3 rounded-lg">
                                <div className="text-sm text-gray-400">Listening Ports</div>
                                <div className="text-lg font-bold text-green-400">
                                    {portData.ports.filter(p => p.state === 'LISTEN').length}
                                </div>
                            </div>
                            <div className="bg-gray-800 p-3 rounded-lg">
                                <div className="text-sm text-gray-400">Expected Services</div>
                                <div className="text-lg font-bold text-blue-400">
                                    {portData.ports.filter(p => p.isExpected && p.state === 'LISTEN').length}
                                </div>
                            </div>
                            <div className="bg-gray-800 p-3 rounded-lg">
                                <div className="text-sm text-gray-400">Unexpected Services</div>
                                <div className="text-lg font-bold text-yellow-400">
                                    {portData.ports.filter(p => !p.isExpected && p.state === 'LISTEN').length}
                                </div>
                            </div>
                            <div className="bg-gray-800 p-3 rounded-lg">
                                <div className="text-sm text-gray-400">Missing Services</div>
                                <div className="text-lg font-bold text-red-400">
                                    {portData.ports.filter(p => p.isExpected && p.state !== 'LISTEN').length}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // Main Dashboard Component
        const HealthDashboard = () => {
            const [healthData, setHealthData] = useState(null);
            const [alerts, setAlerts] = useState([]);
            const [restartingServices, setRestartingServices] = useState(new Set());
            const [ws, setWs] = useState(null);
            const [lastUpdate, setLastUpdate] = useState(new Date());
            const [portData, setPortData] = useState(null);
            const [showPortMonitor, setShowPortMonitor] = useState(false);
            
            // WebSocket connection
            useEffect(() => {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;
                
                const websocket = new WebSocket(wsUrl);
                
                websocket.onopen = () => {
                    console.log('WebSocket connected');
                    setWs(websocket);
                };
                
                websocket.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    
                    switch (message.type) {
                        case 'health-data':
                            setHealthData(message.data);
                            setLastUpdate(new Date());
                            break;
                        case 'system-metrics':
                            setHealthData(prev => prev ? { ...prev, system: message.data, timestamp: new Date() } : null);
                            break;
                        case 'port-scan':
                            setPortData(message.data);
                            break;
                        case 'service-restart':
                        case 'service-start':
                        case 'service-stop':
                            // Remove from restarting set when operation completes
                            setRestartingServices(prev => {
                                const newSet = new Set(prev);
                                newSet.delete(message.data.service);
                                return newSet;
                            });
                            break;
                    }
                };
                
                websocket.onclose = () => {
                    console.log('WebSocket disconnected');
                    setWs(null);
                    // Attempt to reconnect after 5 seconds
                    setTimeout(() => {
                        window.location.reload();
                    }, 5000);
                };
                
                websocket.onerror = (error) => {
                    console.error('WebSocket error:', error);
                };
                
                return () => {
                    websocket.close();
                };
            }, []);
            
            // Initial data fetch
            useEffect(() => {
                fetch('/api/health-data')
                    .then(res => res.json())
                    .then(data => {
                        setHealthData(data);
                        setLastUpdate(new Date());
                    })
                    .catch(console.error);
                
                fetch('/api/alerts')
                    .then(res => res.json())
                    .then(setAlerts)
                    .catch(console.error);

                fetch('/api/ports')
                    .then(res => res.json())
                    .then(setPortData)
                    .catch(console.error);
            }, []);
            
            const handleServiceAction = async (action, serviceName) => {
                setRestartingServices(prev => new Set(prev).add(serviceName));
                
                try {
                    const response = await fetch(`/api/services/${action}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ service: serviceName })
                    });
                    
                    const result = await response.json();
                    console.log(`${action} result:`, result);
                    
                } catch (error) {
                    console.error(`Failed to ${action} service:`, error);
                    setRestartingServices(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(serviceName);
                        return newSet;
                    });
                }
            };

            const handleKillPort = async (port) => {
                if (!confirm(`Are you sure you want to kill the process on port ${port}?`)) {
                    return;
                }

                try {
                    const response = await fetch(`/api/ports/${port}/kill`, {
                        method: 'POST'
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`Successfully killed process on port ${port}`);
                        // Refresh port data
                        const portResponse = await fetch('/api/ports');
                        const newPortData = await portResponse.json();
                        setPortData(newPortData);
                    } else {
                        alert(`Failed to kill port ${port}: ${result.message}`);
                    }

                } catch (error) {
                    console.error('Kill port failed:', error);
                    alert(`Error killing port ${port}: ${error.message}`);
                }
            };

            if (!healthData) {
                return (
                    <div className="min-h-screen flex items-center justify-center">
                        <div className="text-center">
                            <div className="loading-spinner mx-auto mb-4" style={{width: '40px', height: '40px'}}></div>
                            <p className="text-xl">Loading Health Dashboard...</p>
                        </div>
                    </div>
                );
            }
            
            return (
                <div className="min-h-screen p-6">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 data-testid="dashboard-title" className="text-3xl font-bold flex items-center space-x-3">
                                    <i className="fas fa-heartbeat text-red-400"></i>
                                    <span>Galactic Genesis Health Dashboard</span>
                                </h1>
                                <p className="text-gray-400 mt-2">Real-time monitoring and control for all services</p>
                            </div>
                            <div className="text-right">
                                <div data-testid="overall-status" className={`inline-flex items-center space-x-2 px-4 py-2 rounded-lg ${getStatusColor(healthData.overall.status)}`}>
                                    <i className={getStatusIcon(healthData.overall.status)}></i>
                                    <span className="font-semibold">
                                        {healthData.overall.status.charAt(0).toUpperCase() + healthData.overall.status.slice(1)}
                                    </span>
                                </div>
                                <div className="text-sm text-gray-400 mt-1">
                                    {healthData.overall.healthyServices}/{healthData.overall.totalServices} services healthy
                                </div>
                                <div data-testid="last-updated" className="text-xs text-gray-500">
                                    Last update: {lastUpdate.toLocaleTimeString()}
                                </div>
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    className={`px-4 py-2 rounded-lg transition-colors ${!showPortMonitor ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
                                    onClick={() => setShowPortMonitor(false)}
                                >
                                    <i className="fas fa-heartbeat mr-2"></i>
                                    Health Monitor
                                </button>
                                <button
                                    className={`px-4 py-2 rounded-lg transition-colors ${showPortMonitor ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
                                    onClick={() => setShowPortMonitor(true)}
                                >
                                    <i className="fas fa-network-wired mr-2"></i>
                                    Port Monitor
                                </button>
                            </div>
                        </div>
                    </div>

                    {showPortMonitor ? (
                        /* Port Monitor */
                        <div className="mb-8">
                            <PortMonitor portData={portData} onKillPort={handleKillPort} />
                        </div>
                    ) : (
                        <>
                            {/* System Metrics */}
                            <div className="mb-8">
                                <SystemMetrics system={healthData.system} />
                            </div>

                            {/* Services Grid */}
                    <div data-testid="services-section" className="mb-8">
                        <h2 className="text-2xl font-bold mb-4 flex items-center space-x-2">
                            <i className="fas fa-cogs text-blue-400"></i>
                            <span>Services</span>
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {healthData.services.map(service => (
                                <ServiceWidget
                                    key={service.name}
                                    service={service}
                                    onRestart={(name) => handleServiceAction('restart', name)}
                                    onStart={(name) => handleServiceAction('start', name)}
                                    onStop={(name) => handleServiceAction('stop', name)}
                                    isRestarting={restartingServices.has(service.name)}
                                />
                            ))}
                        </div>
                    </div>
                    
                    {/* Database Status */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold mb-4 flex items-center space-x-2">
                            <i className="fas fa-database text-green-400"></i>
                            <span>Database</span>
                        </h2>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {healthData.databases.map(db => (
                                <div key={db.name} className="glass-card p-6">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-xl font-semibold">{db.name}</h3>
                                        <div className={`w-4 h-4 rounded-full ${getStatusColor(db.status)}`}></div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span className="text-gray-400">Connections:</span>
                                            <div className="font-semibold">{db.activeConnections}/{db.maxConnections}</div>
                                        </div>
                                        <div>
                                            <span className="text-gray-400">Response Time:</span>
                                            <div className="font-semibold">{db.responseTime}ms</div>
                                        </div>
                                        {db.size && (
                                            <div>
                                                <span className="text-gray-400">Size:</span>
                                                <div className="font-semibold">{db.size}</div>
                                            </div>
                                        )}
                                        {db.version && (
                                            <div>
                                                <span className="text-gray-400">Version:</span>
                                                <div className="font-semibold">{db.version}</div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    
                    {/* Alerts */}
                    {healthData.overall.alerts.length > 0 && (
                        <div data-testid="alerts-section" className="mb-8">
                            <h2 className="text-2xl font-bold mb-4 flex items-center space-x-2">
                                <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                                <span>Recent Alerts</span>
                            </h2>
                            <div className="space-y-3">
                                {healthData.overall.alerts.slice(0, 10).map(alert => (
                                    <div key={alert.id} className={`glass-card p-4 alert-${alert.type}`}>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3">
                                                <i className={`fas ${alert.type === 'error' ? 'fa-times-circle' : alert.type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'}`}></i>
                                                <div>
                                                    <div className="font-semibold">{alert.service}</div>
                                                    <div className="text-sm opacity-90">{alert.message}</div>
                                                </div>
                                            </div>
                                            <div className="text-sm opacity-75">
                                                {new Date(alert.timestamp).toLocaleTimeString()}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                        </>
                    )}
                </div>
            );
        };
        
        // Render the app
        ReactDOM.render(<HealthDashboard />, document.getElementById('root'));
    </script>
</body>
</html>
