{"name": "health-dashboard", "version": "1.0.0", "description": "Health monitoring dashboard service for Galactic Genesis", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:run": "vitest run", "lint": "eslint src --ext .ts", "clean": "rm -rf dist"}, "dependencies": {"fastify": "^5.1.0", "@fastify/cors": "^10.0.1", "@fastify/websocket": "^11.0.1", "pg": "^8.13.1", "node-cron": "^3.0.3", "systeminformation": "^5.23.5", "nanoid": "^5.0.9"}, "devDependencies": {"@types/node": "^22.10.2", "@types/pg": "^8.11.10", "@types/node-cron": "^3.0.11", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^2.1.8", "eslint": "^9.17.0", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1"}, "keywords": ["health", "monitoring", "dashboard", "galactic-genesis"], "author": "Galactic Genesis Team", "license": "MIT"}