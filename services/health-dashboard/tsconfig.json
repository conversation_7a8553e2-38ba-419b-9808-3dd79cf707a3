{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}