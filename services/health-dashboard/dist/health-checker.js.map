{"version": 3, "file": "health-checker.js", "sourceRoot": "", "sources": ["../src/health-checker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAC1B,OAAO,EAAE,MAAM,mBAAmB,CAAC;AASnC,OAAO,EAAE,eAAe,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAEpE,MAAM,OAAO,aAAa;IAChB,MAAM,GAAY,EAAE,CAAC;IACrB,MAAM,CAAO;IAErB;QACE,2DAA2D;QAC3D,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC;YACrB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,WAAW;YACvC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI;YACxC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI;YACxC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,IAAI;YAChC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,YAAY;YAChD,GAAG,EAAE,CAAC,EAAE,mCAAmC;YAC3C,iBAAiB,EAAE,KAAK;YACxB,uBAAuB,EAAE,IAAI;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAsB;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,oBAAoB,CAAC,CAAC;YAE7E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,cAAc,EAAE,EAAE;gBACtE,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;oBAC5B,YAAY,EAAE,qBAAqB;iBACpC;aACF,CAAC,CAAC;YAEH,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE,CAAC;YAC9B,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC3C,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzD,CAAC;YAAC,MAAM,CAAC;gBACP,kDAAkD;YACpD,CAAC;YAED,MAAM,MAAM,GAAkB;gBAC5B,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;gBAC3C,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,OAAO;aACR,CAAC;YAEF,uCAAuC;YACvC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,oBAAoB,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACzG,CAAC;YAED,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAE5H,OAAO;gBACL,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,WAAW;gBACnB,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC7E,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAE3C,IAAI,CAAC;gBACH,uBAAuB;gBACvB,MAAM,UAAU,GAAG;;;;;;SAMlB,CAAC;gBAEF,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBACnD,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAElC,oBAAoB;gBACpB,MAAM,SAAS,GAAG;;SAEjB,CAAC;gBACF,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACjD,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;gBAEtC,cAAc;gBACd,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBAC7D,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE9D,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE5C,OAAO;oBACL,IAAI,EAAE,YAAY;oBAClB,MAAM,EAAE,SAAS;oBACjB,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC;oBAClD,iBAAiB,EAAE,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC;oBACrD,cAAc,EAAE,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC;oBAC/C,YAAY;oBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,OAAO;oBACP,IAAI;iBACL,CAAC;YAEJ,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,YAAY,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAE9H,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,WAAW;gBACnB,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;gBACpB,cAAc,EAAE,CAAC;gBACjB,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClE,EAAE,CAAC,WAAW,EAAE;gBAChB,EAAE,CAAC,GAAG,EAAE;gBACR,EAAE,CAAC,MAAM,EAAE;gBACX,EAAE,CAAC,YAAY,EAAE;gBACjB,EAAE,CAAC,MAAM,EAAE;aACZ,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;YAEvE,OAAO;gBACL,GAAG,EAAE;oBACH,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC;oBAClC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;oBAC5B,KAAK,EAAE,KAAK;iBACb;gBACD,MAAM,EAAE;oBACN,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;iBACtD;gBACD,IAAI,EAAE;oBACJ,KAAK,EAAE,WAAW,EAAE,IAAI,IAAI,CAAC;oBAC7B,IAAI,EAAE,WAAW,EAAE,IAAI,IAAI,CAAC;oBAC5B,IAAI,EAAE,WAAW,EAAE,SAAS,IAAI,CAAC;oBACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;iBAC/E;gBACD,OAAO,EAAE;oBACP,EAAE,EAAE,cAAc,CAAC,QAAQ,IAAI,CAAC;oBAChC,EAAE,EAAE,cAAc,CAAC,QAAQ,IAAI,CAAC;iBACjC;gBACD,MAAM,EAAE,CAAC;gBACT,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/C,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAEtD,kCAAkC;YAClC,OAAO;gBACL,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;gBAC7C,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;gBAChD,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;gBAC9C,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;gBACzB,MAAM,EAAE,CAAC;gBACT,WAAW,EAAE,CAAC,CAAC,CAAC;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;YACpG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,gBAAgB,EAAE;SACxB,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QAC5E,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEtC,IAAI,aAAa,GAAyC,SAAS,CAAC;QACpE,IAAI,eAAe,KAAK,CAAC,EAAE,CAAC;YAC1B,aAAa,GAAG,WAAW,CAAC;QAC9B,CAAC;aAAM,IAAI,eAAe,GAAG,aAAa,EAAE,CAAC;YAC3C,aAAa,GAAG,UAAU,CAAC;QAC7B,CAAC;QAED,OAAO;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;YACR,SAAS;YACT,MAAM;YACN,OAAO,EAAE;gBACP,MAAM,EAAE,aAAa;gBACrB,eAAe;gBACf,aAAa;gBACb,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE;aAC/B;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,IAAmB,EAAE,OAAe,EAAE,OAAe;QACvE,MAAM,KAAK,GAAU;YACnB,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9D,IAAI;YACJ,OAAO;YACP,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE3B,qCAAqC;QACrC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;IAClE,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,YAAY,CAAC,OAAe;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QACtD,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;CACF"}