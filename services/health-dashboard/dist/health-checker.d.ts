import type { ServiceStatus, DatabaseStatus, SystemMetrics, HealthDashboardData, Alert, ServiceConfig } from './types.js';
export declare class HealthChecker {
    private alerts;
    private pgPool;
    constructor();
    checkServiceHealth(service: ServiceConfig): Promise<ServiceStatus>;
    checkDatabaseHealth(): Promise<DatabaseStatus>;
    getSystemMetrics(): Promise<SystemMetrics>;
    getFullHealthData(): Promise<HealthDashboardData>;
    private createAlert;
    private getRecentAlerts;
    getAlerts(): Alert[];
    resolveAlert(alertId: string): boolean;
    cleanup(): Promise<void>;
}
//# sourceMappingURL=health-checker.d.ts.map