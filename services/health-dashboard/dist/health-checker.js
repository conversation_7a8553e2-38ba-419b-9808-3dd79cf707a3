import { Pool } from 'pg';
import si from 'systeminformation';
import { SERVICES_CONFIG, MAX_RESPONSE_TIME_MS } from './config.js';
export class HealthChecker {
    alerts = [];
    pgPool;
    constructor() {
        // Initialize PostgreSQL connection for database monitoring
        this.pgPool = new Pool({
            host: process.env.PGHOST || 'localhost',
            port: Number(process.env.PGPORT) || 5433,
            database: process.env.PGDATABASE || 'gg',
            user: process.env.PGUSER || 'gg',
            password: process.env.PGPASSWORD || 'ggpassword',
            max: 5, // Limit connections for monitoring
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 5000,
        });
    }
    async checkServiceHealth(service) {
        const startTime = Date.now();
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), MAX_RESPONSE_TIME_MS);
            const response = await fetch(`${service.url}${service.healthEndpoint}`, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'HealthDashboard/1.0'
                }
            });
            clearTimeout(timeoutId);
            const responseTime = Date.now() - startTime;
            const isHealthy = response.ok;
            let details = {};
            try {
                const responseText = await response.text();
                details = responseText ? JSON.parse(responseText) : {};
            }
            catch {
                // Ignore JSON parse errors for non-JSON responses
            }
            const status = {
                name: service.name,
                url: service.url,
                status: isHealthy ? 'healthy' : 'unhealthy',
                responseTime,
                lastChecked: new Date(),
                details
            };
            // Create alert if service is unhealthy
            if (!isHealthy) {
                this.createAlert('error', service.name, `Service returned ${response.status}: ${response.statusText}`);
            }
            return status;
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.createAlert('error', service.name, `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return {
                name: service.name,
                url: service.url,
                status: 'unhealthy',
                responseTime,
                lastChecked: new Date(),
                details: { error: error instanceof Error ? error.message : 'Unknown error' }
            };
        }
    }
    async checkDatabaseHealth() {
        const startTime = Date.now();
        try {
            // Test connection and get stats
            const client = await this.pgPool.connect();
            try {
                // Get connection stats
                const statsQuery = `
          SELECT 
            count(*) as total_connections,
            count(*) FILTER (WHERE state = 'active') as active_connections,
            current_setting('max_connections')::int as max_connections
          FROM pg_stat_activity;
        `;
                const statsResult = await client.query(statsQuery);
                const stats = statsResult.rows[0];
                // Get database size
                const sizeQuery = `
          SELECT pg_size_pretty(pg_database_size(current_database())) as size;
        `;
                const sizeResult = await client.query(sizeQuery);
                const size = sizeResult.rows[0]?.size;
                // Get version
                const versionResult = await client.query('SELECT version()');
                const version = versionResult.rows[0]?.version?.split(' ')[1];
                const responseTime = Date.now() - startTime;
                return {
                    name: 'PostgreSQL',
                    status: 'healthy',
                    connectionCount: parseInt(stats.total_connections),
                    activeConnections: parseInt(stats.active_connections),
                    maxConnections: parseInt(stats.max_connections),
                    responseTime,
                    lastChecked: new Date(),
                    version,
                    size
                };
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.createAlert('error', 'PostgreSQL', `Database check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return {
                name: 'PostgreSQL',
                status: 'unhealthy',
                connectionCount: 0,
                activeConnections: 0,
                maxConnections: 0,
                responseTime,
                lastChecked: new Date()
            };
        }
    }
    async getSystemMetrics() {
        try {
            const [cpu, memory, disk, networkStats, osInfo] = await Promise.all([
                si.currentLoad(),
                si.mem(),
                si.fsSize(),
                si.networkStats(),
                si.osInfo()
            ]);
            const primaryDisk = disk.find(d => d.mount === '/') || disk[0];
            const primaryNetwork = networkStats[0] || { rx_bytes: 0, tx_bytes: 0 };
            return {
                cpu: {
                    usage: Math.round(cpu.currentLoad),
                    cores: cpu.cpus?.length || 0,
                    model: 'CPU'
                },
                memory: {
                    total: memory.total,
                    used: memory.used,
                    free: memory.free,
                    usage: Math.round((memory.used / memory.total) * 100)
                },
                disk: {
                    total: primaryDisk?.size || 0,
                    used: primaryDisk?.used || 0,
                    free: primaryDisk?.available || 0,
                    usage: Math.round(((primaryDisk?.used || 0) / (primaryDisk?.size || 1)) * 100)
                },
                network: {
                    rx: primaryNetwork.rx_bytes || 0,
                    tx: primaryNetwork.tx_bytes || 0
                },
                uptime: 0,
                loadAverage: cpu.avgLoad ? [cpu.avgLoad] : [0]
            };
        }
        catch (error) {
            console.error('Failed to get system metrics:', error);
            // Return default metrics on error
            return {
                cpu: { usage: 0, cores: 0, model: 'Unknown' },
                memory: { total: 0, used: 0, free: 0, usage: 0 },
                disk: { total: 0, used: 0, free: 0, usage: 0 },
                network: { rx: 0, tx: 0 },
                uptime: 0,
                loadAverage: [0]
            };
        }
    }
    async getFullHealthData() {
        const [services, databases, system] = await Promise.all([
            Promise.all(SERVICES_CONFIG.filter(s => s.type !== 'database').map(s => this.checkServiceHealth(s))),
            Promise.all([this.checkDatabaseHealth()]),
            this.getSystemMetrics()
        ]);
        const healthyServices = services.filter(s => s.status === 'healthy').length;
        const totalServices = services.length;
        let overallStatus = 'healthy';
        if (healthyServices === 0) {
            overallStatus = 'unhealthy';
        }
        else if (healthyServices < totalServices) {
            overallStatus = 'degraded';
        }
        return {
            timestamp: new Date(),
            services,
            databases,
            system,
            overall: {
                status: overallStatus,
                healthyServices,
                totalServices,
                alerts: this.getRecentAlerts()
            }
        };
    }
    createAlert(type, service, message) {
        const alert = {
            id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type,
            service,
            message,
            timestamp: new Date(),
            resolved: false
        };
        this.alerts.unshift(alert);
        // Keep only recent alerts (last 100)
        if (this.alerts.length > 100) {
            this.alerts = this.alerts.slice(0, 100);
        }
    }
    getRecentAlerts() {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return this.alerts.filter(alert => alert.timestamp > oneDayAgo);
    }
    getAlerts() {
        return this.alerts;
    }
    resolveAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            return true;
        }
        return false;
    }
    async cleanup() {
        await this.pgPool.end();
    }
}
//# sourceMappingURL=health-checker.js.map