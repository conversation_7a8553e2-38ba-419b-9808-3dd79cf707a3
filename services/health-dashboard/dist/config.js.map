{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": "AAEA,MAAM,CAAC,MAAM,eAAe,GAAoB;IAC9C;QACE,IAAI,EAAE,aAAa;QACnB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,wBAAwB;QAC5D,cAAc,EAAE,YAAY;QAC5B,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,oCAAoC;QACpD,YAAY,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,kBAAkB,CAAC;KAC/D;IACD;QACE,IAAI,EAAE,gBAAgB;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,uBAAuB;QAC1D,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,mCAAmC;QACnD,YAAY,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC;KACnC;IACD;QACE,IAAI,EAAE,gBAAgB;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,uBAAuB;QAC1D,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,mCAAmC;QACnD,YAAY,EAAE,CAAC,UAAU,CAAC;KAC3B;IACD;QACE,IAAI,EAAE,kBAAkB;QACxB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,uBAAuB;QAC5D,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,qCAAqC;QACrD,YAAY,EAAE,CAAC,UAAU,CAAC;KAC3B;IACD;QACE,IAAI,EAAE,cAAc;QACpB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;QACxD,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,iCAAiC;QACjD,YAAY,EAAE,CAAC,UAAU,CAAC;KAC3B;IACD;QACE,IAAI,EAAE,gBAAgB;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,uBAAuB;QAC1D,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,mCAAmC;QACnD,YAAY,EAAE,CAAC,UAAU,CAAC;KAC3B;IACD;QACE,IAAI,EAAE,kBAAkB;QACxB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB;QACtD,cAAc,EAAE,UAAU;QAC1B,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,gBAAgB;QACtB,cAAc,EAAE,yCAAyC;QACzD,YAAY,EAAE,CAAC,MAAM,CAAC;KACvB;IACD;QACE,IAAI,EAAE,YAAY;QAClB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,8CAA8C;QAC/E,cAAc,EAAE,EAAE;QAClB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,UAAU;QAChB,cAAc,EAAE,iCAAiC;KAClD;IACD;QACE,IAAI,EAAE,MAAM;QACZ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,uBAAuB;QACpD,cAAc,EAAE,OAAO;QACvB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,gBAAgB;QACtB,cAAc,EAAE,6BAA6B;KAC9C;IACD;QACE,IAAI,EAAE,UAAU;QAChB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;QACxD,cAAc,EAAE,GAAG;QACnB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,UAAU;QAChB,cAAc,EAAE,4BAA4B;KAC7C;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC,CAAC,YAAY;AACpG,MAAM,CAAC,MAAM,uBAAuB,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,KAAK,CAAC,CAAC,aAAa;AAC1G,MAAM,CAAC,MAAM,qBAAqB,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;AACrF,MAAM,CAAC,MAAM,oBAAoB,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,IAAI,CAAC;AAErF,MAAM,CAAC,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;AACzE,MAAM,CAAC,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,SAAS,CAAC"}