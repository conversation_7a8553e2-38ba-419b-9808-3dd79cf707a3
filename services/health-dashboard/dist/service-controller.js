import { exec } from 'child_process';
import { promisify } from 'util';
import { SERVICES_CONFIG } from './config.js';
const execAsync = promisify(exec);
export class ServiceController {
    restartInProgress = new Set();
    async restartService(request) {
        const { service, force = false } = request;
        // Check if restart is already in progress
        if (this.restartInProgress.has(service) && !force) {
            return {
                success: false,
                message: `Restart already in progress for ${service}`,
                service,
                timestamp: new Date()
            };
        }
        // Find service configuration
        const serviceConfig = SERVICES_CONFIG.find(s => s.name === service);
        if (!serviceConfig || !serviceConfig.restartCommand) {
            return {
                success: false,
                message: `Service ${service} not found or restart command not configured`,
                service,
                timestamp: new Date()
            };
        }
        this.restartInProgress.add(service);
        try {
            console.log(`Restarting service: ${service}`);
            // Execute restart command
            const { stdout, stderr } = await execAsync(serviceConfig.restartCommand, {
                timeout: 30000, // 30 second timeout
                cwd: process.cwd()
            });
            console.log(`Restart output for ${service}:`, stdout);
            if (stderr) {
                console.warn(`Restart stderr for ${service}:`, stderr);
            }
            // Wait a moment for service to start
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Verify service is healthy after restart
            const isHealthy = await this.verifyServiceHealth(serviceConfig);
            if (isHealthy) {
                return {
                    success: true,
                    message: `Service ${service} restarted successfully`,
                    service,
                    timestamp: new Date()
                };
            }
            else {
                return {
                    success: false,
                    message: `Service ${service} restarted but health check failed`,
                    service,
                    timestamp: new Date()
                };
            }
        }
        catch (error) {
            console.error(`Failed to restart ${service}:`, error);
            return {
                success: false,
                message: `Failed to restart ${service}: ${error instanceof Error ? error.message : 'Unknown error'}`,
                service,
                timestamp: new Date()
            };
        }
        finally {
            this.restartInProgress.delete(service);
        }
    }
    async verifyServiceHealth(serviceConfig) {
        try {
            // Skip health check for database services
            if (serviceConfig.type === 'database') {
                return true;
            }
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
            const response = await fetch(`${serviceConfig.url}${serviceConfig.healthEndpoint}`, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'HealthDashboard/1.0'
                }
            });
            clearTimeout(timeoutId);
            return response.ok;
        }
        catch (error) {
            console.warn(`Health check failed for ${serviceConfig.name}:`, error);
            return false;
        }
    }
    async startService(serviceName) {
        const serviceConfig = SERVICES_CONFIG.find(s => s.name === serviceName);
        if (!serviceConfig) {
            return {
                success: false,
                message: `Service ${serviceName} not found`,
                service: serviceName,
                timestamp: new Date()
            };
        }
        try {
            // For Docker services, use docker compose up
            let startCommand = serviceConfig.restartCommand || '';
            if (startCommand.includes('restart')) {
                startCommand = startCommand.replace('restart', 'up -d');
            }
            else if (serviceConfig.name === 'Frontend') {
                startCommand = 'cd frontend && npm run dev &';
            }
            const { stdout, stderr } = await execAsync(startCommand, {
                timeout: 30000,
                cwd: process.cwd()
            });
            console.log(`Start output for ${serviceName}:`, stdout);
            if (stderr) {
                console.warn(`Start stderr for ${serviceName}:`, stderr);
            }
            // Wait for service to start
            await new Promise(resolve => setTimeout(resolve, 3000));
            const isHealthy = await this.verifyServiceHealth(serviceConfig);
            return {
                success: isHealthy,
                message: isHealthy
                    ? `Service ${serviceName} started successfully`
                    : `Service ${serviceName} started but health check failed`,
                service: serviceName,
                timestamp: new Date()
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to start ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
                service: serviceName,
                timestamp: new Date()
            };
        }
    }
    async stopService(serviceName) {
        const serviceConfig = SERVICES_CONFIG.find(s => s.name === serviceName);
        if (!serviceConfig) {
            return {
                success: false,
                message: `Service ${serviceName} not found`,
                service: serviceName,
                timestamp: new Date()
            };
        }
        try {
            // For Docker services, use docker compose stop
            let stopCommand = serviceConfig.restartCommand || '';
            if (stopCommand.includes('restart')) {
                stopCommand = stopCommand.replace('restart', 'stop');
            }
            if (!stopCommand) {
                return {
                    success: false,
                    message: `No stop command configured for ${serviceName}`,
                    service: serviceName,
                    timestamp: new Date()
                };
            }
            const { stdout, stderr } = await execAsync(stopCommand, {
                timeout: 30000,
                cwd: process.cwd()
            });
            console.log(`Stop output for ${serviceName}:`, stdout);
            if (stderr) {
                console.warn(`Stop stderr for ${serviceName}:`, stderr);
            }
            return {
                success: true,
                message: `Service ${serviceName} stopped successfully`,
                service: serviceName,
                timestamp: new Date()
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to stop ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
                service: serviceName,
                timestamp: new Date()
            };
        }
    }
    getRestartStatus() {
        return Array.from(this.restartInProgress);
    }
}
//# sourceMappingURL=service-controller.js.map