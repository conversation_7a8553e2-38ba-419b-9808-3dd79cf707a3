import type { RestartRequest, RestartResponse } from './types.js';
export declare class ServiceController {
    private restartInProgress;
    restartService(request: RestartRequest): Promise<RestartResponse>;
    private verifyServiceHealth;
    startService(serviceName: string): Promise<RestartResponse>;
    stopService(serviceName: string): Promise<RestartResponse>;
    getRestartStatus(): string[];
}
//# sourceMappingURL=service-controller.d.ts.map