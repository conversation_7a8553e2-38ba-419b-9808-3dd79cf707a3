import Fastify from 'fastify';
import cors from '@fastify/cors';
import websocket from '@fastify/websocket';
import cron from 'node-cron';
import { HealthChecker } from './health-checker.js';
import { ServiceController } from './service-controller.js';
import { DASHBOARD_PORT, DASHBOARD_HOST, HEALTH_CHECK_INTERVAL, SYSTEM_METRICS_INTERVAL } from './config.js';
const app = Fastify({
    logger: {
        level: 'info',
        transport: {
            target: 'pino-pretty',
            options: {
                colorize: true
            }
        }
    }
});
// Global state
let latestHealthData = null;
const connectedClients = new Set();
// Initialize services
const healthChecker = new HealthChecker();
const serviceController = new ServiceController();
// Register plugins
await app.register(cors, {
    origin: true,
    credentials: true
});
await app.register(websocket);
// Health endpoints
app.get('/health', async () => {
    return { ok: true, service: 'health-dashboard', timestamp: new Date() };
});
app.get('/healthz', async () => {
    return { status: 'ok', timestamp: new Date() };
});
app.get('/readyz', async (request, reply) => {
    const isReady = latestHealthData !== null;
    reply.status(isReady ? 200 : 503);
    return {
        ready: isReady,
        timestamp: new Date()
    };
});
// API endpoints
app.get('/api/health-data', async () => {
    if (!latestHealthData) {
        latestHealthData = await healthChecker.getFullHealthData();
    }
    return latestHealthData;
});
app.get('/api/alerts', async () => {
    return healthChecker.getAlerts();
});
app.post('/api/alerts/:alertId/resolve', async (request, reply) => {
    const { alertId } = request.params;
    const resolved = healthChecker.resolveAlert(alertId);
    if (!resolved) {
        reply.status(404);
        return { error: `Alert ${alertId} not found` };
    }
    return { success: true, alertId, timestamp: new Date() };
});
// Service control endpoints
app.post('/api/services/restart', async (request) => {
    const result = await serviceController.restartService(request.body);
    // Broadcast restart event to connected clients
    broadcastToClients({
        type: 'service-restart',
        data: result
    });
    return result;
});
app.post('/api/services/start', async (request) => {
    const { service } = request.body;
    const result = await serviceController.startService(service);
    broadcastToClients({
        type: 'service-start',
        data: result
    });
    return result;
});
app.post('/api/services/stop', async (request) => {
    const { service } = request.body;
    const result = await serviceController.stopService(service);
    broadcastToClients({
        type: 'service-stop',
        data: result
    });
    return result;
});
app.get('/api/services/restart-status', async () => {
    return {
        inProgress: serviceController.getRestartStatus(),
        timestamp: new Date()
    };
});
// WebSocket endpoint for real-time updates
app.register(async function (fastify) {
    fastify.get('/ws', { websocket: true }, (connection, req) => {
        console.log('New WebSocket connection established');
        connectedClients.add(connection);
        // Send current health data immediately
        if (latestHealthData) {
            connection.send(JSON.stringify({
                type: 'health-data',
                data: latestHealthData
            }));
        }
        connection.on('close', () => {
            console.log('WebSocket connection closed');
            connectedClients.delete(connection);
        });
        connection.on('error', (error) => {
            console.error('WebSocket error:', error);
            connectedClients.delete(connection);
        });
    });
});
// Serve static files for the dashboard UI
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Fallback route for SPA
app.get('/*', async (request, reply) => {
    try {
        const indexPath = join(__dirname, '../public/index.html');
        const indexContent = readFileSync(indexPath, 'utf-8');
        reply.type('text/html');
        return indexContent;
    }
    catch (error) {
        reply.status(404);
        return { error: 'Dashboard UI not found' };
    }
});
// Background tasks
function startHealthMonitoring() {
    console.log('Starting health monitoring...');
    // Health check interval
    cron.schedule(`*/${Math.floor(HEALTH_CHECK_INTERVAL / 1000)} * * * * *`, async () => {
        try {
            latestHealthData = await healthChecker.getFullHealthData();
            // Broadcast to connected WebSocket clients
            broadcastToClients({
                type: 'health-data',
                data: latestHealthData
            });
        }
        catch (error) {
            console.error('Health check failed:', error);
        }
    });
    // System metrics interval (less frequent)
    cron.schedule(`*/${Math.floor(SYSTEM_METRICS_INTERVAL / 1000)} * * * * *`, async () => {
        try {
            if (latestHealthData) {
                latestHealthData.system = await healthChecker.getSystemMetrics();
                latestHealthData.timestamp = new Date();
                broadcastToClients({
                    type: 'system-metrics',
                    data: latestHealthData.system
                });
            }
        }
        catch (error) {
            console.error('System metrics update failed:', error);
        }
    });
}
function broadcastToClients(message) {
    const messageStr = JSON.stringify(message);
    for (const client of connectedClients) {
        try {
            if (client.readyState === 1) { // WebSocket.OPEN
                client.send(messageStr);
            }
            else {
                connectedClients.delete(client);
            }
        }
        catch (error) {
            console.error('Failed to send message to client:', error);
            connectedClients.delete(client);
        }
    }
}
// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down health dashboard...');
    // Close WebSocket connections
    for (const client of connectedClients) {
        try {
            client.close();
        }
        catch (error) {
            console.error('Error closing WebSocket connection:', error);
        }
    }
    // Cleanup health checker
    await healthChecker.cleanup();
    // Close Fastify
    await app.close();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    await healthChecker.cleanup();
    await app.close();
    process.exit(0);
});
// Start the server
async function start() {
    try {
        // Get initial health data
        console.log('Getting initial health data...');
        latestHealthData = await healthChecker.getFullHealthData();
        // Start background monitoring
        startHealthMonitoring();
        // Start the server
        await app.listen({
            port: DASHBOARD_PORT,
            host: DASHBOARD_HOST
        });
        console.log(`🏥 Health Dashboard running on http://${DASHBOARD_HOST}:${DASHBOARD_PORT}`);
        console.log(`📊 WebSocket endpoint: ws://${DASHBOARD_HOST}:${DASHBOARD_PORT}/ws`);
    }
    catch (error) {
        console.error('Failed to start health dashboard:', error);
        process.exit(1);
    }
}
start();
//# sourceMappingURL=index.js.map