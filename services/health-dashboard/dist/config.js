export const SERVICES_CONFIG = [
    {
        name: 'API Gateway',
        url: process.env.API_GATEWAY_URL || 'http://localhost:19081',
        healthEndpoint: '/v1/health',
        port: 19081,
        type: 'api',
        restartCommand: 'docker compose restart api-gateway',
        dependencies: ['orders-svc', 'fleets-svc', 'event-dispatcher']
    },
    {
        name: 'Orders Service',
        url: process.env.ORDERS_SVC_URL || 'http://localhost:8081',
        healthEndpoint: '/health',
        port: 8081,
        type: 'api',
        restartCommand: 'docker compose restart orders-svc',
        dependencies: ['postgres', 'nats']
    },
    {
        name: 'Fleets Service',
        url: process.env.FLEETS_SVC_URL || 'http://localhost:8082',
        healthEndpoint: '/health',
        port: 8082,
        type: 'api',
        restartCommand: 'docker compose restart fleets-svc',
        dependencies: ['postgres']
    },
    {
        name: 'Colonies Service',
        url: process.env.COLONIES_SVC_URL || 'http://localhost:8083',
        healthEndpoint: '/health',
        port: 8083,
        type: 'api',
        restartCommand: 'docker compose restart colonies-svc',
        dependencies: ['postgres']
    },
    {
        name: 'Tech Service',
        url: process.env.TECH_SVC_URL || 'http://localhost:8084',
        healthEndpoint: '/health',
        port: 8084,
        type: 'api',
        restartCommand: 'docker compose restart tech-svc',
        dependencies: ['postgres']
    },
    {
        name: 'Market Service',
        url: process.env.MARKET_SVC_URL || 'http://localhost:8085',
        healthEndpoint: '/health',
        port: 8085,
        type: 'api',
        restartCommand: 'docker compose restart market-svc',
        dependencies: ['postgres']
    },
    {
        name: 'Event Dispatcher',
        url: process.env.EVENTS_URL || 'http://localhost:8090',
        healthEndpoint: '/healthz',
        port: 8090,
        type: 'message-broker',
        restartCommand: 'docker compose restart event-dispatcher',
        dependencies: ['nats']
    },
    {
        name: 'PostgreSQL',
        url: process.env.POSTGRES_URL || 'postgresql://gg:ggpassword@localhost:5433/gg',
        healthEndpoint: '',
        port: 5433,
        type: 'database',
        restartCommand: 'docker compose restart postgres'
    },
    {
        name: 'NATS',
        url: process.env.NATS_URL || 'http://localhost:8222',
        healthEndpoint: '/varz',
        port: 8222,
        type: 'message-broker',
        restartCommand: 'docker compose restart nats'
    },
    {
        name: 'Frontend',
        url: process.env.FRONTEND_URL || 'http://localhost:5174',
        healthEndpoint: '/',
        port: 5174,
        type: 'frontend',
        restartCommand: 'cd frontend && npm run dev'
    }
];
export const HEALTH_CHECK_INTERVAL = Number(process.env.HEALTH_CHECK_INTERVAL) || 5000; // 5 seconds
export const SYSTEM_METRICS_INTERVAL = Number(process.env.SYSTEM_METRICS_INTERVAL) || 10000; // 10 seconds
export const ALERT_RETENTION_HOURS = Number(process.env.ALERT_RETENTION_HOURS) || 24;
export const MAX_RESPONSE_TIME_MS = Number(process.env.MAX_RESPONSE_TIME_MS) || 5000;
export const DASHBOARD_PORT = Number(process.env.DASHBOARD_PORT) || 8086;
export const DASHBOARD_HOST = process.env.DASHBOARD_HOST || '0.0.0.0';
//# sourceMappingURL=config.js.map