import type { ServiceConfig } from './types.js';
export declare const SERVICES_CONFIG: ServiceConfig[];
export declare const HEALTH_CHECK_INTERVAL: number;
export declare const SYSTEM_METRICS_INTERVAL: number;
export declare const ALERT_RETENTION_HOURS: number;
export declare const MAX_RESPONSE_TIME_MS: number;
export declare const DASHBOARD_PORT: number;
export declare const DASHBOARD_HOST: string;
//# sourceMappingURL=config.d.ts.map