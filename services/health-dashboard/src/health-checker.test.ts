import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HealthChecker } from './health-checker.js';
import type { ServiceConfig } from './types.js';

// Mock dependencies
vi.mock('pg', () => ({
  Pool: vi.fn(() => ({
    connect: vi.fn(),
    end: vi.fn()
  }))
}));

vi.mock('systeminformation', () => ({
  default: {
    currentLoad: vi.fn(),
    mem: vi.fn(),
    fsSize: vi.fn(),
    networkStats: vi.fn(),
    osInfo: vi.fn()
  }
}));

// Mock fetch globally
global.fetch = vi.fn();

describe('HealthChecker', () => {
  let healthChecker: HealthChecker;
  
  beforeEach(() => {
    healthChecker = new HealthChecker();
    vi.clearAllMocks();
  });
  
  afterEach(async () => {
    await healthChecker.cleanup();
  });

  describe('checkServiceHealth', () => {
    const mockService: ServiceConfig = {
      name: 'Test Service',
      url: 'http://localhost:8080',
      healthEndpoint: '/health',
      port: 8080,
      type: 'api'
    };

    it('should return healthy status for successful health check', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: vi.fn().mockResolvedValue('{"status": "ok"}')
      };
      
      (global.fetch as any).mockResolvedValue(mockResponse);
      
      const result = await healthChecker.checkServiceHealth(mockService);
      
      expect(result.status).toBe('healthy');
      expect(result.name).toBe('Test Service');
      expect(result.url).toBe('http://localhost:8080');
      expect(typeof result.responseTime).toBe('number');
      expect(result.lastChecked).toBeInstanceOf(Date);
    });

    it('should return unhealthy status for failed health check', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: vi.fn().mockResolvedValue('')
      };
      
      (global.fetch as any).mockResolvedValue(mockResponse);
      
      const result = await healthChecker.checkServiceHealth(mockService);
      
      expect(result.status).toBe('unhealthy');
      expect(result.name).toBe('Test Service');
      expect(typeof result.responseTime).toBe('number');
    });

    it('should return unhealthy status for network error', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Network error'));
      
      const result = await healthChecker.checkServiceHealth(mockService);
      
      expect(result.status).toBe('unhealthy');
      expect(result.details?.error).toBe('Network error');
    });

    it('should handle timeout correctly', async () => {
      // Mock a slow response
      (global.fetch as any).mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 10000))
      );
      
      const result = await healthChecker.checkServiceHealth(mockService);
      
      expect(result.status).toBe('unhealthy');
    });
  });

  describe('getSystemMetrics', () => {
    it('should return system metrics', async () => {
      const si = await import('systeminformation');
      
      // Mock system information responses
      (si.default.currentLoad as any).mockResolvedValue({
        currentLoad: 25.5,
        cpus: [{ model: 'Intel Core i7' }],
        avgLoad: 1.2
      });
      
      (si.default.mem as any).mockResolvedValue({
        total: **********, // 8GB
        used: **********,  // 4GB
        free: **********   // 4GB
      });
      
      (si.default.fsSize as any).mockResolvedValue([{
        mount: '/',
        size: 1000000000000, // 1TB
        used: 500000000000,  // 500GB
        available: 500000000000 // 500GB
      }]);
      
      (si.default.networkStats as any).mockResolvedValue([{
        rx_bytes: 1000000,
        tx_bytes: 500000
      }]);
      
      (si.default.osInfo as any).mockResolvedValue({
        uptime: 86400 // 1 day
      });
      
      const metrics = await healthChecker.getSystemMetrics();
      
      expect(metrics.cpu.usage).toBe(26); // rounded
      expect(metrics.memory.total).toBe(**********);
      expect(metrics.memory.usage).toBe(50); // 50%
      expect(metrics.disk.total).toBe(1000000000000);
      expect(metrics.disk.usage).toBe(50); // 50%
      expect(metrics.network.rx).toBe(1000000);
      expect(metrics.network.tx).toBe(500000);
    });

    it('should handle system information errors gracefully', async () => {
      const si = await import('systeminformation');
      
      // Mock all calls to throw errors
      (si.default.currentLoad as any).mockRejectedValue(new Error('System error'));
      (si.default.mem as any).mockRejectedValue(new Error('System error'));
      (si.default.fsSize as any).mockRejectedValue(new Error('System error'));
      (si.default.networkStats as any).mockRejectedValue(new Error('System error'));
      (si.default.osInfo as any).mockRejectedValue(new Error('System error'));
      
      const metrics = await healthChecker.getSystemMetrics();
      
      // Should return default values
      expect(metrics.cpu.usage).toBe(0);
      expect(metrics.memory.total).toBe(0);
      expect(metrics.disk.total).toBe(0);
      expect(metrics.network.rx).toBe(0);
      expect(metrics.uptime).toBe(0);
    });
  });

  describe('alert management', () => {
    it('should create and retrieve alerts', async () => {
      const mockService: ServiceConfig = {
        name: 'Test Service',
        url: 'http://localhost:8080',
        healthEndpoint: '/health',
        port: 8080,
        type: 'api'
      };

      // Trigger an unhealthy service to create an alert
      (global.fetch as any).mockRejectedValue(new Error('Service down'));
      
      await healthChecker.checkServiceHealth(mockService);
      
      const alerts = healthChecker.getAlerts();
      expect(alerts.length).toBeGreaterThan(0);
      expect(alerts[0].service).toBe('Test Service');
      expect(alerts[0].type).toBe('error');
    });

    it('should resolve alerts', async () => {
      const mockService: ServiceConfig = {
        name: 'Test Service',
        url: 'http://localhost:8080',
        healthEndpoint: '/health',
        port: 8080,
        type: 'api'
      };

      // Create an alert
      (global.fetch as any).mockRejectedValue(new Error('Service down'));
      await healthChecker.checkServiceHealth(mockService);
      
      const alerts = healthChecker.getAlerts();
      const alertId = alerts[0].id;
      
      const resolved = healthChecker.resolveAlert(alertId);
      expect(resolved).toBe(true);
      
      const updatedAlerts = healthChecker.getAlerts();
      expect(updatedAlerts[0].resolved).toBe(true);
    });
  });

  describe('getFullHealthData', () => {
    it('should return complete health data', async () => {
      // Mock successful service checks
      (global.fetch as any).mockResolvedValue({
        ok: true,
        status: 200,
        text: vi.fn().mockResolvedValue('{"status": "ok"}')
      });
      
      // Mock system metrics
      const si = await import('systeminformation');
      (si.default.currentLoad as any).mockResolvedValue({
        currentLoad: 25.5,
        cpus: [{ model: 'Intel Core i7' }],
        avgLoad: 1.2
      });
      (si.default.mem as any).mockResolvedValue({
        total: **********,
        used: **********,
        free: **********
      });
      (si.default.fsSize as any).mockResolvedValue([{
        mount: '/',
        size: 1000000000000,
        used: 500000000000,
        available: 500000000000
      }]);
      (si.default.networkStats as any).mockResolvedValue([{
        rx_bytes: 1000000,
        tx_bytes: 500000
      }]);
      (si.default.osInfo as any).mockResolvedValue({
        uptime: 86400
      });
      
      const healthData = await healthChecker.getFullHealthData();
      
      expect(healthData.timestamp).toBeInstanceOf(Date);
      expect(Array.isArray(healthData.services)).toBe(true);
      expect(Array.isArray(healthData.databases)).toBe(true);
      expect(healthData.system).toBeDefined();
      expect(healthData.overall).toBeDefined();
      expect(healthData.overall.status).toMatch(/healthy|degraded|unhealthy/);
      expect(typeof healthData.overall.healthyServices).toBe('number');
      expect(typeof healthData.overall.totalServices).toBe('number');
    });
  });
});
