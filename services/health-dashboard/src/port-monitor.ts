import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface PortInfo {
  port: number;
  protocol: 'tcp' | 'udp';
  state: 'LISTEN' | 'ESTABLISHED' | 'CLOSED' | 'TIME_WAIT' | 'UNKNOWN';
  pid?: number;
  processName?: string;
  service?: string;
  description?: string;
  isExpected: boolean;
  lastChecked: Date;
}

export interface PortScanResult {
  timestamp: Date;
  ports: PortInfo[];
  summary: {
    total: number;
    listening: number;
    expected: number;
    unexpected: number;
    missing: number;
  };
}

export class PortMonitor {
  private expectedPorts: Map<number, { service: string; description: string; protocol: 'tcp' | 'udp' }>;

  constructor() {
    this.expectedPorts = new Map([
      // Galactic Genesis API Gateway (multiple instances)
      [19081, { service: 'API Gateway', description: 'Main API Gateway (default port)', protocol: 'tcp' }],
      [19080, { service: 'API Gateway', description: 'API Gateway (fixed port)', protocol: 'tcp' }],
      [8080, { service: 'API Gateway', description: 'API Gateway (internal port)', protocol: 'tcp' }],

      // Galactic Genesis Microservices
      [8081, { service: 'Orders Service', description: 'Orders microservice (PORT=8081)', protocol: 'tcp' }],
      [8082, { service: 'Fleets Service', description: 'Fleets microservice (PORT=8082)', protocol: 'tcp' }],
      [8083, { service: 'Colonies Service', description: 'Colonies microservice (PORT=8083)', protocol: 'tcp' }],
      [8084, { service: 'Tech Service', description: 'Technology microservice (PORT=8084)', protocol: 'tcp' }],
      [8085, { service: 'Market Service', description: 'Market microservice (PORT=8085)', protocol: 'tcp' }],
      [8090, { service: 'Event Dispatcher', description: 'Event streaming WebSocket service', protocol: 'tcp' }],



      // Infrastructure Services
      [5433, { service: 'PostgreSQL', description: 'Database server (Docker host port)', protocol: 'tcp' }],
      [5432, { service: 'PostgreSQL', description: 'Database server (container/local)', protocol: 'tcp' }],
      [4222, { service: 'NATS', description: 'Message broker (client port)', protocol: 'tcp' }],
      [8222, { service: 'NATS', description: 'Message broker (monitoring port)', protocol: 'tcp' }],

      // Frontend Development Servers
      [5174, { service: 'Frontend', description: 'React frontend (Vite dev server)', protocol: 'tcp' }],
      [5175, { service: 'Frontend', description: 'React frontend (Vite alt port)', protocol: 'tcp' }],
      [5176, { service: 'Frontend', description: 'React frontend (Vite current)', protocol: 'tcp' }],
      [5173, { service: 'Frontend', description: 'Vite dev server (default)', protocol: 'tcp' }],

      // Health & Monitoring
      [8086, { service: 'Health Dashboard', description: 'Health monitoring dashboard', protocol: 'tcp' }],
    ]);
  }

  async scanPorts(): Promise<PortScanResult> {
    const timestamp = new Date();
    const ports: PortInfo[] = [];

    try {
      // Get listening ports using ss (socket statistics)
      const { stdout: ssOutput } = await execAsync('ss -tlnp 2>/dev/null || true');
      const listeningPorts = this.parseSSOutput(ssOutput);

      // Get all expected ports and check their status
      for (const [port, config] of this.expectedPorts) {
        const foundPort = listeningPorts.find(p => p.port === port);
        
        if (foundPort) {
          ports.push({
            ...foundPort,
            service: config.service,
            description: config.description,
            isExpected: true,
            lastChecked: timestamp
          });
        } else {
          // Port is expected but not listening
          ports.push({
            port,
            protocol: config.protocol,
            state: 'CLOSED',
            service: config.service,
            description: config.description,
            isExpected: true,
            lastChecked: timestamp
          });
        }
      }

      // Add any unexpected listening ports
      for (const listeningPort of listeningPorts) {
        if (!this.expectedPorts.has(listeningPort.port)) {
          ports.push({
            ...listeningPort,
            service: 'Unknown',
            description: 'Unexpected service',
            isExpected: false,
            lastChecked: timestamp
          });
        }
      }

      // Sort ports by port number
      ports.sort((a, b) => a.port - b.port);

      // Calculate summary
      const summary = {
        total: ports.length,
        listening: ports.filter(p => p.state === 'LISTEN').length,
        expected: ports.filter(p => p.isExpected && p.state === 'LISTEN').length,
        unexpected: ports.filter(p => !p.isExpected && p.state === 'LISTEN').length,
        missing: ports.filter(p => p.isExpected && p.state !== 'LISTEN').length
      };

      return {
        timestamp,
        ports,
        summary
      };

    } catch (error) {
      console.error('Error scanning ports:', error);
      return {
        timestamp,
        ports: [],
        summary: { total: 0, listening: 0, expected: 0, unexpected: 0, missing: 0 }
      };
    }
  }

  private parseSSOutput(output: string): PortInfo[] {
    const ports: PortInfo[] = [];
    const portMap = new Map<number, PortInfo>(); // Deduplicate by port number
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      try {
        // Parse ss output format: State Recv-Q Send-Q Local Address:Port Peer Address:Port Process
        const parts = line.trim().split(/\s+/);
        if (parts.length < 4) continue;

        const state = parts[0];
        const localAddress = parts[3];
        const processInfo = parts[5] || '';

        // Extract port from local address (format: *:port or ip:port)
        const portMatch = localAddress.match(/:(\d+)$/);
        if (!portMatch) continue;

        const port = parseInt(portMatch[1], 10);
        if (isNaN(port)) continue;

        // Skip if we already have this port (deduplication)
        if (portMap.has(port)) continue;

        // Extract process info
        let pid: number | undefined;
        let processName: string | undefined;

        const pidMatch = processInfo.match(/pid=(\d+)/);
        if (pidMatch) {
          pid = parseInt(pidMatch[1], 10);
        }

        const processMatch = processInfo.match(/users:\(\("([^"]+)"/);
        if (processMatch) {
          processName = processMatch[1];
        }

        const portInfo: PortInfo = {
          port,
          protocol: 'tcp', // ss -tln only shows TCP
          state: state as any,
          pid,
          processName,
          isExpected: false, // Will be set later
          lastChecked: new Date()
        };

        portMap.set(port, portInfo);

      } catch (error) {
        // Skip malformed lines
        continue;
      }
    }

    // Convert map to array
    return Array.from(portMap.values());
  }

  async killPort(port: number): Promise<{ success: boolean; message: string }> {
    try {
      // Find processes using the port
      const { stdout } = await execAsync(`ss -tlnp | grep :${port} || true`);
      
      if (!stdout.trim()) {
        return { success: false, message: `No process found on port ${port}` };
      }

      // Extract PIDs
      const pidMatches = stdout.match(/pid=(\d+)/g);
      if (!pidMatches) {
        return { success: false, message: `Could not extract PID for port ${port}` };
      }

      const pids = pidMatches.map(match => match.replace('pid=', '')).filter((pid, index, arr) => arr.indexOf(pid) === index);

      // Kill processes
      for (const pid of pids) {
        await execAsync(`kill -9 ${pid}`);
      }

      return { 
        success: true, 
        message: `Successfully killed ${pids.length} process(es) on port ${port}: ${pids.join(', ')}` 
      };

    } catch (error) {
      return { 
        success: false, 
        message: `Failed to kill processes on port ${port}: ${error.message}` 
      };
    }
  }

  async checkPortAvailability(port: number): Promise<boolean> {
    try {
      const { stdout } = await execAsync(`ss -tln | grep :${port} || true`);
      return !stdout.trim(); // Port is available if no output
    } catch (error) {
      return false;
    }
  }

  async findFreePort(startPort: number = 3000, maxAttempts: number = 50): Promise<number | null> {
    for (let i = 0; i < maxAttempts; i++) {
      const port = startPort + i;
      const isAvailable = await this.checkPortAvailability(port);
      if (isAvailable) {
        return port;
      }
    }
    return null;
  }

  getExpectedPorts(): Array<{ port: number; service: string; description: string; protocol: string }> {
    return Array.from(this.expectedPorts.entries()).map(([port, config]) => ({
      port,
      ...config
    }));
  }

  addExpectedPort(port: number, service: string, description: string, protocol: 'tcp' | 'udp' = 'tcp'): void {
    this.expectedPorts.set(port, { service, description, protocol });
  }

  removeExpectedPort(port: number): void {
    this.expectedPorts.delete(port);
  }
}
