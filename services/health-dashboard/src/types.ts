export interface ServiceStatus {
  name: string;
  url: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  responseTime: number;
  lastChecked: Date;
  uptime?: number;
  version?: string;
  details?: Record<string, any>;
}

export interface DatabaseStatus {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  connectionCount: number;
  activeConnections: number;
  maxConnections: number;
  responseTime: number;
  lastChecked: Date;
  version?: string;
  size?: string;
}

export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    model: string;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    rx: number;
    tx: number;
  };
  uptime: number;
  loadAverage: number[];
}

export interface HealthDashboardData {
  timestamp: Date;
  services: ServiceStatus[];
  databases: DatabaseStatus[];
  system: SystemMetrics;
  overall: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    healthyServices: number;
    totalServices: number;
    alerts: Alert[];
  };
}

export interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info';
  service: string;
  message: string;
  timestamp: Date;
  resolved?: boolean;
}

export interface ServiceConfig {
  name: string;
  url: string;
  healthEndpoint: string;
  port: number;
  type: 'api' | 'database' | 'message-broker' | 'frontend';
  restartCommand?: string;
  dependencies?: string[];
}

export interface RestartRequest {
  service: string;
  force?: boolean;
}

export interface RestartResponse {
  success: boolean;
  message: string;
  service: string;
  timestamp: Date;
}
