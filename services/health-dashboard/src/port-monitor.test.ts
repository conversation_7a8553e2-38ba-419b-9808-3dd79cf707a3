import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PortMonitor } from './port-monitor.js';
import { exec } from 'child_process';

// Mock child_process
vi.mock('child_process', () => ({
  exec: vi.fn()
}));

const mockExec = vi.mocked(exec);

describe('PortMonitor', () => {
  let portMonitor: PortMonitor;

  beforeEach(() => {
    portMonitor = new PortMonitor();
    vi.clearAllMocks();
  });

  describe('scanPorts', () => {
    it('should parse ss output correctly', async () => {
      const mockSSOutput = `State      Recv-Q Send-Q Local Address:Port   Peer Address:Port Process
LISTEN     0      128          *:22                *:*     users:(("sshd",pid=1234,fd=3))
LISTEN     0      128          *:8080              *:*     users:(("node",pid=5678,fd=4))
LISTEN     0      50           *:5432              *:*     users:(("postgres",pid=9012,fd=5))`;

      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: mockSSOutput, stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.scanPorts();

      expect(result.ports.length).toBeGreaterThanOrEqual(3); // At least the 3 ports from mock data
      expect(result.summary.total).toBeGreaterThan(0);
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should handle empty ss output', async () => {
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: '', stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.scanPorts();

      expect(result.ports.length).toBeGreaterThanOrEqual(0); // Should include expected ports even if no listening ports
      expect(result.summary.total).toBeGreaterThanOrEqual(0);
    });

    it('should handle ss command errors gracefully', async () => {
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(new Error('Command failed'), { stdout: '', stderr: 'Error' });
        }
        return {} as any;
      });

      const result = await portMonitor.scanPorts();

      expect(result.ports).toEqual([]);
      expect(result.summary.total).toBe(0);
    });
  });

  describe('killPort', () => {
    it('should kill process successfully', async () => {
      const mockSSOutput = `LISTEN     0      128          *:8080              *:*     users:(("node",pid=5678,fd=4))`;

      mockExec
        .mockImplementationOnce((cmd, callback) => {
          // First call to find processes
          if (typeof callback === 'function') {
            callback(null, { stdout: mockSSOutput, stderr: '' });
          }
          return {} as any;
        })
        .mockImplementationOnce((cmd, callback) => {
          // Second call to kill process
          if (typeof callback === 'function') {
            callback(null, { stdout: '', stderr: '' });
          }
          return {} as any;
        });

      const result = await portMonitor.killPort(8080);

      expect(result.success).toBe(true);
      expect(result.message).toContain('5678');
    });

    it('should handle no process found', async () => {
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: '', stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.killPort(8080);

      expect(result.success).toBe(false);
      expect(result.message).toContain('No process found');
    });

    it('should handle kill command errors', async () => {
      const mockSSOutput = `LISTEN     0      128          *:8080              *:*     users:(("node",pid=5678,fd=4))`;

      mockExec
        .mockImplementationOnce((cmd, callback) => {
          // First call to find processes
          if (typeof callback === 'function') {
            callback(null, { stdout: mockSSOutput, stderr: '' });
          }
          return {} as any;
        })
        .mockImplementationOnce((cmd, callback) => {
          // Second call to kill process fails
          if (typeof callback === 'function') {
            callback(new Error('Permission denied'), { stdout: '', stderr: 'Permission denied' });
          }
          return {} as any;
        });

      const result = await portMonitor.killPort(8080);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to kill');
    });
  });

  describe('checkPortAvailability', () => {
    it('should return true for available port', async () => {
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: '', stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.checkPortAvailability(8080);

      expect(result).toBe(true);
    });

    it('should return false for occupied port', async () => {
      const mockSSOutput = `LISTEN     0      128          *:8080              *:*`;

      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: mockSSOutput, stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.checkPortAvailability(8080);

      expect(result).toBe(false);
    });

    it('should handle command errors', async () => {
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(new Error('Command failed'), { stdout: '', stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.checkPortAvailability(8080);

      expect(result).toBe(false);
    });
  });

  describe('findFreePort', () => {
    it('should find first available port', async () => {
      let callCount = 0;
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          // First port (3000) is occupied, second (3001) is free
          const stdout = callCount === 0 ? 'LISTEN     0      128          *:3000' : '';
          callback(null, { stdout, stderr: '' });
          callCount++;
        }
        return {} as any;
      });

      const result = await portMonitor.findFreePort(3000, 10);

      expect(result).toBe(3001);
    });

    it('should return null if no free port found', async () => {
      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          // All ports are occupied
          callback(null, { stdout: 'LISTEN     0      128          *:3000', stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.findFreePort(3000, 2);

      expect(result).toBe(null);
    });
  });

  describe('expected ports management', () => {
    it('should return expected ports', () => {
      const expectedPorts = portMonitor.getExpectedPorts();

      expect(expectedPorts).toBeInstanceOf(Array);
      expect(expectedPorts.length).toBeGreaterThan(0);
      expect(expectedPorts[0]).toHaveProperty('port');
      expect(expectedPorts[0]).toHaveProperty('service');
      expect(expectedPorts[0]).toHaveProperty('description');
      expect(expectedPorts[0]).toHaveProperty('protocol');
    });

    it('should add expected port', () => {
      const initialCount = portMonitor.getExpectedPorts().length;
      
      portMonitor.addExpectedPort(9999, 'Test Service', 'Test description', 'tcp');
      
      const newCount = portMonitor.getExpectedPorts().length;
      expect(newCount).toBe(initialCount + 1);
      
      const addedPort = portMonitor.getExpectedPorts().find(p => p.port === 9999);
      expect(addedPort).toBeDefined();
      expect(addedPort?.service).toBe('Test Service');
    });

    it('should remove expected port', () => {
      portMonitor.addExpectedPort(9999, 'Test Service', 'Test description', 'tcp');
      const initialCount = portMonitor.getExpectedPorts().length;
      
      portMonitor.removeExpectedPort(9999);
      
      const newCount = portMonitor.getExpectedPorts().length;
      expect(newCount).toBe(initialCount - 1);
      
      const removedPort = portMonitor.getExpectedPorts().find(p => p.port === 9999);
      expect(removedPort).toBeUndefined();
    });
  });

  describe('port classification', () => {
    it('should classify expected ports correctly', async () => {
      // Mock API Gateway port as listening
      const mockSSOutput = `LISTEN     0      128          *:19081              *:*     users:(("node",pid=5678,fd=4))`;

      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: mockSSOutput, stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.scanPorts();
      const apiGatewayPort = result.ports.find(p => p.port === 19081);

      if (apiGatewayPort) {
        expect(apiGatewayPort.isExpected).toBe(true);
        expect(apiGatewayPort.service).toBe('API Gateway');
        expect(apiGatewayPort.state).toBe('LISTEN');
      }
    });

    it('should identify unexpected ports', async () => {
      // Mock unexpected port
      const mockSSOutput = `LISTEN     0      128          *:9999              *:*     users:(("unknown",pid=1234,fd=4))`;

      mockExec.mockImplementation((cmd, callback) => {
        if (typeof callback === 'function') {
          callback(null, { stdout: mockSSOutput, stderr: '' });
        }
        return {} as any;
      });

      const result = await portMonitor.scanPorts();
      const unexpectedPort = result.ports.find(p => p.port === 9999);

      if (unexpectedPort) {
        expect(unexpectedPort.isExpected).toBe(false);
        expect(unexpectedPort.service).toBe('Unknown');
      }
    });
  });
});
