import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ServiceController } from './service-controller.js';
import type { RestartRequest } from './types.js';

// Mock child_process
vi.mock('child_process', () => ({
  exec: vi.fn()
}));

// Mock util
vi.mock('util', () => ({
  promisify: vi.fn((fn) => vi.fn())
}));

// Mock fetch globally
global.fetch = vi.fn();

describe('ServiceController', () => {
  let serviceController: ServiceController;
  let mockExecAsync: any;
  
  beforeEach(() => {
    serviceController = new ServiceController();
    vi.clearAllMocks();
    
    // Setup mock for execAsync
    const { promisify } = require('util');
    mockExecAsync = promisify();
  });

  describe('restartService', () => {
    it('should successfully restart a service', async () => {
      const request: RestartRequest = {
        service: 'API Gateway'
      };
      
      // Mock successful command execution
      mockExecAsync.mockResolvedValue({
        stdout: 'Service restarted successfully',
        stderr: ''
      });
      
      // Mock successful health check
      (global.fetch as any).mockResolvedValue({
        ok: true,
        status: 200
      });
      
      const result = await serviceController.restartService(request);
      
      expect(result.success).toBe(true);
      expect(result.service).toBe('API Gateway');
      expect(result.message).toContain('restarted successfully');
    });

    it('should handle restart command failure', async () => {
      const request: RestartRequest = {
        service: 'API Gateway'
      };
      
      // Mock command execution failure
      mockExecAsync.mockRejectedValue(new Error('Command failed'));
      
      const result = await serviceController.restartService(request);
      
      expect(result.success).toBe(false);
      expect(result.service).toBe('API Gateway');
      expect(result.message).toContain('Failed to restart');
    });

    it('should handle unknown service', async () => {
      const request: RestartRequest = {
        service: 'Unknown Service'
      };
      
      const result = await serviceController.restartService(request);
      
      expect(result.success).toBe(false);
      expect(result.message).toContain('not found');
    });

    it('should prevent concurrent restarts', async () => {
      const request: RestartRequest = {
        service: 'API Gateway'
      };
      
      // Mock slow command execution
      mockExecAsync.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          stdout: 'Service restarted',
          stderr: ''
        }), 1000))
      );
      
      // Start first restart
      const firstRestart = serviceController.restartService(request);
      
      // Try second restart immediately
      const secondRestart = serviceController.restartService(request);
      
      const [firstResult, secondResult] = await Promise.all([firstRestart, secondRestart]);
      
      // One should succeed, one should be blocked
      const results = [firstResult, secondResult];
      const successCount = results.filter(r => r.success).length;
      const blockedCount = results.filter(r => r.message.includes('already in progress')).length;
      
      expect(successCount + blockedCount).toBe(2);
    });

    it('should force restart when requested', async () => {
      const request: RestartRequest = {
        service: 'API Gateway',
        force: true
      };
      
      mockExecAsync.mockResolvedValue({
        stdout: 'Service restarted',
        stderr: ''
      });
      
      (global.fetch as any).mockResolvedValue({
        ok: true,
        status: 200
      });
      
      // Start first restart
      const firstRestart = serviceController.restartService({ service: 'API Gateway' });
      
      // Force second restart
      const secondRestart = serviceController.restartService(request);
      
      const [firstResult, secondResult] = await Promise.all([firstRestart, secondRestart]);
      
      // Both should attempt to restart (though one might fail due to timing)
      expect(secondResult.message).not.toContain('already in progress');
    });
  });

  describe('startService', () => {
    it('should successfully start a service', async () => {
      mockExecAsync.mockResolvedValue({
        stdout: 'Service started successfully',
        stderr: ''
      });
      
      (global.fetch as any).mockResolvedValue({
        ok: true,
        status: 200
      });
      
      const result = await serviceController.startService('API Gateway');
      
      expect(result.success).toBe(true);
      expect(result.service).toBe('API Gateway');
      expect(result.message).toContain('started successfully');
    });

    it('should handle start command failure', async () => {
      mockExecAsync.mockRejectedValue(new Error('Start command failed'));
      
      const result = await serviceController.startService('API Gateway');
      
      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to start');
    });
  });

  describe('stopService', () => {
    it('should successfully stop a service', async () => {
      mockExecAsync.mockResolvedValue({
        stdout: 'Service stopped successfully',
        stderr: ''
      });
      
      const result = await serviceController.stopService('API Gateway');
      
      expect(result.success).toBe(true);
      expect(result.service).toBe('API Gateway');
      expect(result.message).toContain('stopped successfully');
    });

    it('should handle stop command failure', async () => {
      mockExecAsync.mockRejectedValue(new Error('Stop command failed'));
      
      const result = await serviceController.stopService('API Gateway');
      
      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to stop');
    });
  });

  describe('getRestartStatus', () => {
    it('should return empty array when no restarts in progress', () => {
      const status = serviceController.getRestartStatus();
      expect(Array.isArray(status)).toBe(true);
      expect(status.length).toBe(0);
    });

    it('should track restart status', async () => {
      // Mock slow restart
      mockExecAsync.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          stdout: 'Service restarted',
          stderr: ''
        }), 500))
      );
      
      // Start restart
      const restartPromise = serviceController.restartService({ service: 'API Gateway' });
      
      // Check status immediately
      const status = serviceController.getRestartStatus();
      expect(status).toContain('API Gateway');
      
      // Wait for restart to complete
      await restartPromise;
      
      // Check status after completion
      const finalStatus = serviceController.getRestartStatus();
      expect(finalStatus).not.toContain('API Gateway');
    });
  });
});
