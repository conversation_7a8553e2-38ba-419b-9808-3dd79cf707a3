#!/usr/bin/env python3
"""
Stellar & Planetary Database ETL Pipeline
Enterprise-grade data ingestion for 20 light-year stellar neighborhood

Usage:
  python run_pipeline.py --steps all
  python run_pipeline.py --steps gaia,nasa,join,proc
  python run_pipeline.py --steps schema --force

This pipeline:
  1) Ensures database schema exists
  2) Downloads Gaia DR3 star data (within 20 ly)
  3) Downloads NASA exoplanet data (within 20 ly)
  4) Cross-matches planets to host stars
  5) Generates procedural planets for empty systems
  6) Computes habitability and resource metrics
  7) Creates spatial indexes for fast queries
"""

import argparse
import logging
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Optional

import yaml
from dotenv import load_dotenv

# Setup paths
ROOT = Path(__file__).resolve().parent
PROJECT_ROOT = ROOT.parent
SQL_SCHEMA = PROJECT_ROOT / "db/sql/008_stellar_planetary_database.sql"

# Available pipeline steps
STEPS = ["schema", "gaia", "nasa", "simbad", "join", "proc", "index", "validate"]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(ROOT / 'stellar_etl.log')
    ]
)
logger = logging.getLogger(__name__)


class ETLError(Exception):
    """Custom exception for ETL pipeline errors"""
    pass


def run_command(cmd: List[str], check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command with proper logging and error handling"""
    logger.info(f"Executing: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=check,
            timeout=300  # 5 minute timeout
        )
        if result.stdout:
            logger.debug(f"STDOUT: {result.stdout}")
        if result.stderr and result.returncode == 0:
            logger.debug(f"STDERR: {result.stderr}")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with return code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        raise ETLError(f"Command failed: {' '.join(cmd)}")
    except subprocess.TimeoutExpired:
        logger.error(f"Command timed out: {' '.join(cmd)}")
        raise ETLError(f"Command timed out: {' '.join(cmd)}")


def ensure_schema(force: bool = False) -> None:
    """Create or update database schema"""
    logger.info("Ensuring database schema exists...")
    
    if not SQL_SCHEMA.exists():
        raise ETLError(f"Schema file not found: {SQL_SCHEMA}")
    
    # Build psql command
    psql_cmd = [
        os.environ.get("PSQL", "psql"),
        "-h", os.environ["PGHOST"],
        "-p", os.environ.get("PGPORT", "5432"),
        "-U", os.environ["PGUSER"],
        "-d", os.environ["PGDATABASE"],
        "-v", "ON_ERROR_STOP=1",
        "-f", str(SQL_SCHEMA)
    ]
    
    if force:
        logger.warning("Force flag set - this may drop existing data!")
    
    try:
        run_command(psql_cmd)
        logger.info("Database schema created/updated successfully")
    except ETLError:
        logger.error("Failed to create database schema")
        raise


def validate_environment() -> None:
    """Validate required environment variables and dependencies"""
    required_vars = ["PGHOST", "PGPORT", "PGDATABASE", "PGUSER", "PGPASSWORD"]
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    
    if missing_vars:
        raise ETLError(f"Missing required environment variables: {missing_vars}")
    
    # Test database connection
    test_cmd = [
        os.environ.get("PSQL", "psql"),
        "-h", os.environ["PGHOST"],
        "-p", os.environ.get("PGPORT", "5432"),
        "-U", os.environ["PGUSER"],
        "-d", os.environ["PGDATABASE"],
        "-c", "SELECT version();"
    ]
    
    try:
        result = run_command(test_cmd)
        logger.info("Database connection successful")
        logger.debug(f"Database version: {result.stdout.strip()}")
    except ETLError:
        logger.error("Failed to connect to database")
        raise


def load_config() -> dict:
    """Load and validate configuration"""
    config_path = ROOT / "config.yaml"
    if not config_path.exists():
        raise ETLError(f"Configuration file not found: {config_path}")
    
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    
    logger.info("Configuration loaded successfully")
    return config


def run_step(step: str, config: dict) -> None:
    """Execute a single pipeline step"""
    logger.info(f"Starting step: {step}")
    start_time = time.time()
    
    try:
        if step == "schema":
            ensure_schema()
        elif step == "gaia":
            import gaia_import
            gaia_import.run(config)
        elif step == "nasa":
            import nasa_exoplanet_import
            nasa_exoplanet_import.run(config)
        elif step == "simbad":
            import simbad_import
            simbad_import.run(config)
        elif step == "join":
            import join_enrich
            join_enrich.run(config)
        elif step == "proc":
            import procedural_generate
            procedural_generate.run(config)
        elif step == "index":
            import create_indexes
            create_indexes.run(config)
        elif step == "validate":
            import validate_data
            validate_data.run(config)
        else:
            raise ETLError(f"Unknown step: {step}")
        
        elapsed = time.time() - start_time
        logger.info(f"Step '{step}' completed successfully in {elapsed:.2f} seconds")
        
    except Exception as e:
        elapsed = time.time() - start_time
        logger.error(f"Step '{step}' failed after {elapsed:.2f} seconds: {e}")
        raise ETLError(f"Step '{step}' failed: {e}")


def main() -> None:
    """Main pipeline orchestrator"""
    parser = argparse.ArgumentParser(
        description="Stellar & Planetary Database ETL Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_pipeline.py --steps all
  python run_pipeline.py --steps gaia,nasa,join
  python run_pipeline.py --steps schema --force
  python run_pipeline.py --steps validate --verbose
        """
    )
    
    parser.add_argument(
        "--steps",
        default="all",
        help="Comma-separated list of steps to run, or 'all' for complete pipeline"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force schema recreation (WARNING: may drop existing data)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without executing"
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Verbose logging enabled")
    
    # Load environment
    env_file = ROOT / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        logger.info(f"Loaded environment from {env_file}")
    else:
        logger.warning(f"No .env file found at {env_file}")
    
    try:
        # Validate environment
        validate_environment()
        
        # Load configuration
        config = load_config()
        
        # Determine steps to run
        if args.steps == "all":
            steps_to_run = STEPS
        else:
            steps_to_run = [step.strip() for step in args.steps.split(",")]
            invalid_steps = [step for step in steps_to_run if step not in STEPS]
            if invalid_steps:
                raise ETLError(f"Invalid steps: {invalid_steps}. Valid steps: {STEPS}")
        
        logger.info(f"Pipeline will execute steps: {steps_to_run}")
        
        if args.dry_run:
            logger.info("DRY RUN - No actual execution")
            return
        
        # Execute pipeline
        total_start = time.time()
        
        for step in steps_to_run:
            if step == "schema" and args.force:
                ensure_schema(force=True)
            else:
                run_step(step, config)
        
        total_elapsed = time.time() - total_start
        logger.info(f"Pipeline completed successfully in {total_elapsed:.2f} seconds")
        
        # Print summary
        print("\n" + "="*60)
        print("STELLAR DATABASE ETL PIPELINE COMPLETED")
        print("="*60)
        print(f"Steps executed: {', '.join(steps_to_run)}")
        print(f"Total time: {total_elapsed:.2f} seconds")
        print(f"Database: {os.environ['PGDATABASE']} on {os.environ['PGHOST']}")
        print("="*60)
        
    except ETLError as e:
        logger.error(f"Pipeline failed: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.warning("Pipeline interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
