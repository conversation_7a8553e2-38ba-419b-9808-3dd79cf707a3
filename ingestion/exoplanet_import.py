#!/usr/bin/env python3
"""
Comprehensive Exoplanet Data Import
Imports confirmed exoplanets within 20 light-years
"""

import os
import sys
import time
import math

def execute_sql(sql):
    """Execute SQL using docker exec"""
    sql_escaped = sql.replace("'", "''")
    cmd = f'docker exec gg_postgres psql -U gg -d gg -c "{sql_escaped}"'
    result = os.system(cmd)
    return result == 0

def get_star_id_by_name(star_name):
    """Get star_id for a given star name"""
    # This is a simplified lookup - in production, use proper database queries
    star_mapping = {
        "Proxima Centauri": "proxima_centauri",
        "Alpha Centauri A": "alpha_centauri_a", 
        "Barnard's Star": "barnards_star",
        "Wolf 359": "wolf_359",
        "Lalande 21185": "lalande_21185",
        "Epsilon Eridani": "epsilon_eridani",
        "Ross 128": "ross_128",
        "Tau Ceti": "tau_ceti",
        "Epsilon Indi A": "epsilon_indi_a",
        "YZ Ceti": "yz_ceti",
        "Luyten's Star": "luytens_star",
        "Teegarden's Star": "teegardens_star",
        "Kapteyn's Star": "kapteyns_star",
        "Wolf 1061": "wolf_1061",
        "70 Ophiuchi A": "70_ophiuchi_a"
    }
    return star_mapping.get(star_name)

def get_comprehensive_exoplanet_data():
    """Get comprehensive list of confirmed exoplanets within 20 light-years"""
    return [
        # Proxima Centauri system
        {
            "name": "Proxima Centauri b",
            "host_star": "Proxima Centauri",
            "mass_earth": 1.17,
            "radius_earth": 1.03,
            "semi_major_axis_au": 0.0485,
            "period_days": 11.186,
            "eccentricity": 0.02,
            "eq_temp_k": 234,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2016,
            "habitable": True
        },
        {
            "name": "Proxima Centauri c",
            "host_star": "Proxima Centauri", 
            "mass_earth": 7.0,
            "radius_earth": 1.5,
            "semi_major_axis_au": 1.489,
            "period_days": 1928,
            "eccentricity": 0.04,
            "eq_temp_k": 87,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": False
        },
        {
            "name": "Proxima Centauri d",
            "host_star": "Proxima Centauri",
            "mass_earth": 0.26,
            "radius_earth": 0.81,
            "semi_major_axis_au": 0.029,
            "period_days": 5.15,
            "eccentricity": 0.0,
            "eq_temp_k": 340,
            "composition": "rocky",
            "discovery_method": "Radial Velocity", 
            "discovery_year": 2022,
            "habitable": False
        },
        
        # Alpha Centauri system
        {
            "name": "Alpha Centauri Bb",
            "host_star": "Alpha Centauri A",
            "mass_earth": 1.13,
            "radius_earth": 1.1,
            "semi_major_axis_au": 0.04,
            "period_days": 3.236,
            "eccentricity": 0.0,
            "eq_temp_k": 1200,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2012,
            "habitable": False
        },
        
        # Barnard's Star system
        {
            "name": "Barnard's Star b",
            "host_star": "Barnard's Star",
            "mass_earth": 3.2,
            "radius_earth": 1.4,
            "semi_major_axis_au": 0.404,
            "period_days": 233,
            "eccentricity": 0.32,
            "eq_temp_k": 105,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2018,
            "habitable": False
        },
        
        # Wolf 359 system
        {
            "name": "Wolf 359 b",
            "host_star": "Wolf 359",
            "mass_earth": 1.9,
            "radius_earth": 1.2,
            "semi_major_axis_au": 0.018,
            "period_days": 2.69,
            "eccentricity": 0.0,
            "eq_temp_k": 650,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": False
        },
        
        # Lalande 21185 system
        {
            "name": "Lalande 21185 b",
            "host_star": "Lalande 21185",
            "mass_earth": 2.89,
            "radius_earth": 1.35,
            "semi_major_axis_au": 0.393,
            "period_days": 125.6,
            "eccentricity": 0.08,
            "eq_temp_k": 255,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": True
        },
        {
            "name": "Lalande 21185 c",
            "host_star": "Lalande 21185",
            "mass_earth": 1.15,
            "radius_earth": 1.05,
            "semi_major_axis_au": 0.635,
            "period_days": 237.6,
            "eccentricity": 0.12,
            "eq_temp_k": 198,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": True
        },
        
        # Epsilon Eridani system
        {
            "name": "Epsilon Eridani b",
            "host_star": "Epsilon Eridani",
            "mass_earth": 305,
            "radius_earth": 7.2,
            "semi_major_axis_au": 3.39,
            "period_days": 2502,
            "eccentricity": 0.7,
            "eq_temp_k": 120,
            "composition": "gas_giant",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2000,
            "habitable": False
        },
        
        # Ross 128 system
        {
            "name": "Ross 128 b",
            "host_star": "Ross 128",
            "mass_earth": 1.35,
            "radius_earth": 1.1,
            "semi_major_axis_au": 0.0496,
            "period_days": 9.865,
            "eccentricity": 0.0,
            "eq_temp_k": 269,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": True
        },
        
        # Tau Ceti system
        {
            "name": "Tau Ceti e",
            "host_star": "Tau Ceti",
            "mass_earth": 3.93,
            "radius_earth": 1.51,
            "semi_major_axis_au": 0.538,
            "period_days": 162.87,
            "eccentricity": 0.05,
            "eq_temp_k": 240,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2012,
            "habitable": True
        },
        {
            "name": "Tau Ceti f",
            "host_star": "Tau Ceti",
            "mass_earth": 3.93,
            "radius_earth": 1.51,
            "semi_major_axis_au": 1.35,
            "period_days": 642,
            "eccentricity": 0.03,
            "eq_temp_k": 158,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2012,
            "habitable": True
        },
        {
            "name": "Tau Ceti g",
            "host_star": "Tau Ceti",
            "mass_earth": 1.75,
            "radius_earth": 1.15,
            "semi_major_axis_au": 0.133,
            "period_days": 20.0,
            "eccentricity": 0.06,
            "eq_temp_k": 422,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": False
        },
        {
            "name": "Tau Ceti h",
            "host_star": "Tau Ceti",
            "mass_earth": 1.83,
            "radius_earth": 1.17,
            "semi_major_axis_au": 0.243,
            "period_days": 49.4,
            "eccentricity": 0.23,
            "eq_temp_k": 334,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": False
        },
        
        # Epsilon Indi system
        {
            "name": "Epsilon Indi Ab",
            "host_star": "Epsilon Indi A",
            "mass_earth": 965,
            "radius_earth": 8.5,
            "semi_major_axis_au": 11.55,
            "period_days": 13000,
            "eccentricity": 0.26,
            "eq_temp_k": 76,
            "composition": "gas_giant",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": False
        },
        
        # YZ Ceti system
        {
            "name": "YZ Ceti b",
            "host_star": "YZ Ceti",
            "mass_earth": 0.75,
            "radius_earth": 0.91,
            "semi_major_axis_au": 0.01557,
            "period_days": 1.97,
            "eccentricity": 0.0,
            "eq_temp_k": 620,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": False
        },
        {
            "name": "YZ Ceti c",
            "host_star": "YZ Ceti",
            "mass_earth": 1.14,
            "radius_earth": 1.05,
            "semi_major_axis_au": 0.02067,
            "period_days": 3.06,
            "eccentricity": 0.0,
            "eq_temp_k": 520,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": False
        },
        {
            "name": "YZ Ceti d",
            "host_star": "YZ Ceti",
            "mass_earth": 1.09,
            "radius_earth": 1.03,
            "semi_major_axis_au": 0.02845,
            "period_days": 4.66,
            "eccentricity": 0.0,
            "eq_temp_k": 450,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": False
        },
        
        # Luyten's Star system
        {
            "name": "Luyten's Star b",
            "host_star": "Luyten's Star",
            "mass_earth": 2.89,
            "radius_earth": 1.35,
            "semi_major_axis_au": 0.091,
            "period_days": 18.65,
            "eccentricity": 0.10,
            "eq_temp_k": 273,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2017,
            "habitable": True
        },
        
        # Teegarden's Star system
        {
            "name": "Teegarden's Star b",
            "host_star": "Teegarden's Star",
            "mass_earth": 1.05,
            "radius_earth": 1.02,
            "semi_major_axis_au": 0.0252,
            "period_days": 4.91,
            "eccentricity": 0.0,
            "eq_temp_k": 279,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": True
        },
        {
            "name": "Teegarden's Star c",
            "host_star": "Teegarden's Star",
            "mass_earth": 1.11,
            "radius_earth": 1.04,
            "semi_major_axis_au": 0.0443,
            "period_days": 11.4,
            "eccentricity": 0.0,
            "eq_temp_k": 216,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2019,
            "habitable": True
        },
        
        # Kapteyn's Star system
        {
            "name": "Kapteyn b",
            "host_star": "Kapteyn's Star",
            "mass_earth": 4.8,
            "radius_earth": 1.6,
            "semi_major_axis_au": 0.168,
            "period_days": 48.6,
            "eccentricity": 0.21,
            "eq_temp_k": 266,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2014,
            "habitable": True
        },
        {
            "name": "Kapteyn c",
            "host_star": "Kapteyn's Star",
            "mass_earth": 7.0,
            "radius_earth": 1.8,
            "semi_major_axis_au": 0.311,
            "period_days": 121.5,
            "eccentricity": 0.23,
            "eq_temp_k": 188,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2014,
            "habitable": False
        },
        
        # Wolf 1061 system
        {
            "name": "Wolf 1061 b",
            "host_star": "Wolf 1061",
            "mass_earth": 1.36,
            "radius_earth": 1.1,
            "semi_major_axis_au": 0.0375,
            "period_days": 4.89,
            "eccentricity": 0.0,
            "eq_temp_k": 525,
            "composition": "rocky",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2015,
            "habitable": False
        },
        {
            "name": "Wolf 1061 c",
            "host_star": "Wolf 1061",
            "mass_earth": 4.25,
            "radius_earth": 1.6,
            "semi_major_axis_au": 0.084,
            "period_days": 17.9,
            "eccentricity": 0.11,
            "eq_temp_k": 323,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2015,
            "habitable": True
        },
        {
            "name": "Wolf 1061 d",
            "host_star": "Wolf 1061",
            "mass_earth": 5.21,
            "radius_earth": 1.7,
            "semi_major_axis_au": 0.204,
            "period_days": 67.2,
            "eccentricity": 0.55,
            "eq_temp_k": 223,
            "composition": "super_earth",
            "discovery_method": "Radial Velocity",
            "discovery_year": 2015,
            "habitable": False
        }
    ]

def insert_exoplanets():
    """Insert exoplanet data into database"""
    planets = get_comprehensive_exoplanet_data()
    
    print(f"🪐 Inserting {len(planets)} confirmed exoplanets...")
    
    inserted_count = 0
    
    for planet in planets:
        try:
            # Build SQL
            sql = f"""
                INSERT INTO planets (
                    src, src_key, name,
                    star_id, mass_earth, radius_earth,
                    sma_au, period_days, eccentricity,
                    eq_temp_k, composition, discovery_method, discovery_year,
                    in_habitable_zone
                ) VALUES (
                    'comprehensive', '{planet['name'].replace("'", "''")}', '{planet['name'].replace("'", "''")}',
                    (SELECT star_id FROM stars WHERE name = '{planet['host_star'].replace("'", "''")}' LIMIT 1),
                    {planet['mass_earth']}, {planet['radius_earth']},
                    {planet['semi_major_axis_au']}, {planet['period_days']}, {planet['eccentricity']},
                    {planet['eq_temp_k']}, '{planet['composition']}', '{planet['discovery_method']}', {planet['discovery_year']},
                    {str(planet['habitable']).lower()}
                );
            """
            
            cmd = f'docker exec gg_postgres psql -U gg -d gg -c "{sql}"'
            result = os.system(cmd)
            
            if result == 0:
                inserted_count += 1
                if inserted_count % 5 == 0:
                    print(f"  ✅ Processed {inserted_count}/{len(planets)} planets...")
            else:
                print(f"  ⚠️  Error inserting {planet['name']}")
                
        except Exception as e:
            print(f"  ⚠️  Error processing {planet.get('name', 'unknown')}: {e}")
            continue
    
    print(f"✅ Successfully inserted {inserted_count} exoplanets")
    return inserted_count

def main():
    """Main exoplanet import process"""
    print("🚀 Starting comprehensive exoplanet data import...")
    print("=" * 70)
    
    start_time = time.time()
    
    # Insert exoplanet data
    planet_count = insert_exoplanets()
    
    elapsed = time.time() - start_time
    print("=" * 70)
    print(f"🎉 Exoplanet import completed in {elapsed:.2f} seconds")
    print(f"🪐 Added {planet_count} confirmed exoplanets to database!")
    
    # Print final statistics
    print("\n📊 EXOPLANET DATABASE SUMMARY:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(*) as total_planets, src FROM planets GROUP BY src ORDER BY src;"')
    
    print("\n🌍 PLANET COMPOSITION BREAKDOWN:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT composition, COUNT(*) as count FROM planets GROUP BY composition ORDER BY count DESC;"')
    
    print("\n🌡️ HABITABLE PLANETS:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(*) as habitable_planets FROM planets WHERE in_habitable_zone = true;"')
    
    print("\n🔭 DISCOVERY METHODS:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT discovery_method, COUNT(*) as count FROM planets GROUP BY discovery_method ORDER BY count DESC;"')

if __name__ == "__main__":
    main()
