# Stellar & Planetary Database ETL Configuration
# Optimized for 20 light-year radius around Sol

# NASA Exoplanet Archive configuration
nasa_exoplanet:
  # Use TAP service for more flexible queries
  tap_url: "https://exoplanetarchive.ipac.caltech.edu/TAP/sync"
  # Query for planets within 20 light-years
  adql_query: |
    SELECT 
      pl_name, hostname, ra, dec, sy_dist,
      pl_rade, pl_bmasse, pl_orbsmax, pl_orbper, pl_orbeccen, pl_orbincl,
      pl_eqt, pl_dens, discoverymethod, disc_year, disc_facility,
      st_spectype, st_teff, st_rad, st_mass, st_lum, st_age
    FROM pscomppars 
    WHERE sy_dist <= 20.0 
    AND sy_dist IS NOT NULL
    ORDER BY sy_dist

# Gaia DR3 configuration  
gaia:
  tap_url: "https://gea.esac.esa.int/tap-server/tap/sync"
  # Query for stars within 20 light-years (parallax > 50 mas)
  adql_query: |
    SELECT TOP 10000
      source_id, ra, dec, parallax, parallax_error,
      pmra, pmdec, pmra_error, pmdec_error,
      phot_g_mean_mag, phot_bp_mean_mag, phot_rp_mean_mag,
      bp_rp, teff_gspphot, radius_gspphot, mass_gspphot,
      lum_gspphot, ag_gspphot, ebpminrp_gspphot
    FROM gaiadr3.gaia_source 
    WHERE parallax > 50.0 
    AND parallax_over_error > 5.0
    AND phot_g_mean_mag IS NOT NULL
    ORDER BY parallax DESC

# SIMBAD for stellar names and classifications
simbad:
  enabled: true
  tap_url: "http://simbad.cds.unistra.fr/simbad/sim-tap/sync"
  # Query for proper names of nearby stars
  adql_query: |
    SELECT 
      main_id, ra, dec, plx_value, sp_type, sp_qual,
      flux_v, flux_b, flux_g
    FROM basic 
    WHERE plx_value > 50.0
    AND otype = 'Star'

# CELESTA habitable zone data (optional)
celesta:
  enabled: false
  csv_path: "./data/CELESTA.csv"

# Data processing configuration
processing:
  # Distance calculation
  distance_limit_ly: 20.0
  parallax_min_mas: 50.0  # Corresponds to 20 ly
  parallax_error_threshold: 5.0  # Signal-to-noise ratio
  
  # Star classification
  spectral_types: ["O", "B", "A", "F", "G", "K", "M"]
  luminosity_classes: ["I", "II", "III", "IV", "V"]
  
  # Planet classification
  planet_types:
    rocky:
      radius_earth_min: 0.0
      radius_earth_max: 1.8
      mass_earth_max: 10.0
    super_earth:
      radius_earth_min: 1.8
      radius_earth_max: 2.5
      mass_earth_max: 20.0
    sub_neptune:
      radius_earth_min: 2.5
      radius_earth_max: 4.0
      mass_earth_max: 50.0
    gas_giant:
      radius_earth_min: 4.0
      radius_earth_max: 20.0
      mass_earth_min: 50.0

# Cross-matching configuration
crossmatch:
  # Position matching threshold (arcseconds)
  position_threshold_arcsec: 2.0
  # Name matching patterns
  name_patterns:
    - "HD {number}"
    - "HIP {number}"
    - "Gliese {number}"
    - "Wolf {number}"
    - "LHS {number}"
    - "Ross {number}"

# Procedural generation rules
procedural:
  enabled: true
  max_planets_per_star: 4
  
  # Planet generation probabilities by spectral type
  generation_rules:
    M:  # Red dwarfs
      rocky: 0.7
      super_earth: 0.2
      sub_neptune: 0.1
      gas_giant: 0.0
      avg_planets: 2.5
    K:  # Orange dwarfs
      rocky: 0.6
      super_earth: 0.25
      sub_neptune: 0.1
      gas_giant: 0.05
      avg_planets: 2.8
    G:  # Sun-like stars
      rocky: 0.4
      super_earth: 0.3
      sub_neptune: 0.2
      gas_giant: 0.1
      avg_planets: 3.2
    F:  # Hot stars
      rocky: 0.3
      super_earth: 0.3
      sub_neptune: 0.25
      gas_giant: 0.15
      avg_planets: 2.5
    A:  # Very hot stars
      rocky: 0.2
      super_earth: 0.2
      sub_neptune: 0.3
      gas_giant: 0.3
      avg_planets: 2.0
  
  # Resource generation
  resources:
    mineral_richness:
      base: 0.5
      variation: 0.3
      spectral_modifiers:
        M: 1.2  # Metal-rich red dwarfs
        K: 1.1
        G: 1.0
        F: 0.9
        A: 0.8
    
    energy_potential:
      base: 0.5
      variation: 0.4
      distance_modifier: 0.1  # Closer to star = more energy

# Quality control
quality:
  # Minimum data quality thresholds
  min_parallax_snr: 5.0
  min_photometry_quality: 0.8
  max_distance_error_percent: 20.0
  
  # Data validation rules
  validation:
    stellar_mass_range: [0.08, 100.0]  # Solar masses
    stellar_radius_range: [0.1, 50.0]  # Solar radii
    stellar_temp_range: [2000, 50000]  # Kelvin
    planet_mass_range: [0.001, 5000.0]  # Earth masses
    planet_radius_range: [0.1, 30.0]   # Earth radii

# Performance optimization
performance:
  batch_size: 1000
  max_workers: 4
  chunk_size: 100
  cache_size_mb: 512
  
  # Database optimization
  use_bulk_insert: true
  vacuum_after_load: true
  analyze_after_load: true

# Known stellar systems (for validation and seeding)
known_systems:
  sol:
    name: "Sol"
    ra_deg: 0.0  # Reference point
    dec_deg: 0.0
    distance_ly: 0.0
    spectral_type: "G2V"
    planets: 8
  
  alpha_centauri_a:
    name: "Alpha Centauri A"
    ra_deg: 219.9020625
    dec_deg: -60.8339583
    distance_ly: 4.37
    spectral_type: "G2V"
  
  alpha_centauri_b:
    name: "Alpha Centauri B"
    ra_deg: 219.9020625
    dec_deg: -60.8339583
    distance_ly: 4.37
    spectral_type: "K1V"
  
  proxima_centauri:
    name: "Proxima Centauri"
    ra_deg: 217.4289167
    dec_deg: -62.6795556
    distance_ly: 4.24
    spectral_type: "M5.5Ve"
    planets: 3
  
  barnards_star:
    name: "Barnard's Star"
    ra_deg: 269.4520833
    dec_deg: 4.6933056
    distance_ly: 5.96
    spectral_type: "M4.0V"
  
  wolf_359:
    name: "Wolf 359"
    ra_deg: 164.1205833
    dec_deg: 7.0069444
    distance_ly: 7.86
    spectral_type: "M6.0V"
  
  sirius_a:
    name: "Sirius A"
    ra_deg: 101.2871250
    dec_deg: -16.7161111
    distance_ly: 8.66
    spectral_type: "A1V"
  
  sirius_b:
    name: "Sirius B"
    ra_deg: 101.2871250
    dec_deg: -16.7161111
    distance_ly: 8.66
    spectral_type: "DA2"  # White dwarf

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "stellar_etl.log"
  max_size_mb: 100
  backup_count: 5
