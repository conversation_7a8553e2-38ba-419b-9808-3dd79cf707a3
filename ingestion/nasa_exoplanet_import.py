#!/usr/bin/env python3
"""
NASA Exoplanet Archive Import
Downloads real confirmed exoplanet data from NASA Exoplanet Archive
"""

import os
import sys
import requests
import csv
import json
import time

def execute_sql(sql):
    """Execute SQL using docker exec"""
    # Write SQL to a temporary file to avoid shell escaping issues
    with open('/tmp/temp_sql.sql', 'w') as f:
        f.write(sql)

    cmd = 'docker exec -i gg_postgres psql -U gg -d gg < /tmp/temp_sql.sql'
    result = os.system(cmd)
    return result == 0

def safe_sql_string(value):
    """Safely escape a string for SQL insertion"""
    if value is None:
        return 'NULL'
    return "'" + str(value).replace("'", "''") + "'"

def download_nasa_exoplanet_data():
    """Download confirmed exoplanet data from NASA Exoplanet Archive"""
    print("🌌 Downloading confirmed exoplanet data from NASA Exoplanet Archive...")
    
    # NASA Exoplanet Archive API endpoint for confirmed planets
    url = "https://exoplanetarchive.ipac.caltech.edu/TAP/sync"
    
    # ADQL query for confirmed planets with host star distance <= 20 parsecs (65.2 ly)
    # We use a bit more than 20 ly to ensure we get all nearby systems
    query = """
    SELECT DISTINCT
        pl_name,
        hostname,
        sy_dist,
        pl_masse,
        pl_rade,
        pl_orbsmax,
        pl_orbper,
        pl_orbeccen,
        pl_eqt,
        pl_bmasse,
        pl_bmassprov,
        discoverymethod,
        disc_year,
        pl_controv_flag,
        pl_refname
    FROM ps 
    WHERE sy_dist <= 6.13 
    AND pl_controv_flag = 0
    AND default_flag = 1
    ORDER BY sy_dist, hostname, pl_name
    """
    
    params = {
        'REQUEST': 'doQuery',
        'LANG': 'ADQL',
        'FORMAT': 'csv',
        'QUERY': query
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        # Save to temporary file
        with open('/tmp/nasa_exoplanets.csv', 'w') as f:
            f.write(response.text)

        # Read with csv module
        data = []
        with open('/tmp/nasa_exoplanets.csv', 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)

        print(f"✅ Downloaded {len(data)} confirmed exoplanets from NASA")
        return data
        
    except Exception as e:
        print(f"❌ Error downloading NASA data: {e}")
        return None

def process_nasa_data(data):
    """Process NASA exoplanet data and prepare for database insertion"""
    print("🔄 Processing NASA exoplanet data...")

    processed_planets = []

    for row in data:
        try:
            # Helper function to check if value is valid
            def is_valid(val):
                return val and val.strip() and val.strip().lower() not in ['', 'null', 'nan', 'none']

            # Convert distance from parsecs to light-years
            distance_ly = float(row['sy_dist']) * 3.26156 if is_valid(row.get('sy_dist')) else None

            # Skip if too far (> 20 ly)
            if distance_ly and distance_ly > 20.0:
                continue

            # Process planet data
            planet = {
                'name': str(row['pl_name']).strip(),
                'host_star': str(row['hostname']).strip(),
                'distance_ly': distance_ly,
                'mass_earth': float(row['pl_masse']) if is_valid(row.get('pl_masse')) else None,
                'radius_earth': float(row['pl_rade']) if is_valid(row.get('pl_rade')) else None,
                'semi_major_axis_au': float(row['pl_orbsmax']) if is_valid(row.get('pl_orbsmax')) else None,
                'period_days': float(row['pl_orbper']) if is_valid(row.get('pl_orbper')) else None,
                'eccentricity': float(row['pl_orbeccen']) if is_valid(row.get('pl_orbeccen')) else 0.0,
                'eq_temp_k': float(row['pl_eqt']) if is_valid(row.get('pl_eqt')) else None,
                'discovery_method': str(row['discoverymethod']).strip() if is_valid(row.get('discoverymethod')) else 'Unknown',
                'discovery_year': int(float(row['disc_year'])) if is_valid(row.get('disc_year')) else None,
                'reference': str(row['pl_refname']).strip() if is_valid(row.get('pl_refname')) else None
            }
            
            # Determine composition based on mass/radius
            # Valid values: 'rocky', 'gas_giant', 'ice_giant', 'sub_neptune', 'super_earth', 'unknown'
            composition = 'unknown'
            if planet['mass_earth']:
                if planet['mass_earth'] < 2.0:
                    composition = 'rocky'
                elif planet['mass_earth'] < 10.0:
                    composition = 'super_earth'
                elif planet['mass_earth'] < 50.0:
                    composition = 'sub_neptune'
                else:
                    composition = 'gas_giant'
            elif planet['radius_earth']:
                if planet['radius_earth'] < 1.5:
                    composition = 'rocky'
                elif planet['radius_earth'] < 2.5:
                    composition = 'super_earth'
                elif planet['radius_earth'] < 6.0:
                    composition = 'sub_neptune'
                else:
                    composition = 'gas_giant'
            
            planet['composition'] = composition
            
            # Rough habitability assessment
            habitable = False
            if (planet['eq_temp_k'] and 200 <= planet['eq_temp_k'] <= 350 and 
                planet['mass_earth'] and 0.5 <= planet['mass_earth'] <= 5.0):
                habitable = True
            
            planet['habitable'] = habitable
            
            processed_planets.append(planet)
            
        except Exception as e:
            print(f"⚠️  Error processing planet {row.get('pl_name', 'unknown')}: {e}")
            continue
    
    print(f"✅ Processed {len(processed_planets)} planets within 20 light-years")
    return processed_planets

def find_star_id(star_name):
    """Find star_id for a given star name in the database"""
    # Try exact match first
    sql = f"SELECT star_id FROM stars WHERE name = '{star_name}' LIMIT 1;"
    
    # For now, return a placeholder - in production, implement proper lookup
    # This is a simplified approach for the demo
    return None

def import_nasa_exoplanets():
    """Main function to import NASA exoplanet data"""
    print("🚀 Starting NASA Exoplanet Archive import...")
    
    # Download data
    df = download_nasa_exoplanet_data()
    if df is None:
        print("❌ Failed to download NASA data")
        return False
    
    # Process data
    planets = process_nasa_data(df)
    if not planets:
        print("❌ No planets to import")
        return False
    
    # Clear existing NASA-sourced planets
    print("🧹 Clearing existing NASA exoplanet data...")
    if not execute_sql("DELETE FROM planets WHERE src = 'nasa_archive';"):
        print("⚠️  Warning: Could not clear existing NASA data")
    
    # Import planets
    print("📥 Importing planets to database...")
    success_count = 0
    
    for planet in planets:
        try:
            # For now, we'll insert without linking to stars (star_id = NULL)
            # In production, implement proper star matching
            sql = f"""
            INSERT INTO planets (
                name, host_star_name, mass_earth, radius_earth,
                sma_au, period_days, eccentricity,
                eq_temp_k, composition, discovery_method,
                discovery_year, in_habitable_zone, src, reference
            ) VALUES (
                {safe_sql_string(planet['name'])},
                {safe_sql_string(planet['host_star'])},
                {planet['mass_earth'] if planet['mass_earth'] else 'NULL'},
                {planet['radius_earth'] if planet['radius_earth'] else 'NULL'},
                {planet['semi_major_axis_au'] if planet['semi_major_axis_au'] else 'NULL'},
                {planet['period_days'] if planet['period_days'] else 'NULL'},
                {planet['eccentricity']},
                {planet['eq_temp_k'] if planet['eq_temp_k'] else 'NULL'},
                {safe_sql_string(planet['composition'])},
                {safe_sql_string(planet['discovery_method'])},
                {planet['discovery_year'] if planet['discovery_year'] else 'NULL'},
                {str(planet['habitable']).lower()},
                {safe_sql_string('nasa_archive')},
                {safe_sql_string(planet['reference'][:100] if planet['reference'] else '')}
            );
            """
            
            if execute_sql(sql):
                success_count += 1
            else:
                print(f"⚠️  Failed to insert {planet['name']}")
                
        except Exception as e:
            print(f"❌ Error inserting {planet['name']}: {e}")
            continue
    
    print(f"✅ Successfully imported {success_count}/{len(planets)} planets")
    
    # Show summary
    execute_sql("SELECT COUNT(*) as nasa_planets FROM planets WHERE src = 'nasa_archive';")
    execute_sql("SELECT COUNT(*) as total_planets FROM planets;")
    
    return success_count > 0

if __name__ == "__main__":
    success = import_nasa_exoplanets()
    sys.exit(0 if success else 1)
