#!/usr/bin/env python3
"""
Simplified Stellar Data Import
Downloads and imports comprehensive stellar and planetary data without external dependencies
"""

import json
import urllib.request
import urllib.parse
import csv
import io
import os
import sys
import time
import math

# Use docker exec for database operations (simpler approach)
def execute_sql(sql, params=None):
    """Execute SQL using docker exec"""
    if params:
        # For parameterized queries, we'll use a different approach
        return execute_sql_with_params(sql, params)

    # Escape single quotes in SQL
    sql_escaped = sql.replace("'", "''")

    cmd = f'docker exec gg_postgres psql -U gg -d gg -c "{sql_escaped}"'
    result = os.system(cmd)
    return result == 0

def execute_sql_with_params(sql, params):
    """Execute parameterized SQL by building the query"""
    # This is a simplified approach - in production, use proper parameterization
    try:
        # Convert params to strings and escape them
        escaped_params = []
        for param in params:
            if param is None:
                escaped_params.append('NULL')
            elif isinstance(param, str):
                escaped_params.append(f"'{param.replace('\'', '\'\'')}'")
            else:
                escaped_params.append(str(param))

        # Replace %s with actual values
        formatted_sql = sql
        for param in escaped_params:
            formatted_sql = formatted_sql.replace('%s', param, 1)

        return execute_sql(formatted_sql)
    except Exception as e:
        print(f"Error executing SQL: {e}")
        return False

def fetch_gaia_data():
    """Fetch nearby stars from Gaia DR3 via TAP service"""
    print("🌟 Fetching stellar data from Gaia DR3...")
    
    # ADQL query for stars within 20 light-years (parallax > 50 mas)
    query = """
    SELECT TOP 5000
        source_id,
        ra, dec, parallax, parallax_error,
        pmra, pmdec,
        phot_g_mean_mag as mag_g,
        phot_bp_mean_mag as mag_bp,
        phot_rp_mean_mag as mag_rp,
        teff_gspphot as teff,
        radius_gspphot as radius,
        mass_gspphot as mass,
        logg_gspphot as logg
    FROM gaiadr3.gaia_source
    WHERE parallax > 50.0 
        AND parallax_error < parallax/5.0
        AND phot_g_mean_mag IS NOT NULL
        AND teff_gspphot IS NOT NULL
    ORDER BY parallax DESC
    """
    
    # Prepare TAP request
    tap_url = "https://gea.esac.esa.int/tap-server/tap/sync"
    data = {
        'REQUEST': 'doQuery',
        'LANG': 'ADQL',
        'FORMAT': 'csv',
        'QUERY': query
    }
    
    try:
        # Encode data for POST request
        post_data = urllib.parse.urlencode(data).encode('utf-8')
        
        # Create request with headers
        req = urllib.request.Request(
            tap_url,
            data=post_data,
            headers={
                'User-Agent': 'GalacticGenesis/1.0 (Stellar Database ETL)',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        )
        
        print("📡 Querying Gaia TAP service...")
        with urllib.request.urlopen(req, timeout=300) as response:
            csv_data = response.read().decode('utf-8')
        
        print(f"✅ Received {len(csv_data)} characters from Gaia")
        return csv_data
        
    except Exception as e:
        print(f"❌ Gaia query failed: {e}")
        return None

def parse_csv_data(csv_data):
    """Parse CSV data into list of dictionaries"""
    if not csv_data:
        return []
    
    reader = csv.DictReader(io.StringIO(csv_data))
    rows = []
    
    for row in reader:
        # Skip rows with missing critical data
        if not row.get('parallax') or not row.get('ra') or not row.get('dec'):
            continue
            
        try:
            parallax = float(row['parallax'])
            if parallax <= 0:
                continue
                
            # Calculate distance in light-years
            distance_ly = 3.26156 / parallax  # Convert parallax (mas) to distance (ly)
            
            # Skip if beyond 20 light-years
            if distance_ly > 20.0:
                continue
                
            # Estimate spectral type from temperature
            teff = float(row.get('teff', 0)) if row.get('teff') else None
            spectral_type = estimate_spectral_type(teff) if teff else 'Unknown'
            
            # Calculate luminosity from Gaia photometry
            mag_g = float(row.get('mag_g', 99)) if row.get('mag_g') else 99
            luminosity = calculate_luminosity(mag_g, distance_ly)
            
            processed_row = {
                'source_id': row['source_id'],
                'ra': float(row['ra']),
                'dec': float(row['dec']),
                'distance_ly': distance_ly,
                'parallax': parallax,
                'pmra': float(row.get('pmra', 0)) if row.get('pmra') else None,
                'pmdec': float(row.get('pmdec', 0)) if row.get('pmdec') else None,
                'mag_g': mag_g if mag_g < 90 else None,
                'teff': teff,
                'spectral_type': spectral_type,
                'mass_solar': float(row.get('mass', 1.0)) if row.get('mass') else None,
                'radius_solar': float(row.get('radius', 1.0)) if row.get('radius') else None,
                'luminosity_solar': luminosity
            }
            
            rows.append(processed_row)
            
        except (ValueError, TypeError) as e:
            continue  # Skip malformed rows
    
    return rows

def estimate_spectral_type(teff):
    """Estimate spectral type from effective temperature"""
    if teff >= 30000:
        return 'O5V'
    elif teff >= 10000:
        return 'B5V'
    elif teff >= 7500:
        return 'A5V'
    elif teff >= 6000:
        return 'F5V'
    elif teff >= 5200:
        return 'G5V'
    elif teff >= 3700:
        return 'K5V'
    elif teff >= 2400:
        return 'M5V'
    else:
        return 'L5'  # Brown dwarf

def calculate_luminosity(mag_g, distance_ly):
    """Calculate luminosity from apparent magnitude and distance"""
    if mag_g is None or mag_g > 90:
        return None
    
    try:
        # Convert to absolute magnitude
        distance_pc = distance_ly / 3.26156
        abs_mag = mag_g - 5 * math.log10(distance_pc) + 5
        
        # Convert to solar luminosities (using Sun's absolute G magnitude ≈ 4.67)
        sun_abs_mag_g = 4.67
        luminosity = 10 ** ((sun_abs_mag_g - abs_mag) / 2.5)
        
        return luminosity
    except:
        return None

def calculate_habitable_zone(luminosity):
    """Calculate habitable zone boundaries"""
    if not luminosity or luminosity <= 0:
        return None, None
    
    # Conservative habitable zone (Kopparapu et al. 2013)
    hz_inner = 0.95 * math.sqrt(luminosity)  # Inner edge
    hz_outer = 1.37 * math.sqrt(luminosity)  # Outer edge
    
    return hz_inner, hz_outer

def insert_stars(stars_data):
    """Insert star data into database"""
    if not stars_data:
        print("❌ No star data to insert")
        return

    print(f"💾 Inserting {len(stars_data)} stars into database...")

    inserted_count = 0

    for star in stars_data:
        try:
            # Calculate habitable zone
            hz_inner, hz_outer = calculate_habitable_zone(star.get('luminosity_solar'))

            # Generate a name based on Gaia source ID
            name = f"Gaia DR3 {star['source_id']}"

            # Build INSERT statement
            sql = f"""
                INSERT INTO stars (
                    src, src_key, name, catalog_name,
                    ra_deg, dec_deg, distance_ly, parallax_mas,
                    pm_ra_masyr, pm_dec_masyr, mag_g,
                    spectral_type, mass_solar, radius_solar,
                    teff_k, luminosity_solar,
                    hz_inner_au, hz_outer_au,
                    is_colonizable, discovery_status
                ) VALUES (
                    'gaia', '{star['source_id']}', '{name}', '{name}',
                    {star['ra']}, {star['dec']}, {star['distance_ly']}, {star['parallax']},
                    {star.get('pmra') or 'NULL'}, {star.get('pmdec') or 'NULL'}, {star.get('mag_g') or 'NULL'},
                    '{star['spectral_type']}', {star.get('mass_solar') or 'NULL'}, {star.get('radius_solar') or 'NULL'},
                    {star.get('teff') or 'NULL'}, {star.get('luminosity_solar') or 'NULL'},
                    {hz_inner or 'NULL'}, {hz_outer or 'NULL'},
                    true, 'known'
                ) ON CONFLICT (src, src_key) DO UPDATE SET
                    name = EXCLUDED.name,
                    ra_deg = EXCLUDED.ra_deg,
                    dec_deg = EXCLUDED.dec_deg,
                    distance_ly = EXCLUDED.distance_ly,
                    spectral_type = EXCLUDED.spectral_type,
                    teff_k = EXCLUDED.teff_k,
                    luminosity_solar = EXCLUDED.luminosity_solar,
                    hz_inner_au = EXCLUDED.hz_inner_au,
                    hz_outer_au = EXCLUDED.hz_outer_au;
            """

            if execute_sql(sql):
                inserted_count += 1
            else:
                print(f"⚠️  Error inserting star {star.get('source_id', 'unknown')}")

        except Exception as e:
            print(f"⚠️  Error processing star {star.get('source_id', 'unknown')}: {e}")
            continue

    print(f"✅ Successfully processed {inserted_count} stars")

def main():
    """Main import process"""
    print("🚀 Starting comprehensive stellar data import...")
    print("=" * 60)
    
    start_time = time.time()
    
    # Step 1: Fetch Gaia data
    csv_data = fetch_gaia_data()
    if not csv_data:
        print("❌ Failed to fetch Gaia data")
        return
    
    # Step 2: Parse and process data
    print("🔄 Processing stellar data...")
    stars_data = parse_csv_data(csv_data)
    
    if not stars_data:
        print("❌ No valid stellar data found")
        return
    
    print(f"📊 Processed {len(stars_data)} valid stars within 20 light-years")
    
    # Step 3: Insert into database
    insert_stars(stars_data)
    
    # Step 4: Summary
    elapsed = time.time() - start_time
    print("=" * 60)
    print(f"🎉 Import completed in {elapsed:.2f} seconds")
    print(f"📈 Database now contains comprehensive stellar data!")
    
    # Print statistics using docker exec
    print("\n📊 DATABASE SUMMARY:")

    # Get star counts by source
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(*) as count, src FROM stars GROUP BY src ORDER BY src;"')

    # Get distance range
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT MIN(distance_ly) as min_dist, MAX(distance_ly) as max_dist FROM stars WHERE distance_ly IS NOT NULL;"')

    # Get spectral type distribution
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT spectral_type, COUNT(*) as count FROM stars WHERE src=\'gaia\' GROUP BY spectral_type ORDER BY COUNT(*) DESC LIMIT 10;"')

if __name__ == "__main__":
    main()
