#!/usr/bin/env python3
"""
Procedural Planet Generator
Generates realistic undetected planets for star systems based on astronomical research
"""

import os
import sys
import random
import math

def execute_sql(sql):
    """Execute SQL using docker exec"""
    with open('/tmp/temp_sql.sql', 'w') as f:
        f.write(sql)
    
    cmd = 'docker exec -i gg_postgres psql -U gg -d gg < /tmp/temp_sql.sql'
    result = os.system(cmd)
    return result == 0

def safe_sql_string(value):
    """Safely escape a string for SQL insertion"""
    if value is None:
        return 'NULL'
    return "'" + str(value).replace("'", "''") + "'"

def get_star_systems_without_planets():
    """Get star systems that have no detected planets"""
    print("🔍 Finding star systems without detected planets...")

    # Query database for star systems within 20 light-years that have no planets
    sql = """
    SELECT
        s.star_id,
        s.name,
        s.distance_ly,
        s.spectral_type,
        s.mass_solar
    FROM stars s
    WHERE s.distance_ly <= 20
    AND NOT EXISTS (
        SELECT 1 FROM planets p WHERE p.star_id = s.star_id
    )
    ORDER BY s.distance_ly;
    """

    # Write query to temp file and execute
    with open('/tmp/query_stars.sql', 'w') as f:
        f.write(sql)

    # Execute and capture output
    import subprocess
    try:
        result = subprocess.run([
            'docker', 'exec', '-i', 'gg_postgres',
            'psql', '-U', 'gg', '-d', 'gg', '-t', '-A', '-F', '|'
        ], input=sql, text=True, capture_output=True)

        if result.returncode != 0:
            print(f"❌ Database query failed: {result.stderr}")
            return []

        # Parse results
        systems = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 5:
                    systems.append({
                        'star_id': parts[0],
                        'name': parts[1],
                        'distance_ly': float(parts[2]) if parts[2] else 0.0,
                        'spectral_type': parts[3] if parts[3] else 'Unknown',
                        'mass_solar': float(parts[4]) if parts[4] else 1.0
                    })

        return systems

    except Exception as e:
        print(f"❌ Error querying database: {e}")
        return []

def calculate_planet_occurrence_rate(spectral_type, mass_solar):
    """Calculate planet occurrence rate based on stellar properties"""
    
    # Based on astronomical research:
    # - M dwarfs: ~2.5 planets per star on average
    # - G dwarfs (like Sun): ~1.6 planets per star
    # - F dwarfs: ~1.2 planets per star  
    # - A dwarfs: ~0.8 planets per star (fewer due to stellar evolution)
    
    if spectral_type.startswith('M'):
        base_rate = 2.5
    elif spectral_type.startswith('K'):
        base_rate = 2.0
    elif spectral_type.startswith('G'):
        base_rate = 1.6
    elif spectral_type.startswith('F'):
        base_rate = 1.2
    elif spectral_type.startswith('A'):
        base_rate = 0.8
    elif spectral_type.startswith('B'):
        base_rate = 0.3  # Very few planets around hot stars
    else:
        base_rate = 1.0  # Default
    
    # Adjust for stellar mass (more massive stars tend to have fewer planets)
    if mass_solar > 2.0:
        base_rate *= 0.5
    elif mass_solar > 1.5:
        base_rate *= 0.7
    
    return base_rate

def generate_procedural_planet(star_info, planet_number):
    """Generate a single procedural planet for a star system"""
    
    # Generate orbital distance (AU) - log-normal distribution
    # Most planets are close to their star, fewer at large distances
    log_distance = random.normalvariate(0.0, 1.2)  # Mean ~1 AU, spread
    distance_au = math.exp(log_distance)
    
    # Limit to reasonable range
    distance_au = max(0.01, min(distance_au, 50.0))
    
    # Calculate orbital period using Kepler's 3rd law
    # P^2 = a^3 (for solar masses)
    period_days = 365.25 * math.sqrt(distance_au**3 / star_info['mass_solar'])
    
    # Generate planet mass (Earth masses) - power law distribution
    # Most planets are small, fewer large ones
    mass_exponent = random.uniform(-1.5, 2.0)  # Favors smaller planets
    mass_earth = 10**mass_exponent
    mass_earth = max(0.01, min(mass_earth, 1000.0))  # Reasonable limits
    
    # Generate radius based on mass-radius relationship
    if mass_earth < 2.0:
        # Rocky planet relationship: R ∝ M^0.27
        radius_earth = mass_earth**0.27
    elif mass_earth < 10.0:
        # Super-Earth/mini-Neptune: R ∝ M^0.5
        radius_earth = 1.4 * (mass_earth / 2.0)**0.5
    else:
        # Gas giant: R ∝ M^0.1 (roughly constant radius)
        radius_earth = 3.5 * (mass_earth / 10.0)**0.1
    
    # Determine composition based on mass and distance
    if mass_earth < 2.0:
        composition = 'rocky'
    elif mass_earth < 10.0:
        if distance_au < 2.0:
            composition = 'super_earth'
        else:
            composition = 'sub_neptune'
    else:
        if distance_au > 1.0:
            composition = 'gas_giant'
        else:
            composition = 'sub_neptune'
    
    # Generate eccentricity (most orbits are nearly circular)
    eccentricity = random.betavariate(0.5, 2.0)  # Favors low eccentricity
    eccentricity = min(eccentricity, 0.9)  # Stable orbits only
    
    # Estimate equilibrium temperature
    # T ∝ sqrt(L_star / distance^2)
    stellar_luminosity = star_info['mass_solar']**3.5  # Rough approximation
    eq_temp_k = 278 * math.sqrt(stellar_luminosity / distance_au)
    
    # Generate planet name
    planet_name = f"{star_info['name']} {chr(ord('b') + planet_number - 1)}"
    
    return {
        'star_id': star_info['star_id'],
        'name': planet_name,
        'host_star_name': star_info['name'],
        'mass_earth': round(mass_earth, 3),
        'radius_earth': round(radius_earth, 3),
        'sma_au': round(distance_au, 4),
        'period_days': round(period_days, 2),
        'eccentricity': round(eccentricity, 3),
        'eq_temp_k': round(eq_temp_k, 1),
        'composition': composition,
        'discovery_method': 'Procedural Generation',
        'discovery_year': None,
        'in_habitable_zone': 200 <= eq_temp_k <= 350,
        'planet_status': 'procedural',
        'src': 'procedural'
    }

def generate_planets_for_system(star_info):
    """Generate all procedural planets for a star system"""
    
    # Calculate expected number of planets
    base_rate = calculate_planet_occurrence_rate(star_info['spectral_type'], star_info['mass_solar'])
    
    # Add some randomness - Poisson distribution around the base rate
    num_planets = max(0, int(random.normalvariate(base_rate, math.sqrt(base_rate))))
    
    # Limit to reasonable range
    num_planets = min(num_planets, 8)
    
    if num_planets == 0:
        return []
    
    print(f"  🪐 Generating {num_planets} planets for {star_info['name']}")
    
    planets = []
    for i in range(num_planets):
        planet = generate_procedural_planet(star_info, i + 1)
        planets.append(planet)
    
    # Sort planets by orbital distance
    planets.sort(key=lambda p: p['sma_au'])
    
    # Rename planets in order (b, c, d, etc.)
    for i, planet in enumerate(planets):
        planet['name'] = f"{star_info['name']} {chr(ord('b') + i)}"
    
    return planets

def import_procedural_planets():
    """Main function to generate and import procedural planets"""
    print("🌌 Starting Procedural Planet Generation...")
    
    # Get star systems without planets
    star_systems = get_star_systems_without_planets()
    print(f"📊 Found {len(star_systems)} star systems without detected planets")
    
    if not star_systems:
        print("ℹ️  No star systems need procedural planets")
        return True
    
    # Generate planets for each system
    all_planets = []
    for star in star_systems:
        planets = generate_planets_for_system(star)
        all_planets.extend(planets)
    
    print(f"🎲 Generated {len(all_planets)} procedural planets")
    
    if not all_planets:
        print("ℹ️  No planets generated")
        return True
    
    # Import planets to database
    print("📥 Importing procedural planets to database...")
    success_count = 0
    
    for planet in all_planets:
        try:
            sql = f"""
            INSERT INTO planets (
                star_id, name, host_star_name, mass_earth, radius_earth,
                sma_au, period_days, eccentricity,
                eq_temp_k, composition, discovery_method,
                discovery_year, in_habitable_zone, planet_status, src
            ) VALUES (
                {safe_sql_string(planet['star_id'])},
                {safe_sql_string(planet['name'])},
                {safe_sql_string(planet['host_star_name'])},
                {planet['mass_earth']},
                {planet['radius_earth']},
                {planet['sma_au']},
                {planet['period_days']},
                {planet['eccentricity']},
                {planet['eq_temp_k']},
                {safe_sql_string(planet['composition'])},
                {safe_sql_string(planet['discovery_method'])},
                NULL,
                {str(planet['in_habitable_zone']).lower()},
                {safe_sql_string(planet['planet_status'])},
                {safe_sql_string(planet['src'])}
            );
            """
            
            if execute_sql(sql):
                success_count += 1
            else:
                print(f"⚠️  Failed to insert {planet['name']}")
                
        except Exception as e:
            print(f"❌ Error inserting {planet['name']}: {e}")
            continue
    
    print(f"✅ Successfully imported {success_count}/{len(all_planets)} procedural planets")
    
    # Show summary
    execute_sql("SELECT COUNT(*) as procedural_planets FROM planets WHERE planet_status = 'procedural';")
    execute_sql("SELECT COUNT(*) as confirmed_planets FROM planets WHERE planet_status = 'confirmed';")
    execute_sql("SELECT COUNT(*) as total_planets FROM planets;")
    
    return success_count > 0

if __name__ == "__main__":
    success = import_procedural_planets()
    sys.exit(0 if success else 1)
