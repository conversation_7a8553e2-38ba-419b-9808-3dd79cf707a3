# Stellar & Planetary Database ETL Dependencies
# Pinned versions for reproducible builds

# Core data processing
pandas==2.2.2
numpy==1.26.4
scipy==1.13.1

# HTTP requests and APIs
requests==2.32.3
urllib3==2.2.2

# Database connectivity
psycopg2-binary==2.9.9
sqlalchemy==2.0.32

# Astronomy libraries
astropy==6.0.1
astroquery==0.4.7

# Configuration and environment
pyyaml==6.0.2
python-dotenv==1.0.1

# Progress tracking and logging
tqdm==4.66.4
colorlog==6.8.2

# Data validation and quality
cerberus==1.3.5
jsonschema==4.23.0

# Parallel processing
joblib==1.4.2
concurrent-futures==3.1.1

# Utilities
click==8.1.7
tabulate==0.9.0
humanize==4.10.0

# Development and testing
pytest==8.2.2
pytest-cov==5.0.0
black==24.4.2
flake8==7.1.0

# Optional: Spatial indexing (if PostGIS available)
# geoalchemy2==0.15.2
# shapely==2.0.4
