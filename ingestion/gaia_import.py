"""
Gaia DR3 Star Data Import Module
Fetches stellar data within 20 light-years from Gaia Data Release 3

This module:
1. Queries Gaia TAP service for nearby stars (parallax > 50 mas = < 20 ly)
2. Normalizes stellar properties and computes distances
3. Enriches with spectral classifications and habitability zones
4. Loads data into PostgreSQL with conflict resolution
"""

import logging
import os
import time
from io import StringIO
from typing import Dict, Optional

import pandas as pd
import requests
from sqlalchemy import create_engine, text

logger = logging.getLogger(__name__)

# Data source identifier
SRC = "gaia"

# Spectral type mapping for Gaia effective temperatures
SPECTRAL_TYPE_MAP = {
    (50000, float('inf')): 'O5V',
    (30000, 50000): 'B0V',
    (10000, 30000): 'A0V',
    (7500, 10000): 'F0V',
    (6000, 7500): 'G0V',
    (5200, 6000): 'K0V',
    (3700, 5200): 'M0V',
    (2400, 3700): 'M5V',
    (0, 2400): 'L0V'
}


def get_engine():
    """Create SQLAlchemy engine from environment variables"""
    url = (
        f"postgresql+psycopg2://{os.environ['PGUSER']}:{os.environ['PGPASSWORD']}"
        f"@{os.environ['PGHOST']}:{os.environ.get('PGPORT', '5432')}"
        f"/{os.environ['PGDATABASE']}"
    )
    return create_engine(url, echo=False)


def estimate_spectral_type(teff: Optional[float]) -> Optional[str]:
    """Estimate spectral type from effective temperature"""
    if pd.isna(teff) or teff is None:
        return None
    
    for (temp_min, temp_max), spec_type in SPECTRAL_TYPE_MAP.items():
        if temp_min <= teff < temp_max:
            return spec_type
    
    return 'M9V'  # Default for very cool stars


def calculate_distance_ly(parallax_mas: float) -> Optional[float]:
    """Calculate distance in light-years from parallax in milliarcseconds"""
    if pd.isna(parallax_mas) or parallax_mas <= 0:
        return None
    
    # Distance in parsecs = 1000 / parallax_mas
    # Distance in light-years = parsecs * 3.26156
    distance_pc = 1000.0 / parallax_mas
    distance_ly = distance_pc * 3.26156
    
    # Sanity check: should be within 20 light-years
    if distance_ly > 25.0:  # Allow some margin for measurement errors
        logger.warning(f"Calculated distance {distance_ly:.2f} ly exceeds expected range")
    
    return distance_ly


def calculate_habitable_zone(luminosity_solar: Optional[float]) -> tuple:
    """Calculate conservative habitable zone boundaries"""
    if pd.isna(luminosity_solar) or luminosity_solar is None or luminosity_solar <= 0:
        return None, None
    
    # Conservative habitable zone calculation
    # Inner edge: runaway greenhouse limit
    # Outer edge: maximum greenhouse limit
    hz_inner = (luminosity_solar / 1.1) ** 0.5
    hz_outer = (luminosity_solar / 0.53) ** 0.5
    
    return hz_inner, hz_outer


def fetch_gaia_data(config: Dict) -> str:
    """Fetch star data from Gaia TAP service"""
    logger.info("Fetching star data from Gaia DR3...")
    
    tap_url = config["gaia"]["tap_url"]
    adql_query = config["gaia"]["adql_query"]
    
    # Prepare TAP request
    data = {
        "REQUEST": "doQuery",
        "LANG": "ADQL",
        "FORMAT": "csv",
        "QUERY": adql_query
    }
    
    logger.debug(f"ADQL Query: {adql_query}")
    
    try:
        response = requests.post(
            tap_url,
            data=data,
            timeout=300,  # 5 minute timeout for large queries
            headers={'User-Agent': 'GalacticGenesis/1.0 (Stellar Database ETL)'}
        )
        response.raise_for_status()
        
        logger.info(f"Gaia query completed, received {len(response.text)} characters")
        return response.text
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to fetch Gaia data: {e}")
        raise


def normalize_gaia_data(df: pd.DataFrame) -> pd.DataFrame:
    """Normalize Gaia data to our stellar database schema"""
    logger.info(f"Normalizing {len(df)} Gaia stars...")
    
    # Rename columns to match our schema
    column_mapping = {
        'source_id': 'src_key',
        'ra': 'ra_deg',
        'dec': 'dec_deg',
        'parallax': 'parallax_mas',
        'pmra': 'pm_ra_masyr',
        'pmdec': 'pm_dec_masyr',
        'phot_g_mean_mag': 'mag_g',
        'phot_bp_mean_mag': 'mag_b',
        'phot_rp_mean_mag': 'mag_r',
        'bp_rp': 'color_bv',  # Approximate B-V from BP-RP
        'teff_gspphot': 'teff_k',
        'radius_gspphot': 'radius_solar',
        'mass_gspphot': 'mass_solar',
        'lum_gspphot': 'luminosity_solar'
    }
    
    # Select and rename available columns
    available_cols = [col for col in column_mapping.keys() if col in df.columns]
    df_norm = df[available_cols].rename(columns=column_mapping)
    
    # Add source identifier
    df_norm['src'] = SRC
    
    # Convert source_id to string
    df_norm['src_key'] = df_norm['src_key'].astype(str)
    
    # Calculate distance from parallax
    df_norm['distance_ly'] = df_norm['parallax_mas'].apply(calculate_distance_ly)
    
    # Filter to 20 light-year limit
    initial_count = len(df_norm)
    df_norm = df_norm[df_norm['distance_ly'] <= 20.0].copy()
    filtered_count = len(df_norm)
    
    if filtered_count < initial_count:
        logger.info(f"Filtered {initial_count - filtered_count} stars beyond 20 ly limit")
    
    # Estimate spectral types from effective temperature
    df_norm['spectral_type'] = df_norm['teff_k'].apply(estimate_spectral_type)
    
    # Calculate habitable zones
    hz_data = df_norm['luminosity_solar'].apply(
        lambda lum: calculate_habitable_zone(lum) if pd.notna(lum) else (None, None)
    )
    df_norm['hz_inner_au'] = [hz[0] for hz in hz_data]
    df_norm['hz_outer_au'] = [hz[1] for hz in hz_data]
    
    # Add default values for missing columns
    df_norm['discovery_status'] = 'known'
    df_norm['is_colonizable'] = True
    
    # Select final columns for database
    final_columns = [
        'src', 'src_key', 'ra_deg', 'dec_deg', 'distance_ly', 'parallax_mas',
        'pm_ra_masyr', 'pm_dec_masyr', 'mag_g', 'mag_b', 'color_bv',
        'spectral_type', 'mass_solar', 'radius_solar', 'teff_k', 'luminosity_solar',
        'hz_inner_au', 'hz_outer_au', 'discovery_status', 'is_colonizable'
    ]
    
    # Keep only columns that exist
    final_columns = [col for col in final_columns if col in df_norm.columns]
    df_norm = df_norm[final_columns]
    
    logger.info(f"Normalized {len(df_norm)} stars for database insertion")
    return df_norm


def upsert_stars(df: pd.DataFrame) -> None:
    """Insert or update stars in database with conflict resolution"""
    logger.info(f"Upserting {len(df)} stars to database...")
    
    engine = get_engine()
    
    with engine.begin() as conn:
        # Create temporary staging table
        df.to_sql('_gaia_stars_stage', conn, if_exists='replace', index=False)
        
        # Upsert with conflict resolution
        upsert_sql = text("""
            INSERT INTO stars (
                src, src_key, ra_deg, dec_deg, distance_ly, parallax_mas,
                pm_ra_masyr, pm_dec_masyr, mag_g, mag_b, color_bv,
                spectral_type, mass_solar, radius_solar, teff_k, luminosity_solar,
                hz_inner_au, hz_outer_au, discovery_status, is_colonizable
            )
            SELECT 
                src, src_key, ra_deg, dec_deg, distance_ly, parallax_mas,
                pm_ra_masyr, pm_dec_masyr, mag_g, mag_b, color_bv,
                spectral_type, mass_solar, radius_solar, teff_k, luminosity_solar,
                hz_inner_au, hz_outer_au, discovery_status, is_colonizable
            FROM _gaia_stars_stage
            ON CONFLICT (src, src_key) DO UPDATE SET
                ra_deg = EXCLUDED.ra_deg,
                dec_deg = EXCLUDED.dec_deg,
                distance_ly = EXCLUDED.distance_ly,
                parallax_mas = EXCLUDED.parallax_mas,
                pm_ra_masyr = EXCLUDED.pm_ra_masyr,
                pm_dec_masyr = EXCLUDED.pm_dec_masyr,
                mag_g = EXCLUDED.mag_g,
                mag_b = EXCLUDED.mag_b,
                color_bv = EXCLUDED.color_bv,
                spectral_type = EXCLUDED.spectral_type,
                mass_solar = EXCLUDED.mass_solar,
                radius_solar = EXCLUDED.radius_solar,
                teff_k = EXCLUDED.teff_k,
                luminosity_solar = EXCLUDED.luminosity_solar,
                hz_inner_au = EXCLUDED.hz_inner_au,
                hz_outer_au = EXCLUDED.hz_outer_au,
                updated_at = now();
        """)
        
        result = conn.execute(upsert_sql)
        
        # Clean up staging table
        conn.execute(text("DROP TABLE _gaia_stars_stage"))
        
        logger.info(f"Upserted stars to database successfully")


def run(config: Dict) -> None:
    """Main entry point for Gaia import"""
    start_time = time.time()
    
    try:
        # Fetch data from Gaia
        csv_data = fetch_gaia_data(config)
        
        # Parse CSV
        df = pd.read_csv(StringIO(csv_data))
        
        if df.empty:
            logger.warning("Gaia query returned no data")
            return
        
        logger.info(f"Received {len(df)} stars from Gaia DR3")
        
        # Normalize data
        df_normalized = normalize_gaia_data(df)
        
        if df_normalized.empty:
            logger.warning("No stars remain after filtering and normalization")
            return
        
        # Load to database
        upsert_stars(df_normalized)
        
        elapsed = time.time() - start_time
        logger.info(f"Gaia import completed in {elapsed:.2f} seconds")
        
        # Print summary
        print(f"\nGAIA IMPORT SUMMARY:")
        print(f"Stars fetched: {len(df)}")
        print(f"Stars within 20 ly: {len(df_normalized)}")
        print(f"Spectral types: {df_normalized['spectral_type'].value_counts().to_dict()}")
        print(f"Distance range: {df_normalized['distance_ly'].min():.2f} - {df_normalized['distance_ly'].max():.2f} ly")
        
    except Exception as e:
        logger.error(f"Gaia import failed: {e}")
        raise


if __name__ == "__main__":
    # For testing
    import yaml
    from dotenv import load_dotenv
    
    load_dotenv()
    
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)
    
    run(config)
