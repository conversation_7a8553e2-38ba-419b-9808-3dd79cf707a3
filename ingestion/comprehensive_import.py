#!/usr/bin/env python3
"""
Comprehensive Stellar Data Import
Imports a comprehensive dataset of nearby stars and exoplanets
"""

import os
import sys
import time
import math

def execute_sql(sql):
    """Execute SQL using docker exec"""
    # Escape single quotes in SQL
    sql_escaped = sql.replace("'", "''")
    cmd = f'docker exec gg_postgres psql -U gg -d gg -c "{sql_escaped}"'
    result = os.system(cmd)
    return result == 0

def get_comprehensive_star_data():
    """Get comprehensive list of nearby stars within 20 light-years"""
    return [
        # Alpha Centauri System
        {"name": "Proxima Centauri", "ra": 217.43, "dec": -62.68, "distance": 4.24, "spectral": "M5.5Ve", "mass": 0.12, "radius": 0.15, "teff": 3042, "planets": 3},
        {"name": "Alpha Centauri A", "ra": 219.90, "dec": -60.83, "distance": 4.37, "spectral": "G2V", "mass": 1.1, "radius": 1.22, "teff": 5790, "planets": 1},
        {"name": "Alpha Centauri B", "ra": 219.90, "dec": -60.83, "distance": 4.37, "spectral": "K1V", "mass": 0.91, "radius": 0.86, "teff": 5260, "planets": 0},
        
        # Barnard's Star System
        {"name": "Barnard's Star", "ra": 269.45, "dec": 4.69, "distance": 5.96, "spectral": "M4.0V", "mass": 0.14, "radius": 0.20, "teff": 3134, "planets": 1},
        
        # Wolf 359 System
        {"name": "Wolf 359", "ra": 164.12, "dec": 7.01, "distance": 7.86, "spectral": "M6.0V", "mass": 0.09, "radius": 0.16, "teff": 2800, "planets": 1},
        
        # Lalande 21185 System
        {"name": "Lalande 21185", "ra": 165.92, "dec": 35.97, "distance": 8.31, "spectral": "M2.0V", "mass": 0.39, "radius": 0.39, "teff": 3828, "planets": 2},
        
        # Sirius System
        {"name": "Sirius A", "ra": 101.29, "dec": -16.72, "distance": 8.66, "spectral": "A1V", "mass": 2.06, "radius": 1.71, "teff": 9940, "planets": 0},
        {"name": "Sirius B", "ra": 101.29, "dec": -16.72, "distance": 8.66, "spectral": "DA2", "mass": 0.98, "radius": 0.008, "teff": 25200, "planets": 0},
        
        # UV Ceti System (Luyten 726-8)
        {"name": "Luyten 726-8 A", "ra": 24.50, "dec": -17.95, "distance": 8.73, "spectral": "M5.5Ve", "mass": 0.10, "radius": 0.14, "teff": 2670, "planets": 0},
        {"name": "Luyten 726-8 B", "ra": 24.50, "dec": -17.95, "distance": 8.73, "spectral": "M6.0Ve", "mass": 0.10, "radius": 0.14, "teff": 2650, "planets": 0},
        
        # Ross 154
        {"name": "Ross 154", "ra": 288.14, "dec": -23.76, "distance": 9.69, "spectral": "M3.5Ve", "mass": 0.17, "radius": 0.24, "teff": 3340, "planets": 0},
        
        # Ross 248
        {"name": "Ross 248", "ra": 351.34, "dec": 44.17, "distance": 10.32, "spectral": "M5.5Ve", "mass": 0.12, "radius": 0.16, "teff": 2799, "planets": 0},
        
        # Epsilon Eridani System
        {"name": "Epsilon Eridani", "ra": 53.23, "dec": -9.46, "distance": 10.52, "spectral": "K2V", "mass": 0.82, "radius": 0.74, "teff": 5084, "planets": 1},
        
        # Lacaille 9352
        {"name": "Lacaille 9352", "ra": 341.36, "dec": -35.86, "distance": 10.74, "spectral": "M1.5V", "mass": 0.49, "radius": 0.47, "teff": 3626, "planets": 0},
        
        # Ross 128
        {"name": "Ross 128", "ra": 176.95, "dec": 0.80, "distance": 11.01, "spectral": "M4.0V", "mass": 0.17, "radius": 0.20, "teff": 3192, "planets": 1},
        
        # EZ Aquarii System
        {"name": "EZ Aquarii A", "ra": 326.84, "dec": -13.09, "distance": 11.27, "spectral": "M5.0Ve", "mass": 0.11, "radius": 0.14, "teff": 3200, "planets": 0},
        {"name": "EZ Aquarii B", "ra": 326.84, "dec": -13.09, "distance": 11.27, "spectral": "M5.5Ve", "mass": 0.10, "radius": 0.13, "teff": 3100, "planets": 0},
        {"name": "EZ Aquarii C", "ra": 326.84, "dec": -13.09, "distance": 11.27, "spectral": "M6.5Ve", "mass": 0.09, "radius": 0.10, "teff": 2800, "planets": 0},
        
        # Procyon System
        {"name": "Procyon A", "ra": 114.83, "dec": 5.22, "distance": 11.46, "spectral": "F5IV-V", "mass": 1.50, "radius": 2.05, "teff": 6530, "planets": 0},
        {"name": "Procyon B", "ra": 114.83, "dec": 5.22, "distance": 11.46, "spectral": "DQZ", "mass": 0.60, "radius": 0.01, "teff": 7740, "planets": 0},
        
        # 61 Cygni System
        {"name": "61 Cygni A", "ra": 316.14, "dec": 38.48, "distance": 11.40, "spectral": "K5.0V", "mass": 0.70, "radius": 0.67, "teff": 4374, "planets": 0},
        {"name": "61 Cygni B", "ra": 316.18, "dec": 38.46, "distance": 11.40, "spectral": "K7.0V", "mass": 0.63, "radius": 0.60, "teff": 4077, "planets": 0},
        
        # Struve 2398 System
        {"name": "Struve 2398 A", "ra": 287.46, "dec": 59.39, "distance": 11.52, "spectral": "M3.0V", "mass": 0.34, "radius": 0.35, "teff": 3500, "planets": 0},
        {"name": "Struve 2398 B", "ra": 287.46, "dec": 59.39, "distance": 11.52, "spectral": "M3.5V", "mass": 0.28, "radius": 0.32, "teff": 3400, "planets": 0},
        
        # Groombridge 34 System
        {"name": "Groombridge 34 A", "ra": 24.50, "dec": 43.59, "distance": 11.62, "spectral": "M1.5V", "mass": 0.38, "radius": 0.38, "teff": 3600, "planets": 0},
        {"name": "Groombridge 34 B", "ra": 24.50, "dec": 43.59, "distance": 11.62, "spectral": "M3.5V", "mass": 0.17, "radius": 0.21, "teff": 3300, "planets": 0},
        
        # DX Cancri
        {"name": "DX Cancri", "ra": 125.63, "dec": 26.30, "distance": 11.83, "spectral": "M6.5Ve", "mass": 0.09, "radius": 0.11, "teff": 2840, "planets": 0},
        
        # Tau Ceti System
        {"name": "Tau Ceti", "ra": 26.02, "dec": -15.94, "distance": 11.91, "spectral": "G8.5V", "mass": 0.78, "radius": 0.79, "teff": 5344, "planets": 4},
        
        # Epsilon Indi System
        {"name": "Epsilon Indi A", "ra": 330.85, "dec": -56.79, "distance": 11.87, "spectral": "K5V", "mass": 0.76, "radius": 0.73, "teff": 4630, "planets": 1},
        {"name": "Epsilon Indi Ba", "ra": 330.85, "dec": -56.79, "distance": 11.87, "spectral": "T1", "mass": 0.048, "radius": 0.091, "teff": 1300, "planets": 0},
        {"name": "Epsilon Indi Bb", "ra": 330.85, "dec": -56.79, "distance": 11.87, "spectral": "T6", "mass": 0.028, "radius": 0.096, "teff": 880, "planets": 0},
        
        # YZ Ceti
        {"name": "YZ Ceti", "ra": 18.87, "dec": -16.95, "distance": 12.11, "spectral": "M4.5V", "mass": 0.13, "radius": 0.17, "teff": 3056, "planets": 3},
        
        # Luyten's Star
        {"name": "Luyten's Star", "ra": 110.62, "dec": 7.01, "distance": 12.39, "spectral": "M3.5V", "mass": 0.26, "radius": 0.30, "teff": 3382, "planets": 1},
        
        # Teegarden's Star
        {"name": "Teegarden's Star", "ra": 32.96, "dec": 16.89, "distance": 12.43, "spectral": "M7.0V", "mass": 0.08, "radius": 0.13, "teff": 2904, "planets": 2},
        
        # Kapteyn's Star
        {"name": "Kapteyn's Star", "ra": 77.24, "dec": -45.02, "distance": 12.78, "spectral": "M1.5V", "mass": 0.28, "radius": 0.29, "teff": 3570, "planets": 2},
        
        # Lacaille 8760
        {"name": "Lacaille 8760", "ra": 319.31, "dec": -38.87, "distance": 12.87, "spectral": "M0.0V", "mass": 0.60, "radius": 0.51, "teff": 3934, "planets": 0},
        
        # Kruger 60 System
        {"name": "Kruger 60 A", "ra": 330.79, "dec": 57.70, "distance": 13.15, "spectral": "M3.0V", "mass": 0.27, "radius": 0.35, "teff": 3500, "planets": 0},
        {"name": "Kruger 60 B", "ra": 330.79, "dec": 57.70, "distance": 13.15, "spectral": "M4.0V", "mass": 0.16, "radius": 0.24, "teff": 3200, "planets": 0},
        
        # Ross 614 System
        {"name": "Ross 614 A", "ra": 95.94, "dec": -2.77, "distance": 13.35, "spectral": "M4.5V", "mass": 0.22, "radius": 0.24, "teff": 3200, "planets": 0},
        {"name": "Ross 614 B", "ra": 95.94, "dec": -2.77, "distance": 13.35, "spectral": "M5.5V", "mass": 0.11, "radius": 0.18, "teff": 2700, "planets": 0},
        
        # Wolf 1061
        {"name": "Wolf 1061", "ra": 244.78, "dec": -12.54, "distance": 13.82, "spectral": "M3.0V", "mass": 0.25, "radius": 0.31, "teff": 3342, "planets": 3},
        
        # Van Maanen's Star
        {"name": "Van Maanen's Star", "ra": 18.05, "dec": 5.39, "distance": 14.07, "spectral": "DZ7", "mass": 0.68, "radius": 0.010, "teff": 5850, "planets": 0},
        
        # Wolf 359 (additional data)
        {"name": "CN Leonis", "ra": 164.12, "dec": 7.01, "distance": 7.86, "spectral": "M6.0V", "mass": 0.09, "radius": 0.16, "teff": 2800, "planets": 1},
        
        # Gliese 1 (additional nearby stars)
        {"name": "Gliese 1", "ra": 2.09, "dec": -21.08, "distance": 14.22, "spectral": "M1.5V", "mass": 0.45, "radius": 0.44, "teff": 3700, "planets": 0},
        
        # Additional M-dwarfs within 15 ly
        {"name": "LP 944-20", "ra": 52.85, "dec": -35.43, "distance": 14.98, "spectral": "M9V", "mass": 0.056, "radius": 0.10, "teff": 2200, "planets": 0},
        {"name": "Wolf 424 A", "ra": 183.10, "dec": 9.01, "distance": 14.31, "spectral": "M5.5Ve", "mass": 0.14, "radius": 0.17, "teff": 2800, "planets": 0},
        {"name": "Wolf 424 B", "ra": 183.10, "dec": 9.01, "distance": 14.31, "spectral": "M7Ve", "mass": 0.13, "radius": 0.16, "teff": 2600, "planets": 0},
        
        # G-type stars
        {"name": "Alpha Centauri A", "ra": 219.90, "dec": -60.83, "distance": 4.37, "spectral": "G2V", "mass": 1.10, "radius": 1.22, "teff": 5790, "planets": 1},
        {"name": "70 Ophiuchi A", "ra": 270.16, "dec": 2.50, "distance": 16.58, "spectral": "K0V", "mass": 0.90, "radius": 0.84, "teff": 5297, "planets": 1},
        {"name": "70 Ophiuchi B", "ra": 270.16, "dec": 2.50, "distance": 16.58, "spectral": "K4V", "mass": 0.71, "radius": 0.70, "teff": 4467, "planets": 0},
        
        # Additional K-type stars
        {"name": "36 Ophiuchi A", "ra": 260.82, "dec": -26.60, "distance": 19.51, "spectral": "K0V", "mass": 0.85, "radius": 0.84, "teff": 5557, "planets": 0},
        {"name": "36 Ophiuchi B", "ra": 260.82, "dec": -26.60, "distance": 19.51, "spectral": "K1V", "mass": 0.85, "radius": 0.83, "teff": 5421, "planets": 0},
        {"name": "36 Ophiuchi C", "ra": 260.82, "dec": -26.60, "distance": 19.51, "spectral": "K5V", "mass": 0.71, "radius": 0.74, "teff": 4617, "planets": 0},
        
        # Brown dwarfs and very low mass stars
        {"name": "WISE J085510.83-071442.5", "ra": 133.79, "dec": -7.25, "distance": 7.27, "spectral": "Y0", "mass": 0.008, "radius": 0.086, "teff": 250, "planets": 0},
        {"name": "Luhman 16 A", "ra": 162.30, "dec": -53.32, "distance": 6.59, "spectral": "L7.5", "mass": 0.030, "radius": 0.10, "teff": 1210, "planets": 0},
        {"name": "Luhman 16 B", "ra": 162.30, "dec": -53.32, "distance": 6.59, "spectral": "T0.5", "mass": 0.027, "radius": 0.096, "teff": 1170, "planets": 0},
    ]

def calculate_luminosity(mass, teff):
    """Calculate stellar luminosity from mass and temperature"""
    if not mass or not teff:
        return None
    
    # Mass-luminosity relation for main sequence stars
    if mass > 0.43:
        luminosity = mass ** 4
    else:
        luminosity = 0.23 * (mass ** 2.3)
    
    return luminosity

def calculate_habitable_zone(luminosity):
    """Calculate habitable zone boundaries"""
    if not luminosity or luminosity <= 0:
        return None, None
    
    # Conservative habitable zone (Kopparapu et al. 2013)
    hz_inner = 0.95 * math.sqrt(luminosity)
    hz_outer = 1.37 * math.sqrt(luminosity)
    
    return hz_inner, hz_outer

def insert_comprehensive_stars():
    """Insert comprehensive star dataset"""
    stars = get_comprehensive_star_data()
    
    print(f"💾 Inserting {len(stars)} comprehensive stellar records...")
    
    inserted_count = 0
    
    for star in stars:
        try:
            # Calculate derived properties
            luminosity = calculate_luminosity(star.get('mass'), star.get('teff'))
            hz_inner, hz_outer = calculate_habitable_zone(luminosity)
            
            # Build SQL
            sql = f"""
                INSERT INTO stars (
                    src, src_key, name, catalog_name,
                    ra_deg, dec_deg, distance_ly,
                    spectral_type, mass_solar, radius_solar,
                    teff_k, luminosity_solar,
                    hz_inner_au, hz_outer_au,
                    is_colonizable, discovery_status
                ) VALUES (
                    'comprehensive', '{star['name'].replace("'", "''")}', '{star['name'].replace("'", "''")}', '{star['name'].replace("'", "''")}',
                    {star['ra']}, {star['dec']}, {star['distance']},
                    '{star['spectral']}', {star['mass']}, {star['radius']},
                    {star['teff']}, {luminosity or 'NULL'},
                    {hz_inner or 'NULL'}, {hz_outer or 'NULL'},
                    true, 'known'
                ) ON CONFLICT (src, src_key) DO UPDATE SET
                    ra_deg = EXCLUDED.ra_deg,
                    dec_deg = EXCLUDED.dec_deg,
                    distance_ly = EXCLUDED.distance_ly,
                    spectral_type = EXCLUDED.spectral_type,
                    mass_solar = EXCLUDED.mass_solar,
                    radius_solar = EXCLUDED.radius_solar,
                    teff_k = EXCLUDED.teff_k,
                    luminosity_solar = EXCLUDED.luminosity_solar,
                    hz_inner_au = EXCLUDED.hz_inner_au,
                    hz_outer_au = EXCLUDED.hz_outer_au;
            """
            
            cmd = f'docker exec gg_postgres psql -U gg -d gg -c "{sql}"'
            result = os.system(cmd)
            
            if result == 0:
                inserted_count += 1
                if inserted_count % 10 == 0:
                    print(f"  ✅ Processed {inserted_count}/{len(stars)} stars...")
            else:
                print(f"  ⚠️  Error inserting {star['name']}")
                
        except Exception as e:
            print(f"  ⚠️  Error processing {star.get('name', 'unknown')}: {e}")
            continue
    
    print(f"✅ Successfully inserted {inserted_count} comprehensive stellar records")
    return inserted_count

def main():
    """Main comprehensive import process"""
    print("🚀 Starting comprehensive stellar database population...")
    print("=" * 70)
    
    start_time = time.time()
    
    # Insert comprehensive star data
    star_count = insert_comprehensive_stars()
    
    elapsed = time.time() - start_time
    print("=" * 70)
    print(f"🎉 Comprehensive import completed in {elapsed:.2f} seconds")
    print(f"📈 Added {star_count} stellar systems to database!")
    
    # Print final statistics
    print("\n📊 FINAL DATABASE SUMMARY:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(*) as total_stars, src FROM stars GROUP BY src ORDER BY src;"')
    
    print("\n🌟 STELLAR CLASSIFICATION BREAKDOWN:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT LEFT(spectral_type, 1) as class, COUNT(*) as count FROM stars GROUP BY LEFT(spectral_type, 1) ORDER BY count DESC;"')
    
    print("\n🪐 PLANETARY SYSTEMS:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(*) as total_planets FROM planets;"')
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(DISTINCT star_id) as systems_with_planets FROM planets;"')
    
    print("\n📏 DISTANCE DISTRIBUTION:")
    os.system('docker exec gg_postgres psql -U gg -d gg -c "SELECT CASE WHEN distance_ly <= 5 THEN \'0-5 ly\' WHEN distance_ly <= 10 THEN \'5-10 ly\' WHEN distance_ly <= 15 THEN \'10-15 ly\' ELSE \'15-20 ly\' END as range, COUNT(*) as count FROM stars GROUP BY CASE WHEN distance_ly <= 5 THEN \'0-5 ly\' WHEN distance_ly <= 10 THEN \'5-10 ly\' WHEN distance_ly <= 15 THEN \'10-15 ly\' ELSE \'15-20 ly\' END ORDER BY range;"')

if __name__ == "__main__":
    main()
