# 🚨 <PERSON>LACTIC GENESIS PORT MANAGEMENT RULES

## ⚠️ CRITICAL: NEVER CHANGE PORTS WITHOUT USER APPROVAL

This document establishes the **ABSOLUTE RULES** for port management in Galactic Genesis. These rules are **NON-NEGOTIABLE** and must be followed by all developers and AI assistants.

## 🔒 **RULE #1: IMMUTABLE PORT CONFIGURATION**

### **NEVER CHANGE THESE PORTS:**
```
GALACTIC GENESIS OFFICIAL PORTS:
===============================
Frontend (React):           5174  ← NEVER CHANGE
API Gateway:                19081 ← NEVER CHANGE  
Orders Service:              8081 ← NEVER CHANGE
Fleets Service:              8082 ← NEVER CHANGE
Colonies Service:            8083 ← NEVER CHANGE
Tech Service:                8084 ← NEVER CHANGE
Market Service:              8085 ← NEVER CHANGE
Health Dashboard:            8086 ← NEVER CHANGE
Event Dispatcher:            8090 ← NEVER CHANGE
PostgreSQL Database:         5433 ← NEVER CHANGE
NATS Message Broker:         4222 ← NEVER CHANGE
===============================
```

## 🔒 **RULE #2: PORT CONFLICT PROTOCOL**

### **IF A PORT IS BUSY:**
1. **STOP** - Do not change the port number
2. **DIAGNOSE** - Run `./scripts/ports/check-port.sh <PORT>`
3. **REPORT** - Tell user exactly what's using the port
4. **ASK** - Request explicit approval before any action
5. **DOCUMENT** - Log any approved changes

### **NEVER DO:**
- ❌ Change port numbers automatically
- ❌ Use alternative ports without approval
- ❌ Kill processes without user consent
- ❌ Assume it's okay to use different ports

### **ALWAYS DO:**
- ✅ Use the diagnostic scripts
- ✅ Report conflicts to the user
- ✅ Ask for explicit approval
- ✅ Follow the established protocol

## 🔒 **RULE #3: ENVIRONMENT VARIABLES**

### **USE THESE ENV VARS:**
```bash
PORT=5174              # Frontend port
API_GATEWAY_PORT=19081 # API Gateway port
VITE_API_URL=http://localhost:19081  # Frontend API URL
```

### **NEVER HARDCODE PORTS IN:**
- Source code files
- Configuration files (except env examples)
- Docker files (use env vars)
- Scripts (use env vars or parameters)

## 🛠️ **DIAGNOSTIC TOOLS**

### **Available Scripts:**
```bash
# Check specific port
./scripts/ports/check-port.sh 5177

# Verify all ports
./scripts/ports/verify-all-ports.sh

# Kill process on port (USER APPROVAL REQUIRED)
./scripts/ports/kill-port.sh 5177
```

## 🚨 **VIOLATION CONSEQUENCES**

### **If ports are changed without approval:**
1. **IMMEDIATE ROLLBACK** - Revert to correct ports
2. **SYSTEM RESTART** - Restart all services on correct ports
3. **DOCUMENTATION UPDATE** - Update this violation log
4. **PROCESS IMPROVEMENT** - Strengthen enforcement

## 📝 **VIOLATION LOG**
- **2025-09-16**: AI changed frontend port from 5177 to 5174 without approval
  - **Resolution**: User approved port 5174, updated system configuration
  - **Prevention**: Created comprehensive port rule system and diagnostic scripts
  - **Status**: System now officially uses port 5174 for frontend

## 🔧 **IMPLEMENTATION CHECKLIST**

### **For Developers:**
- [ ] Read and understand these rules
- [ ] Use diagnostic scripts before making changes
- [ ] Always ask for approval before port changes
- [ ] Update documentation when ports are officially changed

### **For AI Assistants:**
- [ ] Reference this document before any port-related actions
- [ ] Use diagnostic scripts to identify port conflicts
- [ ] Never change ports without explicit user approval
- [ ] Always suggest using scripts/ports/ tools for diagnosis

## 🎯 **REMEMBER:**
**CONSISTENCY IS CRITICAL** - The same ports must be used across all environments, all the time. Port changes break integrations, monitoring, and team workflows.

**WHEN IN DOUBT, ASK THE USER!**
