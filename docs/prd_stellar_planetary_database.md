# Product Requirements Document (PRD) — Stellar & Planetary Database

**Purpose:** Define the requirements for creating and populating a **stars + planets database** for the Space 4X game. This PRD instructs Augment Code (in VS Code) to implement the database schema, ETL pipeline, and data integrity rules.

**Scope:**
- Ingest real astronomy data from public catalogs (Gaia DR3, NASA Exoplanet Archive, CELESTA optional).
- Normalize into PostgreSQL schema.
- Populate with both real and procedurally generated planets.
- Provide API access to game backend.

---

## 1. Goals & Success Criteria
- **Goal:** Build a reliable DB table set containing at least:
  - 1M+ stars (positions, properties).
  - All known exoplanets (size, mass, orbit, composition).
  - Procedural planets to fill gaps.
- **Success Criteria:**
  - DB queries return star + planet data within <200 ms for small queries.
  - All imported rows carry `src` (data source).
  - No duplicate stars or planets for same source.
  - Procedural planets flagged `src='proc'`.

---

## 2. Functional Requirements

### 2.1 Database Schema
- **Table: stars**
  - id (PK)
  - name, src, src_key
  - ra/dec, distance, parallax, pm_ra, pm_dec
  - mag_g, spectral_type, mass, radius, temp, metallicity
- **Table: planets**
  - id (PK)
  - star_id (FK → stars)
  - name, src, src_key
  - mass_earth, radius_earth, semi_major_axis_au, period_days, eccentricity
  - eq_temp_k, composition, discovery_method, discovery_year
- **Constraints:**
  - UNIQUE (src, src_key) on both stars and planets.
  - Indexes on (ra,dec), (star_id), (name).

### 2.2 Data Sources
- **Gaia DR3:**
  - RA, Dec, parallax → distance.
  - Magnitudes, motions.
- **NASA Exoplanet Archive:**
  - Planet mass, radius, orbit.
  - Host star context.
- **CELESTA (optional):**
  - Habitable zone estimates.
- **Procedural Generator:**
  - Fills missing systems.

### 2.3 ETL Pipeline
- **Modules:** gaia_import, nasa_exoplanet_import, celesta_import, join_enrich, procedural_generate.
- **Pipeline Orchestrator:** `run_pipeline.py --steps all`.
- **Environment:** Python 3.11, requirements.txt pinned, `.env` for DB credentials.
- **Outputs:** Tables `stars`, `planets` populated, reproducible.

### 2.4 Data Enrichment
- Crossmatch NASA planets to Gaia stars (by name or position).
- Guess composition if unknown (rule-based by radius/mass).
- Generate up to 3 planets per star without any known ones.

### 2.5 API Access (Future)
- Provide REST/WS API to fetch stars and planets.
- Endpoints:
  - GET /stars?bbox=…
  - GET /stars/{id}
  - GET /stars/{id}/planets
  - GET /planets/{id}

---

## 3. Non-Functional Requirements
- **Performance:** Support bulk inserts (10k rows/min). Query latency <200 ms for 1000-row queries.
- **Reliability:** ETL idempotent (re-runs don’t duplicate).
- **Maintainability:** Config-driven (YAML endpoints, ADQL queries).
- **Security:** Secrets in `.env`. Read-only public datasets. Safe UPSERTs.
- **Licensing:** All datasets are open/public. Stack is MIT/Apache.

---

## 4. Deliverables
1. PostgreSQL schema script (`/sql/001_schema.sql`).
2. Python ETL scripts (`/ingestion/*.py`).
3. Config (`config.yaml`), env template (`.env.example`).
4. Documentation on how to run pipeline.
5. Test data sample (100 stars + 10 planets) checked in for dev.

---

## 5. Implementation Notes for Augment Code
- **Step 1:** Create DB schema exactly as specified.
- **Step 2:** Implement ETL modules to import Gaia + NASA data.
- **Step 3:** Implement join logic (planets → host stars).
- **Step 4:** Implement procedural planet generator.
- **Step 5:** Verify idempotency by running pipeline twice.
- **Step 6:** Unit test composition guessing logic.
- **Step 7:** Provide example query outputs.

---

## 6. Future Extensions
- Add PostGIS/q3c for fast cone searches.
- Add galaxy-wide map generation.
- Add caching (Redis) for hot queries.
- Expose via REST API service.

---

✅ With this PRD, Augment Code can safely scaffold the DB schema, populate it with Gaia + NASA data, and prepare the backend for galaxy map integration.

## 7. Suggestion for download of planet and star data to our plan and star database
See document docs?data_import_pipeline_gaia_nasa_→_postgres.md