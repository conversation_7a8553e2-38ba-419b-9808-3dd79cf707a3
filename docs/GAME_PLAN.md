# Galactic Genesis — Implementation Plan

This plan outlines the next phases to move from the current working backend to a playable loop. It evolves with the codebase.

Phase 1 — Movement adjacency and validation (IN PROGRESS)
- DB: Add system_links (bidirectional) and seed links
- Orders: Enforce move adjacency when not in test mode
- Tests: Keep unit tests green; integration coverage through gateway

Phase 2 — Turn scheduler and deterministic processing
- Introduce a simple turn service/module that ticks every N seconds
- Orders-svc worker processes all pending orders each turn in a deterministic order
- API: Expose current turn, latest processed turn
- Tests: Ensure per-turn processing order and idempotency

Phase 3 — World seeding and basic combat
- Seed star map with systems, links, and a few fleets per empire
- Implement minimal combat (stance, supply impact, simple outcome)
- Attack order (MVP): co-located fleets from different empires; reduce supply; emit battle.started and battle.resolved

- Events: Publish combat events (battle.started, battle.resolved)
- Tests: Simulate a combat order and assert events + state changes

Phase 4 — Event schema and WebSocket subscriptions
- Define versioned event schema (v1) for key topics
- Dispatcher: Add simple filter query (e.g., ?empire=emp-1)
- Tests: WS client receives only subscribed topics

Phase 5 — Persistence surface and queries
- Expand read APIs: recent orders, fleets by empire, system info with neighbors
- Add indexes and safety constraints
- Tests: API-level validation and query performance sanity checks

Notes
- Keep services small and focused; avoid cross-service domain logic
- Do not modify authentication/login logic without approval
- Prefer minimal changes with strong tests; smoketest before finalizing features

