# Smoke: Read models (neighbors, fleets by empire)

Goal: exercise the new read endpoints via the gateway.

Steps:
1) Ensure services up: `make up`
2) List all systems:
   - `curl http://localhost:19080/v1/systems`
   - Expect JSON: `{ "systems": [ { "id": "sys-1", "neighbor_count": 3, ... }, ... ] }`
3) Neighbors of sys-1:
   - `curl http://localhost:19080/v1/systems/sys-1/neighbors`
   - Expect JSON: `{ "neighbors": [ { "id": "sys-2", ... }, { "id": "sys-4", ... }, { "id": "sys-5", ... } ] }`
4) Fleets by empire emp-1:
   - `curl http://localhost:19080/v1/empires/emp-1/fleets`
   - Expect JSON: `{ "fleets": [ { "id": "fleet-1", ... }, { "id": "fleet-3", ... } ] }`

Notes:
- In TEST_MODE (unit tests), these return empty lists
- In dev with DB, neighbors derive from system_links links; fleets join systems for name

