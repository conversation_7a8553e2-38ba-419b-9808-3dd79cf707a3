# Smoke: Battles read model

Goal: quickly see last 50 attack orders as battle entries.

Steps:
1) Ensure services up: `make up`
2) Create an attack scenario (see docs/smoke/attack.md). If already run, skip.
3) List battles: `curl http://localhost:19080/v1/battles`
   - Expect JSON: `{ "battles": [ { "order_id": ..., "attackerId": ..., "targetFleetId": ..., ... } ] }`
4) Optional: open WS filtered to battles: `websocat -t ws://localhost:19080/v1/stream?topics=battle.started,battle.resolved`

