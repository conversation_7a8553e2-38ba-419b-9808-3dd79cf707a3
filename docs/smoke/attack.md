# Smoke: Attack order

Goal: verify an attack order flows end-to-end in dev.

Pre-reqs:
- Dev stack running: `make up` (Postgres + NATS + services)
- Seeds loaded (compose runs migrations+seeds automatically)

Steps:
1) Check current turn
   - curl http://localhost:19080/v1/turn
   - Expect: `{ "turn": N }`
2) Submit attack order (assumes seeds: fleet-1 at sys-1, fleet-2 at sys-2)
   - First, move fleet-1 to sys-2: `curl -X POST http://localhost:19080/v1/orders -H 'content-type: application/json' -d '{"kind":"move","payload":{"fleetId":"fleet-1","toSystemId":"sys-2"}}'`
   - Then, submit attack: `curl -X POST http://localhost:19080/v1/orders -H 'content-type: application/json' -d '{"kind":"attack","payload":{"fleetId":"fleet-1","targetFleetId":"fleet-2"}}'`
   - Expect 202 receipts
3) Observe events
   - Connect WS: `websocat -t ws://localhost:19080/v1/stream?topics=battle.started,battle.resolved`
   - Expect frames showing battle.started and battle.resolved
4) Check battle history
   - curl http://localhost:19080/v1/battles
   - Expect JSON with battle records from the battles table

Notes:
- Attack validation: fleets must be in the same system and belong to different empires
- Outcome (MVP): attacker supply -20, target supply -30; target destroyed if supply <= 0

