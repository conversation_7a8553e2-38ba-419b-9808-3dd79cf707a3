# Smoke: Comprehensive end-to-end scenario

Goal: exercise the full stack with movement, combat, and read models.

Pre-reqs:
- Dev stack running: `make up`
- Seeds loaded (5 systems, 4 fleets across 2 empires)

Scenario:
1) **World overview**
   - `curl http://localhost:19080/v1/systems`
   - `curl http://localhost:19080/v1/fleets`
   - Observe: 5 systems with neighbor counts, 4 fleets distributed

2) **Movement and positioning**
   - Move fleet-3 from sys-3 to sys-2: `curl -X POST http://localhost:19080/v1/orders -H 'content-type: application/json' -d '{"kind":"move","payload":{"fleetId":"fleet-3","toSystemId":"sys-2"}}'`
   - Check fleets by empire: `curl http://localhost:19080/v1/empires/emp-1/fleets`

3) **Combat**
   - Attack fleet-2 with fleet-3: `curl -X POST http://localhost:19080/v1/orders -H 'content-type: application/json' -d '{"kind":"attack","payload":{"fleetId":"fleet-3","targetFleetId":"fleet-2"}}'`
   - Watch events: `websocat -t ws://localhost:19080/v1/stream?topics=battle.started,battle.resolved`

4) **Battle history**
   - `curl http://localhost:19080/v1/battles`
   - Observe: battle record with supply changes and destruction flag

5) **Strategic overview**
   - Check neighbors of contested system: `curl http://localhost:19080/v1/systems/sys-2/neighbors`
   - Review empire assets: `curl http://localhost:19080/v1/empires/emp-2/fleets`

Expected outcome: Complete demonstration of movement validation, combat resolution, event streaming, and read-model queries across a connected 5-system world.
