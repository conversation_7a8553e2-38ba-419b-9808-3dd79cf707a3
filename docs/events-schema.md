# Event schema and subscriptions (v1)

Dispatcher forwards JSON frames over WebSocket. Minimal frame contract:
- topic (string): logical event name, e.g., order.receipt, order.applied, order.rejected, fleet.moved, fleet.resupplied, battle.started, battle.resolved
- payload (object): event data, backwards compatible with existing publishers

Publishers MAY send raw payloads (without wrapping into {topic,payload}); dispatcher will still forward frames and attempt basic filtering by inspecting payload.topic or payload.type when present. If neither is present or JSON parse fails, frames are broadcast to all clients.

WebSocket filtering
- Clients may connect with query ?topics=order.receipt,fleet.moved
- If provided, dispatcher only forwards frames whose parsed topic matches the allowed set
- If no topics param, all frames are forwarded

Future extensions
- Versioned envelope: { version: "1", topic, payload, ts }
- Per-empire filters and auth hooks

