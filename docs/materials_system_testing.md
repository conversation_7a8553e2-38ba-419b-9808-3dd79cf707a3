# Materials System Testing Documentation

## Overview
This document describes the comprehensive testing strategy for the Galactic Genesis Materials System, including smoke tests, Cypress E2E tests, and Playwright cross-browser tests.

## 🧪 Test Suite Structure

### 1. Smoke Tests
**Location**: `tests/smoke/materials-system-smoke.js`
**Purpose**: Quick validation of basic functionality and service connectivity

#### Test Coverage:
- ✅ **Materials Service Health** - Verifies materials microservice is running
- ✅ **API Gateway Endpoints** - Tests all materials API endpoints through gateway
- ✅ **Database Schema** - Validates database tables and data integrity
- ✅ **Material Distribution** - Checks realistic material distribution patterns
- ✅ **Order Creation** - Tests survey and construction order creation

#### Running Smoke Tests:
```bash
# Run materials smoke tests only
node tests/smoke/materials-system-smoke.js

# Run all smoke tests including materials
node tests/smoke/health-check.js
```

### 2. Cypress E2E Tests
**Location**: `cypress/e2e/materials-system.cy.js`
**Purpose**: End-to-end testing of user workflows and UI interactions

#### Test Coverage:
- ✅ **Materials Management UI** - Component rendering and navigation
- ✅ **Material Categories Display** - Color coding and visual indicators
- ✅ **Deposits Table Functionality** - Data display and richness indicators
- ✅ **Station Construction Workflow** - Form validation and submission
- ✅ **Survey Orders** - Material discovery workflows
- ✅ **Error Handling** - API error responses and user feedback
- ✅ **Responsive Design** - Mobile and tablet compatibility
- ✅ **Performance** - Load times and large dataset handling

#### Running Cypress Tests:
```bash
# Headless mode
npx cypress run --spec "cypress/e2e/materials-system.cy.js"

# Interactive mode
npx cypress open --e2e
```

### 3. Playwright Cross-Browser Tests
**Location**: `tests/playwright/materials-system.spec.js`
**Purpose**: Cross-browser compatibility and advanced testing scenarios

#### Test Coverage:
- ✅ **Multi-Browser Support** - Chrome, Firefox, Safari, Edge
- ✅ **Mobile Testing** - iOS Safari, Android Chrome
- ✅ **Materials Interface** - Cross-browser UI consistency
- ✅ **Station Construction** - Form behavior across browsers
- ✅ **Error Handling** - Consistent error display
- ✅ **Performance Testing** - Load times and responsiveness
- ✅ **Accessibility** - Basic accessibility checks

#### Supported Browsers:
- **Desktop**: Chrome, Firefox, Safari, Edge
- **Mobile**: iOS Safari, Android Chrome

#### Running Playwright Tests:
```bash
# All browsers headless
npx playwright test tests/playwright/materials-system.spec.js

# Specific browser
npx playwright test tests/playwright/materials-system.spec.js --project=chromium

# With UI
npx playwright test tests/playwright/materials-system.spec.js --ui

# Generate report
npx playwright show-report
```

## 🚀 Comprehensive Test Runner

### Quick Start
```bash
# Run all materials system tests
./scripts/run-materials-tests.sh

# Run specific test types
./scripts/run-materials-tests.sh --no-cypress --no-playwright  # Smoke only
./scripts/run-materials-tests.sh --no-smoke --no-playwright    # Cypress only
./scripts/run-materials-tests.sh --no-smoke --no-cypress       # Playwright only

# Run in headed mode (with browser UI)
./scripts/run-materials-tests.sh --headed
```

### Test Runner Features:
- ✅ **Service Health Checks** - Verifies all required services are running
- ✅ **Dependency Management** - Automatically installs test dependencies
- ✅ **Parallel Execution** - Runs compatible tests in parallel
- ✅ **Comprehensive Reporting** - Generates detailed test reports
- ✅ **Error Diagnostics** - Provides troubleshooting recommendations

## 📋 Test Data and Fixtures

### Cypress Fixtures
**Location**: `cypress/fixtures/`
- `large-deposits-dataset.json` - Mock data for performance testing

### Test Data Requirements:
- **Database**: Materials system tables must be populated
- **API Services**: Materials service and API gateway must be running
- **Frontend**: React application must be accessible

## 🔧 Configuration

### Environment Variables
```bash
# Service URLs
FRONTEND_URL=http://localhost:5174
API_BASE_URL=http://localhost:19081
MATERIALS_SVC_URL=http://localhost:8086

# Database
DB_HOST=localhost
DB_PORT=5433
DB_NAME=galactic_genesis
DB_USER=postgres

# Test Configuration
HEADLESS=true
RUN_SMOKE=true
RUN_CYPRESS=true
RUN_PLAYWRIGHT=true
```

### Playwright Configuration
**File**: `playwright.config.js`
- **Browsers**: Chrome, Firefox, Safari, Edge, Mobile
- **Timeouts**: 30s test, 10s action, 30s navigation
- **Artifacts**: Screenshots, videos, traces on failure
- **Parallel**: Full parallel execution
- **Retries**: 2 retries on CI

## 🎯 Test Scenarios

### Critical User Journeys
1. **Material Discovery**
   - Navigate to materials manager
   - View available material types
   - Check material categories and properties

2. **Deposit Exploration**
   - Switch to deposits tab
   - View material deposits table
   - Verify richness color coding
   - Check discovery status

3. **Station Management**
   - View existing space stations
   - Check station status and storage
   - Navigate station details

4. **Station Construction**
   - Open construction form
   - Select station type
   - Configure orbital parameters
   - Submit construction order
   - Verify success feedback

### Error Scenarios
1. **API Failures**
   - Service unavailable
   - Network timeouts
   - Invalid responses

2. **Form Validation**
   - Missing required fields
   - Invalid input values
   - Constraint violations

3. **Data Loading**
   - Empty datasets
   - Large datasets
   - Slow responses

## 📊 Test Metrics and KPIs

### Performance Benchmarks
- **Page Load**: < 3 seconds (Cypress), < 5 seconds (Playwright)
- **API Response**: < 2 seconds for data endpoints
- **Form Submission**: < 3 seconds for order creation
- **Large Dataset**: Handle 1000+ items without performance degradation

### Coverage Targets
- **API Endpoints**: 100% of materials system endpoints
- **UI Components**: All major components and interactions
- **User Workflows**: All critical user journeys
- **Error Paths**: All error conditions and edge cases
- **Browser Support**: 95%+ compatibility across target browsers

## 🐛 Debugging and Troubleshooting

### Common Issues
1. **Service Not Running**
   ```bash
   # Check service status
   curl http://localhost:8086/health
   curl http://localhost:19081/v1/health
   
   # Start services
   cd services/materials-svc && npm start
   cd services/api-gateway && npm start
   ```

2. **Database Issues**
   ```bash
   # Check database connection
   psql -h localhost -p 5433 -U postgres -d galactic_genesis -c "SELECT 1;"
   
   # Run migrations
   psql -f db/sql/015_raw_materials_system.sql
   ```

3. **Test Failures**
   - Check browser console for JavaScript errors
   - Verify API responses in network tab
   - Review test artifacts (screenshots, videos)
   - Check service logs for backend errors

### Debug Commands
```bash
# Verbose test output
DEBUG=cypress:* npx cypress run
DEBUG=pw:* npx playwright test

# Generate detailed reports
npx playwright test --reporter=html
npx cypress run --reporter mochawesome
```

## 🔄 Continuous Integration

### CI Pipeline Integration
```yaml
# Example GitHub Actions workflow
- name: Run Materials System Tests
  run: |
    # Start services
    docker-compose up -d
    
    # Wait for services
    ./scripts/wait-for-services.sh
    
    # Run tests
    ./scripts/run-materials-tests.sh
    
    # Collect artifacts
    mkdir -p artifacts
    cp -r test-results/* artifacts/
    cp -r cypress/screenshots artifacts/ || true
    cp -r cypress/videos artifacts/ || true
```

### Test Reporting
- **JUnit XML**: For CI integration
- **HTML Reports**: For detailed analysis
- **Screenshots/Videos**: For failure investigation
- **Coverage Reports**: For code coverage tracking

## 📈 Future Enhancements

### Planned Improvements
1. **Visual Regression Testing** - Screenshot comparison
2. **API Contract Testing** - OpenAPI validation
3. **Load Testing** - Performance under load
4. **Accessibility Testing** - WCAG compliance
5. **Security Testing** - Input validation and XSS prevention

### Test Automation
- **Scheduled Runs**: Nightly regression tests
- **PR Validation**: Automated testing on pull requests
- **Performance Monitoring**: Continuous performance tracking
- **Alert System**: Notifications for test failures

This comprehensive testing strategy ensures the Materials System is robust, reliable, and provides an excellent user experience across all supported platforms and browsers.
