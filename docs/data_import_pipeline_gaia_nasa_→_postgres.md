# Space 4X — Data Import Pipeline (Gaia + NASA Exoplanet → Postgres)

**Purpose:** End‑to‑end, scriptable pipeline to harvest public astronomy datasets and populate your game DB with **stars, planets, positions, sizes, and compositions**. Designed for **zero license cost** and easy use by your Augment Code assistant in VS Code.

**Datasets**
- **Gaia DR3** (ESA): accurate star positions (RA, Dec, parallax), proper motions, magnitudes.
- **NASA Exoplanet Archive**: confirmed exoplanets + host star params (radii, masses, orbital elements, equilibrium temps, discovery methods).
- **CELESTA (optional)**: habitable zone estimates for nearby stars (use to seed plausible colonizable targets when no planets known).

**Tech stack**
- Language: **Python 3.11+** (requests, pandas, astropy, psycopg2, sqlalchemy)
- DB: **PostgreSQL 16** (open‑source)
- Format: CSV/JSON over HTTPS; no special SDKs required

> Migration‑friendly: Python ETL can be containerized later; schema aligns with the ERD we created.

---

## 📂 Files in this package (with paths)

1) **`/sql/001_schema.sql`** — *Database schema for stars, planets, and indexes*.  
2) **`/ingestion/.env.example`** — *DB connection template*.  
3) **`/ingestion/requirements.txt`** — *Pinned Python deps for reproducible runs*.  
4) **`/ingestion/config.yaml`** — *Dataset endpoints + query windows*.  
5) **`/ingestion/run_pipeline.py`** — *Orchestrator that calls all steps end‑to‑end*.  
6) **`/ingestion/gaia_import.py`** — *Downloads Gaia stars (cone/box query), normalizes, loads to DB*.  
7) **`/ingestion/nasa_exoplanet_import.py`** — *Downloads NASA Exoplanet Archive (pscomppars), normalizes, loads to DB*.  
8) **`/ingestion/celesta_import.py`** *(optional)* — *Loads CELESTA CSV if present*.  
9) **`/ingestion/join_enrich.py`** — *Crossmatches planets→host stars (by name & by sky‑position), computes distances & derived fields*.  
10) **`/ingestion/procedural_generate.py`** — *Back‑fills plausible planets for stars with no known worlds*.  

Each file below includes: **intro, linked files, and per‑function docstrings** per your standards.

---

## 1) `/sql/001_schema.sql`
**Intro:** Core tables + staging tables + indexes. Mirrors the ERD in `system-architecture-and-erd.md` and is safe to run repeatedly. Adds UPSERT constraints.

```sql
-- 001_schema.sql — Stars & Planets base schema
-- Purpose: normalized core tables + staging, with idempotent creation.

BEGIN;

CREATE TABLE IF NOT EXISTS stars (
  star_id           BIGSERIAL PRIMARY KEY,
  src               TEXT NOT NULL DEFAULT 'unknown',  -- 'gaia', 'nasa', 'celesta'
  src_key           TEXT,                              -- e.g., Gaia DR3 source_id
  name              TEXT,
  ra_deg            DOUBLE PRECISION NOT NULL,         -- Right Ascension (ICRS degrees)
  dec_deg           DOUBLE PRECISION NOT NULL,         -- Declination (ICRS degrees)
  distance_ly       DOUBLE PRECISION,                  -- derived from parallax
  parallax_mas      DOUBLE PRECISION,
  pm_ra_masyr       DOUBLE PRECISION,
  pm_dec_masyr      DOUBLE PRECISION,
  mag_g             DOUBLE PRECISION,
  spectral_type     TEXT,
  mass_solar        DOUBLE PRECISION,
  radius_solar      DOUBLE PRECISION,
  teff_k            DOUBLE PRECISION,
  metallicity_fe_h  DOUBLE PRECISION,
  created_at        TIMESTAMPTZ DEFAULT now(),
  UNIQUE (src, src_key)
);

CREATE TABLE IF NOT EXISTS planets (
  planet_id         BIGSERIAL PRIMARY KEY,
  star_id           BIGINT REFERENCES stars(star_id) ON DELETE CASCADE,
  src               TEXT NOT NULL DEFAULT 'unknown',   -- 'nasa', 'proc', 'celesta'
  src_key           TEXT,
  name              TEXT,
  mass_earth        DOUBLE PRECISION,
  radius_earth      DOUBLE PRECISION,
  sma_au            DOUBLE PRECISION,                  -- semi-major axis
  period_days       DOUBLE PRECISION,
  ecc               DOUBLE PRECISION,
  eq_temp_k         DOUBLE PRECISION,
  composition       TEXT,                              -- rocky, gas_giant, ice, unknown
  discovery_method  TEXT,
  discovery_year    INT,
  created_at        TIMESTAMPTZ DEFAULT now()
);

-- Indices for spatial-ish queries
CREATE INDEX IF NOT EXISTS idx_stars_radec ON stars (ra_deg, dec_deg);
CREATE INDEX IF NOT EXISTS idx_planets_star ON planets (star_id);
CREATE INDEX IF NOT EXISTS idx_planets_name ON planets (name);

COMMIT;
```

---

## 2) `/ingestion/.env.example`
**Intro:** Connection variables for local/dev. Copy to `.env` and edit.

```dotenv
# PostgreSQL connection
PGHOST=127.0.0.1
PGPORT=5432
PGDATABASE=space4x
PGUSER=space_user
PGPASSWORD=please_change_me

# Optional: HTTP proxy if your network requires it
HTTP_PROXY=
HTTPS_PROXY=
```

---

## 3) `/ingestion/requirements.txt`
**Intro:** Minimal, widely available libs with permissive licenses.

```text
pandas==2.2.2
requests==2.32.3
pyyaml==6.0.2
psycopg2-binary==2.9.9
sqlalchemy==2.0.32
astropy==6.0.1
tqdm==4.66.4
python-dotenv==1.0.1
```

---

## 4) `/ingestion/config.yaml`
**Intro:** Central config for endpoints, sizes, and joins. Tune without code changes.

```yaml
# Dataset windows & endpoints
nasa_exoplanet:
  # pscomppars returns one row per planet with many joined host-star fields
  url: "https://exoplanetarchive.ipac.caltech.edu/TAP/sync?query=select+*+from+pscomppars&format=csv"

gaia:
  # Use ADQL via Gaia TAP; for prototype, use a small RA/Dec box around Sol
  # You can later replace with a full DR3 subset you host yourself.
  tap_url: "https://gea.esac.esa.int/tap-server/tap/sync"
  adql_box: |
    SELECT TOP 50000 source_id, ra, dec, parallax, pmra, pmdec, phot_g_mean_mag
    FROM gaiadr3.gaia_source
    WHERE ra BETWEEN 0 AND 60 AND dec BETWEEN -30 AND 30

celesta:
  enabled: false
  csv_path: "./CELESTA.csv"  # put file here if you have it

join:
  # sky match threshold for RA/Dec crossmatch (arcseconds)
  arcsec_threshold: 1.0

procedural:
  # generate up to N planets for stars with none
  max_planets_per_star: 3
```

---

## 5) `/ingestion/run_pipeline.py`
**Intro:** Orchestrator; runs steps in correct order. Can be cron’d.  
**Links:** Calls modules in `ingestion/*`, loads `.env`, reads `config.yaml`.

```python
#!/usr/bin/env python3
"""
run_pipeline.py — Orchestrate ETL: Gaia → NASA → CELESTA → Join/Enrich → Procedural → Load

Usage:
  python run_pipeline.py --steps all
  python run_pipeline.py --steps gaia,nasa,join,proc

This script:
  1) Ensures DB schema exists
  2) Downloads/normalizes datasets
  3) Loads to Postgres
  4) Crossmatches planets to stars
  5) Optionally fills missing planets procedurally
"""
from __future__ import annotations
import argparse, os, subprocess, sys
from pathlib import Path
import yaml
from dotenv import load_dotenv

ROOT = Path(__file__).resolve().parent
SQL_SCHEMA = ROOT.parent / "sql/001_schema.sql"

STEPS = ["gaia", "nasa", "celesta", "join", "proc"]


def sh(cmd: list[str]) -> None:
    """Run a shell command and stream output; raise on nonzero.
    Used for psql invocations so Augment Code can keep it simple."""
    print("$", " ".join(cmd))
    rc = subprocess.call(cmd)
    if rc != 0:
        raise SystemExit(rc)


def ensure_schema() -> None:
    """Create DB schema if missing using psql and 001_schema.sql."""
    psql = os.environ.get("PSQL", "psql")
    sh([psql, "-h", os.environ["PGHOST"], "-p", os.environ.get("PGPORT", "5432"),
        "-U", os.environ["PGUSER"], "-d", os.environ["PGDATABASE"],
        "-v", "ON_ERROR_STOP=1", "-f", str(SQL_SCHEMA)])


def main() -> None:
    load_dotenv(ROOT / ".env")
    parser = argparse.ArgumentParser()
    parser.add_argument("--steps", default="all", help="csv of steps: gaia,nasa,celesta,join,proc or 'all'")
    args = parser.parse_args()

    with open(ROOT / "config.yaml", "r", encoding="utf-8") as f:
        cfg = yaml.safe_load(f)

    ensure_schema()

    steps = STEPS if args.steps == "all" else args.steps.split(",")

    if "gaia" in steps:
        import gaia_import
        gaia_import.run(cfg)

    if "nasa" in steps:
        import nasa_exoplanet_import
        nasa_exoplanet_import.run(cfg)

    if cfg.get("celesta", {}).get("enabled") and "celesta" in steps:
        import celesta_import
        celesta_import.run(cfg)

    if "join" in steps:
        import join_enrich
        join_enrich.run(cfg)

    if "proc" in steps:
        import procedural_generate
        procedural_generate.run(cfg)


if __name__ == "__main__":
    main()
```

---

## 6) `/ingestion/gaia_import.py`
**Intro:** Queries the Gaia TAP service using the ADQL snippet in `config.yaml`, normalizes to our `stars` schema, computes distances, and UPSERTs rows.

```python
"""gaia_import.py — Fetch a Gaia DR3 subset and load into Postgres.

Functions:
  fetch_gaia_csv(cfg) -> str: downloads a CSV string via TAP sync endpoint.
  normalize_gaia(df) -> pd.DataFrame: maps columns and derives distance_ly.
  upsert_stars(df): writes to stars table with ON CONFLICT (src, src_key).
"""
from __future__ import annotations
import os
import requests
import pandas as pd
from io import StringIO
from sqlalchemy import create_engine, text

SRC = "gaia"


def _engine():
    url = f"postgresql+psycopg2://{os.environ['PGUSER']}:{os.environ['PGPASSWORD']}@{os.environ['PGHOST']}:{os.environ.get('PGPORT','5432')}/{os.environ['PGDATABASE']}"
    return create_engine(url)


def fetch_gaia_csv(cfg) -> str:
    tap_url = cfg["gaia"]["tap_url"]
    adql = cfg["gaia"]["adql_box"]
    resp = requests.post(tap_url, data={"REQUEST": "doQuery", "LANG": "ADQL", "FORMAT": "csv", "QUERY": adql}, timeout=120)
    resp.raise_for_status()
    return resp.text


def normalize_gaia(df: pd.DataFrame) -> pd.DataFrame:
    # Parallax in milliarcseconds → distance parsec = 1000 / parallax_mas; ly = pc * 3.26156
    df = df.rename(columns={"ra": "ra_deg", "dec": "dec_deg", "parallax": "parallax_mas", "pmra": "pm_ra_masyr", "pmdec": "pm_dec_masyr", "phot_g_mean_mag": "mag_g"})
    df["distance_ly"] = (1000.0 / df["parallax_mas"]).clip(lower=0.0001) * 3.26156
    df["src"] = SRC
    df["src_key"] = df.get("source_id")
    keep = ["src", "src_key", "ra_deg", "dec_deg", "parallax_mas", "pm_ra_masyr", "pm_dec_masyr", "mag_g", "distance_ly"]
    return df[keep]


def upsert_stars(df: pd.DataFrame) -> None:
    eng = _engine()
    with eng.begin() as cx:
        # load to temp
        df.to_sql("_stars_stage", cx, if_exists="replace", index=False)
        cx.execute(text(
            """
            INSERT INTO stars (src, src_key, ra_deg, dec_deg, parallax_mas, pm_ra_masyr, pm_dec_masyr, mag_g, distance_ly)
            SELECT src, CAST(src_key AS TEXT), ra_deg, dec_deg, parallax_mas, pm_ra_masyr, pm_dec_masyr, mag_g, distance_ly
            FROM _stars_stage
            ON CONFLICT (src, src_key) DO UPDATE SET
              ra_deg = EXCLUDED.ra_deg,
              dec_deg = EXCLUDED.dec_deg,
              parallax_mas = EXCLUDED.parallax_mas,
              pm_ra_masyr = EXCLUDED.pm_ra_masyr,
              pm_dec_masyr = EXCLUDED.pm_dec_masyr,
              mag_g = EXCLUDED.mag_g,
              distance_ly = EXCLUDED.distance_ly;
            DROP TABLE _stars_stage;
            """
        ))


def run(cfg) -> None:
    csv = fetch_gaia_csv(cfg)
    df = pd.read_csv(StringIO(csv))
    if df.empty:
        print("Gaia query returned 0 rows — check config.adql_box")
        return
    df = normalize_gaia(df)
    upsert_stars(df)
    print(f"Loaded {len(df)} Gaia stars")
```

---

## 7) `/ingestion/nasa_exoplanet_import.py`
**Intro:** Pulls the `pscomppars` table (one row per planet) via NASA Exoplanet Archive, maps fields to our schema, and UPSERTs into `planets` (host star linked later during join phase).

```python
"""nasa_exoplanet_import.py — Load confirmed exoplanets.

Functions:
  fetch_pscomppars(cfg) -> str: downloads CSV from NASA Exoplanet Archive.
  normalize_nasa(df) -> pd.DataFrame: maps to our planet fields, keeps host star names/coords for later join.
  stage_planets(df): loads stage table for join step.
"""
from __future__ import annotations
import os
import requests
import pandas as pd
from io import StringIO
from sqlalchemy import create_engine

SRC = "nasa"


def _engine():
    url = f"postgresql+psycopg2://{os.environ['PGUSER']}:{os.environ['PGPASSWORD']}@{os.environ['PGHOST']}:{os.environ.get('PGPORT','5432')}/{os.environ['PGDATABASE']}"
    return create_engine(url)


def fetch_pscomppars(cfg) -> str:
    url = cfg["nasa_exoplanet"]["url"]
    r = requests.get(url, timeout=180)
    r.raise_for_status()
    return r.text


def normalize_nasa(df: pd.DataFrame) -> pd.DataFrame:
    # Columns reference: https://exoplanetarchive.ipac.caltech.edu/docs/API_PS_columns.html
    m = {
        "pl_name": "name",
        "pl_rade": "radius_earth",
        "pl_bmasse": "mass_earth",
        "pl_orbsmax": "sma_au",
        "pl_orbper": "period_days",
        "pl_orbeccen": "ecc",
        "pl_eqt": "eq_temp_k",
        "discoverymethod": "discovery_method",
        "disc_year": "discovery_year",
        # host star context for later crossmatch
        "hostname": "host_name",
        "ra": "host_ra_deg",
        "dec": "host_dec_deg",
    }
    keep = [k for k in m.keys() if k in df.columns]
    df = df[keep].rename(columns=m)
    df["src"] = SRC
    return df


def stage_planets(df: pd.DataFrame) -> None:
    eng = _engine()
    with eng.begin() as cx:
        df.to_sql("_nasa_planets_stage", cx, if_exists="replace", index=False)


def run(cfg) -> None:
    csv = fetch_pscomppars(cfg)
    df = pd.read_csv(StringIO(csv))
    if df.empty:
        print("NASA pscomppars returned 0 rows")
        return
    df = normalize_nasa(df)
    stage_planets(df)
    print(f"Staged {len(df)} NASA planets for join")
```

---

## 8) `/ingestion/celesta_import.py` (optional)
**Intro:** Loads CELESTA CSV (if you have it) for habitable zone ranges; attaches to stars by sky position or identifier.

```python
from __future__ import annotations
import os
import pandas as pd
from sqlalchemy import create_engine

SRC = "celesta"


def _engine():
    url = f"postgresql+psycopg2://{os.environ['PGUSER']}:{os.environ['PGPASSWORD']}@{os.environ['PGHOST']}:{os.environ.get('PGPORT','5432')}/{os.environ['PGDATABASE']}"
    return create_engine(url)


def run(cfg) -> None:
    path = cfg["celesta"]["csv_path"]
    if not os.path.exists(path):
        print("CELESTA not found — skipping")
        return
    df = pd.read_csv(path)
    # Expect columns like: RA, Dec, Teff, Lum, HZ_inner, HZ_outer
    df = df.rename(columns={"RA": "ra_deg", "Dec": "dec_deg"})
    with _engine().begin() as cx:
        df.to_sql("_celesta_stage", cx, if_exists="replace", index=False)
    print(f"Staged {len(df)} CELESTA rows")
```

---

## 9) `/ingestion/join_enrich.py`
**Intro:** Crossmatches staged NASA planets to `stars` table via **(A) host name** and **(B) sky proximity** within configurable arcseconds. Inserts/updates `planets` with linked `star_id`. Adds derived composition guess (rocky/gas) if missing.

```python
from __future__ import annotations
import math, os
import pandas as pd
from sqlalchemy import create_engine, text

THRESH_ARCSEC_DEFAULT = 1.0


def _engine():
    url = f"postgresql+psycopg2://{os.environ['PGUSER']}:{os.environ['PGPASSWORD']}@{os.environ['PGHOST']}:{os.environ.get('PGPORT','5432')}/{os.environ['PGDATABASE']}"
    return create_engine(url)


def _degsep(ra1, dec1, ra2, dec2):
    """Approx angular separation in arcsec (sufficient for small deltas)."""
    # Haversine on sphere; ra/dec in degrees
    from math import radians, sin, cos, asin, sqrt
    ra1, dec1, ra2, dec2 = map(radians, [ra1, dec1, ra2, dec2])
    d = 2 * asin(sqrt((sin((dec2-dec1)/2))**2 + cos(dec1)*cos(dec2)*(sin((ra2-ra1)/2))**2))
    return d * (180/math.pi) * 3600.0  # arcsec


def _guess_composition(row):
    r = row.get("radius_earth")
    m = row.get("mass_earth")
    if pd.isna(r) and pd.isna(m):
        return "unknown"
    if pd.notna(r) and r < 1.8:
        return "rocky"
    if pd.notna(r) and r > 3.5:
        return "gas_giant"
    return "sub_neptune"


def run(cfg) -> None:
    eng = _engine()
    arcsec = float(cfg.get("join", {}).get("arcsec_threshold", THRESH_ARCSEC_DEFAULT))
    with eng.begin() as cx:
        nasa = pd.read_sql("SELECT * FROM _nasa_planets_stage", cx)
        stars = pd.read_sql("SELECT star_id, name, ra_deg, dec_deg FROM stars", cx)

    # name-based quick join if host_name matches star.name
    merged = nasa.merge(stars, left_on="host_name", right_on="name", how="left", suffixes=("", "_star"))

    # position-based fixups for rows without star_id
    need_pos = merged[merged["star_id"].isna()].copy()
    if not need_pos.empty:
        # naive nearest neighbor scan (fine for prototype)
        matched_ids = []
        for _, row in need_pos.iterrows():
            ra, dec = row.get("host_ra_deg"), row.get("host_dec_deg")
            if pd.isna(ra) or pd.isna(dec):
                matched_ids.append(None)
                continue
            # compute separations
            stars["sep_arcsec"] = stars.apply(lambda s: _degsep(ra, dec, s["ra_deg"], s["dec_deg"]), axis=1)
            best = stars.nsmallest(1, "sep_arcsec").iloc[0]
            matched_ids.append(best["star_id"] if best["sep_arcsec"] <= arcsec else None)
        need_pos["star_id"] = matched_ids
        merged.update(need_pos["star_id"])  # fill back

    # Prepare final planets frame
    cols = [
        "star_id","name","mass_earth","radius_earth","sma_au","period_days","ecc","eq_temp_k","discovery_method","discovery_year"
    ]
    out = merged.rename(columns={
        "sma_au": "sma_au",
        "period_days": "period_days",
        "ecc": "ecc",
        "eq_temp_k": "eq_temp_k",
        "discovery_method": "discovery_method",
        "discovery_year": "discovery_year",
    })
    out["composition"] = merged.apply(_guess_composition, axis=1)
    out["src"] = "nasa"

    out = out[ ["star_id","src","name","mass_earth","radius_earth","sma_au","period_days","ecc","eq_temp_k","composition","discovery_method","discovery_year"] ]

    with eng.begin() as cx:
        out.to_sql("_planets_stage_joined", cx, if_exists="replace", index=False)
        cx.execute(text(
            """
            INSERT INTO planets (star_id, src, name, mass_earth, radius_earth, sma_au, period_days, ecc, eq_temp_k, composition, discovery_method, discovery_year)
            SELECT star_id, src, name, mass_earth, radius_earth, sma_au, period_days, ecc, eq_temp_k, composition, discovery_method, discovery_year
            FROM _planets_stage_joined
            ON CONFLICT (star_id, name) DO NOTHING;
            DROP TABLE _planets_stage_joined;
            DROP TABLE IF EXISTS _nasa_planets_stage;
            """
        ))
    print("Joined and loaded planets → planets table")
```

---

## 10) `/ingestion/procedural_generate.py`
**Intro:** Adds plausible planets to stars with none, based on simple rules for **spectral type** and brightness. This ensures the map is rich even beyond known exoplanets. Marked with `src='proc'` for transparency.

```python
from __future__ import annotations
import os, random
import pandas as pd
from sqlalchemy import create_engine, text

random.seed(42)


def _engine():
    url = f"postgresql+psycopg2://{os.environ['PGUSER']}:{os.environ['PGPASSWORD']}@{os.environ['PGHOST']}:{os.environ.get('PGPORT','5432')}/{os.environ['PGDATABASE']}"
    return create_engine(url)


RULES = {
    # spectral hint → distribution of planet types (weights)
    "G": {"rocky": 0.5, "sub_neptune": 0.3, "gas_giant": 0.2},
    "K": {"rocky": 0.6, "sub_neptune": 0.3, "gas_giant": 0.1},
    "M": {"rocky": 0.7, "sub_neptune": 0.25, "gas_giant": 0.05},
    "A": {"rocky": 0.3, "sub_neptune": 0.4, "gas_giant": 0.3},
}


def pick_composition(spec: str) -> str:
    spec = (spec or "G").upper()
    key = next((k for k in RULES.keys() if spec.startswith(k)), "G")
    dist = RULES[key]
    r = random.random()
    acc = 0.0
    for kind, w in dist.items():
        acc += w
        if r <= acc:
            return kind
    return "rocky"


def run(cfg) -> None:
    eng = _engine()
    maxp = int(cfg.get("procedural", {}).get("max_planets_per_star", 2))
    with eng.begin() as cx:
        stars = pd.read_sql("SELECT star_id, spectral_type, mag_g FROM stars", cx)
        counts = pd.read_sql("SELECT star_id, COUNT(*) AS n FROM planets GROUP BY star_id", cx)
    with_planets = set(counts[counts["n"] > 0]["star_id"].astype(int).tolist())
    targets = [s for s in stars.to_dict("records") if s["star_id"] not in with_planets]

    rows = []
    for s in targets:
        n = random.randint(1, max(1, maxp))
        for i in range(n):
            comp = pick_composition(s.get("spectral_type"))
            if comp == "rocky":
                radius = round(random.uniform(0.5, 1.8), 2)
                mass = round(radius ** 3, 2)
            elif comp == "gas_giant":
                radius = round(random.uniform(4, 12), 1)
                mass = round(random.uniform(30, 300), 1)
            else:  # sub_neptune
                radius = round(random.uniform(1.8, 3.5), 2)
                mass = round(random.uniform(5, 20), 1)
            period = round(random.uniform(10, 1500), 1)
            sma = round(random.uniform(0.05, 20.0), 3)
            name = f"{s['star_id']}-p{i+1}"
            rows.append({
                "star_id": s["star_id"],
                "src": "proc",
                "name": name,
                "mass_earth": mass,
                "radius_earth": radius,
                "sma_au": sma,
                "period_days": period,
                "ecc": round(random.uniform(0, 0.4), 2),
                "eq_temp_k": None,
                "composition": comp,
                "discovery_method": "procedural",
                "discovery_year": None,
            })

    if not rows:
        print("No procedural planets added (all stars already have planets)")
        return

    out = pd.DataFrame(rows)
    eng.begin().connection
    with eng.begin() as cx:
        out.to_sql("_proc_planets_stage", cx, if_exists="replace", index=False)
        cx.execute(text(
            """
            INSERT INTO planets (star_id, src, name, mass_earth, radius_earth, sma_au, period_days, ecc, eq_temp_k, composition, discovery_method, discovery_year)
            SELECT star_id, src, name, mass_earth, radius_earth, sma_au, period_days, ecc, eq_temp_k, composition, discovery_method, discovery_year
            FROM _proc_planets_stage;
            DROP TABLE _proc_planets_stage;
            """
        ))
    print(f"Inserted {len(out)} procedural planets")
```

---

## 🧪 How to run (local)
1. **Create DB** and apply schema:
   ```bash
   createdb space4x
   psql -d space4x -f sql/001_schema.sql
   ```
2. **Setup env & deps**:
   ```bash
   cd ingestion
   cp .env.example .env  # edit with your Postgres creds
   python -m venv .venv && source .venv/bin/activate
   pip install -r requirements.txt
   ```
3. **Run full pipeline**:
   ```bash
   python run_pipeline.py --steps all
   ```

**Security note:** Secrets live in `.env` only; never commit it. This ETL performs only **GET/POST** to public datasets and **INSERT/UPSERT** to your DB.

---

## ✅ Next steps
- Add a **Dockerfile + docker-compose** for one‑command local runs (PG + ETL).
- Expand Gaia query window beyond the example RA/Dec box.
- Add **indexes** for nearest‑neighbor (e.g., PostGIS or q3c) if you need fast sky matches galaxy‑wide.
- Wire the game client to fetch systems/planets from Postgres via your API.

