#!/bin/bash

# SSL Certificate Creation and Implementation for star.omnilyzer.ai
# This script creates and implements SSL certificate with proper HTTPS configuration

set -e

echo "🔒 Creating SSL Certificate for star.omnilyzer.ai..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    print_error "This script must be run with sudo"
    echo "Usage: sudo ./create-ssl-certificate.sh"
    exit 1
fi

print_status "Starting SSL certificate setup process..."

# Step 1: Install temporary HTTP configuration
print_status "Installing temporary HTTP configuration..."
cp star-temp.nginx.conf /etc/nginx/sites-available/star-temp.conf
ln -sf /etc/nginx/sites-available/star-temp.conf /etc/nginx/sites-enabled/star-temp.conf

# Remove any existing star.conf to avoid conflicts
if [ -f "/etc/nginx/sites-enabled/star.conf" ]; then
    rm -f /etc/nginx/sites-enabled/star.conf
fi

# Test nginx configuration
nginx -t
if [ $? -eq 0 ]; then
    print_success "Temporary nginx configuration is valid"
else
    print_error "Nginx configuration test failed"
    exit 1
fi

# Reload nginx with temporary config
systemctl reload nginx
print_success "Nginx reloaded with temporary HTTP configuration"

# Step 2: Test HTTP access
print_status "Testing HTTP access..."
sleep 2
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://star.omnilyzer.ai/health || echo "000")
if [ "$HTTP_STATUS" = "200" ]; then
    print_success "HTTP site is accessible (Status: $HTTP_STATUS)"
else
    print_warning "HTTP test returned status: $HTTP_STATUS - continuing anyway"
fi

# Step 3: Obtain SSL certificate
print_status "Obtaining SSL certificate from Let's Encrypt..."

# Check if certificate already exists
if [ -f "/etc/letsencrypt/live/star.omnilyzer.ai/fullchain.pem" ]; then
    print_warning "SSL certificate already exists for star.omnilyzer.ai"
    print_status "Checking certificate validity..."
    
    # Check if certificate is valid and not expired
    if openssl x509 -checkend 86400 -noout -in /etc/letsencrypt/live/star.omnilyzer.ai/cert.pem; then
        print_success "Existing certificate is valid"
        CERT_EXISTS=true
    else
        print_warning "Existing certificate is expired or invalid, obtaining new one"
        CERT_EXISTS=false
    fi
else
    CERT_EXISTS=false
fi

if [ "$CERT_EXISTS" = false ]; then
    # Try webroot method first (most reliable)
    print_status "Attempting certificate generation with webroot method..."
    
    if certbot certonly --webroot -w /var/www/star/frontend/dist -d star.omnilyzer.ai --email <EMAIL> --agree-tos --non-interactive; then
        print_success "SSL certificate obtained successfully with webroot method"
    else
        print_warning "Webroot method failed, trying nginx method..."
        
        if certbot certonly --nginx -d star.omnilyzer.ai --email <EMAIL> --agree-tos --non-interactive; then
            print_success "SSL certificate obtained successfully with nginx method"
        else
            print_error "Failed to obtain SSL certificate with both methods"
            print_error "Please check domain DNS and try manual certificate generation"
            exit 1
        fi
    fi
fi

# Step 4: Install HTTPS-only configuration
print_status "Installing HTTPS-only configuration..."
cp star.nginx.conf /etc/nginx/sites-available/star.conf

# Remove temporary configuration
rm -f /etc/nginx/sites-enabled/star-temp.conf
rm -f /etc/nginx/sites-available/star-temp.conf

# Enable HTTPS configuration
ln -sf /etc/nginx/sites-available/star.conf /etc/nginx/sites-enabled/star.conf

# Test nginx configuration
nginx -t
if [ $? -eq 0 ]; then
    print_success "HTTPS nginx configuration is valid"
else
    print_error "HTTPS nginx configuration test failed"
    exit 1
fi

# Step 5: Reload nginx with HTTPS configuration
print_status "Reloading nginx with HTTPS configuration..."
systemctl reload nginx
print_success "Nginx reloaded with HTTPS-only configuration"

# Step 6: Test HTTPS
print_status "Testing HTTPS connection..."
sleep 3

HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://star.omnilyzer.ai/health || echo "000")
if [ "$HTTPS_STATUS" = "200" ]; then
    print_success "HTTPS site is working! (Status: $HTTPS_STATUS)"
else
    print_warning "HTTPS test returned status: $HTTPS_STATUS"
    print_warning "Certificate might still be propagating, please wait a few minutes"
fi

# Step 7: Test HTTP to HTTPS redirect
print_status "Testing HTTP to HTTPS redirect..."
HTTP_REDIRECT=$(curl -s -o /dev/null -w "%{http_code}" http://star.omnilyzer.ai || echo "000")
if [ "$HTTP_REDIRECT" = "301" ] || [ "$HTTP_REDIRECT" = "302" ]; then
    print_success "HTTP to HTTPS redirect working (Status: $HTTP_REDIRECT)"
else
    print_warning "HTTP redirect test returned status: $HTTP_REDIRECT"
fi

# Step 8: Display certificate information
print_status "Certificate information:"
if [ -f "/etc/letsencrypt/live/star.omnilyzer.ai/cert.pem" ]; then
    CERT_EXPIRY=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/star.omnilyzer.ai/cert.pem | cut -d= -f2)
    print_success "Certificate expires: $CERT_EXPIRY"
    
    CERT_SUBJECT=$(openssl x509 -subject -noout -in /etc/letsencrypt/live/star.omnilyzer.ai/cert.pem | cut -d= -f2-)
    print_success "Certificate subject: $CERT_SUBJECT"
fi

# Final summary
echo ""
echo "🌟 =================================="
echo "🌟 SSL CERTIFICATE SETUP COMPLETE"
echo "🌟 =================================="
echo ""
print_success "✅ SSL certificate created and installed"
print_success "✅ HTTPS-only configuration active"
print_success "✅ HTTP to HTTPS redirect enabled"
print_success "✅ Security headers configured"
echo ""
echo -e "${GREEN}🎮 Galactic Genesis is now available at:${NC}"
echo -e "${BLUE}   https://star.omnilyzer.ai${NC}"
echo ""
echo -e "${YELLOW}🔒 Security Features Active:${NC}"
echo "   • TLS 1.2 & 1.3 encryption"
echo "   • HSTS (HTTP Strict Transport Security)"
echo "   • CSP (Content Security Policy)"
echo "   • XSS and clickjacking protection"
echo ""
echo -e "${GREEN}🚀 SSL setup completed successfully!${NC}"

# Set up auto-renewal reminder
print_status "Setting up certificate auto-renewal..."
if crontab -l 2>/dev/null | grep -q "certbot renew"; then
    print_success "Certificate auto-renewal already configured"
else
    print_warning "Consider adding certificate auto-renewal to crontab:"
    echo "0 12 * * * /usr/bin/certbot renew --quiet"
fi
