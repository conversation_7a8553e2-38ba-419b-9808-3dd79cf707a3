#!/bin/bash

# Galactic Genesis Game Deployment Script
# Deploys the game to https://star.omnilyzer.ai

set -e  # Exit on any error

echo "🚀 Starting Galactic Genesis deployment to star.omnilyzer.ai..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "frontend/package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Build the application
print_status "Building the frontend application..."
cd frontend
npm run build
if [ $? -eq 0 ]; then
    print_success "Frontend build completed successfully"
else
    print_error "Frontend build failed"
    exit 1
fi
cd ..

# Step 2: Check if dist directory exists
if [ ! -d "frontend/dist" ]; then
    print_error "Build directory not found. Build may have failed."
    exit 1
fi

print_success "Build artifacts ready in frontend/dist/"

# Step 3: Copy nginx configuration
print_status "Setting up nginx configuration..."
sudo cp star.nginx.conf /etc/nginx/sites-available/star.conf
if [ $? -eq 0 ]; then
    print_success "Nginx configuration copied"
else
    print_error "Failed to copy nginx configuration"
    exit 1
fi

# Step 4: Enable the site
print_status "Enabling the site..."
sudo ln -sf /etc/nginx/sites-available/star.conf /etc/nginx/sites-enabled/star.conf
if [ $? -eq 0 ]; then
    print_success "Site enabled"
else
    print_warning "Site may already be enabled"
fi

# Step 5: Test nginx configuration
print_status "Testing nginx configuration..."
sudo nginx -t
if [ $? -eq 0 ]; then
    print_success "Nginx configuration is valid"
else
    print_error "Nginx configuration test failed"
    exit 1
fi

# Step 6: Get SSL certificate
print_status "Setting up SSL certificate..."
if [ ! -f "/etc/letsencrypt/live/star.omnilyzer.ai/fullchain.pem" ]; then
    print_status "Obtaining SSL certificate from Let's Encrypt..."
    sudo certbot certonly --nginx -d star.omnilyzer.ai --non-interactive --agree-tos --email <EMAIL>
    if [ $? -eq 0 ]; then
        print_success "SSL certificate obtained"
    else
        print_error "Failed to obtain SSL certificate"
        print_warning "You may need to run: sudo certbot certonly --nginx -d star.omnilyzer.ai"
        exit 1
    fi
else
    print_success "SSL certificate already exists"
fi

# Step 7: Reload nginx
print_status "Reloading nginx..."
sudo systemctl reload nginx
if [ $? -eq 0 ]; then
    print_success "Nginx reloaded successfully"
else
    print_error "Failed to reload nginx"
    exit 1
fi

# Step 8: Check if nginx is running
print_status "Checking nginx status..."
sudo systemctl is-active --quiet nginx
if [ $? -eq 0 ]; then
    print_success "Nginx is running"
else
    print_warning "Nginx may not be running. Starting nginx..."
    sudo systemctl start nginx
fi

# Step 9: Test the deployment
print_status "Testing the deployment..."
sleep 2

# Test HTTP redirect
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://star.omnilyzer.ai || echo "000")
if [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    print_success "HTTP to HTTPS redirect working (Status: $HTTP_STATUS)"
else
    print_warning "HTTP redirect test returned status: $HTTP_STATUS"
fi

# Test HTTPS
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://star.omnilyzer.ai || echo "000")
if [ "$HTTPS_STATUS" = "200" ]; then
    print_success "HTTPS site is accessible (Status: $HTTPS_STATUS)"
else
    print_warning "HTTPS test returned status: $HTTPS_STATUS"
fi

# Test health endpoint
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://star.omnilyzer.ai/health || echo "000")
if [ "$HEALTH_STATUS" = "200" ]; then
    print_success "Health check endpoint working"
else
    print_warning "Health check returned status: $HEALTH_STATUS"
fi

# Final summary
echo ""
echo "🌟 =================================="
echo "🌟 DEPLOYMENT SUMMARY"
echo "🌟 =================================="
echo ""
print_success "✅ Frontend built successfully"
print_success "✅ Nginx configuration deployed"
print_success "✅ SSL certificate configured"
print_success "✅ Site enabled and nginx reloaded"
echo ""
echo -e "${GREEN}🎮 Galactic Genesis is now live at:${NC}"
echo -e "${BLUE}   https://star.omnilyzer.ai${NC}"
echo ""
echo -e "${YELLOW}📊 Useful commands:${NC}"
echo "   • Check logs: sudo tail -f /var/log/nginx/star-access.log"
echo "   • Check errors: sudo tail -f /var/log/nginx/star-error.log"
echo "   • Test config: sudo nginx -t"
echo "   • Reload nginx: sudo systemctl reload nginx"
echo ""
echo -e "${GREEN}🚀 Deployment completed successfully!${NC}"
