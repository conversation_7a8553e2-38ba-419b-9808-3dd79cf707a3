openapi: 3.1.0
info:
  title: Galactic Genesis API
  version: 0.1.0
servers:
  - url: http://localhost:8080
paths:
  /v1/health:
    get:
      operationId: health
      responses:
        '200':
          description: OK
  /v1/orders:
    get:
      operationId: listOrders
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      type: object
    post:
      operationId: submitOrder
      parameters:
        - name: Idempotency-Key
          in: header
          required: false
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                kind:
                  type: string
                payload:
                  type: object
              required: [kind, payload]
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                type: object
                properties:
                  orderId:
                    type: string
                  target_turn:
                    type: integer
  /v1/orders/{id}:
    get:
      operationId: getOrder
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string }
      responses:
        '200': { description: OK }
        '404': { description: Not Found }

  /v1/fleets:
    get:
      operationId: listFleets
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  fleets:
                    type: array
                    items:
                      type: object
    post:
      operationId: createFleet
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id: { type: string }
                empire_id: { type: string }
                system_id: { type: string }
                stance: { type: string }
                supply: { type: integer }
              required: [empire_id, system_id]
      responses:
        '201': { description: Created }

  /v1/fleets/{id}:
    get:
      operationId: getFleet
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string }
      responses:
        '200': { description: OK }
        '404': { description: Not Found }
    patch:
      operationId: updateFleet
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                stance: { type: string }
                supply: { type: integer, minimum: 0 }
      responses:
        '200': { description: OK }
        '404': { description: Not Found }
