### Health
@baseUrl={{baseUrl}}
GET {{baseUrl}}/v1/health

### Fleets
GET {{baseUrl}}/v1/fleets

### Create Fleet
POST {{baseUrl}}/v1/fleets
content-type: application/json

{
  "empire_id": "emp-1",
  "system_id": "sys-1",
  "stance": "neutral",
  "supply": 100
}

### Submit move order
POST {{baseUrl}}/v1/orders
content-type: application/json
Idempotency-Key: demo-cli-1

{
  "kind": "move",
  "payload": { "fleetId": "f1", "toSystemId": "sys-2" }
}

### List orders
GET {{baseUrl}}/v1/orders

### Get order
GET {{baseUrl}}/v1/orders/{{orderId}}

