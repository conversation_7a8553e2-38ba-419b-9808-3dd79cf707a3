-- Migration: Enhance planets table and create moons table with raw materials data
-- Add comprehensive raw materials and visual enhancement data

-- Add raw materials and visual enhancement columns to planets table
ALTER TABLE planets ADD COLUMN IF NOT EXISTS raw_materials TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS material_category VARCHAR(50) DEFAULT 'Scientific';
ALTER TABLE planets ADD COLUMN IF NOT EXISTS dominant_color VARCHAR(50);
ALTER TABLE planets ADD COLUMN IF NOT EXISTS color_hex VARCHAR(7);
ALTER TABLE planets ADD COLUMN IF NOT EXISTS albedo DECIMAL(4, 3);
ALTER TABLE planets ADD COLUMN IF NOT EXISTS visual_texture VARCHAR(100);
ALTER TABLE planets ADD COLUMN IF NOT EXISTS atmospheric_effects TEXT;

-- Create moons table
CREATE TABLE IF NOT EXISTS moons (
    moon_id SERIAL PRIMARY KEY,
    planet_id INTEGER NOT NULL REFERENCES planets(planet_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    
    -- Orbital characteristics
    distance_km INTEGER NOT NULL, -- Distance from planet in kilometers
    orbital_period_days DECIMAL(10, 6) NOT NULL, -- Orbital period in days
    orbital_eccentricity DECIMAL(8, 6) DEFAULT 0,
    orbital_inclination_deg DECIMAL(8, 4) DEFAULT 0,
    
    -- Physical characteristics
    diameter_km DECIMAL(8, 2) NOT NULL,
    mass_earth DECIMAL(10, 6), -- Mass relative to Earth
    radius_earth DECIMAL(8, 4), -- Radius relative to Earth
    density_g_cm3 DECIMAL(6, 3),
    
    -- Rotational characteristics
    rotation_period_hours DECIMAL(10, 4),
    is_tidally_locked BOOLEAN DEFAULT TRUE,
    
    -- Surface and composition
    surface_composition TEXT,
    raw_materials TEXT,
    material_category VARCHAR(50) DEFAULT 'Scientific',
    avg_surface_temp_c INTEGER,
    
    -- Visual characteristics
    dominant_color VARCHAR(50),
    color_hex VARCHAR(7),
    albedo DECIMAL(4, 3),
    visual_texture VARCHAR(100),
    
    -- Classification
    moon_type VARCHAR(50), -- regular, irregular, captured_asteroid, etc.
    
    -- Discovery
    discovery_year INTEGER,
    discovery_method VARCHAR(100),
    
    -- Game-specific
    mineral_richness DECIMAL(3, 2) DEFAULT 0,
    exploration_status VARCHAR(50) DEFAULT 'unexplored',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(planet_id, name),
    CHECK (diameter_km > 0),
    CHECK (distance_km > 0),
    CHECK (orbital_period_days > 0),
    CHECK (mineral_richness >= 0 AND mineral_richness <= 1)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_moons_planet_id ON moons(planet_id);
CREATE INDEX IF NOT EXISTS idx_moons_material_category ON moons(material_category);

-- Update trigger for moons
CREATE OR REPLACE FUNCTION update_moons_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_moons_updated_at
    BEFORE UPDATE ON moons
    FOR EACH ROW
    EXECUTE FUNCTION update_moons_updated_at();

-- Update Sol system planets with raw materials data
UPDATE planets SET 
    raw_materials = 'Silicates (SiO₂, Mg-silicates), metallic iron, sulfur, trace Na/K',
    material_category = 'Scientific',
    dominant_color = 'Gray',
    color_hex = '#8C7853',
    albedo = 0.142,
    visual_texture = 'rocky_cratered',
    atmospheric_effects = 'none'
WHERE name = 'Mercury' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'Basaltic silicates, iron oxides, sulfur (surface), atmosphere rich in CO₂ and H₂SO₄',
    material_category = 'Scientific',
    dominant_color = 'Yellow-Orange',
    color_hex = '#FFC649',
    albedo = 0.689,
    visual_texture = 'volcanic_cloudy',
    atmospheric_effects = 'thick_clouds,sulfuric_acid'
WHERE name = 'Venus' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'Silicates, iron, aluminum, calcium, carbon, water (oceans), diverse mineral resources',
    material_category = 'Scientific',
    dominant_color = 'Blue-Green',
    color_hex = '#6B93D6',
    albedo = 0.367,
    visual_texture = 'oceanic_continental',
    atmospheric_effects = 'clouds,weather_systems'
WHERE name = 'Earth' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'Iron oxides (Fe₂O₃), basalt, olivine, pyroxene, sulfates, perchlorates, subsurface water-ice',
    material_category = 'Scientific',
    dominant_color = 'Red-Orange',
    color_hex = '#CD5C5C',
    albedo = 0.170,
    visual_texture = 'desert_polar_caps',
    atmospheric_effects = 'dust_storms,thin_atmosphere'
WHERE name = 'Mars' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'Primarily H₂, He gas; trace CH₄, NH₃, H₂O; metallic hydrogen layer',
    material_category = 'Scientific',
    dominant_color = 'Orange-Brown',
    color_hex = '#D8CA9D',
    albedo = 0.538,
    visual_texture = 'gas_giant_bands',
    atmospheric_effects = 'great_red_spot,atmospheric_bands,storms'
WHERE name = 'Jupiter' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'Primarily H₂, He gas; trace CH₄, NH₃, water ice in atmosphere',
    material_category = 'Scientific',
    dominant_color = 'Golden-Yellow',
    color_hex = '#FAD5A5',
    albedo = 0.499,
    visual_texture = 'gas_giant_rings',
    atmospheric_effects = 'ring_system,atmospheric_bands,hexagonal_storm'
WHERE name = 'Saturn' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'H₂, He, CH₄ gas; icy mantle (H₂O, NH₃, CH₄ ices)',
    material_category = 'Scientific',
    dominant_color = 'Cyan-Blue',
    color_hex = '#4FD0E7',
    albedo = 0.488,
    visual_texture = 'ice_giant_smooth',
    atmospheric_effects = 'methane_clouds,tilted_rotation'
WHERE name = 'Uranus' AND star_id = 1;

UPDATE planets SET 
    raw_materials = 'H₂, He, CH₄ gas; icy mantle (water, methane, ammonia); deep volatile ices',
    material_category = 'Scientific',
    dominant_color = 'Deep Blue',
    color_hex = '#4B70DD',
    albedo = 0.442,
    visual_texture = 'ice_giant_dynamic',
    atmospheric_effects = 'great_dark_spot,high_speed_winds,methane_clouds'
WHERE name = 'Neptune' AND star_id = 1;

-- Insert major moons data
-- Earth's Moon
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Luna', 384400, 27.3, 3474.8, 0.0123, 0.273,
       'Anorthosite highlands, basaltic maria',
       'Oxygen (oxides), silicon, aluminum, calcium, iron, magnesium, titanium, trace hydrogen at poles',
       'Scientific', 'Gray', '#C0C0C0', 0.136, 'cratered_maria', NULL, 'regular', 0.3
FROM planets WHERE name = 'Earth' AND star_id = 1;

-- Mars moons
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Phobos', 9376, 0.32, 22.5, 1.47e-8, 0.0018,
       'Carbonaceous chondrite-like material',
       'Carbonaceous chondrite–like: carbon, silicates, phyllosilicates',
       'Suggested', 'Dark Gray', '#4A4A4A', 0.071, 'asteroid_like', 1877, 'irregular', 0.2
FROM planets WHERE name = 'Mars' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Deimos', 23463, 1.26, 12.4, 2.4e-9, 0.00098,
       'Similar to Phobos',
       'Similar to Phobos: carbonaceous rock, silicates, volatiles',
       'Suggested', 'Dark Gray', '#3A3A3A', 0.068, 'asteroid_like', 1877, 'irregular', 0.15
FROM planets WHERE name = 'Mars' AND star_id = 1;

-- Jupiter's major moons
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Io', 421700, 1.77, 3643.2, 0.015, 0.286,
       'Sulfur and sulfur dioxide',
       'Sulfur, sulfur dioxide, silicates, iron core',
       'Scientific', 'Yellow-Orange', '#FFFF99', 0.63, 'volcanic_sulfur', 1610, 'regular', 0.4
FROM planets WHERE name = 'Jupiter' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Europa', 671034, 3.55, 3121.6, 0.008, 0.245,
       'Water ice crust over silicate interior',
       'Water-ice crust, silicates beneath, possible salty ocean',
       'Scientific', 'Blue-White', '#B0E0E6', 0.67, 'icy_smooth', 1610, 'regular', 0.6
FROM planets WHERE name = 'Jupiter' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Ganymede', 1070412, 7.15, 5268.2, 0.025, 0.413,
       'Water ice and silicate rock',
       'Silicates, iron-nickel core, large amounts of water-ice',
       'Scientific', 'Gray-Brown', '#8B7D6B', 0.43, 'icy_cratered', 1610, 'regular', 0.5
FROM planets WHERE name = 'Jupiter' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Callisto', 1882709, 16.69, 4820.6, 0.018, 0.378,
       'Water ice and rock mixture',
       'Water-ice, silicates, carbon compounds, CO₂, SO₂',
       'Scientific', 'Dark Gray', '#4F4F4F', 0.22, 'heavily_cratered', 1610, 'regular', 0.3
FROM planets WHERE name = 'Jupiter' AND star_id = 1;

-- Saturn's major moons
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Titan', 1221830, 15.95, 5149.5, 0.0225, 0.404,
       'Organic compounds and water ice',
       'Water-ice, hydrocarbons (methane, ethane), organic tholins',
       'Scientific', 'Orange-Brown', '#CD853F', 0.22, 'organic_hazy', 1655, 'regular', 0.8
FROM planets WHERE name = 'Saturn' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Enceladus', 238020, 1.37, 504.2, 0.000018, 0.0395,
       'Water ice with subsurface ocean',
       'Water-ice, salts, silicate core, organic compounds in plumes',
       'Scientific', 'Bright White', '#F8F8FF', 0.81, 'icy_geysers', 1789, 'regular', 0.7
FROM planets WHERE name = 'Saturn' AND star_id = 1;

-- Additional Saturn moons
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Mimas', 185539, 0.94, 396.4, 0.0000063, 0.031,
       'Water ice and rock',
       'Water-ice, silicate rock',
       'Suggested', 'Gray', '#A0A0A0', 0.962, 'heavily_cratered', 1789, 'regular', 0.2
FROM planets WHERE name = 'Saturn' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Rhea', 527108, 4.52, 1527.6, 0.000039, 0.120,
       'Water ice and rock',
       'Water-ice, rocky silicates',
       'Suggested', 'Light Gray', '#D3D3D3', 0.949, 'icy_cratered', 1672, 'regular', 0.3
FROM planets WHERE name = 'Saturn' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Iapetus', 3561300, 79.33, 1468.6, 0.000030, 0.115,
       'Water ice and dark material',
       'Water-ice, dark carbonaceous material',
       'Suggested', 'Two-Tone', '#696969', 0.6, 'two_tone_surface', 1671, 'regular', 0.4
FROM planets WHERE name = 'Saturn' AND star_id = 1;

-- Uranus moons
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Miranda', 129390, 1.41, 471.6, 0.0000011, 0.037,
       'Water ice and rock',
       'Water-ice, silicate rock',
       'Suggested', 'Gray', '#808080', 0.32, 'fractured_terrain', 1948, 'regular', 0.25
FROM planets WHERE name = 'Uranus' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Ariel', 190900, 2.52, 1157.8, 0.0000023, 0.091,
       'Water ice and rock with CO₂ frost',
       'Water-ice, silicate rock, CO₂ frost',
       'Suggested', 'Light Gray', '#C0C0C0', 0.53, 'icy_valleys', 1851, 'regular', 0.3
FROM planets WHERE name = 'Uranus' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Umbriel', 266000, 4.14, 1169.4, 0.0000020, 0.092,
       'Water ice and dark material',
       'Water-ice, dark carbonaceous material',
       'Suggested', 'Dark Gray', '#2F4F4F', 0.26, 'dark_cratered', 1851, 'regular', 0.2
FROM planets WHERE name = 'Uranus' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Titania', 436300, 8.71, 1576.8, 0.0000059, 0.124,
       'Water ice and rock',
       'Water-ice, silicate rock',
       'Suggested', 'Gray-Brown', '#A0522D', 0.35, 'icy_canyons', 1787, 'regular', 0.35
FROM planets WHERE name = 'Uranus' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Oberon', 583500, 13.46, 1522.8, 0.0000050, 0.120,
       'Water ice and rock',
       'Water-ice, silicate rock',
       'Suggested', 'Dark Gray', '#696969', 0.31, 'ancient_cratered', 1787, 'regular', 0.3
FROM planets WHERE name = 'Uranus' AND star_id = 1;

-- Neptune moons
INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Triton', 354759, 5.88, 2706.8, 0.0036, 0.212,
       'Nitrogen ice and rock',
       'Nitrogen ice, water-ice, carbon dioxide ice, organics',
       'Scientific', 'Pink-White', '#FFB6C1', 0.76, 'nitrogen_geysers', 1846, 'irregular', 0.6
FROM planets WHERE name = 'Neptune' AND star_id = 1;

INSERT INTO moons (planet_id, name, distance_km, orbital_period_days, diameter_km, mass_earth, radius_earth,
                   surface_composition, raw_materials, material_category, dominant_color, color_hex, albedo,
                   visual_texture, discovery_year, moon_type, mineral_richness)
SELECT planet_id, 'Nereid', 5513818, 360.14, 340, 0.00000005, 0.027,
       'Likely water ice and carbonaceous material',
       'Likely water-ice, carbonaceous material',
       'Suggested', 'Dark Gray', '#2F2F2F', 0.155, 'irregular_shape', 1949, 'irregular', 0.15
FROM planets WHERE name = 'Neptune' AND star_id = 1;
