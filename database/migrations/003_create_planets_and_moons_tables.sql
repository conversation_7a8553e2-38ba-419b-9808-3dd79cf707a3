-- Migration: Create separate planets and moons tables with comprehensive astronomical data
-- Based on NASA/ESA observational data

-- Planets table with complete astronomical data
CREATE TABLE IF NOT EXISTS planets (
    planet_id SERIAL PRIMARY KEY,
    star_id INTEGER NOT NULL REFERENCES stars(star_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    
    -- Orbital characteristics
    distance_au DECIMAL(10, 6) NOT NULL, -- Distance from star in Astronomical Units
    orbital_period_days DECIMAL(12, 4) NOT NULL, -- Orbital period in Earth days
    orbital_eccentricity DECIMAL(8, 6) DEFAULT 0, -- Orbital eccentricity (0 = perfect circle)
    orbital_inclination_deg DECIMAL(8, 4) DEFAULT 0, -- Orbital inclination in degrees
    
    -- Physical characteristics
    diameter_km INTEGER NOT NULL, -- Diameter in kilometers
    mass_earth DECIMAL(10, 6) NOT NULL, -- Mass relative to Earth (Earth = 1.0)
    radius_earth DECIMAL(8, 4) NOT NULL, -- Radius relative to Earth (Earth = 1.0)
    density_g_cm3 DECIMAL(6, 3), -- Density in g/cm³
    
    -- Rotational characteristics
    rotation_period_hours DECIMAL(10, 4), -- Rotation period in hours (negative for retrograde)
    axial_tilt_deg DECIMAL(6, 2), -- Axial tilt in degrees
    
    -- Atmospheric data
    atmosphere_main_gases TEXT, -- Main atmospheric gases (e.g., "78% N₂, 21% O₂")
    atmosphere_pressure_bars DECIMAL(10, 6), -- Atmospheric pressure in bars (Earth = 1.0)
    
    -- Chemical signatures and composition
    chemical_signatures TEXT, -- Observable chemical signatures
    surface_composition TEXT, -- Primary surface composition
    
    -- Temperature data
    avg_surface_temp_c INTEGER, -- Average surface temperature in Celsius
    min_surface_temp_c INTEGER, -- Minimum surface temperature
    max_surface_temp_c INTEGER, -- Maximum surface temperature
    
    -- Visual characteristics
    dominant_color VARCHAR(50) NOT NULL, -- Dominant color as observed
    color_hex VARCHAR(7), -- Hex color code for rendering
    albedo DECIMAL(4, 3), -- Reflectivity (0.0 to 1.0)
    
    -- Classification
    planet_type VARCHAR(50) NOT NULL, -- rocky, gas_giant, ice_giant, etc.
    habitability_score DECIMAL(3, 2) DEFAULT 0, -- 0.0 to 1.0 habitability score
    in_habitable_zone BOOLEAN DEFAULT FALSE,
    
    -- Discovery and exploration
    discovery_year INTEGER,
    discovery_method VARCHAR(100),
    exploration_status VARCHAR(50) DEFAULT 'unexplored', -- unexplored, surveyed, colonized
    
    -- Game-specific attributes
    mineral_richness DECIMAL(3, 2) DEFAULT 0, -- 0.0 to 1.0
    energy_potential DECIMAL(3, 2) DEFAULT 0, -- 0.0 to 1.0
    is_colonized BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(star_id, name),
    CHECK (mass_earth > 0),
    CHECK (radius_earth > 0),
    CHECK (distance_au > 0),
    CHECK (orbital_period_days > 0),
    CHECK (habitability_score >= 0 AND habitability_score <= 1),
    CHECK (mineral_richness >= 0 AND mineral_richness <= 1),
    CHECK (energy_potential >= 0 AND energy_potential <= 1)
);

-- Moons table with detailed lunar data
CREATE TABLE IF NOT EXISTS moons (
    moon_id SERIAL PRIMARY KEY,
    planet_id INTEGER NOT NULL REFERENCES planets(planet_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    
    -- Orbital characteristics
    distance_km INTEGER NOT NULL, -- Distance from planet in kilometers
    orbital_period_days DECIMAL(10, 6) NOT NULL, -- Orbital period in days
    orbital_eccentricity DECIMAL(8, 6) DEFAULT 0,
    orbital_inclination_deg DECIMAL(8, 4) DEFAULT 0,
    
    -- Physical characteristics
    diameter_km DECIMAL(8, 2) NOT NULL,
    mass_kg DECIMAL(15, 3), -- Mass in kilograms
    density_g_cm3 DECIMAL(6, 3),
    
    -- Rotational characteristics (many moons are tidally locked)
    rotation_period_hours DECIMAL(10, 4),
    is_tidally_locked BOOLEAN DEFAULT TRUE,
    
    -- Surface characteristics
    surface_composition TEXT,
    avg_surface_temp_c INTEGER,
    
    -- Visual characteristics
    dominant_color VARCHAR(50),
    color_hex VARCHAR(7),
    albedo DECIMAL(4, 3),
    
    -- Classification
    moon_type VARCHAR(50), -- regular, irregular, captured_asteroid, etc.
    
    -- Discovery
    discovery_year INTEGER,
    discovery_method VARCHAR(100),
    
    -- Game-specific
    mineral_richness DECIMAL(3, 2) DEFAULT 0,
    exploration_status VARCHAR(50) DEFAULT 'unexplored',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(planet_id, name),
    CHECK (diameter_km > 0),
    CHECK (distance_km > 0),
    CHECK (orbital_period_days > 0),
    CHECK (mineral_richness >= 0 AND mineral_richness <= 1)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_planets_star_id ON planets(star_id);
CREATE INDEX IF NOT EXISTS idx_planets_planet_type ON planets(planet_type);
CREATE INDEX IF NOT EXISTS idx_planets_habitable_zone ON planets(in_habitable_zone);
CREATE INDEX IF NOT EXISTS idx_moons_planet_id ON moons(planet_id);

-- Update trigger for planets
CREATE OR REPLACE FUNCTION update_planets_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_planets_updated_at
    BEFORE UPDATE ON planets
    FOR EACH ROW
    EXECUTE FUNCTION update_planets_updated_at();

-- Update trigger for moons
CREATE OR REPLACE FUNCTION update_moons_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_moons_updated_at
    BEFORE UPDATE ON moons
    FOR EACH ROW
    EXECUTE FUNCTION update_moons_updated_at();

-- Add planet count to stars table (computed field)
ALTER TABLE stars ADD COLUMN IF NOT EXISTS planet_count INTEGER DEFAULT 0;

-- Function to update planet count
CREATE OR REPLACE FUNCTION update_star_planet_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE stars SET planet_count = planet_count + 1 WHERE star_id = NEW.star_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE stars SET planet_count = planet_count - 1 WHERE star_id = OLD.star_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' AND OLD.star_id != NEW.star_id THEN
        UPDATE stars SET planet_count = planet_count - 1 WHERE star_id = OLD.star_id;
        UPDATE stars SET planet_count = planet_count + 1 WHERE star_id = NEW.star_id;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_star_planet_count
    AFTER INSERT OR UPDATE OR DELETE ON planets
    FOR EACH ROW
    EXECUTE FUNCTION update_star_planet_count();
