-- Insert real Solar System data based on NASA/ESA observations
-- Data source: NASA Planetary Fact Sheets, JPL, ESA

-- First, ensure Sol exists in the stars table
INSERT INTO stars (star_id, name, catalog_name, ra_deg, dec_deg, distance_ly, spectral_type, mass_solar, radius_solar, teff_k, luminosity_solar, mag_v, discovery_status, is_colonizable)
VALUES (1, 'Sol', 'Sun', 0, 0, 0, 'G2V', 1.0, 1.0, 5778, 1.0, 4.83, 'known', true)
ON CONFLICT (star_id) DO UPDATE SET
    name = EXCLUDED.name,
    catalog_name = EXCLUDED.catalog_name,
    spectral_type = EXCLUDED.spectral_type,
    mass_solar = EXCLUDED.mass_solar,
    radius_solar = EXCLUDED.radius_solar,
    teff_k = EXCLUDED.teff_k,
    luminosity_solar = EXCLUDED.luminosity_solar,
    mag_v = EXCLUDED.mag_v,
    is_colonizable = EXCLUDED.is_colonizable;

-- Insert the 8 planets of the Solar System with comprehensive astronomical data
INSERT INTO planets (
    planet_id, star_id, name, distance_au, orbital_period_days, orbital_eccentricity, orbital_inclination_deg,
    diameter_km, mass_earth, radius_earth, density_g_cm3, rotation_period_hours, axial_tilt_deg,
    atmosphere_main_gases, atmosphere_pressure_bars, chemical_signatures, surface_composition,
    avg_surface_temp_c, min_surface_temp_c, max_surface_temp_c,
    dominant_color, color_hex, albedo, planet_type, habitability_score, in_habitable_zone,
    discovery_year, discovery_method, exploration_status, mineral_richness, energy_potential, is_colonized
) VALUES 
-- Mercury
(1, 1, 'Mercury', 0.39, 87.969, 0.205630, 7.005,
 4879, 0.055, 0.3829, 5.427, 1407.6, 0.034,
 'Exosphere: Na, K, He, O₂ (trace)', 0.000000000001, 'Silicates, Fe', 'Silicate rock, iron core',
 167, -173, 427,
 'Dark gray', '#8C7853', 0.142, 'rocky', 0.0, false,
 -3000, 'naked_eye', 'surveyed', 0.85, 0.95, false),

-- Venus  
(2, 1, 'Venus', 0.72, 224.701, 0.006772, 3.39458,
 12104, 0.815, 0.9499, 5.243, -5832.5, 177.36,
 '96.5% CO₂, 3.5% N₂, H₂SO₄ clouds', 92.0, 'CO₂ bands, SO₂', 'Basaltic rock, sulfuric compounds',
 465, 450, 480,
 'Pale yellow-white', '#FFC649', 0.689, 'rocky', 0.0, false,
 -3000, 'naked_eye', 'surveyed', 0.7, 0.3, false),

-- Earth
(3, 1, 'Earth', 1.00, 365.256, 0.0167086, 0.00005,
 12742, 1.0, 1.0, 5.514, 23.934, 23.44,
 '78% N₂, 21% O₂, trace H₂O, CO₂', 1.0, 'O₂ bands, H₂O, vegetation red-edge', 'Silicate rock, water, organic matter',
 15, -88, 58,
 'Blue/white/green', '#6B93D6', 0.367, 'rocky', 1.0, true,
 0, 'home_planet', 'colonized', 0.6, 0.8, true),

-- Mars
(4, 1, 'Mars', 1.52, 686.980, 0.0934, 1.850,
 6779, 0.107, 0.532, 3.933, 24.622, 25.19,
 '95% CO₂, 3% N₂, 1.6% Ar', 0.00636, 'CO₂, iron oxides, perchlorates', 'Iron oxide, basaltic rock',
 -65, -125, 20,
 'Red-orange', '#CD5C5C', 0.170, 'rocky', 0.3, true,
 -3000, 'naked_eye', 'surveyed', 0.9, 0.4, false),

-- Jupiter
(5, 1, 'Jupiter', 5.20, 4332.59, 0.0489, 1.303,
 139820, 317.8, 11.209, 1.326, 9.925, 3.13,
 '~90% H₂, ~10% He, CH₄, NH₃, H₂O', 1000000, 'CH₄, NH₃, H₂O bands', 'Hydrogen, helium, metallic hydrogen core',
 -110, -145, -75,
 'Striped orange/brown', '#D8CA9D', 0.343, 'gas_giant', 0.0, false,
 -3000, 'naked_eye', 'surveyed', 0.1, 0.2, false),

-- Saturn
(6, 1, 'Saturn', 9.58, 10759.22, 0.0565, 2.485,
 116460, 95.16, 9.449, 0.687, 10.656, 26.73,
 '~96% H₂, 3% He, CH₄', 1000000, 'CH₄, NH₃', 'Hydrogen, helium, ice, rock core',
 -140, -178, -120,
 'Pale yellow', '#FAD5A5', 0.342, 'gas_giant', 0.0, false,
 -3000, 'naked_eye', 'surveyed', 0.1, 0.1, false),

-- Uranus
(7, 1, 'Uranus', 19.2, 30688.5, 0.046381, 0.773,
 50724, 14.54, 4.007, 1.270, -17.24, 97.77,
 '83% H₂, 15% He, 2% CH₄', 1000000, 'CH₄ absorption (red cutoff)', 'Water, methane, ammonia ices',
 -195, -224, -170,
 'Blue-green', '#4FD0E7', 0.300, 'ice_giant', 0.0, false,
 1781, 'telescope', 'unexplored', 0.3, 0.05, false),

-- Neptune  
(8, 1, 'Neptune', 30.1, 60182, 0.009456, 1.767,
 49244, 17.15, 3.883, 1.638, 16.108, 28.32,
 '80% H₂, 19% He, 1.5% CH₄', 1000000, 'CH₄ absorption', 'Water, methane, ammonia ices',
 -200, -214, -180,
 'Deep blue', '#4B70DD', 0.290, 'ice_giant', 0.0, false,
 1846, 'telescope', 'unexplored', 0.3, 0.05, false)

ON CONFLICT (planet_id) DO UPDATE SET
    star_id = EXCLUDED.star_id,
    name = EXCLUDED.name,
    distance_au = EXCLUDED.distance_au,
    orbital_period_days = EXCLUDED.orbital_period_days,
    orbital_eccentricity = EXCLUDED.orbital_eccentricity,
    orbital_inclination_deg = EXCLUDED.orbital_inclination_deg,
    diameter_km = EXCLUDED.diameter_km,
    mass_earth = EXCLUDED.mass_earth,
    radius_earth = EXCLUDED.radius_earth,
    density_g_cm3 = EXCLUDED.density_g_cm3,
    rotation_period_hours = EXCLUDED.rotation_period_hours,
    axial_tilt_deg = EXCLUDED.axial_tilt_deg,
    atmosphere_main_gases = EXCLUDED.atmosphere_main_gases,
    atmosphere_pressure_bars = EXCLUDED.atmosphere_pressure_bars,
    chemical_signatures = EXCLUDED.chemical_signatures,
    surface_composition = EXCLUDED.surface_composition,
    avg_surface_temp_c = EXCLUDED.avg_surface_temp_c,
    min_surface_temp_c = EXCLUDED.min_surface_temp_c,
    max_surface_temp_c = EXCLUDED.max_surface_temp_c,
    dominant_color = EXCLUDED.dominant_color,
    color_hex = EXCLUDED.color_hex,
    albedo = EXCLUDED.albedo,
    planet_type = EXCLUDED.planet_type,
    habitability_score = EXCLUDED.habitability_score,
    in_habitable_zone = EXCLUDED.in_habitable_zone,
    discovery_year = EXCLUDED.discovery_year,
    discovery_method = EXCLUDED.discovery_method,
    exploration_status = EXCLUDED.exploration_status,
    mineral_richness = EXCLUDED.mineral_richness,
    energy_potential = EXCLUDED.energy_potential,
    is_colonized = EXCLUDED.is_colonized,
    updated_at = CURRENT_TIMESTAMP;

-- Insert major moons of the Solar System
INSERT INTO moons (
    moon_id, planet_id, name, distance_km, orbital_period_days, orbital_eccentricity,
    diameter_km, mass_kg, density_g_cm3, rotation_period_hours, is_tidally_locked,
    surface_composition, avg_surface_temp_c, dominant_color, color_hex, albedo,
    moon_type, discovery_year, discovery_method, mineral_richness, exploration_status
) VALUES
-- Earth's Moon
(1, 3, 'Luna', 384400, 27.322, 0.0549,
 3474.8, 7.342e22, 3.344, 655.728, true,
 'Anorthosite, basalt, regolith', -20, 'Gray', '#C0C0C0', 0.136,
 'regular', -3000, 'naked_eye', 0.4, 'surveyed'),

-- Mars' Moons
(2, 4, 'Phobos', 9376, 0.319, 0.0151,
 22.5, 1.0659e16, 1.876, 7.65, true,
 'Carbonaceous chondrite', -40, 'Dark gray', '#696969', 0.071,
 'captured_asteroid', 1877, 'telescope', 0.6, 'surveyed'),

(3, 4, 'Deimos', 23463, 1.262, 0.0003,
 12.4, 1.4762e15, 1.471, 30.3, true,
 'Carbonaceous chondrite', -40, 'Dark gray', '#696969', 0.068,
 'captured_asteroid', 1877, 'telescope', 0.5, 'surveyed'),

-- Jupiter's Major Moons (Galilean)
(4, 5, 'Io', 421700, 1.769, 0.0041,
 3643.2, 8.932e22, 3.528, 42.46, true,
 'Sulfur compounds, silicate rock', -130, 'Yellow-orange', '#FFFF99', 0.63,
 'regular', 1610, 'telescope', 0.8, 'surveyed'),

(5, 5, 'Europa', 671034, 3.551, 0.009,
 3121.6, 4.800e22, 3.013, 85.23, true,
 'Water ice, silicate rock', -160, 'White-blue', '#B0E0E6', 0.67,
 'regular', 1610, 'telescope', 0.3, 'surveyed'),

(6, 5, 'Ganymede', 1070412, 7.155, 0.0013,
 5268.2, 1.482e23, 1.936, 171.7, true,
 'Water ice, silicate rock', -180, 'Gray-brown', '#8B7D6B', 0.43,
 'regular', 1610, 'telescope', 0.4, 'surveyed'),

(7, 5, 'Callisto', 1882709, 16.689, 0.0074,
 4820.6, 1.076e23, 1.834, 400.5, true,
 'Water ice, silicate rock', -190, 'Dark gray', '#4A4A4A', 0.22,
 'regular', 1610, 'telescope', 0.3, 'surveyed'),

-- Saturn's Major Moons
(8, 6, 'Titan', 1221830, 15.945, 0.0288,
 5149.5, 1.345e23, 1.880, 382.7, true,
 'Water ice, hydrocarbons', -179, 'Orange-brown', '#CD853F', 0.22,
 'regular', 1655, 'telescope', 0.5, 'surveyed'),

(9, 6, 'Enceladus', 238020, 1.370, 0.0047,
 504.2, 1.080e20, 1.609, 32.9, true,
 'Water ice', -200, 'White', '#FFFFFF', 0.81,
 'regular', 1789, 'telescope', 0.2, 'surveyed')

ON CONFLICT (moon_id) DO UPDATE SET
    planet_id = EXCLUDED.planet_id,
    name = EXCLUDED.name,
    distance_km = EXCLUDED.distance_km,
    orbital_period_days = EXCLUDED.orbital_period_days,
    orbital_eccentricity = EXCLUDED.orbital_eccentricity,
    diameter_km = EXCLUDED.diameter_km,
    mass_kg = EXCLUDED.mass_kg,
    density_g_cm3 = EXCLUDED.density_g_cm3,
    rotation_period_hours = EXCLUDED.rotation_period_hours,
    is_tidally_locked = EXCLUDED.is_tidally_locked,
    surface_composition = EXCLUDED.surface_composition,
    avg_surface_temp_c = EXCLUDED.avg_surface_temp_c,
    dominant_color = EXCLUDED.dominant_color,
    color_hex = EXCLUDED.color_hex,
    albedo = EXCLUDED.albedo,
    moon_type = EXCLUDED.moon_type,
    discovery_year = EXCLUDED.discovery_year,
    discovery_method = EXCLUDED.discovery_method,
    mineral_richness = EXCLUDED.mineral_richness,
    exploration_status = EXCLUDED.exploration_status,
    updated_at = CURRENT_TIMESTAMP;
