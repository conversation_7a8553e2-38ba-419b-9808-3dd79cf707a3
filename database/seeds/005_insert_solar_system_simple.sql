-- Insert Solar System data using existing schema
-- Clear existing planets for Sol first
DELETE FROM planets WHERE star_id = 1;

-- Insert the 8 planets of the Solar System using existing column names
INSERT INTO planets (
    planet_id, star_id, name, mass_earth, radius_earth, sma_au, period_days,
    eccentricity, inclination_deg, surface_temp_k, atmosphere, composition,
    habitability_score, in_habitable_zone, mineral_richness, energy_potential,
    discovery_year, discovery_method
) VALUES 
-- Mercury
(1, 1, 'Mercury', 0.055, 0.3829, 0.39, 87.969,
 0.205630, 7.005, 440, 'Exosphere: Na, K, He, O₂ (trace)', 'rocky',
 0.0, false, 0.85, 0.95, -3000, 'naked_eye'),

-- Venus  
(2, 1, 'Venus', 0.815, 0.9499, 0.72, 224.701,
 0.006772, 3.39458, 737, '96.5% CO₂, 3.5% N₂, H₂SO₄ clouds', 'rocky',
 0.0, false, 0.7, 0.3, -3000, 'naked_eye'),

-- Earth
(3, 1, 'Earth', 1.0, 1.0, 1.00, 365.256,
 0.0167086, 0.00005, 288, '78% N₂, 21% O₂, trace H₂O, CO₂', 'rocky',
 1.0, true, 0.6, 0.8, 0, 'home_planet'),

-- Mars
(4, 1, 'Mars', 0.107, 0.532, 1.52, 686.980,
 0.0934, 1.850, 210, '95% CO₂, 3% N₂, 1.6% Ar', 'rocky',
 0.3, true, 0.9, 0.4, -3000, 'naked_eye'),

-- Jupiter
(5, 1, 'Jupiter', 317.8, 11.209, 5.20, 4332.59,
 0.0489, 1.303, 165, '~90% H₂, ~10% He, CH₄, NH₃, H₂O', 'gas_giant',
 0.0, false, 0.1, 0.2, -3000, 'naked_eye'),

-- Saturn
(6, 1, 'Saturn', 95.16, 9.449, 9.58, 10759.22,
 0.0565, 2.485, 134, '~96% H₂, 3% He, CH₄', 'gas_giant',
 0.0, false, 0.1, 0.1, -3000, 'naked_eye'),

-- Uranus
(7, 1, 'Uranus', 14.54, 4.007, 19.2, 30688.5,
 0.046381, 0.773, 76, '83% H₂, 15% He, 2% CH₄', 'ice_giant',
 0.0, false, 0.3, 0.05, 1781, 'telescope'),

-- Neptune  
(8, 1, 'Neptune', 17.15, 3.883, 30.1, 60182,
 0.009456, 1.767, 72, '80% H₂, 19% He, 1.5% CH₄', 'ice_giant',
 0.0, false, 0.3, 0.05, 1846, 'telescope')

ON CONFLICT (planet_id) DO UPDATE SET
    star_id = EXCLUDED.star_id,
    name = EXCLUDED.name,
    mass_earth = EXCLUDED.mass_earth,
    radius_earth = EXCLUDED.radius_earth,
    sma_au = EXCLUDED.sma_au,
    period_days = EXCLUDED.period_days,
    eccentricity = EXCLUDED.eccentricity,
    inclination_deg = EXCLUDED.inclination_deg,
    surface_temp_k = EXCLUDED.surface_temp_k,
    atmosphere = EXCLUDED.atmosphere,
    composition = EXCLUDED.composition,
    habitability_score = EXCLUDED.habitability_score,
    in_habitable_zone = EXCLUDED.in_habitable_zone,
    mineral_richness = EXCLUDED.mineral_richness,
    energy_potential = EXCLUDED.energy_potential,
    discovery_year = EXCLUDED.discovery_year,
    discovery_method = EXCLUDED.discovery_method;
