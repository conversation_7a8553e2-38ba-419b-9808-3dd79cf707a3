describe('Port Monitor Dashboard', () => {
  const HEALTH_DASHBOARD_URL = 'http://localhost:8086';

  beforeEach(() => {
    cy.visit(HEALTH_DASHBOARD_URL);
    cy.wait(2000); // Wait for initial data load
  });

  it('should load the health dashboard', () => {
    cy.contains('Galactic Genesis Health Dashboard').should('be.visible');
    cy.get('[data-testid="last-updated"]').should('be.visible');
  });

  it('should have port monitor navigation button', () => {
    cy.contains('Port Monitor').should('be.visible');
    cy.contains('Health Monitor').should('be.visible');
  });

  it('should switch to port monitor view', () => {
    // Click on Port Monitor button
    cy.contains('Port Monitor').click();
    
    // Should see port monitor content
    cy.contains('Port Monitor').should('be.visible');
    cy.contains('Last scan:').should('be.visible');
    
    // Should see port summary metrics
    cy.contains('Total Ports').should('be.visible');
    cy.contains('Listening').should('be.visible');
    cy.contains('Expected').should('be.visible');
    cy.contains('Unexpected').should('be.visible');
    cy.contains('Missing').should('be.visible');
  });

  it('should display port information correctly', () => {
    // Switch to port monitor
    cy.contains('Port Monitor').click();

    // Wait for port data to load
    cy.wait(3000);

    // Should see port entries (check if they exist, not necessarily visible due to scrolling)
    cy.contains('Port 8086').should('exist'); // Health Dashboard port
    cy.contains('Health Dashboard').should('exist');

    // Check for status indicators
    cy.get('.w-3.h-3.rounded-full').should('exist'); // Status dots

    // Should see protocol information
    cy.contains('TCP').should('exist');
  });

  it('should show port summary metrics', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);
    
    // Check that summary metrics are numbers
    cy.get('.metric-card').should('have.length.at.least', 5);
    
    // Verify metric cards have proper structure
    cy.get('.metric-card').first().within(() => {
      cy.get('.text-2xl').should('exist'); // Metric value
      cy.get('.text-xs').should('exist');  // Metric label
    });
  });

  it('should handle port kill functionality UI', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);
    
    // Look for kill buttons (should exist for listening ports)
    cy.get('.btn-danger').should('exist');
    
    // Click on a kill button (but cancel the confirmation)
    cy.get('.btn-danger').first().click();
    
    // Should show confirmation dialog
    cy.on('window:confirm', (text) => {
      expect(text).to.contain('Are you sure you want to kill');
      return false; // Cancel the action
    });
  });

  it('should switch back to health monitor view', () => {
    // Start in port monitor
    cy.contains('Port Monitor').click();
    cy.wait(2000);
    
    // Switch back to health monitor
    cy.contains('Health Monitor').click();
    
    // Should see health monitor content
    cy.get('[data-testid="services-section"]').should('be.visible');
    cy.contains('Services').should('be.visible');
    cy.contains('Database').should('be.visible');
  });

  it('should update port data in real-time', () => {
    cy.contains('Port Monitor').click();
    
    // Get initial timestamp
    cy.contains('Last scan:').invoke('text').then((initialText) => {
      const initialTime = initialText.replace('Last scan: ', '');
      
      // Wait for next update (port scans happen every 30 seconds, but we'll wait less)
      cy.wait(35000);
      
      // Check if timestamp updated
      cy.contains('Last scan:').invoke('text').then((newText) => {
        const newTime = newText.replace('Last scan: ', '');
        expect(newTime).to.not.equal(initialTime);
      });
    });
  });

  it('should display expected vs unexpected ports correctly', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);

    // Should see different colored status indicators
    cy.get('.bg-green-500').should('exist'); // Expected running ports
    cy.get('.bg-red-500, .bg-yellow-500').should('exist'); // Missing or unexpected ports

    // Should see service names for expected ports (check existence, not visibility due to scrolling)
    cy.contains('API Gateway').should('exist');
    cy.contains('Health Dashboard').should('exist');
  });

  it('should show process information for listening ports', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);
    
    // Look for process information (PID and process name)
    cy.contains('PID:').should('exist');
    
    // Should see process names like 'node', 'postgres', etc.
    cy.get('div').contains(/PID: \d+/).should('exist');
  });

  it('should handle responsive design', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);
    
    // Test mobile viewport
    cy.viewport(375, 667);
    cy.get('.metric-card').should('be.visible');
    cy.contains('Port Monitor').should('be.visible');
    
    // Test tablet viewport
    cy.viewport(768, 1024);
    cy.get('.metric-card').should('be.visible');
    
    // Test desktop viewport
    cy.viewport(1200, 800);
    cy.get('.metric-card').should('be.visible');
  });

  it('should show port descriptions and service information', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);

    // Should see service descriptions (check existence due to scrollable container)
    cy.contains('Health monitoring dashboard').should('exist');
    cy.contains('Main API Gateway').should('exist');

    // Should see port numbers
    cy.contains('Port 8086').should('exist');
    cy.contains('Port 19081').should('exist');
  });

  it('should maintain state when switching between views', () => {
    // Start in health monitor, note a service status
    cy.get('[data-testid="services-section"]').should('be.visible');
    
    // Switch to port monitor
    cy.contains('Port Monitor').click();
    cy.wait(2000);
    cy.contains('Total Ports').should('be.visible');
    
    // Switch back to health monitor
    cy.contains('Health Monitor').click();
    
    // Should still see the same content
    cy.get('[data-testid="services-section"]').should('be.visible');
  });

  it('should handle WebSocket connection for real-time updates', () => {
    cy.contains('Port Monitor').click();
    
    // Wait for WebSocket connection and initial data
    cy.wait(5000);
    
    // Should see updated data (WebSocket should be working)
    cy.contains('Last scan:').should('be.visible');
    
    // The page should not show loading indicators after initial load
    cy.contains('Loading port data...').should('not.exist');
  });

  it('should show appropriate status text for different port states', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);
    
    // Should see different status texts
    cy.contains('Running').should('exist');    // For expected listening ports
    cy.contains('Missing').should('exist');    // For expected non-listening ports
    cy.contains('Unexpected').should('exist'); // For unexpected listening ports
  });

  it('should display port protocol information', () => {
    cy.contains('Port Monitor').click();
    cy.wait(3000);
    
    // Should see protocol information (TCP/UDP)
    cy.contains('TCP').should('be.visible');
    
    // Protocol should be displayed in uppercase
    cy.get('div').contains(/^TCP$|^UDP$/).should('exist');
  });
});
