describe('Health Dashboard E2E Tests', () => {
  const DASHBOARD_URL = 'http://localhost:8086';
  
  beforeEach(() => {
    // Visit the health dashboard
    cy.visit(DASHBOARD_URL);
    
    // Wait for the page to load
    cy.get('[data-testid="dashboard-title"]', { timeout: 10000 })
      .should('contain', 'Galactic Genesis Health Dashboard');
  });

  describe('Dashboard Layout and Navigation', () => {
    it('should display the main dashboard components', () => {
      // Check main title
      cy.get('[data-testid="dashboard-title"]')
        .should('be.visible')
        .and('contain', 'Galactic Genesis Health Dashboard');
      
      // Check overall status
      cy.get('[data-testid="overall-status"]')
        .should('be.visible');
      
      // Check services section
      cy.get('[data-testid="services-section"]')
        .should('be.visible');
      
      // Check system metrics section
      cy.get('[data-testid="system-metrics"]')
        .should('be.visible');
      
      // Check alerts section
      cy.get('[data-testid="alerts-section"]')
        .should('be.visible');
    });

    it('should display service widgets', () => {
      // Wait for services to load
      cy.get('[data-testid="service-widget"]', { timeout: 10000 })
        .should('have.length.greaterThan', 0);
      
      // Check that each service widget has required elements
      cy.get('[data-testid="service-widget"]').each(($widget) => {
        cy.wrap($widget).within(() => {
          // Service name
          cy.get('[data-testid="service-name"]').should('be.visible');
          
          // Service status
          cy.get('[data-testid="service-status"]').should('be.visible');
          
          // Control buttons
          cy.get('[data-testid="service-controls"]').should('be.visible');
        });
      });
    });

    it('should display system metrics', () => {
      // CPU usage
      cy.get('[data-testid="cpu-usage"]')
        .should('be.visible')
        .and('contain', '%');
      
      // Memory usage
      cy.get('[data-testid="memory-usage"]')
        .should('be.visible')
        .and('contain', '%');
      
      // Disk usage
      cy.get('[data-testid="disk-usage"]')
        .should('be.visible')
        .and('contain', '%');
      
      // Network stats
      cy.get('[data-testid="network-stats"]')
        .should('be.visible');
    });
  });

  describe('Real-time Updates', () => {
    it('should update timestamps periodically', () => {
      // Get initial timestamp
      cy.get('[data-testid="last-updated"]')
        .invoke('text')
        .then((initialTime) => {
          // Wait for update (health check interval is 5 seconds)
          cy.wait(6000);
          
          // Check that timestamp has changed
          cy.get('[data-testid="last-updated"]')
            .invoke('text')
            .should('not.equal', initialTime);
        });
    });

    it('should show loading states during updates', () => {
      // This test would require triggering a manual refresh
      // For now, we'll just verify the loading indicator exists
      cy.get('[data-testid="refresh-button"]')
        .should('be.visible');
    });
  });

  describe('Service Controls', () => {
    it('should display service control buttons', () => {
      cy.get('[data-testid="service-widget"]').first().within(() => {
        // Start button
        cy.get('[data-testid="start-button"]')
          .should('be.visible')
          .and('contain', 'Start');
        
        // Restart button
        cy.get('[data-testid="restart-button"]')
          .should('be.visible')
          .and('contain', 'Restart');
        
        // Stop button
        cy.get('[data-testid="stop-button"]')
          .should('be.visible')
          .and('contain', 'Stop');
      });
    });

    it('should show confirmation dialog for restart action', () => {
      cy.get('[data-testid="service-widget"]').first().within(() => {
        cy.get('[data-testid="restart-button"]').click();
      });
      
      // Check for confirmation dialog
      cy.get('[data-testid="confirm-dialog"]')
        .should('be.visible');
      
      // Check dialog content
      cy.get('[data-testid="confirm-message"]')
        .should('contain', 'restart');
      
      // Cancel the action
      cy.get('[data-testid="cancel-button"]').click();
      
      // Dialog should disappear
      cy.get('[data-testid="confirm-dialog"]')
        .should('not.exist');
    });

    it('should disable buttons during service operations', () => {
      // This test would require actually triggering a service operation
      // For safety, we'll just verify the button states can change
      cy.get('[data-testid="service-widget"]').first().within(() => {
        cy.get('[data-testid="restart-button"]')
          .should('not.be.disabled');
      });
    });
  });

  describe('Alerts Management', () => {
    it('should display alerts when present', () => {
      // Check if alerts section exists
      cy.get('[data-testid="alerts-section"]')
        .should('be.visible');
      
      // If alerts are present, check their structure
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="alert-item"]').length > 0) {
          cy.get('[data-testid="alert-item"]').first().within(() => {
            cy.get('[data-testid="alert-type"]').should('be.visible');
            cy.get('[data-testid="alert-message"]').should('be.visible');
            cy.get('[data-testid="alert-timestamp"]').should('be.visible');
          });
        }
      });
    });

    it('should allow resolving alerts', () => {
      // Check if there are any unresolved alerts
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="alert-item"]:not([data-resolved="true"])').length > 0) {
          cy.get('[data-testid="alert-item"]:not([data-resolved="true"])').first().within(() => {
            cy.get('[data-testid="resolve-button"]').click();
          });
          
          // Verify alert is marked as resolved
          cy.get('[data-testid="alert-item"]').first()
            .should('have.attr', 'data-resolved', 'true');
        }
      });
    });
  });

  describe('Responsive Design', () => {
    it('should work on mobile viewport', () => {
      cy.viewport('iphone-x');
      
      // Check that main components are still visible
      cy.get('[data-testid="dashboard-title"]')
        .should('be.visible');
      
      cy.get('[data-testid="overall-status"]')
        .should('be.visible');
      
      // Services might be stacked on mobile
      cy.get('[data-testid="services-section"]')
        .should('be.visible');
    });

    it('should work on tablet viewport', () => {
      cy.viewport('ipad-2');
      
      // Check that layout adapts properly
      cy.get('[data-testid="dashboard-title"]')
        .should('be.visible');
      
      cy.get('[data-testid="service-widget"]')
        .should('have.length.greaterThan', 0);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', () => {
      // Intercept API calls and simulate network error
      cy.intercept('GET', '/api/health-data', { forceNetworkError: true }).as('healthDataError');
      
      // Refresh the page
      cy.reload();
      
      // Should show error state
      cy.get('[data-testid="error-message"]', { timeout: 10000 })
        .should('be.visible');
    });

    it('should retry failed requests', () => {
      // Intercept API calls and simulate temporary failure
      let callCount = 0;
      cy.intercept('GET', '/api/health-data', (req) => {
        callCount++;
        if (callCount === 1) {
          req.reply({ statusCode: 500 });
        } else {
          req.continue();
        }
      }).as('healthDataRetry');
      
      // Refresh the page
      cy.reload();
      
      // Should eventually succeed and show data
      cy.get('[data-testid="overall-status"]', { timeout: 15000 })
        .should('be.visible');
    });
  });

  describe('Performance', () => {
    it('should load within acceptable time', () => {
      const startTime = Date.now();
      
      cy.visit(DASHBOARD_URL);
      
      cy.get('[data-testid="dashboard-title"]', { timeout: 5000 })
        .should('be.visible')
        .then(() => {
          const loadTime = Date.now() - startTime;
          expect(loadTime).to.be.lessThan(5000); // Should load within 5 seconds
        });
    });

    it('should not have memory leaks during updates', () => {
      // Let the dashboard run for a while with updates
      cy.wait(10000);
      
      // Check that the page is still responsive
      cy.get('[data-testid="refresh-button"]')
        .should('be.visible')
        .click();
      
      cy.get('[data-testid="last-updated"]')
        .should('be.visible');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="service-widget"]').first().within(() => {
        cy.get('[data-testid="restart-button"]')
          .should('have.attr', 'aria-label');
      });
    });

    it('should be keyboard navigable', () => {
      // Tab through interactive elements
      cy.get('body').tab();
      cy.focused().should('be.visible');
      
      // Continue tabbing
      cy.focused().tab();
      cy.focused().should('be.visible');
    });

    it('should have sufficient color contrast', () => {
      // This would typically be tested with axe-core
      // For now, just verify elements are visible
      cy.get('[data-testid="service-status"]')
        .should('be.visible')
        .and('have.css', 'color');
    });
  });
});
