describe('Materials System E2E Tests', () => {
  const FRONTEND_URL = 'http://localhost:5174';
  const API_BASE_URL = 'http://localhost:19081';
  
  beforeEach(() => {
    // Intercept API calls to ensure they're working
    cy.intercept('GET', `${API_BASE_URL}/v1/materials`).as('getMaterials');
    cy.intercept('GET', `${API_BASE_URL}/v1/material-deposits*`).as('getMaterialDeposits');
    cy.intercept('GET', `${API_BASE_URL}/v1/stations*`).as('getStations');
    cy.intercept('GET', `${API_BASE_URL}/v1/station-types`).as('getStationTypes');
    cy.intercept('POST', `${API_BASE_URL}/v1/survey-orders`).as('createSurveyOrder');
    cy.intercept('POST', `${API_BASE_URL}/v1/station-construction-orders`).as('createConstructionOrder');
    
    // Visit the frontend
    cy.visit(FRONTEND_URL);
    
    // Wait for the app to load
    cy.get('body', { timeout: 10000 }).should('be.visible');
  });

  describe('Materials Management UI', () => {
    it('should display materials manager component', () => {
      // Navigate to materials management (assuming it's accessible from main UI)
      // This might need adjustment based on actual navigation structure
      cy.get('[data-testid="materials-manager"]', { timeout: 10000 })
        .should('be.visible');
      
      // Check for tab navigation
      cy.get('[data-testid="materials-tab"]').should('be.visible');
      cy.get('[data-testid="deposits-tab"]').should('be.visible');
      cy.get('[data-testid="stations-tab"]').should('be.visible');
      cy.get('[data-testid="construction-tab"]').should('be.visible');
    });

    it('should load and display materials list', () => {
      // Click on materials tab
      cy.get('[data-testid="materials-tab"]').click();
      
      // Wait for API call
      cy.wait('@getMaterials');
      
      // Check that materials are displayed
      cy.get('[data-testid="material-card"]', { timeout: 10000 })
        .should('have.length.greaterThan', 0);
      
      // Check material card content
      cy.get('[data-testid="material-card"]').first().within(() => {
        cy.get('[data-testid="material-name"]').should('be.visible');
        cy.get('[data-testid="material-category"]').should('be.visible');
        cy.get('[data-testid="material-description"]').should('be.visible');
        cy.get('[data-testid="material-value"]').should('be.visible');
      });
    });

    it('should display material categories with correct colors', () => {
      cy.get('[data-testid="materials-tab"]').click();
      cy.wait('@getMaterials');
      
      // Check for different material categories
      const expectedCategories = ['metal', 'volatile', 'silicate', 'exotic', 'processed'];
      
      expectedCategories.forEach(category => {
        cy.get(`[data-testid="material-category"][data-category="${category}"]`)
          .should('exist')
          .and('have.class', `bg-${category === 'metal' ? 'gray' : category === 'volatile' ? 'blue' : category === 'silicate' ? 'yellow' : category === 'exotic' ? 'purple' : 'green'}-600`);
      });
    });

    it('should load and display material deposits', () => {
      // Click on deposits tab
      cy.get('[data-testid="deposits-tab"]').click();
      
      // Wait for API call
      cy.wait('@getMaterialDeposits');
      
      // Check that deposits table is displayed
      cy.get('[data-testid="deposits-table"]', { timeout: 10000 })
        .should('be.visible');
      
      // Check table headers
      cy.get('[data-testid="deposits-table"] thead').within(() => {
        cy.contains('Material').should('be.visible');
        cy.contains('Body').should('be.visible');
        cy.contains('Richness').should('be.visible');
        cy.contains('Accessibility').should('be.visible');
        cy.contains('Status').should('be.visible');
      });
      
      // Check that deposit rows exist
      cy.get('[data-testid="deposit-row"]')
        .should('have.length.greaterThan', 0);
    });

    it('should display richness indicators with correct colors', () => {
      cy.get('[data-testid="deposits-tab"]').click();
      cy.wait('@getMaterialDeposits');
      
      // Check richness color coding
      cy.get('[data-testid="richness-indicator"]').each(($indicator) => {
        const richness = parseFloat($indicator.text());
        
        if (richness >= 80) {
          cy.wrap($indicator).should('have.class', 'text-green-400');
        } else if (richness >= 60) {
          cy.wrap($indicator).should('have.class', 'text-yellow-400');
        } else if (richness >= 40) {
          cy.wrap($indicator).should('have.class', 'text-orange-400');
        } else {
          cy.wrap($indicator).should('have.class', 'text-red-400');
        }
      });
    });

    it('should load and display space stations', () => {
      // Click on stations tab
      cy.get('[data-testid="stations-tab"]').click();
      
      // Wait for API call
      cy.wait('@getStations');
      
      // Check that stations are displayed
      cy.get('[data-testid="station-card"]', { timeout: 10000 })
        .should('exist');
      
      // If stations exist, check their content
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="station-card"]').length > 0) {
          cy.get('[data-testid="station-card"]').first().within(() => {
            cy.get('[data-testid="station-name"]').should('be.visible');
            cy.get('[data-testid="station-type"]').should('be.visible');
            cy.get('[data-testid="station-status"]').should('be.visible');
            cy.get('[data-testid="station-storage"]').should('be.visible');
          });
        }
      });
    });
  });

  describe('Station Construction Workflow', () => {
    it('should display construction form', () => {
      // Click on construction tab
      cy.get('[data-testid="construction-tab"]').click();
      
      // Wait for station types to load
      cy.wait('@getStationTypes');
      
      // Check form elements
      cy.get('[data-testid="construction-form"]', { timeout: 10000 })
        .should('be.visible');
      
      cy.get('[data-testid="station-type-select"]').should('be.visible');
      cy.get('[data-testid="orbiting-body-type-select"]').should('be.visible');
      cy.get('[data-testid="orbital-distance-input"]').should('be.visible');
      cy.get('[data-testid="construct-button"]').should('be.visible');
    });

    it('should populate station type options', () => {
      cy.get('[data-testid="construction-tab"]').click();
      cy.wait('@getStationTypes');
      
      // Check that station type select has options
      cy.get('[data-testid="station-type-select"]').click();
      cy.get('[data-testid="station-type-option"]')
        .should('have.length.greaterThan', 0);
      
      // Check for expected station types
      const expectedTypes = ['Orbital Mining Platform', 'Trading Hub', 'Refinery'];
      expectedTypes.forEach(type => {
        cy.get('[data-testid="station-type-select"]')
          .should('contain', type);
      });
    });

    it('should show/hide body ID field based on orbiting body type', () => {
      cy.get('[data-testid="construction-tab"]').click();
      cy.wait('@getStationTypes');
      
      // Initially, body ID should be hidden (deep space)
      cy.get('[data-testid="body-id-input"]').should('not.exist');
      
      // Select planet as orbiting body
      cy.get('[data-testid="orbiting-body-type-select"]').select('planet');
      
      // Body ID field should now be visible
      cy.get('[data-testid="body-id-input"]').should('be.visible');
      
      // Select deep space again
      cy.get('[data-testid="orbiting-body-type-select"]').select('');
      
      // Body ID field should be hidden again
      cy.get('[data-testid="body-id-input"]').should('not.exist');
    });

    it('should validate construction form', () => {
      cy.get('[data-testid="construction-tab"]').click();
      cy.wait('@getStationTypes');
      
      // Try to submit without required fields
      cy.get('[data-testid="construct-button"]').click();
      
      // Form should not submit (browser validation)
      cy.get('[data-testid="station-type-select"]:invalid').should('exist');
      
      // Fill required fields
      cy.get('[data-testid="station-type-select"]').select('orbital_mining_platform');
      cy.get('[data-testid="orbital-distance-input"]').clear().type('10000');
      
      // Now form should be valid
      cy.get('[data-testid="station-type-select"]:invalid').should('not.exist');
    });

    it('should submit construction order successfully', () => {
      cy.get('[data-testid="construction-tab"]').click();
      cy.wait('@getStationTypes');
      
      // Fill out the form
      cy.get('[data-testid="station-type-select"]').select('orbital_mining_platform');
      cy.get('[data-testid="orbiting-body-type-select"]').select('moon');
      cy.get('[data-testid="body-id-input"]').type('1');
      cy.get('[data-testid="orbital-distance-input"]').clear().type('15000');
      
      // Submit the form
      cy.get('[data-testid="construct-button"]').click();
      
      // Wait for API call
      cy.wait('@createConstructionOrder');
      
      // Check for success message or form reset
      cy.get('[data-testid="success-message"]', { timeout: 5000 })
        .should('be.visible')
        .and('contain', 'Construction order created');
      
      // Form should be reset
      cy.get('[data-testid="station-type-select"]').should('have.value', '');
    });
  });

  describe('Survey Orders Workflow', () => {
    it('should create survey orders for material discovery', () => {
      // This test assumes there's a way to create survey orders from the UI
      // Implementation may vary based on actual UI design
      
      cy.get('[data-testid="survey-button"]', { timeout: 10000 })
        .should('be.visible')
        .click();
      
      // Fill survey form
      cy.get('[data-testid="survey-body-type-select"]').select('planet');
      cy.get('[data-testid="survey-body-id-input"]').type('1');
      cy.get('[data-testid="survey-type-select"]').select('basic');
      
      // Submit survey order
      cy.get('[data-testid="submit-survey-button"]').click();
      
      // Wait for API call
      cy.wait('@createSurveyOrder');
      
      // Check for success
      cy.get('[data-testid="survey-success-message"]')
        .should('be.visible')
        .and('contain', 'Survey order created');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      // Intercept API calls to return errors
      cy.intercept('GET', `${API_BASE_URL}/v1/materials`, {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('getMaterialsError');
      
      // Navigate to materials tab
      cy.get('[data-testid="materials-tab"]').click();
      
      // Wait for error response
      cy.wait('@getMaterialsError');
      
      // Check that error message is displayed
      cy.get('[data-testid="error-message"]', { timeout: 5000 })
        .should('be.visible')
        .and('contain', 'Failed to load');
    });

    it('should handle network timeouts', () => {
      // Intercept API calls with delay
      cy.intercept('GET', `${API_BASE_URL}/v1/materials`, (req) => {
        req.reply((res) => {
          res.delay(10000); // 10 second delay
          res.send({ materials: [] });
        });
      }).as('getMaterialsTimeout');
      
      // Navigate to materials tab
      cy.get('[data-testid="materials-tab"]').click();
      
      // Check that loading indicator is shown
      cy.get('[data-testid="loading-indicator"]')
        .should('be.visible');
      
      // Wait for timeout or response
      cy.wait('@getMaterialsTimeout', { timeout: 15000 });
    });
  });

  describe('Responsive Design', () => {
    it('should work on mobile devices', () => {
      // Set mobile viewport
      cy.viewport(375, 667);
      
      // Check that materials manager is still functional
      cy.get('[data-testid="materials-manager"]')
        .should('be.visible');
      
      // Check that tabs are accessible
      cy.get('[data-testid="materials-tab"]').should('be.visible').click();
      cy.get('[data-testid="deposits-tab"]').should('be.visible').click();
      cy.get('[data-testid="stations-tab"]').should('be.visible').click();
      cy.get('[data-testid="construction-tab"]').should('be.visible').click();
    });

    it('should work on tablet devices', () => {
      // Set tablet viewport
      cy.viewport(768, 1024);
      
      // Check that layout adapts properly
      cy.get('[data-testid="materials-manager"]')
        .should('be.visible');
      
      // Check grid layouts
      cy.get('[data-testid="materials-tab"]').click();
      cy.wait('@getMaterials');
      
      cy.get('[data-testid="material-card"]')
        .should('be.visible')
        .and('have.length.greaterThan', 0);
    });
  });

  describe('Performance', () => {
    it('should load materials data within acceptable time', () => {
      const startTime = Date.now();
      
      cy.get('[data-testid="materials-tab"]').click();
      cy.wait('@getMaterials').then(() => {
        const loadTime = Date.now() - startTime;
        expect(loadTime).to.be.lessThan(3000); // 3 seconds max
      });
    });

    it('should handle large datasets efficiently', () => {
      // Mock large dataset
      cy.intercept('GET', `${API_BASE_URL}/v1/material-deposits*`, {
        fixture: 'large-deposits-dataset.json'
      }).as('getLargeDeposits');
      
      cy.get('[data-testid="deposits-tab"]').click();
      cy.wait('@getLargeDeposits');
      
      // Check that UI remains responsive
      cy.get('[data-testid="deposits-table"]')
        .should('be.visible');
      
      // Check that scrolling works
      cy.get('[data-testid="deposits-table"]').scrollTo('bottom');
      cy.get('[data-testid="deposits-table"]').scrollTo('top');
    });
  });
});
