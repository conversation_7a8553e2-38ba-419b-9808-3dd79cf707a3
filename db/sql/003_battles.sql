-- Battles table to persist combat outcomes

create table if not exists battles (
  id text primary key,              -- use order_id for MVP
  order_id text references orders(id),
  system_id text references systems(id),
  attacker_fleet_id text references fleets(id),
  target_fleet_id text references fleets(id),
  attacker_empire_id text references empires(id),
  target_empire_id text references empires(id),
  attacker_supply_after integer,
  target_supply_after integer,
  destroyed boolean,
  created_at timestamptz default now()
);

