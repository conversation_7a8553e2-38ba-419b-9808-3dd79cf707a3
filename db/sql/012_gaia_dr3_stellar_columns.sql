-- GAIA DR3 Stellar Data Columns Migration
-- Adds columns needed for GAIA DR3 stellar data integration

-- Add GAIA DR3 specific columns to stars table
DO $$ 
BEGIN
    -- GAIA DR3 source identifier
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'gaia_source_id') THEN
        ALTER TABLE stars ADD COLUMN gaia_source_id BIGINT;
        CREATE INDEX IF NOT EXISTS idx_stars_gaia_source_id ON stars(gaia_source_id);
    END IF;

    -- GAIA DR3 photometry
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'phot_g_mean_mag') THEN
        ALTER TABLE stars ADD COLUMN phot_g_mean_mag DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'phot_bp_mean_mag') THEN
        ALTER TABLE stars ADD COLUMN phot_bp_mean_mag DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'phot_rp_mean_mag') THEN
        ALTER TABLE stars ADD COLUMN phot_rp_mean_mag DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'bp_rp') THEN
        ALTER TABLE stars ADD COLUMN bp_rp DOUBLE PRECISION;
    END IF;

    -- GAIA DR3 astrometry
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'parallax_error') THEN
        ALTER TABLE stars ADD COLUMN parallax_error DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'parallax_over_error') THEN
        ALTER TABLE stars ADD COLUMN parallax_over_error DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'pmra_error') THEN
        ALTER TABLE stars ADD COLUMN pmra_error DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'pmdec_error') THEN
        ALTER TABLE stars ADD COLUMN pmdec_error DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'ruwe') THEN
        ALTER TABLE stars ADD COLUMN ruwe DOUBLE PRECISION;
    END IF;

    -- GAIA DR3 astrophysical parameters
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'teff_gspphot') THEN
        ALTER TABLE stars ADD COLUMN teff_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'teff_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN teff_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'teff_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN teff_gspphot_upper DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'logg_gspphot') THEN
        ALTER TABLE stars ADD COLUMN logg_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'logg_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN logg_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'logg_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN logg_gspphot_upper DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'mh_gspphot') THEN
        ALTER TABLE stars ADD COLUMN mh_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'mh_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN mh_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'mh_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN mh_gspphot_upper DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'distance_gspphot') THEN
        ALTER TABLE stars ADD COLUMN distance_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'distance_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN distance_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'distance_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN distance_gspphot_upper DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'azero_gspphot') THEN
        ALTER TABLE stars ADD COLUMN azero_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'ag_gspphot') THEN
        ALTER TABLE stars ADD COLUMN ag_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'ebpminrp_gspphot') THEN
        ALTER TABLE stars ADD COLUMN ebpminrp_gspphot DOUBLE PRECISION;
    END IF;

    -- GAIA DR3 stellar mass and radius
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'mass_gspphot') THEN
        ALTER TABLE stars ADD COLUMN mass_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'mass_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN mass_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'mass_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN mass_gspphot_upper DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'radius_gspphot') THEN
        ALTER TABLE stars ADD COLUMN radius_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'radius_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN radius_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'radius_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN radius_gspphot_upper DOUBLE PRECISION;
    END IF;

    -- GAIA DR3 luminosity
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'lum_gspphot') THEN
        ALTER TABLE stars ADD COLUMN lum_gspphot DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'lum_gspphot_lower') THEN
        ALTER TABLE stars ADD COLUMN lum_gspphot_lower DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'lum_gspphot_upper') THEN
        ALTER TABLE stars ADD COLUMN lum_gspphot_upper DOUBLE PRECISION;
    END IF;

    -- GAIA DR3 radial velocity
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'dr2_radial_velocity') THEN
        ALTER TABLE stars ADD COLUMN dr2_radial_velocity DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'dr2_radial_velocity_error') THEN
        ALTER TABLE stars ADD COLUMN dr2_radial_velocity_error DOUBLE PRECISION;
    END IF;

    -- GAIA DR3 variability
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'phot_variable_flag') THEN
        ALTER TABLE stars ADD COLUMN phot_variable_flag TEXT;
    END IF;

    -- GAIA DR3 data quality flags
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'astrometric_excess_noise') THEN
        ALTER TABLE stars ADD COLUMN astrometric_excess_noise DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'astrometric_excess_noise_sig') THEN
        ALTER TABLE stars ADD COLUMN astrometric_excess_noise_sig DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'astrometric_chi2_al') THEN
        ALTER TABLE stars ADD COLUMN astrometric_chi2_al DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'astrometric_n_good_obs_al') THEN
        ALTER TABLE stars ADD COLUMN astrometric_n_good_obs_al INTEGER;
    END IF;

    -- GAIA DR3 epoch photometry availability
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'has_epoch_photometry') THEN
        ALTER TABLE stars ADD COLUMN has_epoch_photometry BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'has_epoch_rv') THEN
        ALTER TABLE stars ADD COLUMN has_epoch_rv BOOLEAN DEFAULT false;
    END IF;

    -- GAIA DR3 source classification
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'in_qso_candidates') THEN
        ALTER TABLE stars ADD COLUMN in_qso_candidates BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'in_galaxy_candidates') THEN
        ALTER TABLE stars ADD COLUMN in_galaxy_candidates BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'non_single_star') THEN
        ALTER TABLE stars ADD COLUMN non_single_star INTEGER DEFAULT 0;
    END IF;

    -- GAIA DR3 data processing flags
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'duplicated_source') THEN
        ALTER TABLE stars ADD COLUMN duplicated_source BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'phot_bp_rp_excess_factor') THEN
        ALTER TABLE stars ADD COLUMN phot_bp_rp_excess_factor DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'phot_proc_mode') THEN
        ALTER TABLE stars ADD COLUMN phot_proc_mode INTEGER;
    END IF;

    -- GAIA DR3 data release info
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'gaia_data_release') THEN
        ALTER TABLE stars ADD COLUMN gaia_data_release TEXT DEFAULT 'DR3';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stars' AND column_name = 'gaia_data_processing_date') THEN
        ALTER TABLE stars ADD COLUMN gaia_data_processing_date TIMESTAMPTZ;
    END IF;

END $$;

-- Create additional indexes for GAIA DR3 data
CREATE INDEX IF NOT EXISTS idx_stars_gaia_phot_g ON stars(phot_g_mean_mag);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_bp_rp ON stars(bp_rp);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_parallax_error ON stars(parallax_over_error);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_ruwe ON stars(ruwe);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_teff ON stars(teff_gspphot);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_mass ON stars(mass_gspphot);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_radius ON stars(radius_gspphot);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_distance ON stars(distance_gspphot);
CREATE INDEX IF NOT EXISTS idx_stars_gaia_variable ON stars(phot_variable_flag);

-- Add comments for GAIA DR3 columns
COMMENT ON COLUMN stars.gaia_source_id IS 'GAIA DR3 unique source identifier';
COMMENT ON COLUMN stars.phot_g_mean_mag IS 'GAIA G-band mean magnitude';
COMMENT ON COLUMN stars.phot_bp_mean_mag IS 'GAIA BP-band mean magnitude';
COMMENT ON COLUMN stars.phot_rp_mean_mag IS 'GAIA RP-band mean magnitude';
COMMENT ON COLUMN stars.bp_rp IS 'GAIA BP-RP color index';
COMMENT ON COLUMN stars.parallax_over_error IS 'GAIA parallax divided by its error';
COMMENT ON COLUMN stars.ruwe IS 'GAIA Renormalised Unit Weight Error';
COMMENT ON COLUMN stars.teff_gspphot IS 'GAIA effective temperature from GSP-Phot';
COMMENT ON COLUMN stars.logg_gspphot IS 'GAIA surface gravity from GSP-Phot';
COMMENT ON COLUMN stars.mh_gspphot IS 'GAIA metallicity [M/H] from GSP-Phot';
COMMENT ON COLUMN stars.mass_gspphot IS 'GAIA stellar mass from GSP-Phot (solar masses)';
COMMENT ON COLUMN stars.radius_gspphot IS 'GAIA stellar radius from GSP-Phot (solar radii)';
COMMENT ON COLUMN stars.distance_gspphot IS 'GAIA distance from GSP-Phot (parsecs)';
COMMENT ON COLUMN stars.lum_gspphot IS 'GAIA luminosity from GSP-Phot (solar luminosities)';
COMMENT ON COLUMN stars.phot_variable_flag IS 'GAIA photometric variability flag';
COMMENT ON COLUMN stars.non_single_star IS 'GAIA non-single star flag (0=single, >0=multiple)';

-- Update existing stellar_mass and stellar_radius columns to use GAIA data where available
UPDATE stars 
SET 
    mass_solar = COALESCE(mass_gspphot, mass_solar),
    radius_solar = COALESCE(radius_gspphot, radius_solar),
    teff_k = COALESCE(teff_gspphot, teff_k),
    luminosity_solar = COALESCE(lum_gspphot, luminosity_solar)
WHERE src = 'gaia_dr3' OR gaia_source_id IS NOT NULL;
