-- Technology Effects System
-- Adds technology-unlocked improvements and empire bonuses

-- Add tech_required column to improvement_types if it doesn't exist
alter table improvement_types add column if not exists tech_required text references tech_nodes(id);

-- Add new technology-unlocked improvement types
insert into improvement_types (type, name, base_cost, yields, maintenance, max_level, tech_required, description) values
-- Physics branch unlocks
('advanced_generator', 'Advanced Generator', '{"minerals": 120, "energy": 20, "credits": 70}', '{"energy": 8}', '{"minerals": 2}', 5, 'phys_basic_energy', 'High-efficiency energy production facility'),
('advanced_mine', 'Advanced Mine', '{"minerals": 150, "energy": 25, "credits": 80}', '{"minerals": 6}', '{"energy": 3}', 5, 'phys_basic_materials', 'High-efficiency mineral extraction facility'),
('fusion_reactor', 'Fusion Reactor', '{"minerals": 300, "energy": 50, "credits": 150}', '{"energy": 15}', '{"minerals": 3}', 3, 'phys_fusion_power', 'Clean fusion energy generation'),
('advanced_factory', 'Advanced Factory', '{"minerals": 200, "energy": 50, "credits": 120}', '{"credits": 8}', '{"energy": 6, "minerals": 3}', 3, 'phys_advanced_materials', 'High-tech manufacturing facility'),

-- Biotech branch unlocks
('genetic_lab', 'Genetic Laboratory', '{"minerals": 150, "energy": 40, "credits": 80}', '{"research": 4}', '{"energy": 4}', 5, 'bio_basic_genetics', 'Advanced genetic research facility'),
('hydroponic_farm', 'Hydroponic Farm', '{"minerals": 100, "energy": 30, "credits": 60}', '{"food": 5}', '{"energy": 3}', 5, 'bio_hydroponics', 'Soil-free agricultural system'),
('gene_clinic', 'Gene Clinic', '{"minerals": 180, "energy": 60, "credits": 100}', '{"research": 2}', '{"energy": 5}', 3, 'bio_genetic_engineering', 'Advanced genetic modification facility'),
('terraforming_station', 'Terraforming Station', '{"minerals": 500, "energy": 100, "credits": 200}', '{"research": 3}', '{"energy": 10, "minerals": 5}', 1, 'bio_terraforming', 'Planetary climate modification system'),

-- Society branch unlocks
('administrative_center', 'Administrative Center', '{"minerals": 150, "energy": 40, "credits": 80}', '{"influence": 4}', '{"energy": 3}', 3, 'soc_basic_administration', 'Improved governmental organization'),
('cultural_center', 'Cultural Center', '{"minerals": 120, "energy": 30, "credits": 90}', '{"culture": 4}', '{"energy": 2}', 5, 'soc_cultural_exchange', 'Arts and cultural exchange facility'),
('trade_hub', 'Trade Hub', '{"minerals": 200, "energy": 40, "credits": 150}', '{"credits": 6, "influence": 2}', '{"energy": 4}', 3, 'soc_trade_networks', 'Commercial trading center')

on conflict (type) do nothing;

-- Empire technology bonuses table
create table if not exists empire_tech_bonuses (
  empire_id text references empires(id) on delete cascade,
  bonus_type text not null, -- 'energy_production', 'population_growth', etc.
  bonus_value numeric not null default 0, -- percentage bonus (0.1 = 10%)
  source_tech text references tech_nodes(id) on delete cascade,
  primary key (empire_id, bonus_type, source_tech)
);

-- Function to calculate empire technology bonuses
create or replace function calculate_empire_tech_bonuses(p_empire_id text)
returns table(bonus_type text, total_bonus numeric) as $$
begin
  return query
  select 
    etb.bonus_type,
    sum(etb.bonus_value) as total_bonus
  from empire_tech_bonuses etb
  where etb.empire_id = p_empire_id
  group by etb.bonus_type;
end;
$$ language plpgsql;

-- Function to apply technology bonuses when a tech is unlocked
create or replace function apply_technology_bonuses(p_empire_id text, p_tech_id text)
returns void as $$
declare
  tech_effects jsonb;
  bonus_key text;
  bonus_value numeric;
begin
  -- Get the technology effects
  select effects into tech_effects
  from tech_nodes
  where id = p_tech_id;
  
  -- Apply bonuses if they exist
  if tech_effects ? 'bonuses' then
    for bonus_key, bonus_value in select * from jsonb_each_text(tech_effects->'bonuses')
    loop
      insert into empire_tech_bonuses (empire_id, bonus_type, bonus_value, source_tech)
      values (p_empire_id, bonus_key, bonus_value::numeric, p_tech_id)
      on conflict (empire_id, bonus_type, source_tech) do update
      set bonus_value = excluded.bonus_value;
    end loop;
  end if;
end;
$$ language plpgsql;

-- Function to get available improvement types for an empire (considering tech requirements)
create or replace function get_available_improvements(p_empire_id text)
returns table(
  type text,
  name text,
  base_cost jsonb,
  yields jsonb,
  maintenance jsonb,
  max_level integer,
  tech_required text,
  description text,
  is_available boolean
) as $$
begin
  return query
  select 
    it.type,
    it.name,
    it.base_cost,
    it.yields,
    it.maintenance,
    it.max_level,
    it.tech_required,
    it.description,
    case 
      when it.tech_required is null then true
      when tp.unlocked = true then true
      else false
    end as is_available
  from improvement_types it
  left join tech_progress tp on it.tech_required = tp.tech_id and tp.empire_id = p_empire_id
  order by it.name;
end;
$$ language plpgsql;

-- Apply bonuses for already unlocked technologies
do $$
declare
  empire_record record;
  tech_record record;
begin
  -- For each empire
  for empire_record in select id from empires
  loop
    -- For each unlocked technology
    for tech_record in 
      select tp.tech_id 
      from tech_progress tp 
      where tp.empire_id = empire_record.id and tp.unlocked = true
    loop
      -- Apply the technology bonuses
      perform apply_technology_bonuses(empire_record.id, tech_record.tech_id);
    end loop;
  end loop;
end;
$$;

-- Indexes for performance
create index if not exists idx_empire_tech_bonuses_empire on empire_tech_bonuses(empire_id);
create index if not exists idx_empire_tech_bonuses_type on empire_tech_bonuses(empire_id, bonus_type);
create index if not exists idx_improvement_types_tech on improvement_types(tech_required);
