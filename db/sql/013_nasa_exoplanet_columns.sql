-- NASA Exoplanet Archive Columns Migration
-- Adds columns needed for NASA Exoplanet Archive data integration

-- Add NASA Exoplanet Archive specific columns to planets table
DO $$ 
BEGIN
    -- NASA archive identifiers
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_name') THEN
        ALTER TABLE planets ADD COLUMN pl_name TEXT;
        CREATE INDEX IF NOT EXISTS idx_planets_pl_name ON planets(pl_name);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'hostname') THEN
        ALTER TABLE planets ADD COLUMN hostname TEXT;
        CREATE INDEX IF NOT EXISTS idx_planets_hostname ON planets(hostname);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'default_flag') THEN
        ALTER TABLE planets ADD COLUMN default_flag INTEGER DEFAULT 1;
    END IF;

    -- NASA archive orbital parameters with uncertainties
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbper') THEN
        ALTER TABLE planets ADD COLUMN pl_orbper DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbpererr1') THEN
        ALTER TABLE planets ADD COLUMN pl_orbpererr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbpererr2') THEN
        ALTER TABLE planets ADD COLUMN pl_orbpererr2 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbsmax') THEN
        ALTER TABLE planets ADD COLUMN pl_orbsmax DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbsmaxerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_orbsmaxerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbsmaxerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_orbsmaxerr2 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbeccen') THEN
        ALTER TABLE planets ADD COLUMN pl_orbeccen DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbeccenerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_orbeccenerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbeccenerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_orbeccenerr2 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbincl') THEN
        ALTER TABLE planets ADD COLUMN pl_orbincl DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbinclerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_orbinclerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_orbinclerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_orbinclerr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive mass parameters
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_bmasse') THEN
        ALTER TABLE planets ADD COLUMN pl_bmasse DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_bmasseerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_bmasseerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_bmasseerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_bmasseerr2 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_bmassj') THEN
        ALTER TABLE planets ADD COLUMN pl_bmassj DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_bmassjerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_bmassjerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_bmassjerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_bmassjerr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive radius parameters
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_rade') THEN
        ALTER TABLE planets ADD COLUMN pl_rade DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_radeerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_radeerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_radeerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_radeerr2 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_radj') THEN
        ALTER TABLE planets ADD COLUMN pl_radj DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_radjerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_radjerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_radjerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_radjerr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive density
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_dens') THEN
        ALTER TABLE planets ADD COLUMN pl_dens DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_denserr1') THEN
        ALTER TABLE planets ADD COLUMN pl_denserr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_denserr2') THEN
        ALTER TABLE planets ADD COLUMN pl_denserr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive equilibrium temperature
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_eqt') THEN
        ALTER TABLE planets ADD COLUMN pl_eqt DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_eqterr1') THEN
        ALTER TABLE planets ADD COLUMN pl_eqterr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_eqterr2') THEN
        ALTER TABLE planets ADD COLUMN pl_eqterr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive stellar flux
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_insol') THEN
        ALTER TABLE planets ADD COLUMN pl_insol DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_insolerr1') THEN
        ALTER TABLE planets ADD COLUMN pl_insolerr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_insolerr2') THEN
        ALTER TABLE planets ADD COLUMN pl_insolerr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive discovery information
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'discoverymethod') THEN
        ALTER TABLE planets ADD COLUMN discoverymethod TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'disc_year') THEN
        ALTER TABLE planets ADD COLUMN disc_year INTEGER;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'disc_facility') THEN
        ALTER TABLE planets ADD COLUMN disc_facility TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'disc_telescope') THEN
        ALTER TABLE planets ADD COLUMN disc_telescope TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'disc_instrument') THEN
        ALTER TABLE planets ADD COLUMN disc_instrument TEXT;
    END IF;

    -- NASA archive stellar parameters
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_snum') THEN
        ALTER TABLE planets ADD COLUMN sy_snum INTEGER;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_pnum') THEN
        ALTER TABLE planets ADD COLUMN sy_pnum INTEGER;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_mnum') THEN
        ALTER TABLE planets ADD COLUMN sy_mnum INTEGER;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_dist') THEN
        ALTER TABLE planets ADD COLUMN sy_dist DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_disterr1') THEN
        ALTER TABLE planets ADD COLUMN sy_disterr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_disterr2') THEN
        ALTER TABLE planets ADD COLUMN sy_disterr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive coordinates
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'ra') THEN
        ALTER TABLE planets ADD COLUMN ra DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'dec') THEN
        ALTER TABLE planets ADD COLUMN dec DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_pm') THEN
        ALTER TABLE planets ADD COLUMN sy_pm DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_pmra') THEN
        ALTER TABLE planets ADD COLUMN sy_pmra DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'sy_pmdec') THEN
        ALTER TABLE planets ADD COLUMN sy_pmdec DOUBLE PRECISION;
    END IF;

    -- NASA archive transit parameters
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_trandep') THEN
        ALTER TABLE planets ADD COLUMN pl_trandep DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_trandur') THEN
        ALTER TABLE planets ADD COLUMN pl_trandur DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_tranmid') THEN
        ALTER TABLE planets ADD COLUMN pl_tranmid DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_imppar') THEN
        ALTER TABLE planets ADD COLUMN pl_imppar DOUBLE PRECISION;
    END IF;

    -- NASA archive RV parameters
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_rvamp') THEN
        ALTER TABLE planets ADD COLUMN pl_rvamp DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_rvamperr1') THEN
        ALTER TABLE planets ADD COLUMN pl_rvamperr1 DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_rvamperr2') THEN
        ALTER TABLE planets ADD COLUMN pl_rvamperr2 DOUBLE PRECISION;
    END IF;

    -- NASA archive publication information
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_refname') THEN
        ALTER TABLE planets ADD COLUMN pl_refname TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_pubdate') THEN
        ALTER TABLE planets ADD COLUMN pl_pubdate DATE;
    END IF;

    -- NASA archive data release information
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'releasedate') THEN
        ALTER TABLE planets ADD COLUMN releasedate DATE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_controv_flag') THEN
        ALTER TABLE planets ADD COLUMN pl_controv_flag INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_tsystemref') THEN
        ALTER TABLE planets ADD COLUMN pl_tsystemref TEXT;
    END IF;

    -- NASA archive habitability indicators
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_habitable') THEN
        ALTER TABLE planets ADD COLUMN pl_habitable INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'planets' AND column_name = 'pl_esi') THEN
        ALTER TABLE planets ADD COLUMN pl_esi DOUBLE PRECISION;
    END IF;

END $$;

-- Create additional indexes for NASA Exoplanet Archive data
CREATE INDEX IF NOT EXISTS idx_planets_nasa_pl_name ON planets(pl_name);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_hostname ON planets(hostname);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_discovery_method ON planets(discoverymethod);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_discovery_year ON planets(disc_year);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_facility ON planets(disc_facility);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_coordinates ON planets(ra, dec);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_system_distance ON planets(sy_dist);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_habitable ON planets(pl_habitable);
CREATE INDEX IF NOT EXISTS idx_planets_nasa_esi ON planets(pl_esi);

-- Add comments for NASA Exoplanet Archive columns
COMMENT ON COLUMN planets.pl_name IS 'NASA Exoplanet Archive planet name';
COMMENT ON COLUMN planets.hostname IS 'NASA Exoplanet Archive host star name';
COMMENT ON COLUMN planets.pl_orbper IS 'NASA orbital period (days)';
COMMENT ON COLUMN planets.pl_orbsmax IS 'NASA semi-major axis (AU)';
COMMENT ON COLUMN planets.pl_orbeccen IS 'NASA orbital eccentricity';
COMMENT ON COLUMN planets.pl_orbincl IS 'NASA orbital inclination (degrees)';
COMMENT ON COLUMN planets.pl_bmasse IS 'NASA planet mass (Earth masses)';
COMMENT ON COLUMN planets.pl_rade IS 'NASA planet radius (Earth radii)';
COMMENT ON COLUMN planets.pl_dens IS 'NASA planet density (g/cm³)';
COMMENT ON COLUMN planets.pl_eqt IS 'NASA equilibrium temperature (K)';
COMMENT ON COLUMN planets.pl_insol IS 'NASA insolation flux (Earth flux)';
COMMENT ON COLUMN planets.discoverymethod IS 'NASA discovery method';
COMMENT ON COLUMN planets.disc_year IS 'NASA discovery year';
COMMENT ON COLUMN planets.disc_facility IS 'NASA discovery facility';
COMMENT ON COLUMN planets.sy_snum IS 'NASA number of stars in system';
COMMENT ON COLUMN planets.sy_pnum IS 'NASA number of planets in system';
COMMENT ON COLUMN planets.sy_dist IS 'NASA system distance (parsecs)';
COMMENT ON COLUMN planets.pl_trandep IS 'NASA transit depth (parts per million)';
COMMENT ON COLUMN planets.pl_trandur IS 'NASA transit duration (hours)';
COMMENT ON COLUMN planets.pl_rvamp IS 'NASA radial velocity amplitude (m/s)';
COMMENT ON COLUMN planets.pl_habitable IS 'NASA habitability flag (0=no, 1=yes)';
COMMENT ON COLUMN planets.pl_esi IS 'NASA Earth Similarity Index';

-- Update existing columns with NASA data where available
UPDATE planets 
SET 
    mass_earth = COALESCE(pl_bmasse, mass_earth),
    radius_earth = COALESCE(pl_rade, radius_earth),
    sma_au = COALESCE(pl_orbsmax, sma_au),
    period_days = COALESCE(pl_orbper, period_days),
    eccentricity = COALESCE(pl_orbeccen, eccentricity),
    inclination_deg = COALESCE(pl_orbincl, inclination_deg),
    eq_temp_k = COALESCE(pl_eqt, eq_temp_k),
    density_gcc = COALESCE(pl_dens, density_gcc),
    discovery_method = COALESCE(discoverymethod, discovery_method),
    discovery_year = COALESCE(disc_year, discovery_year),
    discovery_facility = COALESCE(disc_facility, discovery_facility),
    earth_similarity_index = COALESCE(pl_esi, earth_similarity_index)
WHERE src = 'nasa_exoplanet_archive' OR pl_name IS NOT NULL;
