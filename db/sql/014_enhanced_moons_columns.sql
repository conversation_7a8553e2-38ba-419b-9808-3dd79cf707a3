-- Enhanced Moons Table Columns Migration
-- Adds missing columns needed for comprehensive moon data

-- Add missing columns to moons table
DO $$ 
BEGIN
    -- Source tracking columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'src') THEN
        ALTER TABLE moons ADD COLUMN src TEXT NOT NULL DEFAULT 'unknown';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'src_key') THEN
        ALTER TABLE moons ADD COLUMN src_key TEXT;
    END IF;

    -- Physical properties
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'mass_kg') THEN
        ALTER TABLE moons ADD COLUMN mass_kg DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'radius_km') THEN
        ALTER TABLE moons ADD COLUMN radius_km DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'sma_km') THEN
        ALTER TABLE moons ADD COLUMN sma_km DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'sma_planet_radii') THEN
        ALTER TABLE moons ADD COLUMN sma_planet_radii DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'period_days') THEN
        ALTER TABLE moons ADD COLUMN period_days DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'eccentricity') THEN
        ALTER TABLE moons ADD COLUMN eccentricity DOUBLE PRECISION DEFAULT 0.0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'inclination_deg') THEN
        ALTER TABLE moons ADD COLUMN inclination_deg DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'density_gcc') THEN
        ALTER TABLE moons ADD COLUMN density_gcc DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'surface_temp_k') THEN
        ALTER TABLE moons ADD COLUMN surface_temp_k DOUBLE PRECISION;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'composition') THEN
        ALTER TABLE moons ADD COLUMN composition TEXT DEFAULT 'unknown';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'atmosphere') THEN
        ALTER TABLE moons ADD COLUMN atmosphere TEXT;
    END IF;

    -- Geological and physical features
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'has_atmosphere') THEN
        ALTER TABLE moons ADD COLUMN has_atmosphere BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'has_water') THEN
        ALTER TABLE moons ADD COLUMN has_water BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'has_subsurface_ocean') THEN
        ALTER TABLE moons ADD COLUMN has_subsurface_ocean BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'geological_activity') THEN
        ALTER TABLE moons ADD COLUMN geological_activity TEXT DEFAULT 'inactive';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'surface_features') THEN
        ALTER TABLE moons ADD COLUMN surface_features TEXT[];
    END IF;

    -- Tidal properties
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'tidal_heating') THEN
        ALTER TABLE moons ADD COLUMN tidal_heating BOOLEAN DEFAULT false;
    END IF;

    -- Discovery information
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'discoverer') THEN
        ALTER TABLE moons ADD COLUMN discoverer TEXT;
    END IF;

    -- Resource potential (for game mechanics)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'mineral_richness') THEN
        ALTER TABLE moons ADD COLUMN mineral_richness DOUBLE PRECISION DEFAULT 0.5;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'water_ice_content') THEN
        ALTER TABLE moons ADD COLUMN water_ice_content DOUBLE PRECISION DEFAULT 0.0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'rare_elements') THEN
        ALTER TABLE moons ADD COLUMN rare_elements TEXT[];
    END IF;

    -- Game integration
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'is_colonized') THEN
        ALTER TABLE moons ADD COLUMN is_colonized BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'is_exploitable') THEN
        ALTER TABLE moons ADD COLUMN is_exploitable BOOLEAN DEFAULT true;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'strategic_value') THEN
        ALTER TABLE moons ADD COLUMN strategic_value DOUBLE PRECISION DEFAULT 0.5;
    END IF;

    -- Update existing columns to match new schema
    -- Map existing columns to new ones where possible
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'distance_km') THEN
        UPDATE moons SET sma_km = distance_km WHERE sma_km IS NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'orbital_period_days') THEN
        UPDATE moons SET period_days = orbital_period_days WHERE period_days IS NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'orbital_eccentricity') THEN
        UPDATE moons SET eccentricity = orbital_eccentricity WHERE eccentricity IS NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'orbital_inclination_deg') THEN
        UPDATE moons SET inclination_deg = orbital_inclination_deg WHERE inclination_deg IS NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'diameter_km') THEN
        UPDATE moons SET radius_km = diameter_km / 2.0 WHERE radius_km IS NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'density_g_cm3') THEN
        UPDATE moons SET density_gcc = density_g_cm3 WHERE density_gcc IS NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'surface_composition') THEN
        UPDATE moons SET composition = surface_composition WHERE composition = 'unknown';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moons' AND column_name = 'avg_surface_temp_c') THEN
        UPDATE moons SET surface_temp_k = avg_surface_temp_c + 273.15 WHERE surface_temp_k IS NULL AND avg_surface_temp_c IS NOT NULL;
    END IF;

END $$;

-- Create additional indexes for enhanced moon data
CREATE INDEX IF NOT EXISTS idx_moons_src_key ON moons(src, src_key);
CREATE INDEX IF NOT EXISTS idx_moons_composition ON moons(composition);
CREATE INDEX IF NOT EXISTS idx_moons_geological_activity ON moons(geological_activity);
CREATE INDEX IF NOT EXISTS idx_moons_has_water ON moons(has_water);
CREATE INDEX IF NOT EXISTS idx_moons_has_subsurface_ocean ON moons(has_subsurface_ocean);
CREATE INDEX IF NOT EXISTS idx_moons_strategic_value ON moons(strategic_value);
CREATE INDEX IF NOT EXISTS idx_moons_mineral_richness ON moons(mineral_richness);

-- Create unique constraint for source data
CREATE UNIQUE INDEX IF NOT EXISTS idx_moons_src_unique 
ON moons(src, src_key) 
WHERE src_key IS NOT NULL;

-- Add constraints for enhanced moon data
DO $$
BEGIN
    -- Add check constraints if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_mass_kg_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_mass_kg_check CHECK (mass_kg > 0 OR mass_kg IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_radius_km_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_radius_km_check CHECK (radius_km > 0 OR radius_km IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_sma_km_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_sma_km_check CHECK (sma_km > 0 OR sma_km IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_period_days_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_period_days_check CHECK (period_days > 0 OR period_days IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_eccentricity_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_eccentricity_check CHECK (eccentricity >= 0 AND eccentricity < 1 OR eccentricity IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_density_gcc_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_density_gcc_check CHECK (density_gcc > 0 OR density_gcc IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_mineral_richness_check_new') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_mineral_richness_check_new CHECK (mineral_richness >= 0.0 AND mineral_richness <= 1.0);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_water_ice_content_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_water_ice_content_check CHECK (water_ice_content >= 0.0 AND water_ice_content <= 1.0);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_strategic_value_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_strategic_value_check CHECK (strategic_value >= 0.0 AND strategic_value <= 1.0);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_composition_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_composition_check CHECK (composition IN ('rocky', 'icy', 'mixed', 'metallic', 'gaseous', 'unknown'));
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_geological_activity_check') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_geological_activity_check CHECK (geological_activity IN ('active', 'inactive', 'unknown'));
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'moons_exploration_status_check_new') THEN
        ALTER TABLE moons ADD CONSTRAINT moons_exploration_status_check_new CHECK (exploration_status IN ('unexplored', 'surveyed', 'colonized', 'abandoned'));
    END IF;

END $$;

-- Add comments for enhanced moon columns
COMMENT ON COLUMN moons.src IS 'Data source (nasa, iau, proc, manual)';
COMMENT ON COLUMN moons.src_key IS 'Source-specific identifier';
COMMENT ON COLUMN moons.mass_kg IS 'Moon mass in kilograms';
COMMENT ON COLUMN moons.radius_km IS 'Mean radius in kilometers';
COMMENT ON COLUMN moons.sma_km IS 'Semi-major axis from planet (km)';
COMMENT ON COLUMN moons.sma_planet_radii IS 'Semi-major axis in planet radii';
COMMENT ON COLUMN moons.period_days IS 'Orbital period (days)';
COMMENT ON COLUMN moons.eccentricity IS 'Orbital eccentricity';
COMMENT ON COLUMN moons.inclination_deg IS 'Orbital inclination (degrees)';
COMMENT ON COLUMN moons.density_gcc IS 'Density (g/cm³)';
COMMENT ON COLUMN moons.surface_temp_k IS 'Surface temperature (Kelvin)';
COMMENT ON COLUMN moons.composition IS 'Primary composition (rocky, icy, mixed, etc.)';
COMMENT ON COLUMN moons.atmosphere IS 'Atmosphere composition';
COMMENT ON COLUMN moons.has_atmosphere IS 'Has substantial atmosphere';
COMMENT ON COLUMN moons.has_water IS 'Evidence of water/ice';
COMMENT ON COLUMN moons.has_subsurface_ocean IS 'Subsurface ocean present';
COMMENT ON COLUMN moons.geological_activity IS 'Geological activity level';
COMMENT ON COLUMN moons.surface_features IS 'Array of surface feature types';
COMMENT ON COLUMN moons.tidal_heating IS 'Experiences tidal heating';
COMMENT ON COLUMN moons.discoverer IS 'Person/team who discovered';
COMMENT ON COLUMN moons.mineral_richness IS 'Mineral abundance (0.0-1.0)';
COMMENT ON COLUMN moons.water_ice_content IS 'Water ice fraction (0.0-1.0)';
COMMENT ON COLUMN moons.rare_elements IS 'Array of rare element types';
COMMENT ON COLUMN moons.is_colonized IS 'Currently colonized';
COMMENT ON COLUMN moons.is_exploitable IS 'Can be mined/exploited';
COMMENT ON COLUMN moons.strategic_value IS 'Strategic importance (0.0-1.0)';

-- Insert some well-known moons for our Solar System
INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days, 
                   composition, has_water, discovery_year, discoverer) 
SELECT p.planet_id, 'manual', 'luna', 'Luna', 7.342e22, 1737.4, 384400, 27.3,
       'rocky', true, NULL, 'Ancient'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Earth'
ON CONFLICT (planet_id, name) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, has_water, has_subsurface_ocean, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'europa', 'Europa', 4.8e22, 1560.8, 671034, 3.55,
       'icy', true, true, 1610, 'Galileo Galilei'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Jupiter'
ON CONFLICT (planet_id, name) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, geological_activity, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'io', 'Io', 8.93e22, 1821.6, 421700, 1.77,
       'rocky', 'active', 1610, 'Galileo Galilei'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Jupiter'
ON CONFLICT (planet_id, name) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, has_water, has_subsurface_ocean, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'enceladus', 'Enceladus', 1.08e20, 252.1, 238020, 1.37,
       'icy', true, true, 1789, 'William Herschel'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Saturn'
ON CONFLICT (planet_id, name) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, has_atmosphere, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'titan', 'Titan', 1.35e23, 2574, 1221830, 15.95,
       'icy', true, 1655, 'Christiaan Huygens'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Saturn'
ON CONFLICT (planet_id, name) DO NOTHING;
