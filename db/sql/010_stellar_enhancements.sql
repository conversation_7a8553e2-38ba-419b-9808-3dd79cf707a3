-- Stellar Database Enhancements
-- Add comprehensive fields for realistic galaxy map implementation

BEGIN;

-- Add 3D coordinate fields to stars table
ALTER TABLE stars ADD COLUMN IF NOT EXISTS x_pc DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS y_pc DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS z_pc DOUBLE PRECISION;

-- Add comprehensive stellar identification fields
ALTER TABLE stars ADD COLUMN IF NOT EXISTS proper_name TEXT;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS bayer_designation TEXT;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS flamsteed_number INTEGER;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS henry_draper_id INTEGER;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS hipparcos_id INTEGER;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS gliese_id TEXT;

-- Add photometric and spectroscopic data
ALTER TABLE stars ADD COLUMN IF NOT EXISTS color_index_bv DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS color_index_ub DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS absolute_magnitude DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS stellar_class TEXT;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS luminosity_class TEXT;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS variable_type TEXT;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS stellar_color TEXT;

-- Add physical properties
ALTER TABLE stars ADD COLUMN IF NOT EXISTS multiplicity INTEGER DEFAULT 1;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS radial_velocity_kms DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS rotation_period_days DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS magnetic_field_gauss DOUBLE PRECISION;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS age_uncertainty_gyr DOUBLE PRECISION;

-- Add data quality and metadata
ALTER TABLE stars ADD COLUMN IF NOT EXISTS data_quality_score DOUBLE PRECISION DEFAULT 1.0;
ALTER TABLE stars ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT now();

-- Add comprehensive exoplanet fields to planets table
ALTER TABLE planets ADD COLUMN IF NOT EXISTS kepler_name TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS toi_id TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS k2_name TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS host_star_name TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS planet_letter TEXT;

-- Add uncertainty fields
ALTER TABLE planets ADD COLUMN IF NOT EXISTS orbital_period_uncertainty_days DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS mass_uncertainty_earth DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS radius_uncertainty_earth DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS equilibrium_temp_uncertainty_k DOUBLE PRECISION;

-- Add advanced planetary properties
ALTER TABLE planets ADD COLUMN IF NOT EXISTS insolation_flux_earth DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS escape_velocity_kms DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS surface_gravity_ms2 DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS scale_height_km DOUBLE PRECISION;

-- Add transit properties
ALTER TABLE planets ADD COLUMN IF NOT EXISTS transit_duration_hours DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS transit_depth_ppm DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS impact_parameter DOUBLE PRECISION;

-- Add habitability indicators
ALTER TABLE planets ADD COLUMN IF NOT EXISTS stellar_flux_earth DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS tidal_locking_probability DOUBLE PRECISION;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS runaway_greenhouse_flag BOOLEAN DEFAULT false;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS moist_greenhouse_flag BOOLEAN DEFAULT false;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS venus_zone_flag BOOLEAN DEFAULT false;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS earth_similarity_index DOUBLE PRECISION;

-- Add classification and status fields
ALTER TABLE planets ADD COLUMN IF NOT EXISTS planetary_class TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS confirmed_status TEXT DEFAULT 'confirmed';
ALTER TABLE planets ADD COLUMN IF NOT EXISTS disposition TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS data_source TEXT DEFAULT 'manual';
ALTER TABLE planets ADD COLUMN IF NOT EXISTS reference_publication TEXT;
ALTER TABLE planets ADD COLUMN IF NOT EXISTS last_data_update TIMESTAMPTZ DEFAULT now();

-- Create exoplanets table for NASA archive data
CREATE TABLE IF NOT EXISTS exoplanets (
  exoplanet_id BIGSERIAL PRIMARY KEY,
  planet_id BIGINT REFERENCES planets(planet_id),
  nasa_archive_name TEXT UNIQUE,
  toi_id TEXT,
  tic_id TEXT,
  gaia_id TEXT,
  
  -- Discovery information
  discovery_telescope TEXT,
  discovery_instrument TEXT,
  discovery_locale TEXT,
  controversial_flag BOOLEAN DEFAULT false,
  
  -- Orbital parameters with uncertainties
  orbital_period_days DOUBLE PRECISION,
  orbital_period_err1 DOUBLE PRECISION,
  orbital_period_err2 DOUBLE PRECISION,
  orbital_period_limit INTEGER,
  
  -- Transit timing
  transit_epoch_bjd DOUBLE PRECISION,
  transit_epoch_err1 DOUBLE PRECISION,
  transit_epoch_err2 DOUBLE PRECISION,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_stars_coordinates ON stars(x_pc, y_pc, z_pc);
CREATE INDEX IF NOT EXISTS idx_stars_distance ON stars(distance_ly);
CREATE INDEX IF NOT EXISTS idx_stars_spectral_type ON stars(spectral_type);
CREATE INDEX IF NOT EXISTS idx_stars_proper_name ON stars(proper_name);
CREATE INDEX IF NOT EXISTS idx_stars_hipparcos ON stars(hipparcos_id);
CREATE INDEX IF NOT EXISTS idx_stars_henry_draper ON stars(henry_draper_id);

CREATE INDEX IF NOT EXISTS idx_planets_host_star ON planets(host_star_name);
CREATE INDEX IF NOT EXISTS idx_planets_kepler_name ON planets(kepler_name);
CREATE INDEX IF NOT EXISTS idx_planets_toi_id ON planets(toi_id);
CREATE INDEX IF NOT EXISTS idx_planets_earth_similarity ON planets(earth_similarity_index);

CREATE INDEX IF NOT EXISTS idx_exoplanets_nasa_name ON exoplanets(nasa_archive_name);
CREATE INDEX IF NOT EXISTS idx_exoplanets_toi ON exoplanets(toi_id);

-- Add constraints
ALTER TABLE stars ADD CONSTRAINT chk_data_quality CHECK (data_quality_score >= 0.0 AND data_quality_score <= 1.0);
ALTER TABLE planets ADD CONSTRAINT chk_earth_similarity CHECK (earth_similarity_index >= 0.0 AND earth_similarity_index <= 1.0);
ALTER TABLE planets ADD CONSTRAINT chk_tidal_locking CHECK (tidal_locking_probability >= 0.0 AND tidal_locking_probability <= 1.0);

-- Update existing Sol data with proper coordinates
UPDATE stars 
SET 
  x_pc = 0.0,
  y_pc = 0.0, 
  z_pc = 0.0,
  proper_name = 'Sun',
  stellar_color = '#fff4ea',
  absolute_magnitude = 4.83,
  stellar_class = 'G2V',
  luminosity_class = 'V',
  color_index_bv = 0.656,
  data_quality_score = 1.0
WHERE name = 'Sol' OR distance_ly = 0.0;

-- Create view for nearby stellar systems
CREATE OR REPLACE VIEW nearby_stellar_systems AS
SELECT 
  s.*,
  COUNT(p.planet_id) as planet_count,
  COUNT(p.planet_id) FILTER (WHERE p.in_habitable_zone = true) as habitable_planet_count,
  COUNT(p.planet_id) FILTER (WHERE p.is_colonized = true) as colonized_planet_count
FROM stars s
LEFT JOIN planets p ON s.star_id = p.star_id
WHERE s.distance_ly <= 50.0
GROUP BY s.star_id
ORDER BY s.distance_ly;

-- Create view for confirmed exoplanets
CREATE OR REPLACE VIEW confirmed_exoplanets AS
SELECT
  p.planet_id,
  p.star_id,
  p.name as planet_name,
  p.mass_earth,
  p.radius_earth,
  p.sma_au,
  p.period_days,
  p.eccentricity,
  p.eq_temp_k,
  p.composition,
  p.habitability_score,
  p.in_habitable_zone,
  p.confirmed_status,
  s.name as star_name,
  s.distance_ly as star_distance_ly,
  s.spectral_type as star_spectral_type,
  s.x_pc as star_x_pc,
  s.y_pc as star_y_pc,
  s.z_pc as star_z_pc
FROM planets p
JOIN stars s ON p.star_id = s.star_id
WHERE p.confirmed_status = 'confirmed'
  AND s.distance_ly <= 50.0
ORDER BY s.distance_ly, p.sma_au;

-- Add comments for documentation
COMMENT ON COLUMN stars.x_pc IS '3D Cartesian X coordinate in parsecs (galactic frame)';
COMMENT ON COLUMN stars.y_pc IS '3D Cartesian Y coordinate in parsecs (galactic frame)';
COMMENT ON COLUMN stars.z_pc IS '3D Cartesian Z coordinate in parsecs (galactic frame)';
COMMENT ON COLUMN stars.stellar_color IS 'Hex color code based on spectral type for visualization';
COMMENT ON COLUMN stars.data_quality_score IS 'Overall data quality score from 0.0 to 1.0';

COMMENT ON COLUMN planets.earth_similarity_index IS 'Earth Similarity Index (ESI) from 0.0 to 1.0';
COMMENT ON COLUMN planets.stellar_flux_earth IS 'Incident stellar flux relative to Earth (1.0 = Earth level)';
COMMENT ON COLUMN planets.tidal_locking_probability IS 'Probability of tidal locking from 0.0 to 1.0';

COMMENT ON TABLE exoplanets IS 'NASA Exoplanet Archive data with full parameter sets and uncertainties';

COMMIT;
