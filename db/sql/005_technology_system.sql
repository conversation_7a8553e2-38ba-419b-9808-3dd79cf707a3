-- Technology System
-- Adds tables for technology tree, research progress, and tech dependencies

-- Technology nodes - individual technologies that can be researched
create table if not exists tech_nodes (
  id text primary key,
  name text not null,
  branch text not null, -- 'physics', 'biotech', 'society'
  tier integer not null default 1, -- 1-6 technology tiers
  base_cost_knw integer not null, -- base research cost in knowledge points
  effects jsonb not null default '{}', -- what this tech unlocks
  description text,
  icon text, -- icon identifier for UI
  created_at timestamptz default now()
);

-- Technology dependencies - which techs are required for others
create table if not exists tech_edges (
  from_tech text references tech_nodes(id) on delete cascade,
  to_tech text references tech_nodes(id) on delete cascade,
  primary key (from_tech, to_tech)
);

-- Empire research progress - tracks what each empire has researched
create table if not exists tech_progress (
  empire_id text references empires(id) on delete cascade,
  tech_id text references tech_nodes(id) on delete cascade,
  invested_knw integer not null default 0, -- research points invested
  unlocked boolean not null default false, -- whether tech is unlocked
  unlocked_at timestamptz, -- when it was unlocked
  primary key (empire_id, tech_id)
);

-- Research queue - what empires are currently researching
create table if not exists research_queue (
  id text primary key,
  empire_id text references empires(id) on delete cascade,
  tech_id text references tech_nodes(id) on delete cascade,
  priority integer not null default 1, -- queue order
  created_at timestamptz default now(),
  unique(empire_id, tech_id) -- can't research same tech twice
);

-- Seed basic technology tree
-- Physics Branch - Energy, Weapons, Propulsion
insert into tech_nodes (id, name, branch, tier, base_cost_knw, effects, description, icon) values
-- Tier 1 Physics
('phys_basic_energy', 'Basic Energy Theory', 'physics', 1, 50, '{"unlocks": ["improvement:advanced_generator"], "bonuses": {"energy_production": 0.1}}', 'Fundamental understanding of energy manipulation', 'energy'),
('phys_basic_materials', 'Materials Science', 'physics', 1, 50, '{"unlocks": ["improvement:advanced_mine"], "bonuses": {"mineral_production": 0.1}}', 'Advanced material processing techniques', 'materials'),
('phys_basic_propulsion', 'Ion Propulsion', 'physics', 1, 75, '{"unlocks": ["ship:scout_mk2"], "bonuses": {"fleet_speed": 0.2}}', 'Efficient ion drive technology', 'propulsion'),

-- Tier 2 Physics
('phys_fusion_power', 'Fusion Power', 'physics', 2, 150, '{"unlocks": ["improvement:fusion_reactor"], "bonuses": {"energy_production": 0.25}}', 'Clean fusion energy generation', 'fusion'),
('phys_advanced_materials', 'Advanced Alloys', 'physics', 2, 150, '{"unlocks": ["improvement:advanced_factory"], "bonuses": {"construction_speed": 0.2}}', 'Lightweight, durable alloy compositions', 'alloys'),
('phys_plasma_weapons', 'Plasma Weaponry', 'physics', 2, 200, '{"unlocks": ["ship:destroyer"], "bonuses": {"combat_damage": 0.3}}', 'High-energy plasma weapon systems', 'plasma'),

-- Biotech Branch - Life Sciences, Medicine, Terraforming
('bio_basic_genetics', 'Basic Genetics', 'biotech', 1, 50, '{"unlocks": ["improvement:genetic_lab"], "bonuses": {"population_growth": 0.15}}', 'Understanding of genetic manipulation', 'genetics'),
('bio_hydroponics', 'Hydroponics', 'biotech', 1, 50, '{"unlocks": ["improvement:hydroponic_farm"], "bonuses": {"food_production": 0.2}}', 'Soil-free agricultural systems', 'hydroponics'),
('bio_basic_medicine', 'Advanced Medicine', 'biotech', 1, 75, '{"bonuses": {"habitability": 5, "population_growth": 0.1}}', 'Improved medical treatments and procedures', 'medicine'),

-- Tier 2 Biotech
('bio_terraforming', 'Terraforming', 'biotech', 2, 200, '{"unlocks": ["improvement:terraforming_station"], "bonuses": {"habitability": 15}}', 'Planetary atmosphere and climate modification', 'terraform'),
('bio_genetic_engineering', 'Genetic Engineering', 'biotech', 2, 150, '{"unlocks": ["improvement:gene_clinic"], "bonuses": {"population_growth": 0.25, "habitability": 10}}', 'Advanced genetic modification techniques', 'gene_mod'),

-- Society Branch - Government, Culture, Economics
('soc_basic_administration', 'Administrative Efficiency', 'society', 1, 50, '{"unlocks": ["improvement:administrative_center"], "bonuses": {"influence_production": 0.2}}', 'Improved governmental organization', 'admin'),
('soc_cultural_exchange', 'Cultural Exchange', 'society', 1, 50, '{"unlocks": ["improvement:cultural_center"], "bonuses": {"culture_production": 0.2}}', 'Cross-cultural communication and arts', 'culture'),
('soc_trade_networks', 'Trade Networks', 'society', 1, 75, '{"unlocks": ["improvement:trade_hub"], "bonuses": {"credits_production": 0.25}}', 'Efficient commercial systems', 'trade'),

-- Tier 2 Society
('soc_planetary_government', 'Planetary Government', 'society', 2, 150, '{"bonuses": {"influence_production": 0.3, "max_colonies": 2}}', 'Advanced planetary administration', 'government'),
('soc_galactic_diplomacy', 'Galactic Diplomacy', 'society', 2, 200, '{"unlocks": ["feature:diplomatic_missions"], "bonuses": {"influence_production": 0.25}}', 'Inter-stellar diplomatic protocols', 'diplomacy')

on conflict (id) do nothing;

-- Technology dependencies (prerequisites)
insert into tech_edges (from_tech, to_tech) values
-- Physics dependencies
('phys_basic_energy', 'phys_fusion_power'),
('phys_basic_materials', 'phys_advanced_materials'),
('phys_basic_propulsion', 'phys_plasma_weapons'),
('phys_basic_energy', 'phys_plasma_weapons'),

-- Biotech dependencies
('bio_basic_genetics', 'bio_genetic_engineering'),
('bio_basic_medicine', 'bio_terraforming'),
('bio_hydroponics', 'bio_terraforming'),

-- Society dependencies
('soc_basic_administration', 'soc_planetary_government'),
('soc_cultural_exchange', 'soc_galactic_diplomacy'),
('soc_trade_networks', 'soc_galactic_diplomacy'),

-- Cross-branch dependencies
('phys_basic_energy', 'bio_terraforming'), -- terraforming needs energy
('bio_basic_genetics', 'soc_planetary_government'), -- government needs population science
('soc_basic_administration', 'phys_advanced_materials') -- administration helps with materials

on conflict (from_tech, to_tech) do nothing;

-- Initialize research progress for existing empires (all techs available but not unlocked)
insert into tech_progress (empire_id, tech_id, invested_knw, unlocked)
select e.id, t.id, 0, false
from empires e
cross join tech_nodes t
on conflict (empire_id, tech_id) do nothing;

-- Indexes for performance
create index if not exists idx_tech_nodes_branch on tech_nodes(branch);
create index if not exists idx_tech_nodes_tier on tech_nodes(tier);
create index if not exists idx_tech_progress_empire on tech_progress(empire_id);
create index if not exists idx_tech_progress_unlocked on tech_progress(empire_id, unlocked);
create index if not exists idx_research_queue_empire on research_queue(empire_id);
create index if not exists idx_research_queue_priority on research_queue(empire_id, priority);
