-- Moons table: Natural satellites of planets
-- This table stores data about moons/natural satellites

CREATE TABLE IF NOT EXISTS moons (
  moon_id           BIGSERIAL PRIMARY KEY,
  planet_id         BIGINT REFERENCES planets(planet_id) ON DELETE CASCADE,
  src               TEXT NOT NULL DEFAULT 'unknown',   -- 'nasa', 'iau', 'proc', 'manual'
  src_key           TEXT,                              -- Source-specific identifier
  name              TEXT NOT NULL,                     -- Moon name (Luna, Io, Europa, etc.)
  
  -- Orbital parameters
  mass_kg           DOUBLE PRECISION,                  -- Mass in kilograms
  mass_earth        DOUBLE PRECISION,                  -- Mass in Earth masses
  radius_km         DOUBLE PRECISION,                  -- Mean radius in kilometers
  radius_earth      DOUBLE PRECISION,                  -- Radius in Earth radii
  sma_km            DOUBLE PRECISION,                  -- Semi-major axis from planet (km)
  sma_planet_radii  DOUBLE PRECISION,                  -- Semi-major axis in planet radii
  period_days       DOUBLE PRECISION,                  -- Orbital period (days)
  eccentricity      DOUBLE PRECISION DEFAULT 0.0,     -- Orbital eccentricity
  inclination_deg   DOUBLE PRECISION,                  -- Orbital inclination (degrees)
  
  -- Physical properties
  density_gcc       DOUBLE PRECISION,                  -- Density (g/cm³)
  surface_temp_k    DOUBLE PRECISION,                  -- Surface temperature (Kelvin)
  albedo            DOUBLE PRECISION,                  -- Geometric albedo
  composition       TEXT DEFAULT 'unknown',            -- rocky, icy, mixed, metallic
  atmosphere        TEXT,                              -- Atmosphere composition
  
  -- Geological features
  has_atmosphere    BOOLEAN DEFAULT false,             -- Has substantial atmosphere
  has_water         BOOLEAN DEFAULT false,             -- Evidence of water/ice
  has_subsurface_ocean BOOLEAN DEFAULT false,          -- Subsurface ocean present
  geological_activity TEXT DEFAULT 'inactive',         -- active, inactive, unknown
  surface_features  TEXT[],                            -- Array of surface feature types
  
  -- Tidal properties
  is_tidally_locked BOOLEAN DEFAULT true,              -- Tidally locked to planet
  tidal_heating     BOOLEAN DEFAULT false,             -- Experiences tidal heating
  
  -- Discovery data
  discovery_year    INTEGER,                           -- Year of discovery
  discovery_method  TEXT,                              -- Observation method
  discoverer        TEXT,                              -- Person/team who discovered
  
  -- Resource potential (for game mechanics)
  mineral_richness  DOUBLE PRECISION DEFAULT 0.5,     -- 0.0-1.0 mineral abundance
  water_ice_content DOUBLE PRECISION DEFAULT 0.0,     -- 0.0-1.0 water ice fraction
  rare_elements     TEXT[],                            -- Array of rare element types
  
  -- Game integration
  is_colonized      BOOLEAN DEFAULT false,             -- Currently colonized
  is_exploitable    BOOLEAN DEFAULT true,              -- Can be mined/exploited
  exploration_status TEXT DEFAULT 'unexplored',       -- 'unexplored', 'surveyed', 'colonized'
  strategic_value   DOUBLE PRECISION DEFAULT 0.5,     -- 0.0-1.0 strategic importance
  
  -- Metadata
  created_at        TIMESTAMPTZ DEFAULT now(),
  updated_at        TIMESTAMPTZ DEFAULT now(),
  
  -- Constraints
  CHECK (mass_kg > 0 OR mass_kg IS NULL),
  CHECK (mass_earth > 0 OR mass_earth IS NULL),
  CHECK (radius_km > 0 OR radius_km IS NULL),
  CHECK (radius_earth > 0 OR radius_earth IS NULL),
  CHECK (sma_km > 0 OR sma_km IS NULL),
  CHECK (sma_planet_radii > 0 OR sma_planet_radii IS NULL),
  CHECK (period_days > 0 OR period_days IS NULL),
  CHECK (eccentricity >= 0 AND eccentricity < 1 OR eccentricity IS NULL),
  CHECK (density_gcc > 0 OR density_gcc IS NULL),
  CHECK (albedo >= 0 AND albedo <= 1 OR albedo IS NULL),
  CHECK (mineral_richness >= 0.0 AND mineral_richness <= 1.0),
  CHECK (water_ice_content >= 0.0 AND water_ice_content <= 1.0),
  CHECK (strategic_value >= 0.0 AND strategic_value <= 1.0),
  CHECK (composition IN ('rocky', 'icy', 'mixed', 'metallic', 'gaseous', 'unknown')),
  CHECK (geological_activity IN ('active', 'inactive', 'unknown')),
  CHECK (exploration_status IN ('unexplored', 'surveyed', 'colonized', 'abandoned'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_moons_planet_id ON moons(planet_id);
CREATE INDEX IF NOT EXISTS idx_moons_src_key ON moons(src, src_key);
CREATE INDEX IF NOT EXISTS idx_moons_name ON moons(name);
CREATE INDEX IF NOT EXISTS idx_moons_discovery_year ON moons(discovery_year);
CREATE INDEX IF NOT EXISTS idx_moons_exploration_status ON moons(exploration_status);

-- Create unique constraint for source data
CREATE UNIQUE INDEX IF NOT EXISTS idx_moons_src_unique 
ON moons(src, src_key) 
WHERE src_key IS NOT NULL;

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_moons_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_moons_updated_at
    BEFORE UPDATE ON moons
    FOR EACH ROW
    EXECUTE FUNCTION update_moons_updated_at();

-- Insert some well-known moons for our Solar System
INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days, 
                   composition, has_water, discovery_year, discoverer) 
SELECT p.planet_id, 'manual', 'luna', 'Luna', 7.342e22, 1737.4, 384400, 27.3,
       'rocky', true, NULL, 'Ancient'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Earth'
ON CONFLICT (src, src_key) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, has_water, has_subsurface_ocean, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'europa', 'Europa', 4.8e22, 1560.8, 671034, 3.55,
       'icy', true, true, 1610, 'Galileo Galilei'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Jupiter'
ON CONFLICT (src, src_key) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, geological_activity, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'io', 'Io', 8.93e22, 1821.6, 421700, 1.77,
       'rocky', 'active', 1610, 'Galileo Galilei'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Jupiter'
ON CONFLICT (src, src_key) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, has_water, has_subsurface_ocean, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'enceladus', 'Enceladus', 1.08e20, 252.1, 238020, 1.37,
       'icy', true, true, 1789, 'William Herschel'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Saturn'
ON CONFLICT (src, src_key) DO NOTHING;

INSERT INTO moons (planet_id, src, src_key, name, mass_kg, radius_km, sma_km, period_days,
                   composition, has_atmosphere, discovery_year, discoverer)
SELECT p.planet_id, 'manual', 'titan', 'Titan', 1.35e23, 2574, 1221830, 15.95,
       'icy', true, 1655, 'Christiaan Huygens'
FROM planets p 
JOIN stars s ON p.star_id = s.star_id 
WHERE s.name = 'Sol' AND p.name = 'Saturn'
ON CONFLICT (src, src_key) DO NOTHING;

-- Add comment
COMMENT ON TABLE moons IS 'Natural satellites (moons) of planets in stellar systems';
COMMENT ON COLUMN moons.has_subsurface_ocean IS 'Indicates presence of liquid water ocean beneath surface';
COMMENT ON COLUMN moons.tidal_heating IS 'Moon experiences internal heating due to tidal forces';
COMMENT ON COLUMN moons.strategic_value IS 'Game mechanic: strategic importance for colonization/resources';
