-- Trade & Market System
-- Adds tables for resource trading, market orders, and trade routes

-- Market orders - buy/sell orders for resources
create table if not exists market_orders (
  id text primary key,
  empire_id text references empires(id) on delete cascade,
  order_type text not null check (order_type in ('buy', 'sell')),
  resource_type text not null,
  quantity integer not null check (quantity > 0),
  price_per_unit numeric not null check (price_per_unit > 0),
  total_value numeric generated always as (quantity * price_per_unit) stored,
  filled_quantity integer not null default 0 check (filled_quantity >= 0 and filled_quantity <= quantity),
  status text not null default 'active' check (status in ('active', 'completed', 'cancelled', 'expired')),
  expires_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Trade transactions - completed trades between empires
create table if not exists trade_transactions (
  id text primary key,
  buyer_empire_id text references empires(id) on delete cascade,
  seller_empire_id text references empires(id) on delete cascade,
  buy_order_id text references market_orders(id),
  sell_order_id text references market_orders(id),
  resource_type text not null,
  quantity integer not null check (quantity > 0),
  price_per_unit numeric not null check (price_per_unit > 0),
  total_value numeric generated always as (quantity * price_per_unit) stored,
  executed_at timestamptz default now()
);

-- Trade routes - established commercial connections between systems
create table if not exists trade_routes (
  id text primary key,
  empire_id text references empires(id) on delete cascade,
  origin_system_id text not null,
  destination_system_id text not null,
  resource_type text not null,
  quantity_per_turn integer not null check (quantity_per_turn > 0),
  profit_per_turn numeric not null default 0,
  established_at timestamptz default now(),
  status text not null default 'active' check (status in ('active', 'disrupted', 'inactive')),
  unique(empire_id, origin_system_id, destination_system_id, resource_type)
);

-- Market prices - current market rates for resources
create table if not exists market_prices (
  resource_type text primary key,
  current_price numeric not null check (current_price > 0),
  price_trend text not null default 'stable' check (price_trend in ('rising', 'falling', 'stable')),
  daily_volume integer not null default 0,
  last_updated timestamptz default now()
);

-- Trade agreements - diplomatic trade deals between empires
create table if not exists trade_agreements (
  id text primary key,
  empire_a_id text references empires(id) on delete cascade,
  empire_b_id text references empires(id) on delete cascade,
  agreement_type text not null check (agreement_type in ('trade_pact', 'resource_deal', 'commercial_alliance')),
  terms jsonb not null default '{}',
  status text not null default 'proposed' check (status in ('proposed', 'active', 'expired', 'cancelled')),
  created_at timestamptz default now(),
  expires_at timestamptz,
  check (empire_a_id != empire_b_id)
);

-- Initialize market prices for all resource types
insert into market_prices (resource_type, current_price, price_trend, daily_volume) values
('food', 1.0, 'stable', 0),
('minerals', 1.2, 'stable', 0),
('energy', 1.5, 'stable', 0),
('research', 3.0, 'stable', 0),
('credits', 1.0, 'stable', 0),
('influence', 5.0, 'stable', 0),
('culture', 2.0, 'stable', 0)
on conflict (resource_type) do nothing;

-- Function to calculate market price based on supply/demand
create or replace function calculate_market_price(p_resource_type text)
returns numeric as $$
declare
  base_price numeric;
  buy_volume integer;
  sell_volume integer;
  price_modifier numeric;
begin
  -- Get base price
  select current_price into base_price
  from market_prices
  where resource_type = p_resource_type;
  
  if base_price is null then
    return 1.0; -- default price
  end if;
  
  -- Calculate buy/sell volumes from active orders
  select 
    coalesce(sum(case when order_type = 'buy' then quantity - filled_quantity else 0 end), 0),
    coalesce(sum(case when order_type = 'sell' then quantity - filled_quantity else 0 end), 0)
  into buy_volume, sell_volume
  from market_orders
  where resource_type = p_resource_type and status = 'active';
  
  -- Calculate price modifier based on supply/demand ratio
  if sell_volume = 0 and buy_volume > 0 then
    price_modifier := 1.2; -- high demand, no supply
  elsif buy_volume = 0 and sell_volume > 0 then
    price_modifier := 0.8; -- high supply, no demand
  elsif sell_volume > 0 then
    price_modifier := 1.0 + (buy_volume::numeric / sell_volume::numeric - 1.0) * 0.1;
    price_modifier := greatest(0.5, least(2.0, price_modifier)); -- clamp between 0.5x and 2.0x
  else
    price_modifier := 1.0; -- stable
  end if;
  
  return base_price * price_modifier;
end;
$$ language plpgsql;

-- Function to match and execute trade orders
create or replace function execute_trade_matching()
returns table(transaction_id text, buyer_id text, seller_id text, resource text, quantity integer, price numeric) as $$
declare
  buy_order record;
  sell_order record;
  trade_quantity integer;
  transaction_id text;
begin
  -- Find matching buy/sell orders
  for buy_order in
    select mo.* from market_orders mo
    where mo.order_type = 'buy' and mo.status = 'active' and mo.filled_quantity < mo.quantity
    order by mo.price_per_unit desc, mo.created_at asc
  loop
    -- Find compatible sell orders
    for sell_order in
      select mo.* from market_orders mo
      where mo.order_type = 'sell'
        and mo.status = 'active'
        and mo.filled_quantity < mo.quantity
        and mo.resource_type = buy_order.resource_type
        and mo.price_per_unit <= buy_order.price_per_unit
        and mo.empire_id != buy_order.empire_id
      order by mo.price_per_unit asc, mo.created_at asc
    loop
      -- Calculate trade quantity
      trade_quantity := least(
        buy_order.quantity - buy_order.filled_quantity,
        sell_order.quantity - sell_order.filled_quantity
      );

      if trade_quantity > 0 then
        -- Generate transaction ID
        transaction_id := 'txn_' || substr(md5(random()::text), 1, 12);

        -- Create transaction record
        insert into trade_transactions (
          id, buyer_empire_id, seller_empire_id, buy_order_id, sell_order_id,
          resource_type, quantity, price_per_unit
        ) values (
          transaction_id, buy_order.empire_id, sell_order.empire_id,
          buy_order.id, sell_order.id, buy_order.resource_type,
          trade_quantity, sell_order.price_per_unit
        );

        -- Update order fill quantities
        update market_orders
        set filled_quantity = filled_quantity + trade_quantity,
            status = case when filled_quantity + trade_quantity >= market_orders.quantity then 'completed' else 'active' end,
            updated_at = now()
        where id in (buy_order.id, sell_order.id);

        -- Transfer resources
        -- Remove resources from seller
        update empire_resources
        set amount = amount - trade_quantity
        where empire_id = sell_order.empire_id and resource_type = buy_order.resource_type;

        -- Add resources to buyer
        insert into empire_resources (empire_id, resource_type, amount)
        values (buy_order.empire_id, buy_order.resource_type, trade_quantity)
        on conflict (empire_id, resource_type)
        do update set amount = empire_resources.amount + excluded.amount;

        -- Transfer credits (buyer pays seller)
        update empire_resources
        set amount = amount - (trade_quantity * sell_order.price_per_unit)
        where empire_id = buy_order.empire_id and resource_type = 'credits';

        update empire_resources
        set amount = amount + (trade_quantity * sell_order.price_per_unit)
        where empire_id = sell_order.empire_id and resource_type = 'credits';

        -- Return transaction details
        return query select
          transaction_id, buy_order.empire_id, sell_order.empire_id,
          buy_order.resource_type, trade_quantity, sell_order.price_per_unit;

        -- Update buy_order for next iteration
        select mo.* into buy_order from market_orders mo where mo.id = buy_order.id;

        -- Exit if buy order is fully filled
        exit when buy_order.filled_quantity >= buy_order.quantity;
      end if;
    end loop;
  end loop;
end;
$$ language plpgsql;

-- Function to update market prices based on recent trading activity
create or replace function update_market_prices()
returns void as $$
declare
  resource_record record;
  new_price numeric;
  volume_today integer;
begin
  for resource_record in select distinct resource_type from market_prices
  loop
    -- Calculate new price
    new_price := calculate_market_price(resource_record.resource_type);
    
    -- Calculate daily volume
    select coalesce(sum(quantity), 0) into volume_today
    from trade_transactions
    where resource_type = resource_record.resource_type
      and executed_at >= current_date;
    
    -- Update market price
    update market_prices
    set current_price = new_price,
        daily_volume = volume_today,
        price_trend = case
          when new_price > current_price * 1.05 then 'rising'
          when new_price < current_price * 0.95 then 'falling'
          else 'stable'
        end,
        last_updated = now()
    where resource_type = resource_record.resource_type;
  end loop;
end;
$$ language plpgsql;

-- Indexes for performance
create index if not exists idx_market_orders_empire on market_orders(empire_id);
create index if not exists idx_market_orders_resource_type on market_orders(resource_type);
create index if not exists idx_market_orders_status on market_orders(status);
create index if not exists idx_market_orders_type_price on market_orders(order_type, resource_type, price_per_unit);
create index if not exists idx_trade_transactions_empires on trade_transactions(buyer_empire_id, seller_empire_id);
create index if not exists idx_trade_transactions_resource on trade_transactions(resource_type);
create index if not exists idx_trade_transactions_date on trade_transactions(executed_at);
create index if not exists idx_trade_routes_empire on trade_routes(empire_id);
create index if not exists idx_trade_routes_systems on trade_routes(origin_system_id, destination_system_id);
