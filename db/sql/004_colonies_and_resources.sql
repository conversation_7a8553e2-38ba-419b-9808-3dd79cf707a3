-- Colony and Resource Management System
-- Adds tables for colonies, improvements, and empire resources

-- Colonies table - represents settlements on planets
create table if not exists colonies (
  id text primary key,
  system_id text references systems(id) not null,
  empire_id text references empires(id) not null,
  name text not null,
  planet_type text not null default 'terrestrial', -- terrestrial, desert, arctic, ocean, volcanic, gas_giant
  pop integer not null default 1,
  habitability integer not null default 75, -- 0-100 percentage
  yields jsonb not null default '{}', -- {food: 2, minerals: 3, energy: 1, research: 1}
  max_pop integer not null default 10,
  created_at timestamptz default now(),
  unique(system_id, empire_id) -- one colony per empire per system for now
);

-- Improvements/Buildings on colonies
create table if not exists improvements (
  id text primary key,
  colony_id text references colonies(id) on delete cascade,
  type text not null, -- farm, mine, lab, factory, spaceport, etc.
  level integer not null default 1,
  yields jsonb not null default '{}', -- additional yields from this improvement
  maintenance_cost jsonb not null default '{}', -- upkeep costs
  created_at timestamptz default now()
);

-- Empire resource tracking
create table if not exists empire_resources (
  empire_id text references empires(id),
  resource_type text not null, -- food, minerals, energy, research, credits, influence, culture
  amount numeric not null default 0,
  production_rate numeric not null default 0, -- per turn production
  storage_cap numeric not null default 1000,
  last_updated timestamptz default now(),
  primary key (empire_id, resource_type)
);

-- Resource transaction log for debugging and history
create table if not exists resource_transactions (
  id text primary key,
  empire_id text references empires(id),
  resource_type text not null,
  amount numeric not null, -- positive for gain, negative for spend
  source text not null, -- 'colony_production', 'trade', 'research', 'maintenance', etc.
  source_id text, -- colony_id, trade_id, etc.
  created_at timestamptz default now()
);

-- Planet types and their base characteristics
create table if not exists planet_types (
  type text primary key,
  base_habitability integer not null,
  base_yields jsonb not null, -- base resource yields
  description text
);

-- Seed planet types
insert into planet_types (type, base_habitability, base_yields, description) values
('terrestrial', 85, '{"food": 3, "minerals": 2, "energy": 1}', 'Earth-like worlds with balanced resources'),
('desert', 60, '{"food": 1, "minerals": 4, "energy": 2}', 'Arid worlds rich in minerals'),
('arctic', 50, '{"food": 1, "minerals": 2, "energy": 1, "research": 2}', 'Cold worlds suitable for research'),
('ocean', 75, '{"food": 4, "minerals": 1, "energy": 2}', 'Water worlds with abundant food'),
('volcanic', 40, '{"food": 0, "minerals": 6, "energy": 3}', 'Hostile worlds rich in rare minerals'),
('gas_giant', 20, '{"food": 0, "minerals": 1, "energy": 5, "research": 1}', 'Gas giants with energy harvesting potential')
on conflict (type) do nothing;

-- Improvement types and their effects
create table if not exists improvement_types (
  type text primary key,
  name text not null,
  base_cost jsonb not null, -- construction cost
  yields jsonb not null, -- resource yields per level
  maintenance jsonb not null default '{}', -- upkeep per turn
  max_level integer not null default 5,
  requires_tech text, -- tech requirement
  description text
);

-- Seed improvement types
insert into improvement_types (type, name, base_cost, yields, maintenance, max_level, description) values
('farm', 'Agricultural Complex', '{"minerals": 50, "energy": 25}', '{"food": 2}', '{"energy": 1}', 5, 'Increases food production'),
('mine', 'Mining Facility', '{"minerals": 75, "energy": 50}', '{"minerals": 3}', '{"energy": 2}', 5, 'Extracts minerals from the planet'),
('generator', 'Power Plant', '{"minerals": 100, "energy": 25}', '{"energy": 4}', '{"minerals": 1}', 5, 'Generates energy for the colony'),
('lab', 'Research Lab', '{"minerals": 150, "energy": 100}', '{"research": 3}', '{"energy": 3}', 5, 'Conducts scientific research'),
('factory', 'Industrial Complex', '{"minerals": 200, "energy": 150}', '{"credits": 2}', '{"energy": 4, "minerals": 2}', 3, 'Produces manufactured goods'),
('spaceport', 'Spaceport', '{"minerals": 300, "energy": 200}', '{"credits": 1}', '{"energy": 5}', 1, 'Enables fleet construction and trade')
on conflict (type) do nothing;

-- Initialize basic resources for existing empires
insert into empire_resources (empire_id, resource_type, amount, production_rate, storage_cap)
select e.id, r.resource_type, 100, 0, 1000
from empires e
cross join (
  values ('food'), ('minerals'), ('energy'), ('research'), ('credits'), ('influence'), ('culture')
) as r(resource_type)
on conflict (empire_id, resource_type) do nothing;

-- Indexes for performance
create index if not exists idx_colonies_empire on colonies(empire_id);
create index if not exists idx_colonies_system on colonies(system_id);
create index if not exists idx_improvements_colony on improvements(colony_id);
create index if not exists idx_resource_transactions_empire on resource_transactions(empire_id);
create index if not exists idx_resource_transactions_created on resource_transactions(created_at);
