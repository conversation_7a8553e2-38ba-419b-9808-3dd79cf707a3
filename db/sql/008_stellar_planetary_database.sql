-- Stellar & Planetary Database Schema
-- Enterprise-grade schema for real astronomy data + procedural generation
-- Designed for 20 light-year radius around Earth (Sol)

-- Enable PostGIS for spatial indexing (if available)
-- CREATE EXTENSION IF NOT EXISTS postgis;

BEGIN;

-- Stars table: Real stellar data from Gaia DR3 + derived properties
CREATE TABLE IF NOT EXISTS stars (
  star_id           BIGSERIAL PRIMARY KEY,
  src               TEXT NOT NULL DEFAULT 'unknown',  -- 'gaia', 'nasa', 'celesta', 'proc'
  src_key           TEXT,                              -- e.g., Gaia DR3 source_id
  name              TEXT,                              -- Common name (Sol, Alpha Centauri, etc.)
  catalog_name      TEXT,                              -- Catalog designation (HD 164595, etc.)
  
  -- Astrometric data (ICRS J2000.0)
  ra_deg            DOUBLE PRECISION NOT NULL,         -- Right Ascension (degrees)
  dec_deg           DOUBLE PRECISION NOT NULL,         -- Declination (degrees)
  distance_ly       DOUBLE PRECISION,                  -- Distance in light-years
  parallax_mas      DOUBLE PRECISION,                  -- Parallax (milliarcseconds)
  pm_ra_masyr       DOUBLE PRECISION,                  -- Proper motion RA (mas/year)
  pm_dec_masyr      DOUBLE PRECISION,                  -- Proper motion Dec (mas/year)
  
  -- Photometric data
  mag_g             DOUBLE PRECISION,                  -- Gaia G magnitude
  mag_v             DOUBLE PRECISION,                  -- Visual magnitude
  mag_b             DOUBLE PRECISION,                  -- Blue magnitude
  color_bv          DOUBLE PRECISION,                  -- B-V color index
  
  -- Stellar properties
  spectral_type     TEXT,                              -- O, B, A, F, G, K, M + subclass
  mass_solar        DOUBLE PRECISION,                  -- Mass in solar masses
  radius_solar      DOUBLE PRECISION,                  -- Radius in solar radii
  teff_k            DOUBLE PRECISION,                  -- Effective temperature (Kelvin)
  luminosity_solar  DOUBLE PRECISION,                  -- Luminosity in solar luminosities
  metallicity_fe_h  DOUBLE PRECISION,                  -- [Fe/H] metallicity
  age_gyr           DOUBLE PRECISION,                  -- Age in billion years
  
  -- Habitability data
  hz_inner_au       DOUBLE PRECISION,                  -- Habitable zone inner edge (AU)
  hz_outer_au       DOUBLE PRECISION,                  -- Habitable zone outer edge (AU)
  
  -- Game integration
  system_id         TEXT REFERENCES systems(id),      -- Link to existing game systems
  is_colonizable    BOOLEAN DEFAULT true,              -- Can be colonized in game
  discovery_status  TEXT DEFAULT 'known',              -- 'known', 'discovered', 'unexplored'
  
  -- Metadata
  created_at        TIMESTAMPTZ DEFAULT now(),
  updated_at        TIMESTAMPTZ DEFAULT now(),
  
  -- Constraints
  UNIQUE (src, src_key),
  CHECK (distance_ly >= 0),
  CHECK (distance_ly <= 20.0)   -- 20 light-year limit for this dataset

);

-- Planets table: Real exoplanets + procedural planets
CREATE TABLE IF NOT EXISTS planets (
  planet_id         BIGSERIAL PRIMARY KEY,
  star_id           BIGINT REFERENCES stars(star_id) ON DELETE CASCADE,
  src               TEXT NOT NULL DEFAULT 'unknown',   -- 'nasa', 'proc', 'celesta'
  src_key           TEXT,                              -- NASA archive designation
  name              TEXT,                              -- Planet name (Kepler-442b, etc.)
  
  -- Orbital parameters
  mass_earth        DOUBLE PRECISION,                  -- Mass in Earth masses
  radius_earth      DOUBLE PRECISION,                  -- Radius in Earth radii
  sma_au            DOUBLE PRECISION,                  -- Semi-major axis (AU)
  period_days       DOUBLE PRECISION,                  -- Orbital period (days)
  eccentricity      DOUBLE PRECISION DEFAULT 0.0,     -- Orbital eccentricity
  inclination_deg   DOUBLE PRECISION,                  -- Orbital inclination (degrees)
  
  -- Physical properties
  eq_temp_k         DOUBLE PRECISION,                  -- Equilibrium temperature (Kelvin)
  surface_temp_k    DOUBLE PRECISION,                  -- Surface temperature (Kelvin)
  atmosphere        TEXT,                              -- Atmosphere composition
  composition       TEXT NOT NULL DEFAULT 'unknown',  -- rocky, gas_giant, ice_giant, sub_neptune
  density_gcc       DOUBLE PRECISION,                  -- Density (g/cm³)
  
  -- Habitability assessment
  habitability_score DOUBLE PRECISION DEFAULT 0.0,    -- 0.0 = uninhabitable, 1.0 = Earth-like
  in_habitable_zone BOOLEAN DEFAULT false,             -- Within star's habitable zone
  has_atmosphere    BOOLEAN DEFAULT false,             -- Has substantial atmosphere
  has_water         BOOLEAN DEFAULT false,             -- Evidence of water
  
  -- Resource potential (for game mechanics)
  mineral_richness  DOUBLE PRECISION DEFAULT 0.5,     -- 0.0-1.0 mineral abundance
  energy_potential  DOUBLE PRECISION DEFAULT 0.5,     -- 0.0-1.0 energy resource potential
  rare_elements     TEXT[],                            -- Array of rare element types
  
  -- Discovery data
  discovery_method  TEXT,                              -- Transit, radial velocity, etc.
  discovery_year    INTEGER,                           -- Year of discovery
  discovery_facility TEXT,                             -- Telescope/facility
  
  -- Game integration
  colony_id         TEXT,                              -- Link to existing colonies
  is_colonized      BOOLEAN DEFAULT false,             -- Currently colonized
  is_exploitable    BOOLEAN DEFAULT true,              -- Can be mined/exploited
  exploration_status TEXT DEFAULT 'unexplored',       -- 'unexplored', 'surveyed', 'colonized'
  
  -- Metadata
  created_at        TIMESTAMPTZ DEFAULT now(),
  updated_at        TIMESTAMPTZ DEFAULT now(),
  
  -- Constraints
  CHECK (mass_earth > 0 OR mass_earth IS NULL),
  CHECK (radius_earth > 0 OR radius_earth IS NULL),
  CHECK (sma_au > 0 OR sma_au IS NULL),
  CHECK (period_days > 0 OR period_days IS NULL),
  CHECK (eccentricity >= 0 AND eccentricity < 1 OR eccentricity IS NULL),
  CHECK (habitability_score >= 0.0 AND habitability_score <= 1.0),
  CHECK (mineral_richness >= 0.0 AND mineral_richness <= 1.0),
  CHECK (energy_potential >= 0.0 AND energy_potential <= 1.0),
  CHECK (composition IN ('rocky', 'gas_giant', 'ice_giant', 'sub_neptune', 'super_earth', 'unknown'))
);

-- System neighbors for hyperlane connections (enhanced)
CREATE TABLE IF NOT EXISTS star_neighbors (
  id                BIGSERIAL PRIMARY KEY,
  star_id           BIGINT REFERENCES stars(star_id) ON DELETE CASCADE,
  neighbor_star_id  BIGINT REFERENCES stars(star_id) ON DELETE CASCADE,
  distance_ly       DOUBLE PRECISION NOT NULL,
  travel_time_days  DOUBLE PRECISION,                  -- Game travel time
  hyperlane_type    TEXT DEFAULT 'standard',           -- 'standard', 'major', 'minor'
  is_discovered     BOOLEAN DEFAULT false,             -- Player has discovered this route
  created_at        TIMESTAMPTZ DEFAULT now(),
  
  UNIQUE (star_id, neighbor_star_id),
  CHECK (star_id != neighbor_star_id),
  CHECK (distance_ly > 0)
);

-- Stellar observations (for scientific accuracy and discovery mechanics)
CREATE TABLE IF NOT EXISTS stellar_observations (
  observation_id    BIGSERIAL PRIMARY KEY,
  star_id           BIGINT REFERENCES stars(star_id) ON DELETE CASCADE,
  observer          TEXT NOT NULL,                     -- 'player', 'ai', 'historical'
  observation_type  TEXT NOT NULL,                     -- 'spectroscopy', 'photometry', 'astrometry'
  data_quality      DOUBLE PRECISION DEFAULT 1.0,     -- 0.0-1.0 quality score
  observations      JSONB,                             -- Flexible observation data
  observed_at       TIMESTAMPTZ DEFAULT now(),
  
  CHECK (data_quality >= 0.0 AND data_quality <= 1.0)
);

-- Indexes for performance (enterprise-grade)
-- Spatial indexes for cone searches
CREATE INDEX IF NOT EXISTS idx_stars_radec ON stars (ra_deg, dec_deg);
CREATE INDEX IF NOT EXISTS idx_stars_distance ON stars (distance_ly);
CREATE INDEX IF NOT EXISTS idx_stars_spectral ON stars (spectral_type);
CREATE INDEX IF NOT EXISTS idx_stars_system ON stars (system_id);

-- Planet indexes
CREATE INDEX IF NOT EXISTS idx_planets_star ON planets (star_id);
CREATE INDEX IF NOT EXISTS idx_planets_composition ON planets (composition);
CREATE INDEX IF NOT EXISTS idx_planets_habitable ON planets (in_habitable_zone, habitability_score);
CREATE INDEX IF NOT EXISTS idx_planets_resources ON planets (mineral_richness, energy_potential);
CREATE INDEX IF NOT EXISTS idx_planets_colony ON planets (colony_id);

-- Neighbor indexes for pathfinding
CREATE INDEX IF NOT EXISTS idx_neighbors_star ON star_neighbors (star_id);
CREATE INDEX IF NOT EXISTS idx_neighbors_distance ON star_neighbors (distance_ly);
CREATE INDEX IF NOT EXISTS idx_neighbors_discovered ON star_neighbors (is_discovered);

-- Observation indexes
CREATE INDEX IF NOT EXISTS idx_observations_star ON stellar_observations (star_id);
CREATE INDEX IF NOT EXISTS idx_observations_type ON stellar_observations (observation_type);
CREATE INDEX IF NOT EXISTS idx_observations_time ON stellar_observations (observed_at);

-- Functions for game mechanics
CREATE OR REPLACE FUNCTION calculate_habitable_zone(luminosity_solar DOUBLE PRECISION)
RETURNS TABLE(hz_inner_au DOUBLE PRECISION, hz_outer_au DOUBLE PRECISION) AS $$
BEGIN
  -- Conservative habitable zone calculation based on stellar luminosity
  -- Inner edge: runaway greenhouse limit
  -- Outer edge: maximum greenhouse limit
  RETURN QUERY SELECT 
    SQRT(luminosity_solar / 1.1) AS hz_inner_au,
    SQRT(luminosity_solar / 0.53) AS hz_outer_au;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_planet_habitability()
RETURNS TRIGGER AS $$
DECLARE
  hz_inner DOUBLE PRECISION;
  hz_outer DOUBLE PRECISION;
BEGIN
  -- Get habitable zone boundaries for the star
  SELECT
    SQRT(luminosity_solar / 1.1),
    SQRT(luminosity_solar / 0.53)
  INTO hz_inner, hz_outer
  FROM stars
  WHERE star_id = NEW.star_id;

  -- Set habitability fields directly on NEW record
  NEW.in_habitable_zone = (
    NEW.sma_au >= hz_inner AND
    NEW.sma_au <= hz_outer
  );

  NEW.habitability_score = CASE
    WHEN NEW.composition = 'rocky' AND
         NEW.sma_au >= hz_inner AND
         NEW.sma_au <= hz_outer AND
         NEW.radius_earth BETWEEN 0.5 AND 2.0
    THEN LEAST(1.0, 0.8 * (2.0 - ABS(NEW.radius_earth - 1.0)))
    ELSE 0.1
  END;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update habitability
CREATE TRIGGER trigger_update_habitability
  BEFORE INSERT OR UPDATE ON planets
  FOR EACH ROW
  EXECUTE FUNCTION update_planet_habitability();

-- Function to find nearby stars (for exploration mechanics)
CREATE OR REPLACE FUNCTION find_nearby_stars(
  center_ra DOUBLE PRECISION,
  center_dec DOUBLE PRECISION,
  radius_ly DOUBLE PRECISION DEFAULT 5.0,
  limit_count INTEGER DEFAULT 100
)
RETURNS TABLE(
  star_id BIGINT,
  name TEXT,
  distance_ly DOUBLE PRECISION,
  spectral_type TEXT,
  planet_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.star_id,
    s.name,
    s.distance_ly,
    s.spectral_type,
    COUNT(p.planet_id) AS planet_count
  FROM stars s
  LEFT JOIN planets p ON s.star_id = p.star_id
  WHERE s.distance_ly <= radius_ly
    AND s.ra_deg BETWEEN center_ra - (radius_ly * 0.1) AND center_ra + (radius_ly * 0.1)
    AND s.dec_deg BETWEEN center_dec - (radius_ly * 0.1) AND center_dec + (radius_ly * 0.1)
  GROUP BY s.star_id, s.name, s.distance_ly, s.spectral_type
  ORDER BY s.distance_ly
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

COMMIT;
