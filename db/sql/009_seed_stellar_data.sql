-- Seed Stellar Database with Known Nearby Stars (20 light-years)
-- Real astronomical data for immediate testing and demonstration

BEGIN;

-- Insert well-known nearby star systems with accurate data
INSERT INTO stars (
    src, src_key, name, catalog_name, ra_deg, dec_deg, distance_ly, 
    spectral_type, mass_solar, radius_solar, teff_k, luminosity_solar,
    mag_g, mag_v, hz_inner_au, hz_outer_au, discovery_status, is_colonizable
) VALUES 
-- Sol (our Sun) - reference point
('manual', 'sol', 'Sol', 'Sun', 0.0, 0.0, 0.0, 'G2V', 1.0, 1.0, 5778, 1.0, 4.83, 4.83, 0.95, 1.37, 'known', true),

-- Alpha Centauri System (4.37 ly)
('manual', 'alpha_cen_a', 'Alpha Centauri A', 'HD 128620', 219.9020625, -60.8339583, 4.37, 'G2V', 1.1, 1.22, 5790, 1.52, 4.38, 0.01, 1.2, 1.7, 'known', true),
('manual', 'alpha_cen_b', 'Alpha Centauri B', 'HD 128621', 219.9020625, -60.8339583, 4.37, 'K1V', 0.91, 0.86, 5260, 0.5, 5.71, 1.34, 0.73, 1.05, 'known', true),
('manual', 'proxima_cen', 'Proxima Centauri', 'GJ 551', 217.4289167, -62.6795556, 4.24, 'M5.5Ve', 0.12, 0.15, 3042, 0.0017, 15.6, 11.13, 0.023, 0.054, 'known', true),

-- Barnard's Star (5.96 ly)
('manual', 'barnards', 'Barnard''s Star', 'GJ 699', 269.4520833, 4.6933056, 5.96, 'M4.0V', 0.14, 0.20, 3134, 0.0035, 13.2, 9.53, 0.034, 0.082, 'known', true),

-- Wolf 359 (7.86 ly)
('manual', 'wolf_359', 'Wolf 359', 'GJ 406', 164.1205833, 7.0069444, 7.86, 'M6.0V', 0.09, 0.16, 2800, 0.0014, 16.6, 13.44, 0.02, 0.052, 'known', true),

-- Lalande 21185 (8.31 ly)
('manual', 'lalande_21185', 'Lalande 21185', 'GJ 411', 165.9196667, 35.9561111, 8.31, 'M2.0V', 0.39, 0.39, 3828, 0.026, 10.5, 7.47, 0.11, 0.24, 'known', true),

-- Sirius System (8.66 ly)
('manual', 'sirius_a', 'Sirius A', 'HD 48915', 101.2871250, -16.7161111, 8.66, 'A1V', 2.06, 1.71, 9940, 25.4, -1.33, -1.46, 4.9, 8.9, 'known', true),
('manual', 'sirius_b', 'Sirius B', 'HD 48915B', 101.2871250, -16.7161111, 8.66, 'DA2', 0.98, 0.008, 25200, 0.026, 11.2, 8.44, 0.0, 0.0, 'known', false),

-- Luyten 726-8 System (8.73 ly)
('manual', 'luyten_726_8a', 'Luyten 726-8 A', 'GJ 65 A', 23.6883333, -17.9569444, 8.73, 'M5.5Ve', 0.10, 0.14, 2670, 0.0006, 17.4, 12.54, 0.016, 0.036, 'known', true),
('manual', 'luyten_726_8b', 'Luyten 726-8 B', 'GJ 65 B', 23.6883333, -17.9569444, 8.73, 'M6.0Ve', 0.10, 0.14, 2650, 0.0004, 17.9, 12.99, 0.014, 0.032, 'known', true),

-- Ross 154 (9.69 ly)
('manual', 'ross_154', 'Ross 154', 'GJ 729', 288.1395833, -23.7650000, 9.69, 'M3.5Ve', 0.17, 0.24, 3340, 0.0038, 13.1, 10.43, 0.038, 0.088, 'known', true),

-- Ross 248 (10.32 ly)
('manual', 'ross_248', 'Ross 248', 'GJ 905', 353.7379167, 44.1661111, 10.32, 'M5.5Ve', 0.12, 0.16, 2799, 0.0013, 14.8, 12.29, 0.021, 0.050, 'known', true),

-- Epsilon Eridani (10.52 ly)
('manual', 'eps_eri', 'Epsilon Eridani', 'HD 22049', 53.2327083, -9.4583333, 10.52, 'K2V', 0.82, 0.74, 5084, 0.34, 6.19, 3.73, 0.5, 0.85, 'known', true),

-- Lacaille 9352 (10.74 ly)
('manual', 'lacaille_9352', 'Lacaille 9352', 'GJ 887', 347.1379167, -35.8530556, 10.74, 'M1.5V', 0.42, 0.47, 3934, 0.072, 9.6, 7.34, 0.19, 0.38, 'known', true),

-- Ross 128 (11.01 ly)
('manual', 'ross_128', 'Ross 128', 'GJ 447', 176.9508333, 0.8000000, 11.01, 'M4.0V', 0.17, 0.20, 3192, 0.0036, 13.5, 11.13, 0.037, 0.085, 'known', true),

-- EZ Aquarii System (11.27 ly)
('manual', 'ez_aqr_a', 'EZ Aquarii A', 'GJ 866 A', 334.0754167, -13.0938889, 11.27, 'M5.0Ve', 0.11, 0.14, 3100, 0.0008, 15.6, 13.33, 0.018, 0.042, 'known', true),

-- Procyon System (11.46 ly)
('manual', 'procyon_a', 'Procyon A', 'HD 61421', 114.8254167, 5.2250000, 11.46, 'F5IV-V', 1.50, 2.05, 6530, 7.73, 2.84, 0.34, 2.4, 4.5, 'known', true),
('manual', 'procyon_b', 'Procyon B', 'HD 61421B', 114.8254167, 5.2250000, 11.46, 'DQZ', 0.60, 0.012, 7740, 0.00049, 13.2, 10.7, 0.0, 0.0, 'known', false),

-- 61 Cygni System (11.40 ly)
('manual', '61_cyg_a', '61 Cygni A', 'HD 201091', 315.3662500, 38.4844444, 11.40, 'K5.0V', 0.70, 0.67, 4374, 0.15, 7.49, 5.21, 0.24, 0.58, 'known', true),
('manual', '61_cyg_b', '61 Cygni B', 'HD 201092', 315.3662500, 38.4844444, 11.40, 'K7.0V', 0.63, 0.60, 4040, 0.085, 8.31, 6.03, 0.18, 0.44, 'known', true),

-- Struve 2398 System (11.52 ly)
('manual', 'struve_2398_a', 'Struve 2398 A', 'GJ 725 A', 287.4629167, 59.0075000, 11.52, 'M3.0V', 0.34, 0.35, 3518, 0.054, 11.2, 8.90, 0.14, 0.30, 'known', true),
('manual', 'struve_2398_b', 'Struve 2398 B', 'GJ 725 B', 287.4629167, 59.0075000, 11.52, 'M3.5V', 0.19, 0.25, 3400, 0.036, 11.9, 9.69, 0.12, 0.26, 'known', true),

-- Groombridge 34 System (11.62 ly)
('manual', 'groombridge_34_a', 'Groombridge 34 A', 'GJ 15 A', 11.8879167, 44.0194444, 11.62, 'M1.5V', 0.38, 0.45, 3756, 0.063, 10.3, 8.08, 0.16, 0.35, 'known', true),
('manual', 'groombridge_34_b', 'Groombridge 34 B', 'GJ 15 B', 11.8879167, 44.0194444, 11.62, 'M3.5V', 0.16, 0.21, 3318, 0.0039, 13.3, 11.06, 0.039, 0.089, 'known', true),

-- Epsilon Indi (11.87 ly)
('manual', 'eps_ind', 'Epsilon Indi', 'HD 209100', 330.8379167, -56.7947222, 11.87, 'K5V', 0.76, 0.73, 4276, 0.22, 6.89, 4.69, 0.19, 0.40, 'known', true),

-- DX Cancri (11.83 ly)
('manual', 'dx_cnc', 'DX Cancri', 'GJ 1111', 129.8379167, 26.9019444, 11.83, 'M6.5Ve', 0.09, 0.14, 2840, 0.0012, 17.1, 14.78, 0.019, 0.045, 'known', true),

-- Tau Ceti (11.91 ly)
('manual', 'tau_cet', 'Tau Ceti', 'HD 10700', 26.0170833, -15.9375000, 11.91, 'G8.5V', 0.78, 0.79, 5344, 0.52, 5.68, 3.49, 0.55, 1.16, 'known', true),

-- YZ Ceti (12.11 ly)
('manual', 'yz_cet', 'YZ Ceti', 'GJ 54.1', 18.6954167, -16.8997222, 12.11, 'M4.5V', 0.13, 0.17, 3056, 0.0017, 14.1, 12.02, 0.025, 0.058, 'known', true);

-- Insert known exoplanets for these systems
INSERT INTO planets (
    star_id, src, name, mass_earth, radius_earth, sma_au, period_days, 
    eccentricity, eq_temp_k, composition, discovery_method, discovery_year,
    habitability_score, in_habitable_zone, mineral_richness, energy_potential
) VALUES 
-- Proxima Centauri system
((SELECT star_id FROM stars WHERE name = 'Proxima Centauri'), 'manual', 'Proxima Centauri b', 1.17, 1.1, 0.0485, 11.186, 0.11, 234, 'rocky', 'radial_velocity', 2016, 0.7, true, 0.8, 0.3),
((SELECT star_id FROM stars WHERE name = 'Proxima Centauri'), 'manual', 'Proxima Centauri c', 7.0, 1.5, 1.489, 1928, 0.04, 39, 'super_earth', 'radial_velocity', 2019, 0.1, false, 0.9, 0.1),
((SELECT star_id FROM stars WHERE name = 'Proxima Centauri'), 'manual', 'Proxima Centauri d', 0.26, 0.81, 0.029, 5.122, 0.04, 350, 'rocky', 'radial_velocity', 2022, 0.0, false, 0.6, 0.7),

-- Barnard's Star
((SELECT star_id FROM stars WHERE name = 'Barnard''s Star'), 'manual', 'Barnard''s Star b', 3.2, 1.4, 0.404, 233, 0.32, 105, 'super_earth', 'radial_velocity', 2018, 0.2, false, 0.7, 0.2),

-- Wolf 359
((SELECT star_id FROM stars WHERE name = 'Wolf 359'), 'manual', 'Wolf 359 b', 1.9, 1.2, 0.018, 2.69, 0.0, 728, 'rocky', 'radial_velocity', 2019, 0.0, false, 0.5, 0.9),

-- Epsilon Eridani
((SELECT star_id FROM stars WHERE name = 'Epsilon Eridani'), 'manual', 'Epsilon Eridani b', 400, 7.0, 3.39, 2502, 0.7, 176, 'gas_giant', 'radial_velocity', 2000, 0.0, false, 0.3, 0.4),

-- Ross 128
((SELECT star_id FROM stars WHERE name = 'Ross 128'), 'manual', 'Ross 128 b', 1.35, 1.1, 0.0496, 9.865, 0.036, 269, 'rocky', 'radial_velocity', 2017, 0.6, true, 0.8, 0.4),

-- Tau Ceti system (multiple planets)
((SELECT star_id FROM stars WHERE name = 'Tau Ceti'), 'manual', 'Tau Ceti e', 3.93, 1.5, 0.538, 162.87, 0.18, 240, 'super_earth', 'radial_velocity', 2012, 0.5, true, 0.9, 0.5),
((SELECT star_id FROM stars WHERE name = 'Tau Ceti'), 'manual', 'Tau Ceti f', 3.93, 1.5, 1.35, 636.13, 0.03, 131, 'super_earth', 'radial_velocity', 2012, 0.3, true, 0.8, 0.3),
((SELECT star_id FROM stars WHERE name = 'Tau Ceti'), 'manual', 'Tau Ceti g', 1.75, 1.2, 0.133, 20.0, 0.06, 422, 'rocky', 'radial_velocity', 2017, 0.0, false, 0.7, 0.8),
((SELECT star_id FROM stars WHERE name = 'Tau Ceti'), 'manual', 'Tau Ceti h', 1.83, 1.2, 0.243, 49.41, 0.23, 334, 'rocky', 'radial_velocity', 2017, 0.1, false, 0.6, 0.6),

-- YZ Ceti system
((SELECT star_id FROM stars WHERE name = 'YZ Ceti'), 'manual', 'YZ Ceti b', 0.75, 0.9, 0.01557, 2.02, 0.0, 460, 'rocky', 'radial_velocity', 2017, 0.0, false, 0.4, 0.9),
((SELECT star_id FROM stars WHERE name = 'YZ Ceti'), 'manual', 'YZ Ceti c', 1.14, 1.0, 0.02067, 3.06, 0.0, 414, 'rocky', 'radial_velocity', 2017, 0.0, false, 0.5, 0.8),
((SELECT star_id FROM stars WHERE name = 'YZ Ceti'), 'manual', 'YZ Ceti d', 1.09, 1.0, 0.02612, 4.66, 0.0, 368, 'rocky', 'radial_velocity', 2017, 0.0, false, 0.6, 0.7);

-- Create hyperlane connections between nearby systems
INSERT INTO star_neighbors (star_id, neighbor_star_id, distance_ly, travel_time_days, hyperlane_type, is_discovered) 
SELECT 
    s1.star_id, 
    s2.star_id, 
    s2.distance_ly,
    CASE 
        WHEN s2.distance_ly <= 5 THEN s2.distance_ly * 2
        WHEN s2.distance_ly <= 10 THEN s2.distance_ly * 1.5
        ELSE s2.distance_ly * 1.2
    END as travel_time_days,
    CASE 
        WHEN s2.distance_ly <= 5 THEN 'major'
        WHEN s2.distance_ly <= 10 THEN 'standard'
        ELSE 'minor'
    END as hyperlane_type,
    true as is_discovered
FROM stars s1
CROSS JOIN stars s2
WHERE s1.star_id != s2.star_id 
AND s2.distance_ly <= 15.0  -- Only connect to stars within 15 ly
AND s1.name = 'Sol';  -- Connect Sol to nearby systems

-- Add some additional connections for interesting systems
INSERT INTO star_neighbors (star_id, neighbor_star_id, distance_ly, travel_time_days, hyperlane_type, is_discovered)
SELECT 
    s1.star_id,
    s2.star_id,
    ABS(s1.distance_ly - s2.distance_ly) + 1,
    (ABS(s1.distance_ly - s2.distance_ly) + 1) * 1.5,
    'standard',
    true
FROM stars s1, stars s2
WHERE s1.name = 'Alpha Centauri A' AND s2.name = 'Proxima Centauri'
   OR s1.name = 'Alpha Centauri A' AND s2.name = 'Alpha Centauri B'
   OR s1.name = 'Sirius A' AND s2.name = 'Sirius B'
   OR s1.name = 'Procyon A' AND s2.name = 'Procyon B'
   OR s1.name = '61 Cygni A' AND s2.name = '61 Cygni B';

COMMIT;

-- Display summary
SELECT 
    'Stars loaded' as metric,
    COUNT(*) as count
FROM stars
UNION ALL
SELECT 
    'Planets loaded' as metric,
    COUNT(*) as count
FROM planets
UNION ALL
SELECT 
    'Hyperlane connections' as metric,
    COUNT(*) as count
FROM star_neighbors;
