-- Seed minimal data for dev; idempotent via ON CONFLICT DO NOTHING

-- players
insert into players (id, email, display_name)
values ('player-1', '<EMAIL>', 'Demo')
on conflict (id) do nothing;

-- empires
insert into empires (id, owner_player_id, name)
values ('emp-1', 'player-1', 'Demo Empire')
on conflict (id) do nothing;

-- another empire
insert into empires (id, owner_player_id, name)
values ('emp-2', 'player-1', 'Rival Empire')
on conflict (id) do nothing;


-- systems
insert into systems (id, name, position_xyz)
values ('sys-1', 'Sol', '{"x":0,"y":0,"z":0}'::jsonb)
on conflict (id) do nothing;
insert into systems (id, name, position_xyz)
values ('sys-2', 'Alpha Centauri', '{"x":1,"y":0,"z":0}'::jsonb)
on conflict (id) do nothing;
insert into systems (id, name, position_xyz)
values ('sys-3', 'Proxima', '{"x":1,"y":1,"z":0}'::jsonb)
on conflict (id) do nothing;
insert into systems (id, name, position_xyz)
values ('sys-4', 'Vega', '{"x":0,"y":1,"z":0}'::jsonb)
on conflict (id) do nothing;
insert into systems (id, name, position_xyz)
values ('sys-5', 'Sirius', '{"x":-1,"y":0,"z":0}'::jsonb)
on conflict (id) do nothing;

-- fleets
insert into fleets (id, empire_id, system_id, stance, supply)
values ('fleet-1', 'emp-1', 'sys-1', 'neutral', 100)
on conflict (id) do nothing;

-- target fleet in sys-2 for attack demo
insert into fleets (id, empire_id, system_id, stance, supply)
values ('fleet-2', 'emp-2', 'sys-2', 'neutral', 100)
on conflict (id) do nothing;

-- additional fleets across the expanded world
insert into fleets (id, empire_id, system_id, stance, supply)
values ('fleet-3', 'emp-1', 'sys-3', 'neutral', 80)
on conflict (id) do nothing;
insert into fleets (id, empire_id, system_id, stance, supply)
values ('fleet-4', 'emp-2', 'sys-4', 'defensive', 120)
on conflict (id) do nothing;



-- world adjacency links (bidirectional)
insert into system_links (a,b) values ('sys-1','sys-2') on conflict do nothing;
insert into system_links (a,b) values ('sys-2','sys-1') on conflict do nothing;
insert into system_links (a,b) values ('sys-1','sys-5') on conflict do nothing;
insert into system_links (a,b) values ('sys-5','sys-1') on conflict do nothing;
insert into system_links (a,b) values ('sys-2','sys-3') on conflict do nothing;
insert into system_links (a,b) values ('sys-3','sys-2') on conflict do nothing;
insert into system_links (a,b) values ('sys-3','sys-4') on conflict do nothing;
insert into system_links (a,b) values ('sys-4','sys-3') on conflict do nothing;
insert into system_links (a,b) values ('sys-4','sys-1') on conflict do nothing;
insert into system_links (a,b) values ('sys-1','sys-4') on conflict do nothing;
