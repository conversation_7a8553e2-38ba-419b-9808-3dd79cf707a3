#!/bin/bash

# Fix SSL Certificate Issue for star.omnilyzer.ai
# The server is currently serving dev.trusthansen.dk certificate instead of star.omnilyzer.ai

set -e

echo "🔧 Fixing SSL Certificate for star.omnilyzer.ai..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    print_error "This script must be run with sudo"
    echo "Usage: sudo ./fix-ssl-certificate.sh"
    exit 1
fi

print_status "Current issue: Server serving dev.trusthansen.dk certificate instead of star.omnilyzer.ai"
print_status "This is a multi-site server - we'll add star.omnilyzer.ai without affecting existing sites"

# Step 1: Check current nginx sites
print_status "Checking current nginx configuration..."
echo "Current enabled sites:"
ls -la /etc/nginx/sites-enabled/

echo ""
echo "Current server names in configurations:"
grep -r "server_name" /etc/nginx/sites-available/ 2>/dev/null | grep -v default | grep -v "#"

print_status "Existing sites will remain unaffected - only adding star.omnilyzer.ai"

# Step 2: Create temporary HTTP configuration for star.omnilyzer.ai
print_status "Creating temporary HTTP configuration for certificate generation..."

cat > /etc/nginx/sites-available/star-temp.conf << 'EOF'
server {
    listen 80;
    server_name star.omnilyzer.ai;
    
    root /var/www/star/frontend/dist;
    index index.html;
    
    # ACME challenge for Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/star/frontend/dist;
        try_files $uri =404;
    }
    
    # Serve content temporarily
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Health check
    location /health {
        return 200 "Star Omnilyzer - Certificate Setup\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable temporary configuration
ln -sf /etc/nginx/sites-available/star-temp.conf /etc/nginx/sites-enabled/star-temp.conf

# Test configuration
nginx -t
if [ $? -ne 0 ]; then
    print_error "Nginx configuration test failed"
    exit 1
fi

# Reload nginx
systemctl reload nginx
print_success "Temporary HTTP configuration active"

# Step 3: Test HTTP access
print_status "Testing HTTP access..."
sleep 2
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://star.omnilyzer.ai/health || echo "000")
if [ "$HTTP_STATUS" = "200" ]; then
    print_success "HTTP site accessible (Status: $HTTP_STATUS)"
else
    print_warning "HTTP test status: $HTTP_STATUS - continuing anyway"
fi

# Step 4: Generate SSL certificate for star.omnilyzer.ai
print_status "Generating SSL certificate for star.omnilyzer.ai..."

# Check if certificate already exists
if [ -d "/etc/letsencrypt/live/star.omnilyzer.ai" ]; then
    print_warning "Certificate directory exists, backing up and regenerating..."
    mv /etc/letsencrypt/live/star.omnilyzer.ai /etc/letsencrypt/live/star.omnilyzer.ai.backup.$(date +%s)
fi

# Generate certificate using webroot method
if certbot certonly --webroot -w /var/www/star/frontend/dist -d star.omnilyzer.ai --email <EMAIL> --agree-tos --non-interactive --force-renewal; then
    print_success "SSL certificate generated successfully"
else
    print_error "Failed to generate SSL certificate"
    print_error "Please check domain DNS and firewall settings"
    exit 1
fi

# Step 5: Create proper HTTPS configuration
print_status "Creating HTTPS-only configuration..."

cat > /etc/nginx/sites-available/star.conf << 'EOF'
# HTTP to HTTPS redirect
server {
    listen 80;
    server_name star.omnilyzer.ai;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name star.omnilyzer.ai;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/star.omnilyzer.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/star.omnilyzer.ai/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # CSP for WebGL games
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: blob: https:; connect-src 'self' https: wss: ws:; worker-src 'self' blob:; frame-ancestors 'none'; base-uri 'self'; form-action 'self';" always;

    # Logging
    access_log /var/log/nginx/star-access.log;
    error_log /var/log/nginx/star-error.log;

    # Document root
    root /var/www/star/frontend/dist;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # Main application
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Health check
    location /health {
        access_log off;
        return 200 "Galactic Genesis Game - Online (HTTPS)\n";
        add_header Content-Type text/plain;
    }

    # Security
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

# Step 6: Switch to HTTPS configuration
print_status "Switching to HTTPS configuration..."
print_status "Note: Existing sites (dev.trusthansen.dk, dev.omnilyzer.ai) will remain unaffected"

# Remove temporary configuration
rm -f /etc/nginx/sites-enabled/star-temp.conf

# Enable HTTPS configuration (this adds star.omnilyzer.ai alongside existing sites)
ln -sf /etc/nginx/sites-available/star.conf /etc/nginx/sites-enabled/star.conf

print_status "Added star.conf alongside existing site configurations"

# Test configuration
nginx -t
if [ $? -ne 0 ]; then
    print_error "HTTPS nginx configuration test failed"
    exit 1
fi

# Reload nginx
systemctl reload nginx
print_success "HTTPS configuration active"

# Step 7: Test HTTPS
print_status "Testing HTTPS connection..."
sleep 3

# Test HTTPS
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://star.omnilyzer.ai/health || echo "000")
if [ "$HTTPS_STATUS" = "200" ]; then
    print_success "HTTPS working! (Status: $HTTPS_STATUS)"
else
    print_warning "HTTPS test status: $HTTPS_STATUS"
fi

# Test HTTP redirect
HTTP_REDIRECT=$(curl -s -o /dev/null -w "%{http_code}" http://star.omnilyzer.ai || echo "000")
if [ "$HTTP_REDIRECT" = "301" ]; then
    print_success "HTTP to HTTPS redirect working (Status: $HTTP_REDIRECT)"
else
    print_warning "HTTP redirect status: $HTTP_REDIRECT"
fi

# Step 8: Verify certificate
print_status "Verifying SSL certificate..."
CERT_SUBJECT=$(echo | openssl s_client -connect star.omnilyzer.ai:443 -servername star.omnilyzer.ai 2>/dev/null | openssl x509 -noout -subject | cut -d= -f2-)
print_success "Certificate subject: $CERT_SUBJECT"

# Step 9: Verify existing sites still work
print_status "Verifying existing sites are still working..."

# Test dev.trusthansen.dk
DEV_TRUSTHANSEN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://dev.trusthansen.dk 2>/dev/null || echo "000")
if [ "$DEV_TRUSTHANSEN_STATUS" = "200" ] || [ "$DEV_TRUSTHANSEN_STATUS" = "301" ] || [ "$DEV_TRUSTHANSEN_STATUS" = "302" ]; then
    print_success "dev.trusthansen.dk still working (Status: $DEV_TRUSTHANSEN_STATUS)"
else
    print_warning "dev.trusthansen.dk status: $DEV_TRUSTHANSEN_STATUS"
fi

# Test dev.omnilyzer.ai
DEV_OMNILYZER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://dev.omnilyzer.ai 2>/dev/null || echo "000")
if [ "$DEV_OMNILYZER_STATUS" = "200" ] || [ "$DEV_OMNILYZER_STATUS" = "301" ] || [ "$DEV_OMNILYZER_STATUS" = "302" ]; then
    print_success "dev.omnilyzer.ai still working (Status: $DEV_OMNILYZER_STATUS)"
else
    print_warning "dev.omnilyzer.ai status: $DEV_OMNILYZER_STATUS"
fi

echo ""
echo "🌟 =================================="
echo "🌟 SSL CERTIFICATE FIX COMPLETE"
echo "🌟 =================================="
echo ""
print_success "✅ SSL certificate created for star.omnilyzer.ai"
print_success "✅ HTTPS-only configuration active"
print_success "✅ HTTP to HTTPS redirect working"
print_success "✅ Security headers enabled"
print_success "✅ Existing sites (dev.trusthansen.dk, dev.omnilyzer.ai) unaffected"
echo ""
echo -e "${GREEN}🎮 Galactic Genesis is now available at:${NC}"
echo -e "${BLUE}   https://star.omnilyzer.ai${NC}"
echo ""
echo -e "${GREEN}🚀 SSL certificate fix completed successfully!${NC}"
