<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar System Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .star-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .star-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .star-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }
        .star-card.selected {
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }
        .star-name {
            font-size: 1.5em;
            margin-bottom: 10px;
            color: #00ffff;
        }
        .star-info {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .solar-system {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }
        .solar-system h2 {
            color: #00ffff;
            margin-bottom: 20px;
        }
        .planets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        .planet-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .planet-name {
            font-weight: bold;
            color: #ffaa00;
            margin-bottom: 8px;
        }
        .planet-info {
            font-size: 0.85em;
            line-height: 1.4;
        }
        .back-button {
            background: #00ffff;
            color: black;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .back-button:hover {
            background: #00cccc;
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
        }
        .error {
            color: #ff6666;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 Galactic Genesis - Solar System Test</h1>
        <p>Testing solar system view functionality without WebGL</p>
        
        <div id="loading" class="loading">Loading stellar data...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="galaxy-view" style="display: none;">
            <h2>Galaxy View - Click a star to view its solar system</h2>
            <div id="star-list" class="star-list"></div>
        </div>
        <div id="solar-system-view" style="display: none;">
            <button id="back-button" class="back-button">← Back to Galaxy</button>
            <div id="solar-system" class="solar-system"></div>
        </div>
    </div>

    <script>
        let stars = [];
        let selectedStar = null;

        async function loadStars() {
            try {
                const response = await fetch('http://localhost:19081/v1/stellar/stars?limit=20');
                const data = await response.json();
                stars = data.stars;
                displayGalaxyView();
            } catch (error) {
                showError('Failed to load stellar data: ' + error.message);
            }
        }

        function displayGalaxyView() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('galaxy-view').style.display = 'block';
            document.getElementById('solar-system-view').style.display = 'none';
            
            const starList = document.getElementById('star-list');
            starList.innerHTML = '';
            
            stars.forEach(star => {
                const starCard = document.createElement('div');
                starCard.className = 'star-card';
                starCard.innerHTML = `
                    <div class="star-name">${star.name}</div>
                    <div class="star-info">
                        Distance: ${star.distance_ly.toFixed(2)} ly<br>
                        Spectral Type: ${star.spectral_type || 'Unknown'}<br>
                        Planets: ${star.planet_count}
                    </div>
                `;
                starCard.addEventListener('click', () => selectStar(star));
                starList.appendChild(starCard);
            });
        }

        async function selectStar(star) {
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('galaxy-view').style.display = 'none';
                
                const response = await fetch(`http://localhost:19081/v1/stellar/stars/${star.star_id}`);
                const starDetail = await response.json();
                selectedStar = starDetail;
                displaySolarSystem();
            } catch (error) {
                showError('Failed to load star details: ' + error.message);
            }
        }

        function displaySolarSystem() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('solar-system-view').style.display = 'block';
            
            const solarSystemDiv = document.getElementById('solar-system');
            solarSystemDiv.innerHTML = `
                <h2>🌟 ${selectedStar.name} Solar System</h2>
                <p>Distance: ${selectedStar.distance_ly.toFixed(2)} light years</p>
                <p>Spectral Type: ${selectedStar.spectral_type || 'Unknown'}</p>
                <p>Planets: ${selectedStar.planets.length}</p>
                
                <h3>Planets:</h3>
                <div class="planets-grid">
                    ${selectedStar.planets.map(planet => `
                        <div class="planet-card">
                            <div class="planet-name">${planet.name}</div>
                            <div class="planet-info">
                                Mass: ${planet.mass_earth ? planet.mass_earth.toFixed(2) + ' Earth masses' : 'Unknown'}<br>
                                Distance: ${planet.sma_au ? planet.sma_au.toFixed(3) + ' AU' : 'Unknown'}<br>
                                Composition: ${planet.composition || 'Unknown'}<br>
                                Habitable Zone: ${planet.in_habitable_zone ? 'Yes' : 'No'}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').textContent = message;
        }

        document.getElementById('back-button').addEventListener('click', displayGalaxyView);

        // Start loading
        loadStars();
    </script>
</body>
</html>
