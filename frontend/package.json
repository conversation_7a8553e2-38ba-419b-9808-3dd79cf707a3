{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-check": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:browser": "playwright test", "test:browser:ui": "playwright test --ui", "test:browser:headed": "playwright test --headed", "test:import-errors": "playwright test import-errors.spec.ts", "test:comprehensive": "playwright test comprehensive-frontend.spec.ts", "test:port-monitor": "playwright test port-monitor.spec.ts", "test:ci": "playwright test --reporter=github"}, "dependencies": {"@react-three/drei": "^10.7.4", "@react-three/fiber": "^9.3.0", "@react-three/postprocessing": "^3.0.4", "@react-three/rapier": "^2.1.0", "@tanstack/react-query": "^5.87.1", "@types/three": "^0.180.0", "autoprefixer": "^10.4.21", "framer-motion": "^12.23.12", "leva": "^0.10.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "three": "^0.180.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@playwright/test": "^1.55.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^24.3.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "cypress": "^15.1.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "puppeteer": "^24.20.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}