<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galactic Genesis - Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 40px;
            max-width: 600px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
        button {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: scale(1.05);
        }
        #results {
            margin-top: 20px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 Galactic Genesis</h1>
        <h2>Connection Test</h2>
        
        <div class="status success">
            ✅ Frontend Server: Connected
        </div>
        
        <div id="webgl-status" class="status info">
            🔍 Testing WebGL Support...
        </div>
        
        <div id="api-status" class="status info">
            🔍 Testing API Connection...
        </div>
        
        <button onclick="testWebGL()">🎮 Test WebGL</button>
        <button onclick="testAPI()">📡 Test API</button>
        <button onclick="goToGame()">🚀 Launch Game</button>
        
        <div id="results"></div>
    </div>

    <script>
        function testWebGL() {
            const statusEl = document.getElementById('webgl-status');
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    statusEl.className = 'status success';
                    statusEl.innerHTML = '✅ WebGL: Supported';
                    addResult('WebGL is supported and working!');
                } else {
                    statusEl.className = 'status error';
                    statusEl.innerHTML = '❌ WebGL: Not Supported';
                    addResult('WebGL is not supported. The game will show a fallback interface.');
                }
            } catch (e) {
                statusEl.className = 'status error';
                statusEl.innerHTML = '❌ WebGL: Error - ' + e.message;
                addResult('WebGL error: ' + e.message);
            }
        }

        async function testAPI() {
            const statusEl = document.getElementById('api-status');
            try {
                statusEl.innerHTML = '🔍 Testing API Connection...';

                // Add timeout to prevent hanging
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);

                const response = await fetch('http://localhost:19081/v1/stellar/stars?limit=1', {
                    signal: controller.signal,
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    const data = await response.json();
                    statusEl.className = 'status success';
                    statusEl.innerHTML = '✅ API: Connected (' + data.stars.length + ' stars)';
                    addResult('API is working! Found ' + data.stars.length + ' star(s) in database.');
                } else {
                    statusEl.className = 'status error';
                    statusEl.innerHTML = '❌ API: Error ' + response.status;
                    addResult('API error: ' + response.status + ' ' + response.statusText);
                }
            } catch (e) {
                statusEl.className = 'status error';
                if (e.name === 'AbortError') {
                    statusEl.innerHTML = '❌ API: Timeout (5s)';
                    addResult('API connection timed out after 5 seconds. This is normal in some environments.');
                } else {
                    statusEl.innerHTML = '❌ API: Connection Failed';
                    addResult('API connection failed: ' + e.message + ' (This may be due to CORS or network restrictions)');
                }
            }
        }

        function goToGame() {
            window.location.href = '/';
        }

        function addResult(message) {
            const resultsEl = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsEl.innerHTML += `<div style="margin: 5px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px;">[${timestamp}] ${message}</div>`;
        }

        // Auto-run tests on page load
        window.onload = function() {
            setTimeout(testWebGL, 500);
            setTimeout(testAPI, 1000);
        };
    </script>
</body>
</html>
