// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Custom commands for Galactic Genesis
declare global {
  namespace Cypress {
    interface Chainable {
      waitForGameLoad(): Chainable<void>
      clickStar(starName: string): Chainable<void>
      checkStellarData(): Chainable<void>
    }
  }
}

// Wait for the game to fully load
Cypress.Commands.add('waitForGameLoad', () => {
  // Wait for loading screen to disappear
  cy.get('[data-testid="loading-screen"]', { timeout: 15000 }).should('not.exist')
  
  // Wait for 3D canvas to be present
  cy.get('canvas', { timeout: 10000 }).should('be.visible')
  
  // Wait for stellar data to load
  cy.get('[data-testid="galaxy-stats"]', { timeout: 10000 }).should('be.visible')
})

// Click on a star in the 3D galaxy map
Cypress.Commands.add('clickStar', (starName: string) => {
  // This is a simplified version - in reality, clicking 3D objects is complex
  // For now, we'll simulate by checking if star info appears
  cy.get('canvas').click()
  cy.wait(1000) // Wait for selection to register
})

// Check that stellar data is loaded and valid
Cypress.Commands.add('checkStellarData', () => {
  cy.get('[data-testid="galaxy-stats"]').within(() => {
    cy.contains('Stars Loaded').should('be.visible')
    cy.contains(/\d+/).should('be.visible') // Should show a number
  })
})
