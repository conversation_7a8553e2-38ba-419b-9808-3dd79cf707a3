/**
 * E2E Tests for Game Startup
 * Tests the complete user journey from loading to playing
 */

describe('Game Startup', () => {
  beforeEach(() => {
    // Visit the game
    cy.visit('/')
  })

  it('should load the game successfully', () => {
    // Check that the page loads
    cy.get('body').should('be.visible')
    
    // Should show loading screen initially
    cy.get('h1').contains('GALACTIC GENESIS').should('be.visible')
    cy.get('div').contains('Initializing galactic empire').should('be.visible')
    
    // Wait for game to load (with generous timeout for WebSocket failures)
    cy.waitForGameLoad()
    
    // Should show the main game interface
    cy.get('[data-testid="galaxy-view"]', { timeout: 15000 }).should('be.visible')
  })

  it('should display stellar data', () => {
    cy.waitForGameLoad()
    
    // Check that stellar statistics are displayed
    cy.checkStellarData()
    
    // Should show stellar neighborhood info
    cy.get('[data-testid="galaxy-stats"]').within(() => {
      cy.contains('Stellar Neighborhood').should('be.visible')
      cy.contains('Stars Loaded').should('be.visible')
      cy.contains('Total Planets').should('be.visible')
    })
  })

  it('should render 3D galaxy map', () => {
    cy.waitForGameLoad()
    
    // Should have a canvas element for 3D rendering
    cy.get('canvas').should('be.visible')
    cy.get('canvas').should('have.attr', 'width')
    cy.get('canvas').should('have.attr', 'height')
  })

  it('should handle WebSocket connection gracefully', () => {
    // The game should load even if WebSocket fails
    cy.waitForGameLoad()
    
    // Check console for WebSocket warnings (not errors)
    cy.window().then((win) => {
      // Game should be functional regardless of WebSocket status
      expect(win.document.querySelector('canvas')).to.exist
    })
  })

  it('should show tutorial option', () => {
    cy.waitForGameLoad()
    
    // Should have tutorial controls
    cy.get('[data-testid="tutorial-button"]', { timeout: 10000 }).should('be.visible')
  })

  it('should display navigation controls', () => {
    cy.waitForGameLoad()
    
    // Should show main navigation
    cy.get('[data-testid="main-nav"]', { timeout: 10000 }).should('be.visible')
    
    // Should have fleet management
    cy.get('[data-testid="fleet-panel"]', { timeout: 10000 }).should('be.visible')
  })
})

describe('Stellar Database Integration', () => {
  beforeEach(() => {
    cy.visit('/')
    cy.waitForGameLoad()
  })

  it('should load real stellar data', () => {
    // Check that we have actual star data
    cy.get('[data-testid="galaxy-stats"]').within(() => {
      // Should show more than 0 stars
      cy.contains(/Stars Loaded:\s*[1-9]\d*/).should('be.visible')
      
      // Should show planet counts
      cy.contains(/Total Planets:\s*\d+/).should('be.visible')
      
      // Should show stellar classifications
      cy.contains(/M-Dwarfs:\s*\d+/).should('be.visible')
      cy.contains(/G-Type Stars:\s*\d+/).should('be.visible')
    })
  })

  it('should display scientific accuracy attribution', () => {
    cy.get('[data-testid="galaxy-stats"]').within(() => {
      cy.contains('Real data from Gaia DR3 & NASA').should('be.visible')
    })
  })

  it('should show distance information', () => {
    cy.get('[data-testid="galaxy-stats"]').within(() => {
      cy.contains(/Max Distance:\s*[\d.]+\s*ly/).should('be.visible')
    })
  })
})

describe('Error Handling', () => {
  it('should handle API failures gracefully', () => {
    // Intercept API calls and make them fail
    cy.intercept('GET', '**/v1/stellar/**', { forceNetworkError: true }).as('stellarApiFail')
    
    cy.visit('/')
    
    // Game should still load with fallback data
    cy.waitForGameLoad()
    
    // Should show some stellar data (fallback)
    cy.checkStellarData()
  })

  it('should handle slow loading gracefully', () => {
    // Intercept and delay API calls
    cy.intercept('GET', '**/v1/stellar/**', (req) => {
      req.reply((res) => {
        res.delay(2000) // 2 second delay
        res.send({ fixture: 'stellar-data.json' })
      })
    }).as('slowStellarApi')
    
    cy.visit('/')
    
    // Should show loading state
    cy.contains('Loading stellar database').should('be.visible')
    
    // Should eventually load
    cy.waitForGameLoad()
  })
})
