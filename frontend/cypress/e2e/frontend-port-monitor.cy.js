/**
 * Frontend Port Monitor E2E Tests
 * Tests the port monitoring functionality integrated into the main frontend
 */

describe('Frontend Port Monitor', () => {
  const FRONTEND_URL = 'http://localhost:5174';
  const HEALTH_DASHBOARD_URL = 'http://localhost:8086';

  beforeEach(() => {
    // Handle uncaught exceptions from WebSocket errors
    cy.on('uncaught:exception', (err, runnable) => {
      // Ignore WebSocket errors during testing
      if (err.message.includes('WebSocket') || err.message.includes('closed without opened')) {
        return false;
      }
      return true;
    });

    // Visit the main frontend
    cy.visit(FRONTEND_URL);

    // Wait for the page to load
    cy.get('[data-testid="main-nav"]', { timeout: 10000 }).should('be.visible');
  });

  it('should have port monitor button in the navigation', () => {
    // Check that the port monitor button exists
    cy.contains('button', '🔌 Ports').should('be.visible');
  });

  it('should have health dashboard button in the navigation', () => {
    // Check that the health dashboard button exists
    cy.contains('button', '🏥 Health').should('be.visible');
  });

  it('should open port monitor modal when button is clicked', () => {
    // Click the port monitor button
    cy.contains('button', '🔌 Ports').click();
    
    // Check that the modal opens
    cy.contains('Port Monitor').should('be.visible');
    cy.contains('Real-time system port monitoring').should('be.visible');
  });

  it('should display real port data in the monitor', () => {
    // First verify the health dashboard is available
    cy.request(`${HEALTH_DASHBOARD_URL}/api/ports`).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('ports');
      expect(response.body).to.have.property('summary');
    });

    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for the modal to load
    cy.contains('Port Monitor').should('be.visible');
    
    // Check for loading state first
    cy.contains('Loading port data...').should('be.visible');
    
    // Wait for data to load and check summary metrics
    cy.get('[class*="text-2xl font-bold text-blue-400"]', { timeout: 10000 }).should('be.visible');
    
    // Verify summary sections exist
    cy.contains('Total Ports').should('be.visible');
    cy.contains('Listening').should('be.visible');
    cy.contains('Expected').should('be.visible');
    cy.contains('Unexpected').should('be.visible');
    cy.contains('Missing').should('be.visible');
    
    // Verify port table exists
    cy.contains('th', 'Status').should('be.visible');
    cy.contains('th', 'Port').should('be.visible');
    cy.contains('th', 'Service').should('be.visible');
    cy.contains('th', 'Protocol').should('be.visible');
    cy.contains('th', 'Process').should('be.visible');
    cy.contains('th', 'Actions').should('be.visible');
  });

  it('should show port status indicators correctly', () => {
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for data to load
    cy.get('[class*="text-2xl font-bold text-blue-400"]', { timeout: 10000 }).should('be.visible');
    
    // Check for status indicators (colored dots)
    cy.get('[class*="w-3 h-3 rounded-full"]').should('have.length.greaterThan', 0);
    
    // Check for status text
    cy.get('tbody tr').should('have.length.greaterThan', 0);
  });

  it('should display last scan timestamp', () => {
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for data to load
    cy.get('[class*="text-2xl font-bold text-blue-400"]', { timeout: 10000 }).should('be.visible');
    
    // Check for timestamp
    cy.contains('Last scan:').should('be.visible');
  });

  it('should show kill buttons for listening ports', () => {
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for data to load
    cy.get('[class*="text-2xl font-bold text-blue-400"]', { timeout: 10000 }).should('be.visible');
    
    // Check for kill buttons (should exist for listening ports)
    cy.get('button').contains('Kill').should('exist');
  });

  it('should close modal when close button is clicked', () => {
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for modal to open
    cy.contains('Port Monitor').should('be.visible');
    
    // Click the close button (X button)
    cy.get('button').contains('svg').click();
    
    // Verify modal is closed
    cy.contains('Port Monitor').should('not.exist');
  });

  it('should handle service unavailable gracefully', () => {
    // Intercept the API call and make it fail
    cy.intercept('GET', '**/api/ports', { forceNetworkError: true }).as('getPortsFail');
    
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for the error to appear
    cy.contains('Port monitoring service is not available', { timeout: 10000 }).should('be.visible');
    
    // Check that helpful instructions are shown
    cy.contains('To enable port monitoring:').should('be.visible');
    cy.contains('http://localhost:8086').should('be.visible');
  });

  it('should open health dashboard in new tab when health button is clicked', () => {
    // Mock window.open to prevent actual navigation
    cy.window().then((win) => {
      cy.stub(win, 'open').as('windowOpen');
    });
    
    // Click the health dashboard button
    cy.contains('button', '🏥 Health').click();
    
    // Verify window.open was called with correct URL
    cy.get('@windowOpen').should('have.been.calledWith', 'http://localhost:8086', '_blank');
  });

  it('should display comprehensive port overview', () => {
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Wait for data to load
    cy.get('[class*="text-2xl font-bold text-blue-400"]', { timeout: 10000 }).should('be.visible');
    
    // Verify we have a comprehensive overview with multiple ports
    cy.get('tbody tr').should('have.length.greaterThan', 10);
    
    // Verify we have different types of ports
    cy.contains('Running').should('be.visible');
    cy.contains('tcp').should('be.visible');
    
    // Verify we have port numbers displayed
    cy.get('tbody tr').first().within(() => {
      cy.get('td').eq(1).should('contain.text', /^\d+$/);
    });
  });

  it('should show real system ports', () => {
    // First get real port data from the API
    cy.request(`${HEALTH_DASHBOARD_URL}/api/ports`).then((response) => {
      const portData = response.body;
      
      // Open the port monitor
      cy.contains('button', '🔌 Ports').click();
      
      // Wait for data to load
      cy.get('[class*="text-2xl font-bold text-blue-400"]', { timeout: 10000 }).should('be.visible');
      
      // Verify the summary matches real data
      cy.contains('Total Ports').parent().within(() => {
        cy.get('[class*="text-2xl font-bold text-blue-400"]').should('contain.text', portData.summary.total.toString());
      });
      
      cy.contains('Listening').parent().within(() => {
        cy.get('[class*="text-2xl font-bold text-green-400"]').should('contain.text', portData.summary.listening.toString());
      });
      
      // Verify at least some of the actual ports are displayed
      if (portData.ports.length > 0) {
        const firstPort = portData.ports[0];
        cy.contains('td', firstPort.port.toString()).should('be.visible');
      }
    });
  });

  it('should handle responsive design', () => {
    // Test on mobile viewport
    cy.viewport(375, 667);
    
    // Open the port monitor
    cy.contains('button', '🔌 Ports').click();
    
    // Verify modal is responsive
    cy.contains('Port Monitor').should('be.visible');
    cy.get('[class*="w-11/12"]').should('be.visible');
    
    // Test on tablet viewport
    cy.viewport(768, 1024);
    
    // Verify summary grid adapts
    cy.get('[class*="grid-cols-2 md:grid-cols-5"]').should('be.visible');
    
    // Test on desktop viewport
    cy.viewport(1280, 720);
    
    // Verify full layout
    cy.get('[class*="max-w-6xl"]').should('be.visible');
  });
});
