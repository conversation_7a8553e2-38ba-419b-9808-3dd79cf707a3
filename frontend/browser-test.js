#!/usr/bin/env node

/**
 * Real Browser Simulation Test
 * Tests the application with a real Chromium browser to diagnose loading issues
 */

import puppeteer from 'puppeteer';

async function testApplication() {
  console.log('🚀 Starting browser simulation test...');
  
  let browser;
  let page;
  
  try {
    // Launch browser with detailed logging
    console.log('🌐 Launching Chromium browser...');
    browser = await puppeteer.launch({
      headless: true,  // Run headless for server environment
      devtools: false, // No DevTools in headless
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--no-first-run',
        '--no-zygote',
        '--enable-webgl',
        '--use-gl=swiftshader',
        '--enable-accelerated-2d-canvas',
        '--disable-web-security',
        '--enable-logging',
        '--log-level=0'
      ]
    });

    page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });
    
    // Enable console logging
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ Browser Console [${type.toUpperCase()}]:`, text);
    });
    
    // Enable error logging
    page.on('error', err => {
      console.error('❌ Browser Error:', err.message);
    });
    
    page.on('pageerror', err => {
      console.error('❌ Page Error:', err.message);
    });
    
    // Enable request/response logging
    page.on('request', request => {
      console.log(`📤 Request: ${request.method()} ${request.url()}`);
    });
    
    page.on('response', response => {
      const status = response.status();
      const url = response.url();
      const statusIcon = status >= 200 && status < 300 ? '✅' : '❌';
      console.log(`📥 Response: ${statusIcon} ${status} ${url}`);
    });
    
    // Navigate to the application
    console.log('🔗 Navigating to http://localhost:5175/...');
    const response = await page.goto('http://localhost:5175/', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });
    
    console.log(`📊 Initial response status: ${response.status()}`);
    
    // Wait for the application to load
    console.log('⏳ Waiting for application to load...');
    
    // Check if main elements are present
    try {
      await page.waitForSelector('body', { timeout: 10000 });
      console.log('✅ Body element found');
      
      // Check for React root
      const reactRoot = await page.$('#root');
      if (reactRoot) {
        console.log('✅ React root element found');
      } else {
        console.log('❌ React root element not found');
      }
      
      // Check for any error messages
      const errorElements = await page.$$('[class*="error"], [class*="Error"]');
      if (errorElements.length > 0) {
        console.log(`⚠️ Found ${errorElements.length} potential error elements`);
      }
      
      // Get page title
      const title = await page.title();
      console.log(`📄 Page title: "${title}"`);
      
      // Get page content info
      const bodyText = await page.evaluate(() => document.body.innerText);
      console.log(`📝 Page content length: ${bodyText.length} characters`);
      
      if (bodyText.includes('Loading') || bodyText.includes('loading')) {
        console.log('⏳ Page appears to be in loading state');
      }
      
      if (bodyText.includes('Error') || bodyText.includes('error')) {
        console.log('❌ Page appears to have errors');
      }
      
      // Check for 3D canvas
      const canvas = await page.$('canvas');
      if (canvas) {
        console.log('✅ Canvas element found (3D rendering)');

        // Try to click on a star to test solar system view
        console.log('🖱️ Attempting to click on a star...');
        try {
          // Wait for stars to load and click on the canvas
          await page.waitForTimeout(3000);
          await page.click('canvas');
          console.log('✅ First click on canvas (select star)');

          await page.waitForTimeout(1000);
          await page.click('canvas');
          console.log('✅ Second click on canvas (zoom to solar system)');

          await page.waitForTimeout(2000);

          // Check if solar system view loaded
          const backButton = await page.$('button:has-text("Back to Galaxy")');
          if (backButton) {
            console.log('✅ Solar system view loaded (back button found)');
          } else {
            console.log('❌ Solar system view not loaded (no back button)');
          }
        } catch (clickError) {
          console.log('⚠️ Could not test star clicking:', clickError.message);
        }
      } else {
        console.log('❌ Canvas element not found');
      }

      // Wait a bit more for dynamic content
      console.log('⏳ Waiting for dynamic content...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Take a screenshot
      console.log('📸 Taking screenshot...');
      await page.screenshot({ 
        path: 'frontend/debug-screenshot.png',
        fullPage: true 
      });
      console.log('✅ Screenshot saved as debug-screenshot.png');
      
      // Get final console logs
      const logs = await page.evaluate(() => {
        return window.console.history || [];
      });
      
      console.log('🎯 Test completed successfully!');
      
    } catch (error) {
      console.error('❌ Error during application testing:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Browser test failed:', error.message);
  } finally {
    if (browser) {
      console.log('🔚 Closing browser...');
      await browser.close();
    }
  }
}

// Run the test
testApplication().catch(console.error);
