import React, { useEffect, useState } from 'react';
import { stellarApi } from './services/stellarApi';

const TestPage: React.FC = () => {
  const [status, setStatus] = useState('Loading...');
  const [stellarData, setStellarData] = useState<any>(null);

  useEffect(() => {
    const runTests = async () => {
      try {
        setStatus('🚀 Starting tests...');
        
        // Test 1: Basic stellar API
        setStatus('⭐ Testing stellar API...');
        const stars = await stellarApi.getStars(20, 10);
        setStellarData(stars);
        
        // Test 2: Statistics
        setStatus('📊 Testing statistics...');
        const stats = await stellarApi.getStatistics();
        
        setStatus(`✅ All tests passed! Loaded ${stars.stars.length} stars`);
      } catch (error) {
        setStatus(`❌ Test failed: ${error.message}`);
      }
    };

    runTests();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#1a1a1a', 
      color: '#00ff00',
      minHeight: '100vh'
    }}>
      <h1>🧪 Galactic Genesis - Test Page</h1>
      <p><strong>Status:</strong> {status}</p>
      
      {stellarData && (
        <div>
          <h2>📊 Stellar Data</h2>
          <p>Total Stars: {stellarData.total}</p>
          <p>Loaded Stars: {stellarData.stars.length}</p>
          
          <h3>🌟 Sample Stars:</h3>
          <ul>
            {stellarData.stars.slice(0, 5).map((star: any) => (
              <li key={star.star_id}>
                {star.name} ({star.spectral_type}) - {star.distance_ly.toFixed(2)} ly
              </li>
            ))}
          </ul>
        </div>
      )}
      
      <div style={{ marginTop: '20px' }}>
        <h3>🔧 Debug Info</h3>
        <p>Environment: {import.meta.env.DEV ? 'Development' : 'Production'}</p>
        <p>WebSocket Disabled: {import.meta.env.VITE_DISABLE_WS}</p>
        <p>API URL: {import.meta.env.VITE_API_URL}</p>
        <p>Timestamp: {new Date().toISOString()}</p>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <a href="/" style={{ color: '#00ffff' }}>← Back to Main Game</a>
      </div>
    </div>
  );
};

export default TestPage;
