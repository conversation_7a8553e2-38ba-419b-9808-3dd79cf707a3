import React from 'react';

const MinimalApp: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#000',
      color: '#00ff00',
      minHeight: '100vh'
    }}>
      <h1>🚀 Galactic Genesis - Minimal Test</h1>
      <p>✅ React is working!</p>
      <p>✅ TypeScript is working!</p>
      <p>✅ Vite is working!</p>
      <p>Timestamp: {new Date().toISOString()}</p>
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => alert('Button works!')}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#333', 
            color: '#00ff00', 
            border: '1px solid #00ff00',
            cursor: 'pointer'
          }}
        >
          Test Button
        </button>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <a href="/?test=true" style={{ color: '#00ffff' }}>→ Full Test Page</a>
        <br />
        <a href="/" style={{ color: '#00ffff' }}>→ Main Game</a>
      </div>
    </div>
  );
};

export default MinimalApp;
