import { useEffect, useState } from 'react';
import { useGameStore } from './store/gameStore';
import GameHUD from './components/GameHUD';
import EnhancedGalaxy3D from './components/EnhancedGalaxy3D';
import FleetList from './components/FleetList';
import OrderForm from './components/OrderForm';
import LoadingScreen from './components/LoadingScreen';
import ErrorDisplay from './components/ErrorDisplay';
import Tutorial from './components/Tutorial';
import ResourceDisplay from './components/ResourceDisplay';
import ColonyManagement from './components/ColonyManagement';
import TechnologyTree from './components/TechnologyTree';
import { Market } from './components/Market';
import Shipyard from './components/Shipyard';

function EnhancedApp() {
  const {
    initialize,
    isLoading,
    error,
    showOrderForm,
    showTutorialModal
  } = useGameStore();

  const [activeModal, setActiveModal] = useState<'colonies' | 'technology' | 'market' | 'shipyard' | null>(null);

  useEffect(() => {
    console.log('🚀 EnhancedApp: Starting initialization...');
    initialize();
  }, [initialize]);

  // Handle ESC key to close modals
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setActiveModal(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  if (isLoading && !useGameStore.getState().fleets.length) {
    return <LoadingScreen />;
  }

  if (error) {
    return <ErrorDisplay error={error} />;
  }

  return (
    <div className="h-screen w-screen bg-gray-900 starfield-bg overflow-hidden">
      {/* Game HUD - Top bar */}
      <GameHUD />

      {/* Compact Resource Display */}
      <div className="px-4 pt-16">
        <ResourceDisplay empireId="emp-1" />
      </div>

      {/* Main game area */}
      <div className="flex h-full pt-4">
        {/* Left sidebar - Fleet management */}
        <div className="w-80 glass-panel m-4 p-4 overflow-y-auto">
          <FleetList />
        </div>

        {/* Center - Enhanced Galaxy map */}
        <div className="flex-1 relative">
          <EnhancedGalaxy3D />
        </div>

        {/* Right sidebar - Action buttons */}
        <div className="w-20 flex flex-col gap-4 m-4">
          <button
            onClick={() => setActiveModal('colonies')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Manage Colonies"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🏛️</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Colonies</div>
          </button>

          <button
            onClick={() => setActiveModal('technology')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Research Technology"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🔬</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Tech</div>
          </button>

          <button
            onClick={() => setActiveModal('market')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Galactic Market"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">💰</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Market</div>
          </button>

          <button
            onClick={() => setActiveModal('shipyard')}
            className="glass-panel p-4 text-center hover:bg-gray-700 hover:border-cyan-400 transition-all duration-200 group transform hover:scale-105"
            title="Imperial Shipyard"
          >
            <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">🚀</div>
            <div className="text-xs text-gray-400 group-hover:text-cyan-400 transition-colors">Shipyard</div>
          </button>
        </div>
      </div>

      {/* Management Modals */}
      {activeModal === 'colonies' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">🏛️ Colony Management</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <ColonyManagement empireId="emp-1" />
            </div>
          </div>
        </div>
      )}

      {activeModal === 'technology' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">🔬 Technology Tree</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <TechnologyTree empireId="emp-1" />
            </div>
          </div>
        </div>
      )}

      {activeModal === 'market' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">💰 Galactic Market</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <Market empireId="emp-1" />
            </div>
          </div>
        </div>
      )}

      {activeModal === 'shipyard' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform animate-slideUp">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">🚀 Imperial Shipyard</h2>
              <button
                onClick={() => setActiveModal(null)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <Shipyard />
            </div>
          </div>
        </div>
      )}

      {/* Game Modals */}
      {showOrderForm && <OrderForm />}
      {showTutorialModal && <Tutorial />}

      {/* Enhanced mode indicator */}
      <div className="absolute bottom-4 right-4 text-green-400 text-sm">
        🌟 Enhanced Galaxy Mode
        <div className="text-xs text-gray-400">
          Hover systems • Rotation controls • Enhanced planets
        </div>
      </div>
    </div>
  );
}

export default EnhancedApp;
