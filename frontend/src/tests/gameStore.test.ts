/**
 * Unit Tests for Game Store
 * Tests Zustand game state management
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useGameStore } from '../store/gameStore';

// Mock the WebSocket service
vi.mock('../services/websocket', () => ({
  wsService: {
    connect: vi.fn(),
    disconnect: vi.fn(),
    disable: vi.fn(),
    on: vi.fn(() => () => {}),
    onFleetMoved: vi.fn(() => () => {}),
    onBattleResolved: vi.fn(() => () => {}),
    onOrderApplied: vi.fn(() => () => {}),
    onTurnTick: vi.fn(() => () => {}),
    isConnected: false,
    isDisabledOrConnected: true
  }
}));

// Mock the API client
vi.mock('../services/api-simple', () => ({
  simpleApiClient: {
    getFleets: vi.fn().mockResolvedValue([]),
    getSystems: vi.fn().mockResolvedValue([]),
    getOrders: vi.fn().mockResolvedValue([]),
    getBattles: vi.fn().mockResolvedValue([]),
    getTurn: vi.fn().mockResolvedValue({ turn: 1 }),
    getCurrentTurn: vi.fn().mockResolvedValue({ turn: 1 })
  }
}));

describe('Game Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useGameStore.setState({
      turn: 0,
      fleets: [],
      systems: [],
      orders: [],
      battles: [],
      selectedFleetId: null,
      selectedSystemId: null,
      isLoading: false,
      error: null,
      showOrderForm: false,
      orderFormType: null,
      showTutorialModal: false
    });
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useGameStore.getState();
      
      expect(state.turn).toBe(0);
      expect(state.fleets).toEqual([]);
      expect(state.systems).toEqual([]);
      expect(state.orders).toEqual([]);
      expect(state.battles).toEqual([]);
      expect(state.selectedFleetId).toBeNull();
      expect(state.selectedSystemId).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(state.showOrderForm).toBe(false);
      expect(state.orderFormType).toBeNull();
      expect(state.showTutorialModal).toBe(false);
    });
  });

  describe('Fleet Management', () => {
    it('should select fleet', () => {
      const { selectFleet } = useGameStore.getState();
      
      selectFleet('fleet-1');
      
      expect(useGameStore.getState().selectedFleetId).toBe('fleet-1');
    });

    it('should deselect fleet', () => {
      const { selectFleet } = useGameStore.getState();
      
      selectFleet('fleet-1');
      selectFleet(null);
      
      expect(useGameStore.getState().selectedFleetId).toBeNull();
    });
  });

  describe('System Management', () => {
    it('should select system', () => {
      const { selectSystem } = useGameStore.getState();
      
      selectSystem('system-1');
      
      expect(useGameStore.getState().selectedSystemId).toBe('system-1');
    });

    it('should deselect system', () => {
      const { selectSystem } = useGameStore.getState();
      
      selectSystem('system-1');
      selectSystem(null);
      
      expect(useGameStore.getState().selectedSystemId).toBeNull();
    });
  });

  describe('UI State Management', () => {
    it('should show order form', () => {
      const { showOrderFormFor } = useGameStore.getState();

      showOrderFormFor('move');

      const state = useGameStore.getState();
      expect(state.showOrderForm).toBe(true);
      expect(state.orderFormType).toBe('move');
    });

    it('should hide order form', () => {
      const { showOrderFormFor, hideOrderForm } = useGameStore.getState();

      showOrderFormFor('move');
      hideOrderForm();

      const state = useGameStore.getState();
      expect(state.showOrderForm).toBe(false);
      expect(state.orderFormType).toBeNull();
    });

    it('should show tutorial', () => {
      const { showTutorial } = useGameStore.getState();
      
      showTutorial();
      
      expect(useGameStore.getState().showTutorialModal).toBe(true);
    });

    it('should hide tutorial', () => {
      const { showTutorial, hideTutorial } = useGameStore.getState();
      
      showTutorial();
      hideTutorial();
      
      expect(useGameStore.getState().showTutorialModal).toBe(false);
    });
  });

  describe('Initialization', () => {
    it('should initialize without errors', async () => {
      const { initialize } = useGameStore.getState();
      
      await expect(initialize()).resolves.not.toThrow();
    });

    it('should set loading state during initialization', async () => {
      const { initialize } = useGameStore.getState();
      
      const initPromise = initialize();
      
      // Should be loading initially
      expect(useGameStore.getState().isLoading).toBe(true);
      
      await initPromise;
      
      // Should not be loading after completion
      expect(useGameStore.getState().isLoading).toBe(false);
    });
  });

  describe('Data Refresh', () => {
    it('should refresh data without errors', async () => {
      const { refreshData } = useGameStore.getState();
      
      await expect(refreshData()).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      // Mock API to throw error
      const { simpleApiClient } = await import('../services/api-simple');
      vi.mocked(simpleApiClient.getFleets).mockRejectedValueOnce(new Error('API Error'));
      
      const { initialize } = useGameStore.getState();
      
      await initialize();
      
      // Should not crash and should set error state
      const state = useGameStore.getState();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeTruthy();
    });
  });
});
