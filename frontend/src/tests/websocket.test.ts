/**
 * Unit Tests for WebSocket Service
 * Tests WebSocket connection and event handling
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { wsService } from '../services/websocket';

// Mock socket.io-client
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    connected: false,
    on: vi.fn(),
    onAny: vi.fn(),
    disconnect: vi.fn(),
    close: vi.fn()
  }))
}));

describe('WebSocket Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Connection Management', () => {
    it('should initialize without errors', () => {
      expect(wsService).toBeDefined();
      expect(typeof wsService.connect).toBe('function');
      expect(typeof wsService.disconnect).toBe('function');
    });

    it('should handle connection gracefully', () => {
      expect(() => wsService.connect()).not.toThrow();
    });

    it('should handle disconnection gracefully', () => {
      expect(() => wsService.disconnect()).not.toThrow();
    });

    it('should handle disable gracefully', () => {
      expect(() => wsService.disable()).not.toThrow();
    });
  });

  describe('Event Handling', () => {
    it('should allow event subscription', () => {
      const handler = vi.fn();
      const unsubscribe = wsService.on('test.event', handler);
      
      expect(typeof unsubscribe).toBe('function');
      expect(() => unsubscribe()).not.toThrow();
    });

    it('should allow typed event subscriptions', () => {
      const fleetHandler = vi.fn();
      const battleHandler = vi.fn();
      const orderHandler = vi.fn();
      const turnHandler = vi.fn();
      
      expect(() => wsService.onFleetMoved(fleetHandler)).not.toThrow();
      expect(() => wsService.onBattleStarted(battleHandler)).not.toThrow();
      expect(() => wsService.onOrderApplied(orderHandler)).not.toThrow();
      expect(() => wsService.onTurnTick(turnHandler)).not.toThrow();
    });

    it('should handle any event subscription', () => {
      const handler = vi.fn();
      expect(() => wsService.onAnyEvent(handler)).not.toThrow();
    });
  });

  describe('Connection State', () => {
    it('should report connection status', () => {
      expect(typeof wsService.isConnected).toBe('boolean');
    });

    it('should report disabled or connected status', () => {
      expect(typeof wsService.isDisabledOrConnected).toBe('boolean');
    });
  });

  describe('Error Handling', () => {
    it('should handle connection errors gracefully', () => {
      // Test that the service doesn't crash on connection errors
      expect(() => {
        wsService.connect();
        // Simulate connection error
        wsService.disconnect();
      }).not.toThrow();
    });

    it('should handle disable after connection attempts', () => {
      wsService.connect();
      expect(() => wsService.disable()).not.toThrow();
    });
  });
});
