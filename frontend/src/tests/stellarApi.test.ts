/**
 * Unit Tests for Stellar API
 * Tests the stellar database API client functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { stellarApi } from '../services/stellarApi';
import type { Star } from '../types/stellar';

// Mock fetch for testing
global.fetch = vi.fn();

describe('Stellar API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getStars', () => {
    it('should fetch stars successfully', async () => {
      const mockResponse = {
        stars: [
          {
            star_id: 1,
            name: 'Sol',
            ra_deg: 0,
            dec_deg: 0,
            distance_ly: 0,
            spectral_type: 'G2V',
            planet_count: 8,
            discovery_status: 'known',
            is_colonizable: true
          }
        ],
        total: 1
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await stellarApi.getStars(20, 10);
      
      expect(result.stars).toHaveLength(1);
      expect(result.stars[0].name).toBe('Sol');
      expect(result.total).toBe(1);
    });

    it('should use fallback data when API fails', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const result = await stellarApi.getStars(20, 10);
      
      // Should return fallback data
      expect(result.stars).toHaveLength(8); // Fallback has 8 stars
      expect(result.stars[0].name).toBe('Sol');
      expect(result.stars[1].name).toBe('Proxima Centauri');
    });
  });

  describe('getStarColor', () => {
    it('should return correct colors for spectral types', () => {
      expect(stellarApi.getStarColor('O5V')).toBe('#9BB0FF'); // Blue
      expect(stellarApi.getStarColor('B2V')).toBe('#AABFFF'); // Blue-white
      expect(stellarApi.getStarColor('A1V')).toBe('#CAD7FF'); // White
      expect(stellarApi.getStarColor('F5V')).toBe('#F8F7FF'); // Yellow-white
      expect(stellarApi.getStarColor('G2V')).toBe('#FFF4EA'); // Yellow (Sun)
      expect(stellarApi.getStarColor('K1V')).toBe('#FFD2A1'); // Orange
      expect(stellarApi.getStarColor('M5V')).toBe('#FFAD51'); // Red
    });

    it('should return default color for unknown spectral types', () => {
      expect(stellarApi.getStarColor('UNKNOWN')).toBe('#FFFFFF');
      expect(stellarApi.getStarColor(undefined)).toBe('#FFFFFF');
      expect(stellarApi.getStarColor('')).toBe('#FFFFFF');
    });
  });

  describe('getStarSize', () => {
    it('should return correct sizes for different spectral types', () => {
      const mockStars: Partial<Star>[] = [
        { spectral_type: 'O5V', radius_solar: 15 },
        { spectral_type: 'G2V', radius_solar: 1 }, // Sun
        { spectral_type: 'M5V', radius_solar: 0.2 },
        { spectral_type: 'A1V' }, // No radius data
      ];

      expect(stellarApi.getStarSize(mockStars[0] as Star)).toBeCloseTo(3.0); // O-type (clamped to max)
      expect(stellarApi.getStarSize(mockStars[1] as Star)).toBeCloseTo(0.8); // G-type (Sun)
      expect(stellarApi.getStarSize(mockStars[2] as Star)).toBeCloseTo(0.36); // M-dwarf (sqrt(0.2) * 0.8)
      expect(stellarApi.getStarSize(mockStars[3] as Star)).toBeCloseTo(1.2); // A-type default
    });
  });

  describe('convertToCartesian', () => {
    it('should convert astronomical coordinates to 3D Cartesian', () => {
      const star: Partial<Star> = {
        ra_deg: 0,
        dec_deg: 0,
        distance_ly: 10
      };

      const position = stellarApi.convertToCartesian(star as Star);
      
      expect(position).toHaveLength(3);
      expect(typeof position[0]).toBe('number');
      expect(typeof position[1]).toBe('number');
      expect(typeof position[2]).toBe('number');
      
      // For RA=0, Dec=0, should be on positive X axis
      expect(position[0]).toBeCloseTo(10, 1);
      expect(position[1]).toBeCloseTo(0, 1);
      expect(position[2]).toBeCloseTo(0, 1);
    });

    it('should handle edge cases', () => {
      const star: Partial<Star> = {
        ra_deg: 90,
        dec_deg: 90,
        distance_ly: 5
      };

      const position = stellarApi.convertToCartesian(star as Star);
      
      // Should not return NaN or undefined
      expect(position[0]).not.toBeNaN();
      expect(position[1]).not.toBeNaN();
      expect(position[2]).not.toBeNaN();
    });
  });

  describe('getStatistics', () => {
    it('should fetch statistics successfully', async () => {
      const mockStats = {
        total_stars: 29,
        total_planets: 14,
        habitable_planets: 4,
        m_dwarf_count: 17,
        g_dwarf_count: 3,
        rocky_planets: 9
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats
      });

      const result = await stellarApi.getStatistics();
      
      expect(result.total_stars).toBe(29);
      expect(result.habitable_planets).toBe(4);
    });

    it('should return fallback statistics when API fails', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const result = await stellarApi.getStatistics();
      
      // Should return fallback statistics
      expect(result.total_stars).toBeGreaterThan(0);
      expect(typeof result.total_planets).toBe('number');
    });
  });
});
