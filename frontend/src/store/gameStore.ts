import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type {
  Fleet,
  System,
  Order,
  Battle,
  GameState,
  MoveOrderPayload,
  AttackOrderPayload,
  ResupplyOrderPayload,
} from '../types/game';
import { simpleApiClient as apiClient } from '../services/api-simple';
import { wsService } from '../services/websocket';

interface UIState {
  selectedFleetId: string | null;
  selectedSystemId: string | null;
  isLoading: boolean;
  error: string | null;
  showOrderForm: boolean;
  orderFormType: 'move' | 'attack' | 'resupply' | null;
  showTutorialModal: boolean;
}

interface GameStore extends GameState, UIState {
  // Actions
  initialize: () => Promise<void>;
  refreshData: () => Promise<void>;
  
  // Fleet actions
  selectFleet: (fleetId: string | null) => void;
  createFleet: (fleetData: Partial<Fleet>) => Promise<void>;
  updateFleet: (id: string, updates: Partial<Fleet>) => Promise<void>;
  
  // System actions
  selectSystem: (systemId: string | null) => void;
  
  // Order actions
  submitMoveOrder: (payload: MoveOrderPayload) => Promise<void>;
  submitAttackOrder: (payload: AttackOrderPayload) => Promise<void>;
  submitResupplyOrder: (payload: ResupplyOrderPayload) => Promise<void>;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  showOrderFormFor: (type: 'move' | 'attack' | 'resupply') => void;
  hideOrderForm: () => void;
  showTutorial: () => void;
  hideTutorial: () => void;
  
  // WebSocket event handlers
  handleFleetMoved: (fleetId: string, fromSystemId: string, toSystemId: string, supply: number) => void;
  handleBattleResolved: (attackerId: string, targetId: string, attackerSupply: number, targetSupply: number, destroyed: boolean) => void;
  handleOrderApplied: (orderId: string, status: string) => void;
  handleTurnTick: (turn: number) => void;
}

export const useGameStore = create<GameStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    turn: 0,
    fleets: [],
    systems: [],
    orders: [],
    battles: [],
    selectedFleetId: null,
    selectedSystemId: null,
    isLoading: false,
    error: null,
    showOrderForm: false,
    orderFormType: null,
    showTutorialModal: false,

    // Initialize the game
    initialize: async () => {
      // Prevent double initialization in React StrictMode
      if (get().isLoading) {
        console.log('🔄 Game already initializing, skipping...');
        return;
      }

      console.log('🚀 Starting game initialization...');
      set({ isLoading: true, error: null });

      try {
        // Try to connect to WebSocket for real-time updates (non-blocking)
        // Skip WebSocket in development if VITE_DISABLE_WS is set
        if (import.meta.env.VITE_DISABLE_WS === 'true') {
          console.log('🔇 WebSocket disabled by environment variable');
          console.log('🎮 Running in offline mode with stellar data only');
        } else {
          try {
            // Only connect if not already connected or connecting
            if (wsService && !wsService.isConnected && !wsService.isDisabledOrConnected) {
              console.log('🔌 Attempting WebSocket connection...');
              wsService.connect();
            }

        // Set up WebSocket event listeners
        if (wsService) {
          wsService.onFleetMoved((event) => {
          const { fleetId, fromSystemId, toSystemId, supply } = event.payload;
          get().handleFleetMoved(fleetId, fromSystemId, toSystemId, supply);
        });
        
        wsService.onBattleResolved((event) => {
          const { attackerId, targetId, attackerSupply, targetSupply, destroyed } = event.payload;
          get().handleBattleResolved(attackerId, targetId, attackerSupply, targetSupply, destroyed);
        });
        
        wsService.onOrderApplied((event) => {
          const { orderId, status } = event.payload;
          get().handleOrderApplied(orderId, status);
        });
        
        wsService.onTurnTick((event) => {
          // Turn tick events might not include the turn number directly
          // We'll refresh the turn data instead
          get().refreshData();
        });

          console.log('✅ WebSocket connection initiated');
        }
          } catch (wsError) {
            console.warn('⚠️ WebSocket connection failed, continuing without real-time updates:', wsError);
          }
        }

        // Load initial data (this should work even without WebSocket)
        console.log('📊 Loading initial game data...');

        // If WebSocket is disabled, we're probably in offline mode
        if (import.meta.env.VITE_DISABLE_WS === 'true') {
          console.log('🔇 Offline mode detected - skipping API calls');
          // Set minimal data for offline mode
          set({
            turn: 1,
            fleets: [],
            orders: [],
            battles: [],
            systems: []
          });
        } else {
          await get().refreshData();
        }

        console.log('✅ Game initialization completed successfully!');
        
      } catch (error) {
        console.error('Failed to initialize game:', error);
        set({ error: error instanceof Error ? error.message : 'Failed to initialize game' });
      } finally {
        set({ isLoading: false });
      }
    },

    // Refresh all game data
    refreshData: async () => {
      try {
        console.log('🔄 Refreshing game data...');

        // Add timeout to prevent hanging
        const timeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Data refresh timeout after 5 seconds')), 5000)
        );

        const dataPromise = Promise.all([
          apiClient.getCurrentTurn(),
          apiClient.getFleets(),
          apiClient.getOrders(),
          apiClient.getBattles(),
        ]);

        const [turnResponse, fleetsResponse, ordersResponse, battlesResponse] = await Promise.race([
          dataPromise,
          timeout
        ]);

        set({
          turn: turnResponse.turn,
          fleets: fleetsResponse.fleets,
          orders: ordersResponse.orders,
          battles: battlesResponse.battles,
        });

        // Try to load systems if available
        try {
          const systemsResponse = await apiClient.getSystems();
          set({ systems: systemsResponse.systems });
        } catch (error) {
          console.warn('Systems endpoint not available:', error);
        }

        console.log('✅ Game data refreshed successfully');

      } catch (error) {
        console.warn('⚠️ Failed to refresh data, using fallback mode:', error);
        // Set error state and minimal fallback data so the game can still work
        set({
          turn: 1,
          fleets: [],
          orders: [],
          battles: [],
          systems: [],
          error: error instanceof Error ? error.message : 'Failed to refresh data'
        });
      }
    },

    // Fleet actions
    selectFleet: (fleetId) => {
      set({ selectedFleetId: fleetId });
    },

    createFleet: async (fleetData) => {
      set({ isLoading: true, error: null });
      try {
        const newFleet = await apiClient.createFleet(fleetData);
        set((state) => ({
          fleets: [...state.fleets, newFleet],
          isLoading: false,
        }));
      } catch (error) {
        console.error('Failed to create fleet:', error);
        set({ 
          error: error instanceof Error ? error.message : 'Failed to create fleet',
          isLoading: false,
        });
      }
    },

    updateFleet: async (id, updates) => {
      set({ isLoading: true, error: null });
      try {
        const updatedFleet = await apiClient.updateFleet(id, updates);
        set((state) => ({
          fleets: state.fleets.map(fleet => 
            fleet.id === id ? updatedFleet : fleet
          ),
          isLoading: false,
        }));
      } catch (error) {
        console.error('Failed to update fleet:', error);
        set({ 
          error: error instanceof Error ? error.message : 'Failed to update fleet',
          isLoading: false,
        });
      }
    },

    // System actions
    selectSystem: (systemId) => {
      set({ selectedSystemId: systemId });
    },

    // Order actions
    submitMoveOrder: async (payload) => {
      set({ isLoading: true, error: null });
      try {
        const result = await apiClient.submitMoveOrder(payload, `move-${Date.now()}`);
        console.log('Move order submitted:', result);
        
        // Refresh orders to get the latest state
        const ordersResponse = await apiClient.getOrders();
        set({ 
          orders: ordersResponse.orders,
          isLoading: false,
          showOrderForm: false,
        });
      } catch (error) {
        console.error('Failed to submit move order:', error);
        set({ 
          error: error instanceof Error ? error.message : 'Failed to submit move order',
          isLoading: false,
        });
      }
    },

    submitAttackOrder: async (payload) => {
      set({ isLoading: true, error: null });
      try {
        const result = await apiClient.submitAttackOrder(payload, `attack-${Date.now()}`);
        console.log('Attack order submitted:', result);
        
        // Refresh orders to get the latest state
        const ordersResponse = await apiClient.getOrders();
        set({ 
          orders: ordersResponse.orders,
          isLoading: false,
          showOrderForm: false,
        });
      } catch (error) {
        console.error('Failed to submit attack order:', error);
        set({ 
          error: error instanceof Error ? error.message : 'Failed to submit attack order',
          isLoading: false,
        });
      }
    },

    submitResupplyOrder: async (payload) => {
      set({ isLoading: true, error: null });
      try {
        const result = await apiClient.submitResupplyOrder(payload, `resupply-${Date.now()}`);
        console.log('Resupply order submitted:', result);
        
        // Refresh orders to get the latest state
        const ordersResponse = await apiClient.getOrders();
        set({ 
          orders: ordersResponse.orders,
          isLoading: false,
          showOrderForm: false,
        });
      } catch (error) {
        console.error('Failed to submit resupply order:', error);
        set({ 
          error: error instanceof Error ? error.message : 'Failed to submit resupply order',
          isLoading: false,
        });
      }
    },

    // UI actions
    setLoading: (loading) => set({ isLoading: loading }),
    setError: (error) => set({ error }),
    
    showOrderFormFor: (type) => {
      set({ showOrderForm: true, orderFormType: type });
    },
    
    hideOrderForm: () => {
      set({ showOrderForm: false, orderFormType: null });
    },

    showTutorial: () => {
      set({ showTutorialModal: true });
    },

    hideTutorial: () => {
      set({ showTutorialModal: false });
    },

    // WebSocket event handlers
    handleFleetMoved: (fleetId, fromSystemId, toSystemId, supply) => {
      set((state) => ({
        fleets: state.fleets.map(fleet =>
          fleet.id === fleetId
            ? { ...fleet, system_id: toSystemId, supply }
            : fleet
        ),
      }));
    },

    handleBattleResolved: (attackerId, targetId, attackerSupply, targetSupply, destroyed) => {
      set((state) => ({
        fleets: state.fleets.map(fleet => {
          if (fleet.id === attackerId) {
            return { ...fleet, supply: attackerSupply };
          }
          if (fleet.id === targetId) {
            return { ...fleet, supply: targetSupply };
          }
          return fleet;
        }).filter(fleet => !(fleet.id === targetId && destroyed)),
      }));
    },

    handleOrderApplied: (orderId, status) => {
      set((state) => ({
        orders: state.orders.map(order =>
          order.id === orderId
            ? { ...order, status: status as Order['status'] }
            : order
        ),
      }));
    },

    handleTurnTick: (turn) => {
      set({ turn });
    },
  }))
);
