// Game types matching the backend API

export interface Fleet {
  id: string;
  empire_id: string;
  system_id: string;
  stance: 'neutral' | 'aggressive' | 'defensive';
  supply: number;
  system_name: string;
}

export interface System {
  id: string;
  name: string;
  x: number;
  y: number;
  z: number;
}

export interface Order {
  id: string;
  empire_id: string;
  kind: 'move' | 'attack' | 'resupply';
  payload: Record<string, unknown>;
  target_turn: number;
  idem_key: string | null;
  status: 'accepted' | 'applied' | 'rejected';
  created_at: string;
}

export interface Battle {
  id: string;
  order_id: string;
  system_id: string;
  attacker_fleet_id: string;
  target_fleet_id: string;
  attacker_empire_id: string;
  target_empire_id: string;
  attacker_supply_after: number;
  target_supply_after: number;
  destroyed: boolean;
  created_at: string;
}

export interface GameState {
  turn: number;
  fleets: Fleet[];
  systems: System[];
  orders: Order[];
  battles: Battle[];
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface FleetsResponse {
  fleets: Fleet[];
}

export interface OrdersResponse {
  orders: Order[];
}

export interface BattlesResponse {
  battles: Battle[];
}

export interface TurnResponse {
  turn: number;
}

export interface HealthResponse {
  ok: boolean;
}

// Order payloads
export interface MoveOrderPayload {
  fleetId: string;
  toSystemId: string;
}

export interface AttackOrderPayload {
  fleetId: string;
  targetFleetId: string;
}

export interface ResupplyOrderPayload {
  fleetId: string;
  amount: number;
}

// WebSocket event types
export interface WebSocketEvent {
  type: string;
  payload: unknown;
}

export interface FleetMovedEvent {
  type: 'fleet.moved';
  payload: {
    fleetId: string;
    fromSystemId: string;
    toSystemId: string;
    supply: number;
  };
}

export interface BattleStartedEvent {
  type: 'battle.started';
  payload: {
    attackerId: string;
    targetId: string;
    systemId: string;
    orderId: string;
  };
}

export interface BattleResolvedEvent {
  type: 'battle.resolved';
  payload: {
    attackerId: string;
    targetId: string;
    attackerSupply: number;
    targetSupply: number;
    destroyed: boolean;
    orderId: string;
  };
}

export interface OrderAppliedEvent {
  type: 'order.applied';
  payload: {
    orderId: string;
    status: string;
  };
}

export interface TurnTickEvent {
  type: 'turn.tick';
  payload: {
    ts: number;
  };
}
