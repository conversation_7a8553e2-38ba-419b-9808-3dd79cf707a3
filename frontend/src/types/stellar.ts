// Stellar Database Types
// Type definitions for stellar and planetary data

export interface Star {
  star_id: number;
  name: string;
  catalog_name?: string;
  ra_deg: number;
  dec_deg: number;
  distance_ly: number;

  // 3D coordinates in parsecs
  x_pc?: number;
  y_pc?: number;
  z_pc?: number;

  // Enhanced stellar properties
  spectral_type?: string;
  mass_solar?: number;
  radius_solar?: number;
  teff_k?: number;
  luminosity_solar?: number;
  mag_v?: number;
  absolute_magnitude?: number;
  color_bv?: number;
  stellar_color?: string;

  // Identification
  proper_name?: string;
  hipparcos_id?: number;
  henry_draper_id?: number;
  gliese_id?: string;

  // System properties
  hz_inner_au?: number;
  hz_outer_au?: number;
  discovery_status: string;
  is_colonizable: boolean;
  planet_count: number;

  // Data quality
  data_quality_score?: number;
}

export interface Planet {
  planet_id: number | string; // API returns string, but we handle both
  star_id: number;
  name: string;

  // Orbital characteristics
  distance_au?: number;
  sma_au?: number; // Semi-major axis in AU (actual database field)
  orbital_period_days?: number;
  period_days?: number; // Orbital period in days (actual database field)
  orbital_eccentricity?: number;
  eccentricity?: number; // Eccentricity (actual database field)
  orbital_inclination_deg?: number;
  inclination_deg?: number; // Inclination in degrees (actual database field)

  // Physical characteristics
  diameter_km?: number;
  mass_earth: number;
  radius_earth: number;
  density_g_cm3?: number;
  density_gcc?: number; // Density in g/cm³ (actual database field)

  // Rotational characteristics
  rotation_period_hours?: number;
  axial_tilt_deg?: number;

  // Atmospheric data
  atmosphere_main_gases?: string;
  atmosphere?: string; // Atmospheric composition (actual database field)
  atmosphere_pressure_bars?: number;
  has_atmosphere?: boolean; // Whether planet has atmosphere (actual database field)

  // Chemical signatures and composition
  chemical_signatures?: string;
  surface_composition?: string;

  // Temperature data
  avg_surface_temp_c?: number;
  surface_temp_k?: number; // Surface temperature in Kelvin (actual database field)
  eq_temp_k?: number; // Equilibrium temperature in Kelvin (actual database field)
  min_surface_temp_c?: number;
  max_surface_temp_c?: number;

  // Visual characteristics
  dominant_color?: string;
  color_hex?: string;
  albedo?: number;
  visual_texture?: string;
  atmospheric_effects?: string;

  // Raw materials and resources
  raw_materials?: string;
  material_category?: string;

  // Classification
  planet_type?: string;
  composition: string; // Planet composition (actual database field)
  habitability_score: number;
  in_habitable_zone: boolean;

  // Discovery and exploration
  discovery_year?: number;
  discovery_method?: string;
  exploration_status: string;

  // Game-specific attributes
  mineral_richness: number;
  energy_potential: number;
  is_colonized: boolean;

  // Related data
  moons?: Moon[];
  moon_count?: number;
}

export interface Moon {
  moon_id: number;
  planet_id: number;
  name: string;

  // Orbital characteristics
  distance_km: number;
  orbital_period_days: number;
  orbital_eccentricity?: number;
  orbital_inclination_deg?: number;

  // Physical characteristics
  diameter_km: number;
  mass_earth?: number;
  radius_earth?: number;
  mass_kg?: number;
  density_g_cm3?: number;

  // Rotational characteristics
  rotation_period_hours?: number;
  is_tidally_locked?: boolean;

  // Surface characteristics
  surface_composition?: string;
  avg_surface_temp_c?: number;

  // Visual characteristics
  dominant_color?: string;
  color_hex?: string;
  albedo?: number;
  visual_texture?: string;

  // Raw materials and resources
  raw_materials?: string;
  material_category?: string;

  // Classification
  moon_type?: string;

  // Discovery
  discovery_year?: number;
  discovery_method?: string;

  // Game-specific
  mineral_richness?: number;
  exploration_status?: string;
}

export interface StarDetail extends Star {
  planets: Planet[];
}

export interface PlanetDetail extends Planet {
  moons: Moon[];
}

export interface StellarStatistics {
  total_stars: number;
  total_planets: number;
  habitable_planets: number;
  colonized_planets: number;
  avg_star_distance: number;
  m_dwarf_count: number;
  g_dwarf_count: number;
  rocky_planets: number;
}
