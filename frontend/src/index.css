@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for modal system */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out;
}

.animate-slideUp {
  animation: slideUp 0.3s ease-out;
}

@layer base {
  :root {
    font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
  }

  body {
    @apply bg-gray-900 text-white min-h-screen overflow-hidden;
    background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
    background-attachment: fixed;
  }

  * {
    @apply box-border;
  }
}

@layer components {
  .starfield-bg {
    background-image:
      radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent),
      radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
  }

  .neon-glow {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }

  .neon-border {
    @apply border border-neon-cyan;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  }

  .glass-panel {
    @apply bg-gray-900 bg-opacity-80 backdrop-blur-sm border border-gray-700 rounded-lg;
  }

  /* Enhanced stunning visuals */
  .stunning-bg {
    background:
      radial-gradient(ellipse at top, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(ellipse at bottom, rgba(255, 107, 157, 0.2) 0%, transparent 50%),
      radial-gradient(ellipse at left, rgba(78, 205, 196, 0.2) 0%, transparent 50%),
      radial-gradient(ellipse at right, rgba(255, 214, 10, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #0c1445 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #1a1a2e 100%);
    background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
    animation: stunning-gradient 20s ease infinite;
  }

  @keyframes stunning-gradient {
    0%, 100% {
      background-position: 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%;
    }
    50% {
      background-position: 100% 50%, 100% 50%, 100% 50%, 100% 50%, 100% 50%;
    }
  }

  .cosmic-glow {
    box-shadow:
      0 0 20px rgba(120, 119, 198, 0.4),
      0 0 40px rgba(255, 107, 157, 0.2),
      0 0 60px rgba(78, 205, 196, 0.1),
      inset 0 0 20px rgba(255, 255, 255, 0.05);
  }

  .stellar-border {
    border: 1px solid transparent;
    background: linear-gradient(45deg, rgba(120, 119, 198, 0.3), rgba(255, 107, 157, 0.3), rgba(78, 205, 196, 0.3)) border-box;
    border-radius: 8px;
  }

  .nebula-text {
    background: linear-gradient(45deg, #7877c6, #ff6b9d, #4ecdc4, #ffd60a);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: nebula-shift 8s ease-in-out infinite;
  }

  @keyframes nebula-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .hologram-effect {
    position: relative;
    overflow: hidden;
  }

  .hologram-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    animation: hologram-scan 3s linear infinite;
  }

  @keyframes hologram-scan {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  .particle-bg {
    background-image:
      radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 40px 70px, rgba(120, 119, 198, 0.6), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 107, 157, 0.5), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(78, 205, 196, 0.4), transparent),
      radial-gradient(1px 1px at 160px 30px, rgba(255, 214, 10, 0.3), transparent),
      radial-gradient(2px 2px at 200px 60px, rgba(255,255,255,0.2), transparent);
    background-repeat: repeat;
    background-size: 250px 150px;
    animation: particle-drift 30s linear infinite;
  }

  @keyframes particle-drift {
    0% { background-position: 0px 0px; }
    100% { background-position: 250px 150px; }
  }
}
