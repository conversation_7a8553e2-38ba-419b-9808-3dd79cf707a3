import React, { useEffect, useState } from 'react';

const DebugTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    addResult('🚀 Starting comprehensive system test...');

    // Test 1: Basic imports
    try {
      addResult('📦 Testing imports...');
      
      // Test React imports
      const React = await import('react');
      addResult('✅ React import successful');
      
      // Test Zustand
      const { create } = await import('zustand');
      addResult('✅ Zustand import successful');
      
      // Test Three.js
      const THREE = await import('three');
      addResult('✅ Three.js import successful');
      
    } catch (error) {
      addResult(`❌ Import test failed: ${error.message}`);
    }

    // Test 2: Game Store
    try {
      addResult('🎮 Testing game store...');
      const { useGameStore } = await import('./store/gameStore');
      const store = useGameStore.getState();
      addResult('✅ Game store accessible');
      addResult(`📊 Store state: loading=${store.isLoading}, error=${store.error}`);
    } catch (error) {
      addResult(`❌ Game store test failed: ${error.message}`);
    }

    // Test 3: Services
    try {
      addResult('🔧 Testing services...');
      
      const { stellarApi } = await import('./services/stellarApi');
      addResult('✅ Stellar API service imported');
      
      const { wsService } = await import('./services/websocket');
      addResult('✅ WebSocket service imported');
      
      const { simpleApiClient } = await import('./services/api-simple');
      addResult('✅ Simple API client imported');
      
    } catch (error) {
      addResult(`❌ Services test failed: ${error.message}`);
    }

    // Test 4: Components
    try {
      addResult('🧩 Testing component imports...');
      
      const Galaxy3D = await import('./components/Galaxy3D');
      addResult('✅ Galaxy3D component imported');
      
      const GameHUD = await import('./components/GameHUD');
      addResult('✅ GameHUD component imported');
      
      const FleetList = await import('./components/FleetList');
      addResult('✅ FleetList component imported');
      
    } catch (error) {
      addResult(`❌ Component test failed: ${error.message}`);
    }

    // Test 5: API Connectivity
    try {
      addResult('🌐 Testing API connectivity...');
      
      // Test stellar API with timeout
      const timeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('API timeout')), 2000)
      );
      
      const apiTest = fetch('http://localhost:19080/v1/stellar/stars?limit=1');
      
      try {
        const response = await Promise.race([apiTest, timeout]);
        if (response.ok) {
          addResult('✅ Stellar API responding');
        } else {
          addResult(`⚠️ Stellar API returned ${response.status}`);
        }
      } catch (apiError) {
        addResult(`⚠️ Stellar API not available: ${apiError.message}`);
      }
      
    } catch (error) {
      addResult(`❌ API test failed: ${error.message}`);
    }

    // Test 6: Environment Variables
    try {
      addResult('🔧 Testing environment...');
      addResult(`📍 NODE_ENV: ${import.meta.env.NODE_ENV || 'undefined'}`);
      addResult(`📍 VITE_API_URL: ${import.meta.env.VITE_API_URL || 'undefined'}`);
      addResult(`📍 VITE_WS_URL: ${import.meta.env.VITE_WS_URL || 'undefined'}`);
      addResult(`📍 VITE_DISABLE_WS: ${import.meta.env.VITE_DISABLE_WS || 'undefined'}`);
    } catch (error) {
      addResult(`❌ Environment test failed: ${error.message}`);
    }

    addResult('🏁 Test suite completed!');
    setIsRunning(false);
  };

  useEffect(() => {
    // Auto-run tests on mount
    runTests();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#1a1a1a', 
      color: '#00ff00',
      minHeight: '100vh',
      fontSize: '14px'
    }}>
      <h1>🔍 Galactic Genesis - System Debug Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={runTests} 
          disabled={isRunning}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: isRunning ? '#666' : '#333', 
            color: '#00ff00', 
            border: '1px solid #00ff00',
            cursor: isRunning ? 'not-allowed' : 'pointer'
          }}
        >
          {isRunning ? '🔄 Running Tests...' : '🚀 Run Tests'}
        </button>
      </div>

      <div style={{ 
        backgroundColor: '#000', 
        padding: '15px', 
        border: '1px solid #333',
        maxHeight: '70vh',
        overflowY: 'auto'
      }}>
        <h3>📋 Test Results:</h3>
        {testResults.length === 0 && !isRunning && (
          <p style={{ color: '#666' }}>No tests run yet. Click "Run Tests" to start.</p>
        )}
        {testResults.map((result, index) => (
          <div key={index} style={{ 
            marginBottom: '5px',
            color: result.includes('❌') ? '#ff4444' : 
                   result.includes('⚠️') ? '#ffaa00' : '#00ff00'
          }}>
            {result}
          </div>
        ))}
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <a href="/" style={{ color: '#00ffff' }}>← Back to Main Game</a>
        <br />
        <a href="/?minimal=true" style={{ color: '#00ffff' }}>→ Minimal Test</a>
        <br />
        <a href="/?debug=true" style={{ color: '#00ffff' }}>→ Basic Debug</a>
      </div>
    </div>
  );
};

export default DebugTest;
