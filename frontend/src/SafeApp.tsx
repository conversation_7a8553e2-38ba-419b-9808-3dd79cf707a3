import { useEffect, useState } from 'react';
import { useGameStore } from './store/gameStore';
import GameHUD from './components/GameHUD';
import SafeGalaxy3D from './components/SafeGalaxy3D';
import FleetList from './components/FleetList';
import OrderForm from './components/OrderForm';
import LoadingScreen from './components/LoadingScreen';
import ErrorDisplay from './components/ErrorDisplay';
import Tutorial from './components/Tutorial';

function SafeApp() {
  const {
    initialize,
    isLoading,
    error,
    showOrderForm,
    showTutorialModal
  } = useGameStore();

  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    console.log('🚀 SafeApp: Starting initialization...');
    initialize();

    // Monitor console errors
    const originalError = console.error;
    console.error = (...args) => {
      setErrorCount(prev => prev + 1);
      originalError(...args);
    };

    return () => {
      console.error = originalError;
    };
  }, [initialize]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return <ErrorDisplay error={error} />;
  }

  return (
    <div className="h-screen w-screen bg-gray-900 starfield-bg overflow-hidden">
      {/* Error Counter */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        backgroundColor: errorCount > 10 ? '#ff4444' : errorCount > 0 ? '#ffaa00' : '#444',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '12px'
      }}>
        Errors: {errorCount}
      </div>

      {/* Game HUD */}
      <GameHUD />

      {/* Main content area */}
      <div className="flex h-full">
        {/* Left sidebar - Fleet management */}
        <div className="w-80 bg-gray-800 bg-opacity-90 backdrop-blur-sm border-r border-gray-700 overflow-y-auto">
          <FleetList />
        </div>

        {/* Center - Galaxy view */}
        <div className="flex-1 relative">
          <SafeGalaxy3D />
        </div>
      </div>

      {/* Modals */}
      {showOrderForm && <OrderForm />}
      {showTutorialModal && <Tutorial />}

      {/* Safe mode indicator */}
      <div className="absolute bottom-4 left-4 text-yellow-400 text-sm">
        🛡️ Safe Mode - Using SafeGalaxy3D (No shader errors)
      </div>
    </div>
  );
}

export default SafeApp;
