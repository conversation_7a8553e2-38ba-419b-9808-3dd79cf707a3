// Simplified API client without complex imports
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8081';

class SimpleApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const requestOptions = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    // Debug logging removed for production

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', response.status, errorText);
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  // Health check
  async getHealth() {
    return this.request('/v1/health');
  }

  // Turn management
  async getCurrentTurn() {
    return this.request('/v1/turn');
  }

  // Fleet management
  async getFleets() {
    return this.request('/v1/fleets');
  }

  async createFleet(fleetData: any) {
    return this.request('/v1/fleets', {
      method: 'POST',
      body: JSON.stringify(fleetData),
    });
  }

  async updateFleet(id: string, updates: any) {
    return this.request(`/v1/fleets/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  // Order management
  async getOrders() {
    return this.request('/v1/orders');
  }

  async getOrder(id: string) {
    return this.request(`/v1/orders/${id}`);
  }

  async submitMoveOrder(payload: { fleetId: string; toSystemId: string }, idempotencyKey?: string) {
    const url = `${this.baseUrl}/v1/orders`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    const requestBody = {
      kind: 'move',
      payload: {
        fleetId: payload.fleetId,
        toSystemId: payload.toSystemId,
      },
    };

    // Debug: console.log('Submitting move order:', { url, headers, body: requestBody });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Move order failed:', response.status, errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Move order request failed:', error);
      throw error;
    }
  }

  async submitAttackOrder(payload: { fleetId: string; targetFleetId: string }, idempotencyKey?: string) {
    const headers: Record<string, string> = {};
    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    return this.request('/v1/orders', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        kind: 'attack',
        payload,
      }),
    });
  }

  async submitResupplyOrder(payload: { fleetId: string; amount: number }, idempotencyKey?: string) {
    const headers: Record<string, string> = {};
    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    return this.request('/v1/orders', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        kind: 'resupply',
        payload,
      }),
    });
  }

  // Battle management
  async getBattles() {
    return this.request('/v1/battles');
  }

  // Systems (if available)
  async getSystems() {
    return this.request('/v1/systems');
  }

  async getSystemNeighbors(systemId: string): Promise<{ neighbors: Array<{id: string, name: string}> }> {
    return this.request(`/v1/systems/${systemId}/neighbors`);
  }

  // Colony Management
  async getColonies(empireId?: string, systemId?: string): Promise<{ colonies: Array<any> }> {
    const params = new URLSearchParams();
    if (empireId) params.append('empire_id', empireId);
    if (systemId) params.append('system_id', systemId);

    const queryString = params.toString();
    return this.request(`/v1/colonies${queryString ? '?' + queryString : ''}`);
  }

  async createColony(data: { system_id: string; empire_id: string; name: string; planet_type?: string }): Promise<{ colony: any }> {
    return this.request('/v1/colonies', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getColony(colonyId: string): Promise<{ colony: any }> {
    return this.request(`/v1/colonies/${colonyId}`);
  }

  async getColonyImprovements(colonyId: string): Promise<{ improvements: Array<any> }> {
    return this.request(`/v1/colonies/${colonyId}/improvements`);
  }

  async buildImprovement(colonyId: string, data: { type: string; level?: number }): Promise<{ improvement: any }> {
    return this.request(`/v1/colonies/${colonyId}/improvements`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getEmpireResources(empireId: string): Promise<{ resources: Array<any> }> {
    return this.request(`/v1/empires/${empireId}/resources`);
  }

  async getPlanetTypes(): Promise<{ planet_types: Array<any> }> {
    return this.request('/v1/planet-types');
  }

  async getImprovementTypes(): Promise<{ improvement_types: Array<any> }> {
    return this.request('/v1/improvement-types');
  }

  // Technology System
  async getTechTree(empireId: string): Promise<{ tech_tree: Array<any> }> {
    return this.request(`/v1/empires/${empireId}/tech-tree`);
  }

  async getResearchQueue(empireId: string): Promise<{ research_queue: Array<any> }> {
    return this.request(`/v1/empires/${empireId}/research-queue`);
  }

  async addToResearchQueue(empireId: string, data: { tech_id: string; priority?: number }): Promise<{ research_item: any }> {
    return this.request(`/v1/empires/${empireId}/research-queue`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async removeFromResearchQueue(empireId: string, queueId: string): Promise<{ deleted: any }> {
    return this.request(`/v1/empires/${empireId}/research-queue/${queueId}`, {
      method: 'DELETE',
    });
  }

  async investResearch(empireId: string, data: { tech_id: string; research_points: number }): Promise<{ progress: any; unlocked: boolean; overflow: number }> {
    return this.request(`/v1/empires/${empireId}/research`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getTechBranches(): Promise<{ branches: Array<any> }> {
    return this.request('/v1/tech-branches');
  }

  async getTechByBranch(branch: string): Promise<{ technologies: Array<any> }> {
    return this.request(`/v1/tech-branches/${branch}`);
  }

  // Technology Effects
  async getAvailableImprovements(empireId: string): Promise<{ improvement_types: Array<any> }> {
    return this.request(`/v1/empires/${empireId}/improvement-types`);
  }

  async getTechBonuses(empireId: string): Promise<{ bonuses: Array<any> }> {
    return this.request(`/v1/empires/${empireId}/tech-bonuses`);
  }

  // Market & Trade System
  async getMarketPrices(): Promise<{ prices: Array<any> }> {
    return this.request('/v1/market/prices');
  }

  async getMarketOrders(params?: { empire_id?: string; resource_type?: string; order_type?: string; status?: string; limit?: string }): Promise<{ orders: Array<any> }> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });
    }
    const queryString = queryParams.toString();
    return this.request(`/v1/market/orders${queryString ? '?' + queryString : ''}`);
  }

  async createMarketOrder(order: { empire_id: string; order_type: 'buy' | 'sell'; resource_type: string; quantity: number; price_per_unit: number; expires_hours?: number }): Promise<{ order: any }> {
    return this.request('/v1/market/orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order)
    });
  }

  async cancelMarketOrder(orderId: string): Promise<{ cancelled: boolean; refunded: number }> {
    return this.request(`/v1/market/orders/${orderId}`, {
      method: 'DELETE'
    });
  }

  async getTradeTransactions(params?: { empire_id?: string; resource_type?: string; limit?: string }): Promise<{ transactions: Array<any> }> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });
    }
    const queryString = queryParams.toString();
    return this.request(`/v1/market/transactions${queryString ? '?' + queryString : ''}`);
  }

  async executeTradeMatching(): Promise<{ executed_trades: number; transactions: Array<any> }> {
    return this.request('/v1/market/execute-trades', {
      method: 'POST'
    });
  }

  async getTradeRoutes(empireId: string): Promise<{ trade_routes: Array<any> }> {
    return this.request(`/v1/empires/${empireId}/trade-routes`);
  }

  async createTradeRoute(empireId: string, route: { origin_system_id: string; destination_system_id: string; resource_type: string; quantity_per_turn: number }): Promise<{ trade_route: any }> {
    return this.request(`/v1/empires/${empireId}/trade-routes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(route)
    });
  }

  async getMarketStats(): Promise<{ prices: Array<any>; weekly_volume: Array<any>; order_stats: Array<any> }> {
    return this.request('/v1/market/stats');
  }

  // Materials System API
  async getMaterials(): Promise<{ materials: Array<any> }> {
    return this.request('/v1/materials');
  }

  async getMaterial(id: string): Promise<{ material: any }> {
    return this.request(`/v1/materials/${id}`);
  }

  async getMaterialDeposits(params?: { body_type?: string; body_id?: string; material_id?: string; discovered?: boolean }): Promise<{ deposits: Array<any> }> {
    const queryString = params ? '?' + new URLSearchParams(
      Object.entries(params).filter(([_, v]) => v !== undefined).map(([k, v]) => [k, String(v)])
    ).toString() : '';
    return this.request(`/v1/material-deposits${queryString}`);
  }

  async getStations(params?: { system_id?: string; empire_id?: string; station_type?: string; operational?: boolean }): Promise<{ stations: Array<any> }> {
    const queryString = params ? '?' + new URLSearchParams(
      Object.entries(params).filter(([_, v]) => v !== undefined).map(([k, v]) => [k, String(v)])
    ).toString() : '';
    return this.request(`/v1/stations${queryString}`);
  }

  async getStation(id: string): Promise<{ station: any }> {
    return this.request(`/v1/stations/${id}`);
  }

  async getStationMaterials(stationId: string): Promise<{ materials: Array<any> }> {
    return this.request(`/v1/stations/${stationId}/materials`);
  }

  async getMiningOperations(params?: { empire_id?: string; status?: string }): Promise<{ operations: Array<any> }> {
    const queryString = params ? '?' + new URLSearchParams(
      Object.entries(params).filter(([_, v]) => v !== undefined).map(([k, v]) => [k, String(v)])
    ).toString() : '';
    return this.request(`/v1/mining-operations${queryString}`);
  }

  async getStationTypes(): Promise<{ station_types: Array<any> }> {
    return this.request('/v1/station-types');
  }

  async getProcessingRecipes(): Promise<{ recipes: Array<any> }> {
    return this.request('/v1/processing-recipes');
  }

  async createSurveyOrder(order: {
    empire_id: string;
    target_body_type: 'planet' | 'moon' | 'asteroid';
    target_body_id: number;
    system_id: string;
    survey_type: 'basic' | 'detailed' | 'deep_scan';
    fleet_id?: string;
  }): Promise<{ survey_order: any }> {
    return this.request('/v1/survey-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order)
    });
  }

  async createStationConstructionOrder(order: {
    empire_id: string;
    system_id: string;
    station_type_id: string;
    orbiting_body_type?: string;
    orbiting_body_id?: number;
    orbital_distance_km?: number;
  }): Promise<{ construction_order: any }> {
    return this.request('/v1/station-construction-orders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(order)
    });
  }
}

// Create singleton instance
export const simpleApiClient = new SimpleApiClient();
export default simpleApiClient;
