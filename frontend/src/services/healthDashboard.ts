/**
 * Health Dashboard Service
 * Connects to the health dashboard API for system monitoring
 */

export interface HealthStatus {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime?: number;
  error?: string;
}

export interface SystemHealth {
  timestamp: Date;
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: HealthStatus[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    unknown: number;
  };
}

class HealthDashboardService {
  private baseUrl: string;
  private listeners: Set<(health: SystemHealth) => void> = new Set();
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.baseUrl = 'http://localhost:8086';
  }

  /**
   * Get current system health status
   */
  async getSystemHealth(): Promise<SystemHealth> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`);
      if (!response.ok) {
        throw new Error(`Health API error: ${response.status}`);
      }
      const data = await response.json();
      return this.transformHealthData(data);
    } catch (error) {
      console.error('Failed to fetch system health:', error);
      return this.getDefaultHealth();
    }
  }

  /**
   * Get health status for a specific service
   */
  async getServiceHealth(serviceName: string): Promise<HealthStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health/${serviceName}`);
      if (!response.ok) {
        throw new Error(`Service health API error: ${response.status}`);
      }
      const data = await response.json();
      return {
        service: serviceName,
        status: data.status || 'unknown',
        lastCheck: new Date(data.lastCheck || Date.now()),
        responseTime: data.responseTime,
        error: data.error
      };
    } catch (error) {
      console.error(`Failed to fetch health for ${serviceName}:`, error);
      return {
        service: serviceName,
        status: 'unknown',
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Subscribe to real-time health updates
   */
  subscribe(callback: (health: SystemHealth) => void): () => void {
    this.listeners.add(callback);
    
    // Start WebSocket connection if not already connected
    if (!this.ws) {
      this.connectWebSocket();
    }

    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
      if (this.listeners.size === 0) {
        this.disconnect();
      }
    };
  }

  /**
   * Connect to WebSocket for real-time updates
   */
  private connectWebSocket(): void {
    try {
      const wsUrl = this.baseUrl.replace('http', 'ws') + '/ws/health';
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('Health dashboard WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const health = this.transformHealthData(data);
          this.notifyListeners(health);
        } catch (error) {
          console.error('Failed to parse health WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('Health dashboard WebSocket disconnected');
        this.ws = null;
        this.scheduleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('Health dashboard WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect to health dashboard WebSocket:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Schedule WebSocket reconnection
   */
  private scheduleReconnect(): void {
    if (this.listeners.size === 0) return;
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        if (this.listeners.size > 0 && !this.ws) {
          this.connectWebSocket();
        }
      }, delay);
    }
  }

  /**
   * Transform raw health data to SystemHealth format
   */
  private transformHealthData(data: any): SystemHealth {
    const services: HealthStatus[] = [];
    
    if (data.services && Array.isArray(data.services)) {
      services.push(...data.services.map((service: any) => ({
        service: service.name || service.service || 'unknown',
        status: service.status || 'unknown',
        lastCheck: new Date(service.lastCheck || Date.now()),
        responseTime: service.responseTime,
        error: service.error
      })));
    }

    const healthy = services.filter(s => s.status === 'healthy').length;
    const unhealthy = services.filter(s => s.status === 'unhealthy').length;
    const unknown = services.filter(s => s.status === 'unknown').length;

    let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (unhealthy > 0) {
      overall = unhealthy > healthy ? 'unhealthy' : 'degraded';
    } else if (unknown > 0) {
      overall = 'degraded';
    }

    return {
      timestamp: new Date(data.timestamp || Date.now()),
      overall,
      services,
      summary: {
        total: services.length,
        healthy,
        unhealthy,
        unknown
      }
    };
  }

  /**
   * Get default health status when API is unavailable
   */
  private getDefaultHealth(): SystemHealth {
    return {
      timestamp: new Date(),
      overall: 'unknown',
      services: [],
      summary: {
        total: 0,
        healthy: 0,
        unhealthy: 0,
        unknown: 0
      }
    };
  }

  /**
   * Notify all listeners of health updates
   */
  private notifyListeners(health: SystemHealth): void {
    this.listeners.forEach(callback => {
      try {
        callback(health);
      } catch (error) {
        console.error('Error in health update callback:', error);
      }
    });
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
  }
}

// Export singleton instance
export const healthDashboardService = new HealthDashboardService();
