// Stellar Database API Client
// Provides access to real astronomical data for the 3D galaxy map

import type { Star, Planet, StarDetail, StellarStatistics } from '../types/stellar';

// Re-export types for convenience
export type { Star, Planet, StarDetail, StellarStatistics };

class StellarApiClient {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    // Auto-detect environment and use appropriate API URL
    if (baseUrl) {
      this.baseUrl = baseUrl;
    } else if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname === 'star.omnilyzer.ai') {
        // Production environment - use fallback data (no API backend in production)
        this.baseUrl = null; // This will trigger fallback data usage
      } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
        // Local development environment - use the same hostname as the frontend
        this.baseUrl = `http://${hostname}:19081/v1`;
      } else {
        // Default fallback
        this.baseUrl = 'http://localhost:19081/v1';
      }
    } else {
      // Server-side rendering fallback
      this.baseUrl = 'http://localhost:19081/v1';
    }

    console.log('🔧 StellarAPI: Initialized with baseUrl:', this.baseUrl);
  }

  async getStarDetail(starId: number): Promise<StarDetail> {
    console.log(`🌟 StellarAPI: Fetching star detail for star_id: ${starId}`);

    if (!this.baseUrl) {
      throw new Error('StellarAPI: No base URL configured. Cannot fetch star details without database connection.');
    }

    try {
      // Create timeout promise
      const timeout = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Star detail API timeout after 30 seconds')), 30000)
      );

      const cacheBuster = Date.now();
      const url = `${this.baseUrl}/stellar/stars/${starId}?_cb=${cacheBuster}`;
      console.log(`📡 StellarAPI: Fetching star detail from: ${url}`);

      const fetchPromise = fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeout]);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ StellarAPI: Star detail loaded: ${data.name} with ${data.planets?.length || 0} planets from database`);

      // Ensure planets array exists (empty if no planets in database)
      if (!data.planets) {
        data.planets = [];
        console.log(`📊 StellarAPI: ${data.name} has no planets in database`);
      }

      return data;
    } catch (error) {
      console.error('❌ StellarAPI: Failed to fetch star detail:', error);
      throw error;
    }
  }

  async getStars(maxDistance: number = 20, limit: number = 1000): Promise<{ stars: Star[]; total: number }> {
    console.log('🌟 StellarAPI: ===== STARTING STELLAR DATA FETCH =====');
    if (typeof window !== 'undefined') {
      console.log(`🌐 StellarAPI: Browser location: ${window.location.href}`);
    }

    if (!this.baseUrl) {
      throw new Error('StellarAPI: No base URL configured. Cannot fetch stars without database connection.');
    }

    console.log(`🔗 StellarAPI: URL: ${this.baseUrl}/stellar/stars?max_distance=${maxDistance}&limit=${limit}`);

    try {
      // Create timeout promise (longer timeout)
      const timeout = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Stellar API timeout after 30 seconds')), 30000)
      );

      // Create fetch promise with cache busting
      const cacheBuster = Date.now();
      const url = `${this.baseUrl}/stellar/stars?max_distance=${maxDistance}&limit=${limit}&_cb=${cacheBuster}`;
      console.log(`📡 StellarAPI: Making fetch request to: ${url}`);

      const fetchPromise = fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeout]);

      console.log(`📡 StellarAPI: Response received! Status: ${response.status} ${response.statusText}`);
      if (response.headers && response.headers.entries) {
        console.log(`📡 StellarAPI: Response headers:`, Object.fromEntries(response.headers.entries()));
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ StellarAPI: Successfully parsed JSON response');
      console.log('📊 StellarAPI: Data structure:', {
        hasStars: !!data.stars,
        starsLength: data.stars?.length,
        hasTotal: !!data.total,
        totalValue: data.total
      });

      // Verify we got real data (should have more than 8 stars)
      if (data.stars && data.stars.length > 8) {
        console.log('🎉 StellarAPI: SUCCESS! Loaded REAL stellar database with', data.stars.length, 'stars!');
        console.log('🌟 StellarAPI: First 3 stars:', data.stars.slice(0, 3).map(s => `${s.name} (${s.distance_ly} ly)`));
      } else {
        console.warn('⚠️ StellarAPI: Only got', data.stars?.length || 0, 'stars - this might be fallback data');
      }

      return data;
    } catch (error) {
      console.error('❌ StellarAPI: FAILED to fetch real stellar data:', error);
      console.error('🔗 StellarAPI: Attempted URL:', `${this.baseUrl}/stellar/stars?max_distance=${maxDistance}&limit=${limit}`);
      console.error('🌐 StellarAPI: Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });

      throw error;
    }
  }

  async getStatistics(): Promise<StellarStatistics> {
    try {
      const response = await fetch(`${this.baseUrl}/stellar/statistics`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      throw error;
    }
  }

  // Convert astronomical coordinates to 3D Cartesian coordinates
  // This places stars in realistic 3D positions based on their actual sky coordinates and distances
  convertToCartesian(star: Star): [number, number, number] {
    const { ra_deg, dec_deg, distance_ly } = star;

    // Convert degrees to radians
    const ra = (ra_deg * Math.PI) / 180;
    const dec = (dec_deg * Math.PI) / 180;

    // Convert spherical coordinates (RA, Dec, Distance) to Cartesian (X, Y, Z)
    // Using astronomical convention: X towards vernal equinox, Y towards 90° RA, Z towards north pole
    const x = distance_ly * Math.cos(dec) * Math.cos(ra);
    const y = distance_ly * Math.cos(dec) * Math.sin(ra);
    const z = distance_ly * Math.sin(dec);

    // Triple the distances for better visualization spacing (3 light-years = 1 unit in 3D space)
    return [x * 3, y * 3, z * 3];
  }

  // Get star coordinates for 3D positioning (alias for convertToCartesian)
  getStarCoordinates(star: Star): [number, number, number] {
    return this.convertToCartesian(star);
  }

  // Get star color based on stellar data (enhanced for realism)
  getStarColor(spectralType?: string, stellarColor?: string): string {
    // Use pre-calculated stellar color if available (from database)
    if (stellarColor) return stellarColor;

    if (!spectralType) return '#FFFFFF';

    const type = spectralType.charAt(0).toUpperCase();
    const colorMap: Record<string, string> = {
      'O': '#9bb0ff', // Blue giants (very hot)
      'B': '#aabfff', // Blue-white (hot)
      'A': '#cad7ff', // White (Sirius, Vega)
      'F': '#f8f7ff', // Yellow-white (Procyon)
      'G': '#fff4ea', // Yellow (Sun-like)
      'K': '#ffd2a1', // Orange (Arcturus)
      'M': '#ffad51', // Red dwarfs (cool)
      'L': '#ff6600', // Brown dwarfs (very cool)
      'T': '#cc3300', // Cool brown dwarfs
      'Y': '#990000', // Ultra-cool brown dwarfs
      'D': '#ffffff', // White dwarfs (hot but small)
      'C': '#ff4444', // Carbon stars (red)
      'S': '#ff6666', // S-type stars (red)
      'W': '#88bbff', // Wolf-Rayet stars (very hot)
    };

    return colorMap[type] || '#ffffff';
  }

  // Get realistic star size based on luminosity, magnitude, and physical properties
  getStarSize(star: Star): number {
    const baseSize = 0.8;

    // Use absolute magnitude for most accurate sizing
    const magnitude = star.absolute_magnitude || star.mag_v || 5.0;

    // Base size from absolute magnitude (brighter = larger)
    const baseSizeFromMagnitude = Math.max(0.2, 1.5 - magnitude * 0.12);

    // Luminosity scaling (more luminous = larger visual representation)
    const luminosityScale = star.luminosity_solar ?
      Math.max(0.5, Math.min(3.0, Math.pow(star.luminosity_solar, 0.25))) : 1.0;

    // Physical radius scaling if available
    const radiusScale = star.radius_solar ?
      Math.max(0.5, Math.min(2.5, Math.sqrt(star.radius_solar))) : 1.0;

    // Distance scaling for visual clarity (closer = slightly larger for visibility)
    const distanceScale = Math.max(0.8, 6.0 / Math.max(star.distance_ly, 1));

    // Spectral type adjustment for special cases
    let spectralAdjustment = 1.0;
    if (star.spectral_type) {
      const type = star.spectral_type.charAt(0).toUpperCase();
      switch (type) {
        case 'O': spectralAdjustment = 1.4; break; // Blue giants
        case 'B': spectralAdjustment = 1.2; break; // Blue-white
        case 'A': spectralAdjustment = 1.1; break; // White (Sirius, Vega)
        case 'F': spectralAdjustment = 1.0; break; // Yellow-white
        case 'G': spectralAdjustment = 1.0; break; // Yellow (Sun-like)
        case 'K': spectralAdjustment = 0.9; break; // Orange
        case 'M': spectralAdjustment = 0.7; break; // Red dwarfs
        case 'L': spectralAdjustment = 0.5; break; // Brown dwarfs
        case 'T': spectralAdjustment = 0.4; break; // Cool brown dwarfs
        case 'D': spectralAdjustment = 0.6; break; // White dwarfs (small but bright)
      }
    }

    // Combine all factors with reasonable limits
    const finalSize = baseSizeFromMagnitude * luminosityScale * radiusScale * distanceScale * spectralAdjustment;
    return Math.max(0.3, Math.min(finalSize, 4.0));
  }

}

export const stellarApi = new StellarApiClient();
