// Import all types from game types
import type {
  Fleet,
  System,
  Order,
  Battle,
  FleetsResponse,
  OrdersResponse,
  BattlesResponse,
  TurnResponse,
  HealthResponse,
} from '../types/game';

import type {
  MoveOrderPayload,
  AttackOrderPayload,
  ResupplyOrderPayload,
} from '../types/game';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:19080';

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  // Health check
  async getHealth(): Promise<HealthResponse> {
    return this.request<HealthResponse>('/v1/health');
  }

  // Turn management
  async getCurrentTurn(): Promise<TurnResponse> {
    return this.request<TurnResponse>('/v1/turn');
  }

  // Fleet management
  async getFleets(): Promise<FleetsResponse> {
    return this.request<FleetsResponse>('/v1/fleets');
  }

  async createFleet(fleetData: Partial<Fleet>): Promise<Fleet> {
    return this.request<Fleet>('/v1/fleets', {
      method: 'POST',
      body: JSON.stringify(fleetData),
    });
  }

  async updateFleet(id: string, updates: Partial<Fleet>): Promise<Fleet> {
    return this.request<Fleet>(`/v1/fleets/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  // Order management
  async getOrders(): Promise<OrdersResponse> {
    return this.request<OrdersResponse>('/v1/orders');
  }

  async getOrder(id: string): Promise<Order> {
    return this.request<Order>(`/v1/orders/${id}`);
  }

  async submitMoveOrder(
    payload: MoveOrderPayload,
    idempotencyKey?: string
  ): Promise<{ orderId: string; target_turn: number; idemKey?: string; delta: any }> {
    const headers: Record<string, string> = {};
    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    return this.request('/v1/orders', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        kind: 'move',
        payload,
      }),
    });
  }

  async submitAttackOrder(
    payload: AttackOrderPayload,
    idempotencyKey?: string
  ): Promise<{ orderId: string; target_turn: number; idemKey?: string; delta: any }> {
    const headers: Record<string, string> = {};
    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    return this.request('/v1/orders', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        kind: 'attack',
        payload,
      }),
    });
  }

  async submitResupplyOrder(
    payload: ResupplyOrderPayload,
    idempotencyKey?: string
  ): Promise<{ orderId: string; target_turn: number; idemKey?: string; delta: any }> {
    const headers: Record<string, string> = {};
    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    return this.request('/v1/orders', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        kind: 'resupply',
        payload,
      }),
    });
  }

  // Battle management
  async getBattles(): Promise<BattlesResponse> {
    return this.request<BattlesResponse>('/v1/battles');
  }

  // Systems (if available)
  async getSystems(): Promise<{ systems: System[] }> {
    return this.request<{ systems: System[] }>('/v1/systems');
  }

  async getSystemNeighbors(systemId: string): Promise<{ neighbors: System[] }> {
    return this.request<{ neighbors: System[] }>(`/v1/systems/${systemId}/neighbors`);
  }
}

// Create singleton instance
export const apiClient = new ApiClient();
export default apiClient;
