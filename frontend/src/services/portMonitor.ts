/**
 * Port Monitor Service
 * Connects to the health dashboard's port monitoring API
 */

export interface PortInfo {
  port: number;
  protocol: 'tcp' | 'udp';
  state: 'LISTEN' | 'ESTABLISHED' | 'CLOSED' | 'TIME_WAIT' | 'UNKNOWN';
  pid?: number;
  processName?: string;
  service?: string;
  description?: string;
  isExpected: boolean;
  lastChecked: Date;
}

export interface PortScanResult {
  timestamp: Date;
  ports: PortInfo[];
  summary: {
    total: number;
    listening: number;
    expected: number;
    unexpected: number;
    missing: number;
  };
}

export interface ExpectedPort {
  port: number;
  service: string;
  description: string;
  protocol: string;
}

class PortMonitorService {
  private baseUrl: string;
  private wsUrl: string;
  private ws: WebSocket | null = null;
  private listeners: Set<(data: PortScanResult) => void> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  constructor() {
    // Auto-detect health dashboard URL
    const hostname = window.location.hostname;
    this.baseUrl = `http://${hostname}:8086/api`;
    this.wsUrl = `ws://${hostname}:8086/ws`;
  }

  /**
   * Get current port scan data
   */
  async getPortScan(): Promise<PortScanResult> {
    try {
      const response = await fetch(`${this.baseUrl}/ports`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch port scan:', error);
      throw error;
    }
  }

  /**
   * Get expected ports configuration
   */
  async getExpectedPorts(): Promise<ExpectedPort[]> {
    try {
      const response = await fetch(`${this.baseUrl}/ports/expected`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch expected ports:', error);
      throw error;
    }
  }

  /**
   * Check if a specific port is available
   */
  async checkPort(port: number): Promise<{ port: number; available: boolean }> {
    try {
      const response = await fetch(`${this.baseUrl}/ports/${port}/check`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Failed to check port ${port}:`, error);
      throw error;
    }
  }

  /**
   * Kill process on a specific port
   */
  async killPort(port: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/ports/${port}/kill`, {
        method: 'POST'
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}`);
      }
      
      return result;
    } catch (error) {
      console.error(`Failed to kill port ${port}:`, error);
      throw error;
    }
  }

  /**
   * Find a free port in the specified range
   */
  async findFreePort(startPort: number = 3000, maxAttempts: number = 50): Promise<{ port: number; available: boolean } | null> {
    try {
      const response = await fetch(`${this.baseUrl}/ports/find-free?start=${startPort}&max=${maxAttempts}`);
      
      if (response.status === 404) {
        return null; // No free port found
      }
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to find free port:', error);
      throw error;
    }
  }

  /**
   * Subscribe to real-time port updates
   */
  subscribe(callback: (data: PortScanResult) => void): () => void {
    this.listeners.add(callback);
    
    // Connect WebSocket if not already connected
    if (!this.ws || this.ws.readyState === WebSocket.CLOSED) {
      this.connectWebSocket();
    }
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
      
      // Close WebSocket if no more listeners
      if (this.listeners.size === 0 && this.ws) {
        this.ws.close();
        this.ws = null;
      }
    };
  }

  /**
   * Connect to WebSocket for real-time updates
   */
  private connectWebSocket(): void {
    try {
      this.ws = new WebSocket(this.wsUrl);
      
      this.ws.onopen = () => {
        console.log('🔌 Port monitor WebSocket connected');
        this.reconnectAttempts = 0;
      };
      
      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          
          if (message.type === 'port-scan' && message.data) {
            // Notify all listeners
            this.listeners.forEach(callback => {
              try {
                callback(message.data);
              } catch (error) {
                console.error('Error in port monitor callback:', error);
              }
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('🔌 Port monitor WebSocket disconnected');
        this.ws = null;
        
        // Attempt to reconnect if we have listeners
        if (this.listeners.size > 0 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          console.log(`🔄 Attempting to reconnect port monitor WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
          
          setTimeout(() => {
            this.connectWebSocket();
          }, this.reconnectDelay);
        }
      };
      
      this.ws.onerror = (error) => {
        console.error('🔌 Port monitor WebSocket error:', error);
      };
      
    } catch (error) {
      console.error('Failed to connect port monitor WebSocket:', error);
    }
  }

  /**
   * Check if the port monitor service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/ports`, { 
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Disconnect WebSocket and cleanup
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
  }
}

// Export singleton instance
export const portMonitorService = new PortMonitorService();
