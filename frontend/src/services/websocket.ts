import { io, Socket } from 'socket.io-client';
import type {
  WebSocketEvent,
  FleetMovedEvent,
  BattleStartedEvent,
  BattleResolvedEvent,
  OrderAppliedEvent,
  TurnTickEvent,
} from '../types/game';

const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8081';

export type GameEventHandler = (event: WebSocketEvent) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private eventHandlers: Map<string, GameEventHandler[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3; // Reduced from 5
  private reconnectDelay = 1000;
  private isDisabled = false; // Flag to disable WebSocket entirely
  private isConnecting = false; // Flag to prevent multiple connection attempts

  connect(): void {
    if (this.socket?.connected || this.isDisabled || this.isConnecting) {
      console.log('🔄 WebSocket already connected/connecting/disabled, skipping...');
      return;
    }

    this.isConnecting = true;
    console.log('🔌 Connecting to WebSocket:', WS_URL);
    
    this.socket = io(WS_URL, {
      path: '/v1/stream',
      transports: ['websocket'],
      upgrade: false,
      timeout: 5000, // 5 second timeout
      forceNew: true,
    });

    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected successfully');
      this.reconnectAttempts = 0;
      this.isConnecting = false;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket disconnected:', reason);
      this.isConnecting = false;
      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.warn('⚠️ WebSocket connection error:', error);
      this.isConnecting = false;
      this.handleReconnect();
    });

    // Listen for game events
    this.socket.on('fleet.moved', (data) => {
      this.handleEvent({ type: 'fleet.moved', payload: data });
    });

    this.socket.on('battle.started', (data) => {
      this.handleEvent({ type: 'battle.started', payload: data });
    });

    this.socket.on('battle.resolved', (data) => {
      this.handleEvent({ type: 'battle.resolved', payload: data });
    });

    this.socket.on('order.applied', (data) => {
      this.handleEvent({ type: 'order.applied', payload: data });
    });

    this.socket.on('turn.tick', (data) => {
      this.handleEvent({ type: 'turn.tick', payload: data });
    });

    // Generic event listener for any other events
    this.socket.onAny((eventName, data) => {
      if (!['connect', 'disconnect', 'connect_error'].includes(eventName)) {
        this.handleEvent({ type: eventName, payload: data });
      }
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  private handleReconnect(): void {
    if (this.isDisabled) {
      return; // Don't reconnect if disabled
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      console.log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

      setTimeout(() => {
        if (!this.isDisabled) { // Check again before reconnecting
          this.connect();
        }
      }, delay);
    } else {
      console.warn('🔇 Max reconnection attempts reached - disabling WebSocket');
      this.isDisabled = true;
      this.isConnecting = false;
      // Emit a fake connection event to allow the app to proceed
      this.handleEvent({ type: 'websocket.disabled', payload: { reason: 'max_reconnect_attempts' } });
    }
  }

  private handleEvent(event: WebSocketEvent): void {
    console.log('WebSocket event received:', event);
    
    const handlers = this.eventHandlers.get(event.type) || [];
    handlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error handling WebSocket event:', error);
      }
    });

    // Also call generic event handlers
    const genericHandlers = this.eventHandlers.get('*') || [];
    genericHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error handling generic WebSocket event:', error);
      }
    });
  }

  // Event subscription methods
  on(eventType: string, handler: GameEventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    
    this.eventHandlers.get(eventType)!.push(handler);
    
    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  // Typed event subscription methods
  onFleetMoved(handler: (event: FleetMovedEvent) => void): () => void {
    return this.on('fleet.moved', handler as GameEventHandler);
  }

  onBattleStarted(handler: (event: BattleStartedEvent) => void): () => void {
    return this.on('battle.started', handler as GameEventHandler);
  }

  onBattleResolved(handler: (event: BattleResolvedEvent) => void): () => void {
    return this.on('battle.resolved', handler as GameEventHandler);
  }

  onOrderApplied(handler: (event: OrderAppliedEvent) => void): () => void {
    return this.on('order.applied', handler as GameEventHandler);
  }

  onTurnTick(handler: (event: TurnTickEvent) => void): () => void {
    return this.on('turn.tick', handler as GameEventHandler);
  }

  // Subscribe to all events
  onAnyEvent(handler: GameEventHandler): () => void {
    return this.on('*', handler);
  }

  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  get isDisabledOrConnected(): boolean {
    return this.isDisabled || this.isConnected;
  }

  // Method to disable WebSocket entirely (for development/fallback)
  disable(): void {
    this.isDisabled = true;
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    console.log('WebSocket service disabled');
  }
}

// Create singleton instance
export const wsService = new WebSocketService();
export default wsService;
