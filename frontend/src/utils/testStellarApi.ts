// Test utility for stellar API
import { stellarApi } from '../services/stellarApi';

export async function testStellarApi() {
  console.log('🧪 Testing Stellar API...');
  
  try {
    // Test getting stars
    const result = await stellarApi.getStars(20, 10);
    console.log('✅ Stars loaded:', result.stars.length);
    
    // Test coordinate conversion
    if (result.stars.length > 0) {
      const star = result.stars[0];
      const position = stellarApi.convertToCartesian(star);
      console.log('✅ Coordinate conversion:', star.name, '→', position);
      
      // Test star color
      const color = stellarApi.getStarColor(star.spectral_type);
      console.log('✅ Star color:', star.spectral_type, '→', color);
      
      // Test star size
      const size = stellarApi.getStarSize(star);
      console.log('✅ Star size:', star.name, '→', size);
    }
    
    // Test statistics
    const stats = await stellarApi.getStatistics();
    console.log('✅ Statistics loaded:', stats);
    
    console.log('🎉 All stellar API tests passed!');
    return true;
  } catch (error) {
    console.error('❌ Stellar API test failed:', error);
    return false;
  }
}

// Auto-run test in development (disabled to prevent console spam)
// if (import.meta.env.DEV) {
//   testStellarApi();
// }
