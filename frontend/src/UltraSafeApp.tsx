import { useEffect, useState } from 'react';
import { useGameStore } from './store/gameStore';
import GameHUD from './components/GameHUD';
import FleetList from './components/FleetList';
import OrderForm from './components/OrderForm';
import LoadingScreen from './components/LoadingScreen';
import ErrorDisplay from './components/ErrorDisplay';
import Tutorial from './components/Tutorial';

function UltraSafeApp() {
  const {
    initialize,
    isLoading,
    error,
    showOrderForm,
    showTutorialModal,
    stellarData,
    fleets,
    selectedSystemId
  } = useGameStore();

  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    console.log('🚀 UltraSafeApp: Starting initialization...');
    initialize();

    // Monitor console errors
    const originalError = console.error;
    console.error = (...args) => {
      setErrorCount(prev => prev + 1);
      originalError(...args);
    };

    return () => {
      console.error = originalError;
    };
  }, [initialize]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return <ErrorDisplay error={error} />;
  }

  // Safe data processing
  const starCount = Array.isArray(stellarData) ? stellarData.length : 0;
  const fleetCount = Array.isArray(fleets) ? fleets.length : 0;

  return (
    <div className="h-screen w-screen bg-gray-900 starfield-bg overflow-hidden">
      {/* Error Counter */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        backgroundColor: errorCount > 10 ? '#ff4444' : errorCount > 0 ? '#ffaa00' : '#444',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '12px'
      }}>
        Errors: {errorCount}
      </div>

      {/* Game HUD */}
      <GameHUD />

      {/* Main content area */}
      <div className="flex h-full">
        {/* Left sidebar - Fleet management */}
        <div className="w-80 bg-gray-800 bg-opacity-90 backdrop-blur-sm border-r border-gray-700 overflow-y-auto">
          <FleetList />
        </div>

        {/* Center - Text-based galaxy view (NO Three.js) */}
        <div className="flex-1 relative bg-gray-900 p-6 overflow-y-auto">
          <div className="text-white">
            <h2 className="text-2xl font-bold mb-6 text-cyan-400">🌌 Galaxy Overview</h2>
            
            {/* Galaxy stats */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="glass-panel p-4">
                <h3 className="text-lg font-semibold text-yellow-400">⭐ Star Systems</h3>
                <p className="text-2xl font-bold">{starCount}</p>
                <p className="text-sm text-gray-400">Systems discovered</p>
              </div>
              
              <div className="glass-panel p-4">
                <h3 className="text-lg font-semibold text-green-400">🚀 Active Fleets</h3>
                <p className="text-2xl font-bold">{fleetCount}</p>
                <p className="text-sm text-gray-400">Fleets deployed</p>
              </div>
            </div>

            {/* Selected system info */}
            {selectedSystemId && (
              <div className="glass-panel p-4 mb-6">
                <h3 className="text-lg font-semibold text-cyan-400">🎯 Selected System</h3>
                <p className="text-xl">{selectedSystemId}</p>
                <p className="text-sm text-gray-400">System ID</p>
              </div>
            )}

            {/* Star systems list */}
            <div className="glass-panel p-4">
              <h3 className="text-lg font-semibold mb-4 text-purple-400">📋 Star Systems</h3>
              <div className="max-h-96 overflow-y-auto">
                {Array.isArray(stellarData) && stellarData.slice(0, 20).map((star, index) => (
                  <div 
                    key={star?.star_id || index}
                    className="border-b border-gray-700 py-2 hover:bg-gray-800 cursor-pointer"
                    onClick={() => star?.star_id && console.log('Selected:', star.star_id)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="font-medium">{star?.name || `Star ${index + 1}`}</span>
                        <span className="ml-2 text-sm text-gray-400">
                          ({star?.spectral_type || 'Unknown'})
                        </span>
                      </div>
                      <div className="text-sm text-gray-400">
                        {star?.distance_ly ? `${star.distance_ly.toFixed(1)} ly` : 'Unknown distance'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-6 p-4 bg-blue-900 bg-opacity-50 rounded">
              <h4 className="font-semibold text-blue-300 mb-2">🛡️ Ultra-Safe Mode</h4>
              <p className="text-sm text-gray-300">
                This mode completely avoids Three.js rendering to prevent WebGL shader errors. 
                All game functionality is available through the text-based interface.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {showOrderForm && <OrderForm />}
      {showTutorialModal && <Tutorial />}

      {/* Ultra-safe mode indicator */}
      <div className="absolute bottom-4 left-4 text-green-400 text-sm">
        🛡️ Ultra-Safe Mode - No Three.js/WebGL (Zero shader errors)
      </div>
    </div>
  );
}

export default UltraSafeApp;
