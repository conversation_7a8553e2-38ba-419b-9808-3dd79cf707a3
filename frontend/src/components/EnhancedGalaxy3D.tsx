import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Stars, Html } from '@react-three/drei';
import * as THREE from 'three';
import { useGameStore } from '../store/gameStore';
import { stellarApi } from '../services/stellarApi';
import type { Star } from '../types/stellar';

interface StarSystemProps {
  star: Star;
  position: [number, number, number];
  isSelected: boolean;
  hasFleets: boolean;
  onClick: () => void;
  onHover: (star: Star | null) => void;
}

const EnhancedStarSystem: React.FC<StarSystemProps> = ({ 
  star, position, isSelected, hasFleets, onClick, onHover 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const glowRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    try {
      if (meshRef.current) {
        meshRef.current.rotation.y += 0.01;
      }
      if (glowRef.current) {
        glowRef.current.rotation.z += 0.005;
        if (isSelected && glowRef.current.scale && typeof glowRef.current.scale.setScalar === 'function') {
          glowRef.current.scale.setScalar(1 + Math.sin(state.clock.elapsedTime * 3) * 0.1);
        }
      }
    } catch (error) {
      // Silently ignore animation errors
    }
  });

  const starColor = stellarApi.getStarColor(star.spectral_type);
  const starSize = stellarApi.getStarSize(star) * 1.5; // Make stars 50% larger

  return (
    <group position={position}>
      {/* Main star - larger and more visible */}
      <mesh 
        ref={meshRef}
        onClick={onClick}
        onPointerEnter={() => {
          setHovered(true);
          onHover(star);
        }}
        onPointerLeave={() => {
          setHovered(false);
          onHover(null);
        }}
      >
        <sphereGeometry args={[starSize, 32, 32]} />
        <meshStandardMaterial
          color={starColor}
          emissive={starColor}
          emissiveIntensity={hovered ? 0.6 : 0.3}
        />
      </mesh>

      {/* Enhanced glow effect */}
      <mesh ref={glowRef} scale={hovered ? 3 : 2}>
        <sphereGeometry args={[starSize, 16, 16]} />
        <meshBasicMaterial
          color={starColor}
          transparent
          opacity={isSelected ? 0.6 : hovered ? 0.4 : 0.2}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Selection ring */}
      {(isSelected || hovered) && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[starSize * 2, starSize * 2.5, 32]} />
          <meshBasicMaterial
            color={isSelected ? "#00FFFF" : "#FFFFFF"}
            transparent
            opacity={isSelected ? 0.8 : 0.5}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}

      {/* Enhanced planets - more visible */}
      {star.planet_count > 0 && (
        <group>
          {Array.from({ length: Math.min(star.planet_count, 8) }).map((_, i) => {
            const angle = (i / star.planet_count) * Math.PI * 2;
            const radius = starSize + 1 + (i * 0.3);
            const x = Math.cos(angle) * radius;
            const z = Math.sin(angle) * radius;
            
            return (
              <mesh key={i} position={[x, 0, z]}>
                <sphereGeometry args={[0.15, 8, 8]} />
                <meshStandardMaterial 
                  color={i % 3 === 0 ? "#4A90E2" : i % 3 === 1 ? "#8B4513" : "#228B22"} 
                  emissive={i % 3 === 0 ? "#1E3A8A" : i % 3 === 1 ? "#451A03" : "#14532D"}
                  emissiveIntensity={0.2}
                />
              </mesh>
            );
          })}
        </group>
      )}

      {/* Fleet indicator - more prominent */}
      {hasFleets && (
        <mesh position={[0, starSize + 1.5, 0]}>
          <boxGeometry args={[0.4, 0.4, 0.4]} />
          <meshStandardMaterial color="#00FF00" emissive="#00FF00" emissiveIntensity={0.7} />
        </mesh>
      )}

      {/* Simple system name label - only name */}
      <Html distanceFactor={25} position={[0, starSize + 2.5, 0]}>
        <div className={`text-center pointer-events-none ${isSelected ? 'text-cyan-400' : hovered ? 'text-yellow-300' : 'text-white'}`}>
          <div className="text-sm font-bold drop-shadow-lg bg-black bg-opacity-50 px-2 py-1 rounded">
            {star.name}
          </div>
        </div>
      </Html>
    </group>
  );
};

// Hover overlay component
const SystemHoverOverlay: React.FC<{ star: Star | null; mousePosition: { x: number; y: number } }> = ({ 
  star, mousePosition 
}) => {
  if (!star) return null;

  return (
    <div 
      className="fixed z-50 pointer-events-none bg-gray-900 border border-cyan-400 rounded-lg p-4 shadow-2xl max-w-sm"
      style={{
        left: mousePosition.x + 20,
        top: mousePosition.y - 100,
        transform: mousePosition.x > window.innerWidth - 300 ? 'translateX(-100%)' : 'none'
      }}
    >
      <div className="text-cyan-400 font-bold text-lg mb-2">{star.name}</div>
      
      <div className="space-y-1 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-300">Type:</span>
          <span className="text-yellow-400">{star.spectral_type}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-300">Distance:</span>
          <span className="text-blue-400">{star.distance_ly.toFixed(1)} ly</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-300">Mass:</span>
          <span className="text-orange-400">{(star.mass_solar || 0).toFixed(2)} M☉</span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-300">Radius:</span>
          <span className="text-red-400">{(star.radius_solar || 0).toFixed(2)} R☉</span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-300">Temperature:</span>
          <span className="text-purple-400">{(star.teff_k || 0).toFixed(0)} K</span>
        </div>
        
        {star.planet_count > 0 && (
          <div className="flex justify-between">
            <span className="text-gray-300">Planets:</span>
            <span className="text-green-400">{star.planet_count}</span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span className="text-gray-300">Coordinates:</span>
          <span className="text-gray-400 text-xs">
            ({((star as any).x || 0).toFixed(1)}, {((star as any).y || 0).toFixed(1)}, {((star as any).z || 0).toFixed(1)})
          </span>
        </div>
      </div>
    </div>
  );
};

// Rotation controls component
const RotationControls: React.FC<{
  isRotating: boolean;
  onToggleRotation: () => void;
  rotationSpeed: number;
  onSpeedChange: (speed: number) => void;
  onResetCamera: () => void;
}> = ({ isRotating, onToggleRotation, rotationSpeed, onSpeedChange, onResetCamera }) => {
  return (
    <div className="absolute top-4 right-4 bg-gray-900 border border-gray-700 rounded-lg p-3 space-y-2">
      <div className="text-white text-sm font-semibold mb-2">Camera Controls</div>

      <button
        onClick={onToggleRotation}
        className={`w-full px-3 py-2 rounded text-sm font-medium transition-colors ${
          isRotating
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-green-600 hover:bg-green-700 text-white'
        }`}
      >
        {isRotating ? '⏸️ Stop Rotation' : '▶️ Start Rotation'}
      </button>

      <button
        onClick={onResetCamera}
        className="w-full px-3 py-2 rounded text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-colors"
      >
        🌌 Center Galaxy
      </button>

      <button
        onClick={() => {
          window.console.log('🔍 DEBUG: Current stars:', (window as any).debugStars);
          window.console.log('🔍 DEBUG: Last focused:', (window as any).lastFocusedStar);
        }}
        className="w-full px-3 py-2 rounded text-sm font-medium bg-purple-600 hover:bg-purple-700 text-white transition-colors"
      >
        🔍 Debug Info
      </button>

      <div className="space-y-1">
        <label className="text-xs text-gray-400">Speed: {rotationSpeed.toFixed(1)}x</label>
        <input
          type="range"
          min="0.1"
          max="2.0"
          step="0.1"
          value={rotationSpeed}
          onChange={(e) => onSpeedChange(parseFloat(e.target.value))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>
    </div>
  );
};

const EnhancedGalaxy3D: React.FC = () => {
  const [systemConnections, setSystemConnections] = useState<Map<string, string[]>>(new Map());
  const [stellarData, setStellarData] = useState<Star[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hoveredStar, setHoveredStar] = useState<Star | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isRotating, setIsRotating] = useState(true);
  const [rotationSpeed, setRotationSpeed] = useState(0.5);
  const controlsRef = useRef<any>(null);

  const { fleets, selectedSystemId, selectSystem } = useGameStore();

  // Focus camera on a specific star
  const focusOnStar = (star: any) => {
    window.console.log(`🎯 FOCUS ATTEMPT: ${star.name}`);

    if (controlsRef.current) {
      const [x, y, z] = star.position3D;
      window.console.log(`🎯 Focusing on ${star.name} at position:`, [x, y, z]);

      // Force the target to the star position
      controlsRef.current.target.set(x, y, z);

      // Position camera closer to the star for better view
      const distance = 15;
      controlsRef.current.object.position.set(x + distance, y + distance * 0.7, z + distance);

      // Force update
      controlsRef.current.update();

      // Log the actual camera state after update
      window.console.log('📷 Camera target after focus:', controlsRef.current.target);
      window.console.log('📷 Camera position after focus:', controlsRef.current.object.position);

      // Store debug info globally
      (window as any).lastFocusedStar = star;
    } else {
      window.console.error('❌ controlsRef.current is null!');
    }
  };

  // Reset camera to default position with galaxy center view
  const resetCamera = () => {
    if (controlsRef.current) {
      // Calculate the center of all stars for better galaxy view
      if (displayStars.length > 0) {
        const centerX = displayStars.reduce((sum, star) => sum + star.position3D[0], 0) / displayStars.length;
        const centerY = displayStars.reduce((sum, star) => sum + star.position3D[1], 0) / displayStars.length;
        const centerZ = displayStars.reduce((sum, star) => sum + star.position3D[2], 0) / displayStars.length;

        // Set camera to look at the center of the galaxy
        controlsRef.current.target.set(centerX, centerY, centerZ);
        controlsRef.current.object.position.set(centerX + 50, centerY + 30, centerZ + 50);
        controlsRef.current.update();
      } else {
        // Fallback to origin if no stars
        controlsRef.current.reset();
      }
    }
  };

  // Track mouse position for hover overlay
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Load stellar data
  useEffect(() => {
    const loadStellarData = async () => {
      setIsLoading(true);
      try {
        const timeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Stellar data timeout after 5 seconds')), 5000)
        );
        const dataPromise = stellarApi.getStars(20, 100);
        const response = await Promise.race([dataPromise, timeout]) as { stars: Star[] };
        setStellarData(response.stars);
      } catch (error) {
        console.warn('⚠️ Enhanced Galaxy3D: Failed to load stellar data:', error);
        setStellarData([]);
      } finally {
        setIsLoading(false);
      }
    };
    loadStellarData();
  }, []);

  // Process display stars with proper coordinate conversion
  const displayStars = useMemo(() => {
    if (!Array.isArray(stellarData)) return [];

    const processedStars = stellarData.slice(0, 50).map((star, index) => {
      let x, y, z;

      // Check if star already has Cartesian coordinates
      if (typeof (star as any).x === 'number' && !isNaN((star as any).x) &&
          typeof (star as any).y === 'number' && !isNaN((star as any).y) &&
          typeof (star as any).z === 'number' && !isNaN((star as any).z)) {
        x = (star as any).x;
        y = (star as any).y;
        z = (star as any).z;
      }
      // Convert from astronomical coordinates (RA, Dec, Distance)
      else if (typeof star.ra_deg === 'number' && typeof star.dec_deg === 'number' && typeof star.distance_ly === 'number') {
        const ra = star.ra_deg * (Math.PI / 180); // Convert to radians
        const dec = star.dec_deg * (Math.PI / 180); // Convert to radians
        let distance = star.distance_ly;

        // Special handling for Sol (distance = 0) - place it at a reasonable position
        if (distance === 0 && star.name === 'Sol') {
          distance = 0.1; // Very small distance to keep Sol near center but not at origin
        }

        // Convert spherical to Cartesian coordinates
        x = distance * Math.cos(dec) * Math.cos(ra);
        y = distance * Math.cos(dec) * Math.sin(ra);
        z = distance * Math.sin(dec);
      }
      // Fallback: generate spread-out positions
      else {
        const angle = (index / stellarData.length) * Math.PI * 2;
        const radius = 5 + (index % 10) * 2;
        x = Math.cos(angle) * radius;
        y = (Math.random() - 0.5) * 10;
        z = Math.sin(angle) * radius;
      }

      return {
        ...star,
        // Add the converted coordinates to the star object
        x, y, z,
        position3D: [x * 2, y * 2, z * 2] as [number, number, number] // Scale by 2 for better visibility
      };
    });

    // Force console output even in production
    window.console.log('🌌 Enhanced Galaxy: Processed', processedStars.length, 'stars');
    window.console.log('📍 Sample positions:', processedStars.slice(0, 5).map(s => ({
      name: s.name,
      pos: s.position3D,
      coords: { x: s.x, y: s.y, z: s.z },
      original: { ra: s.ra_deg, dec: s.dec_deg, dist: s.distance_ly }
    })));

    // Check if all stars are at origin (which would cause the Sol-centric issue)
    const starsAtOrigin = processedStars.filter(s =>
      Math.abs(s.position3D[0]) < 0.1 &&
      Math.abs(s.position3D[1]) < 0.1 &&
      Math.abs(s.position3D[2]) < 0.1
    );
    if (starsAtOrigin.length > 1) {
      window.console.warn('⚠️ Multiple stars at origin detected:', starsAtOrigin.map(s => s.name));
    }

    // Store debug info globally for inspection
    (window as any).debugStars = processedStars;

    return processedStars;
  }, [stellarData]);

  // Process fleets
  const fleetsBySystem = useMemo(() => {
    const bySystem = new Map<string, any[]>();
    if (Array.isArray(fleets)) {
      fleets.forEach(fleet => {
        if (fleet && fleet.system_id) {
          const starId = fleet.system_id.replace('sys-', '');
          const systemFleets = bySystem.get(starId) || [];
          systemFleets.push(fleet);
          bySystem.set(starId, systemFleets);
        }
      });
    }
    return bySystem;
  }, [fleets]);

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-white text-xl">Loading enhanced galaxy...</div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      <Canvas
        camera={{ position: [50, 30, 50], fov: 75 }}
        style={{ background: 'transparent' }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* Enhanced lighting */}
        <ambientLight intensity={0.4} />
        <pointLight position={[0, 0, 0]} intensity={2} color="#FFD700" />
        <pointLight position={[20, 20, 20]} intensity={0.8} color="#4A90E2" />
        
        {/* Background stars */}
        <Stars radius={100} depth={50} count={3000} factor={4} saturation={0} fade speed={0.3} />

        {/* Enhanced star systems */}
        {displayStars.map(star => (
          <EnhancedStarSystem
            key={star.star_id}
            star={star}
            position={star.position3D}
            isSelected={selectedSystemId === star.star_id.toString()}
            hasFleets={(fleetsBySystem.get(star.star_id.toString()) || []).length > 0}
            onClick={() => {
              selectSystem(star.star_id.toString());
              focusOnStar(star);
            }}
            onHover={setHoveredStar}
          />
        ))}

        {/* Camera controls with rotation toggle */}
        <OrbitControls
          ref={controlsRef}
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          autoRotate={isRotating}
          autoRotateSpeed={rotationSpeed}
          maxDistance={200}
          minDistance={5}
          dampingFactor={0.05}
          enableDamping
          makeDefault
          // No fixed target - allows free navigation
        />
      </Canvas>

      {/* Rotation controls */}
      <RotationControls
        isRotating={isRotating}
        onToggleRotation={() => setIsRotating(!isRotating)}
        rotationSpeed={rotationSpeed}
        onSpeedChange={setRotationSpeed}
        onResetCamera={resetCamera}
      />

      {/* Hover overlay */}
      <SystemHoverOverlay star={hoveredStar} mousePosition={mousePosition} />

      {/* Enhanced stats overlay - more visible */}
      <div className="absolute bottom-4 left-4 text-white z-50">
        <div className="bg-gray-900 border-2 border-cyan-400 rounded-lg p-4 shadow-2xl">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🌌 Enhanced Galaxy</h3>
          <div className="text-sm space-y-1">
            <div>Stars: {displayStars.length}</div>
            <div>Planets: {displayStars.reduce((sum, star) => sum + Number(star.planet_count || 0), 0)}</div>
            <div>Rotation: {isRotating ? 'Active' : 'Paused'}</div>
            {hoveredStar && <div className="text-yellow-400">Hovering: {hoveredStar.name}</div>}
            {selectedSystemId && (
              <div className="text-cyan-400">
                Selected: {displayStars.find(s => s.star_id.toString() === selectedSystemId)?.name || 'Unknown'}
              </div>
            )}
            <div className="text-xs text-gray-400 mt-2 border-t border-gray-600 pt-2">
              Debug: Click stars to focus • Check console (F12)
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedGalaxy3D;
