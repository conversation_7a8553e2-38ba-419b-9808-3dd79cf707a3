import React, { useRef, useMemo, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Stars } from '@react-three/drei';
import * as THREE from 'three';
import { useGameStore } from '../store/gameStore';

// Progressive feature flags
const FEATURES = {
  BASIC_STARS: true,
  STAR_ANIMATIONS: true,
  EMISSIVE_MATERIALS: true,
  HYPERLANES: false, // Disable initially
  FLEET_MARKERS: true,
  NEBULAS: false, // Disable initially
  GALACTIC_CENTER: false, // Disable initially
};

// Safe star component with proper materials
const ProgressiveStar: React.FC<{ 
  star: any; 
  position: [number, number, number];
  isSelected?: boolean;
  onClick?: () => void;
}> = ({ star, position, isSelected = false, onClick }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const glowRef = useRef<THREE.Mesh>(null);

  // Safe animation
  useFrame(() => {
    if (FEATURES.STAR_ANIMATIONS) {
      try {
        if (meshRef.current) {
          meshRef.current.rotation.y += 0.01;
        }
        if (glowRef.current && isSelected) {
          glowRef.current.rotation.z += 0.005;
        }
      } catch (error) {
        // Ignore animation errors
      }
    }
  });

  const starColor = useMemo(() => {
    const colorMap: { [key: string]: string } = {
      'O': '#9bb0ff', 'B': '#aabfff', 'A': '#cad7ff', 'F': '#f8f7ff',
      'G': '#fff4ea', 'K': '#ffd2a1', 'M': '#ffad51'
    };
    return colorMap[star?.spectral_type?.[0]] || '#ffffff';
  }, [star?.spectral_type]);

  const starSize = useMemo(() => {
    const sizeMap: { [key: string]: number } = {
      'O': 1.0, 'B': 0.8, 'A': 0.6, 'F': 0.5, 'G': 0.4, 'K': 0.3, 'M': 0.2
    };
    return sizeMap[star?.spectral_type?.[0]] || 0.4;
  }, [star?.spectral_type]);

  return (
    <group position={position} onClick={onClick}>
      {/* Main star */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[starSize, 16, 16]} />
        {FEATURES.EMISSIVE_MATERIALS ? (
          <meshStandardMaterial
            color={starColor}
            emissive={starColor}
            emissiveIntensity={0.3}
          />
        ) : (
          <meshBasicMaterial color={starColor} />
        )}
      </mesh>
      
      {/* Glow effect for selected stars */}
      {isSelected && (
        <mesh ref={glowRef} scale={2}>
          <sphereGeometry args={[starSize, 8, 8]} />
          <meshBasicMaterial
            color={starColor}
            transparent
            opacity={0.2}
          />
        </mesh>
      )}
      
      {/* Selection ring */}
      {isSelected && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[starSize * 2, starSize * 2.5, 16]} />
          <meshBasicMaterial
            color="#00FFFF"
            transparent
            opacity={0.8}
          />
        </mesh>
      )}
    </group>
  );
};

// Safe fleet marker
const ProgressiveFleetMarker: React.FC<{ 
  fleet: any; 
  systemPosition: [number, number, number];
}> = ({ fleet, systemPosition }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (FEATURES.STAR_ANIMATIONS) {
      try {
        if (meshRef.current) {
          meshRef.current.rotation.y += 0.02;
        }
      } catch (error) {
        // Ignore animation errors
      }
    }
  });

  return (
    <mesh 
      ref={meshRef} 
      position={[systemPosition[0], systemPosition[1] + 2, systemPosition[2]]}
    >
      <boxGeometry args={[0.3, 0.3, 0.3]} />
      {FEATURES.EMISSIVE_MATERIALS ? (
        <meshStandardMaterial color="#00FF00" emissive="#00FF00" emissiveIntensity={0.3} />
      ) : (
        <meshBasicMaterial color="#00FF00" />
      )}
    </mesh>
  );
};

const ProgressiveGalaxy3D: React.FC = () => {
  const { stellarData, fleets, selectedSystemId, selectSystem } = useGameStore();
  const [errorCount, setErrorCount] = useState(0);

  // Safe data processing
  const displayStars = useMemo(() => {
    if (!Array.isArray(stellarData)) return [];
    
    return stellarData.slice(0, 50).map(star => { // Limit to 50 stars for performance
      if (!star || typeof star.x !== 'number' || typeof star.y !== 'number' || typeof star.z !== 'number') {
        return null;
      }
      
      return {
        ...star,
        position3D: [star.x * 10, star.y * 10, star.z * 10] as [number, number, number]
      };
    }).filter(Boolean);
  }, [stellarData]);

  // Safe fleet processing
  const validFleets = useMemo(() => {
    if (!Array.isArray(fleets) || !FEATURES.FLEET_MARKERS) return [];
    return fleets.filter(fleet => fleet && fleet.id && fleet.system_id);
  }, [fleets]);

  console.log('🌌 ProgressiveGalaxy3D: Rendering with', displayStars.length, 'stars,', validFleets.length, 'fleets');

  return (
    <div className="w-full h-full relative">
      <Canvas
        camera={{ position: [20, 15, 20], fov: 60 }}
        style={{ background: 'transparent' }}
        gl={{ 
          antialias: false,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onError={(error) => {
          console.error('🚨 ProgressiveGalaxy3D Canvas error:', error);
          setErrorCount(prev => prev + 1);
        }}
      >
        {/* Proper lighting for standard materials */}
        {FEATURES.EMISSIVE_MATERIALS && (
          <>
            <ambientLight intensity={0.3} />
            <pointLight position={[0, 0, 0]} intensity={1.5} color="#FFD700" />
          </>
        )}
        
        {/* Background stars */}
        <Stars
          radius={50}
          depth={20}
          count={500}
          factor={2}
          saturation={0}
          fade
        />

        {/* Progressive star systems */}
        {FEATURES.BASIC_STARS && displayStars.map((star, index) => {
          if (!star || !star.position3D) return null;
          
          return (
            <ProgressiveStar
              key={star.star_id || index}
              star={star}
              position={star.position3D}
              isSelected={selectedSystemId === star.star_id?.toString()}
              onClick={() => star.star_id && selectSystem(star.star_id.toString())}
            />
          );
        })}

        {/* Progressive fleet markers */}
        {FEATURES.FLEET_MARKERS && validFleets.map(fleet => {
          const starId = fleet.system_id.replace('sys-', '');
          const star = displayStars.find(s => s && s.star_id && s.star_id.toString() === starId);
          if (!star || !star.position3D) return null;

          return (
            <ProgressiveFleetMarker
              key={fleet.id}
              fleet={fleet}
              systemPosition={star.position3D}
            />
          );
        })}

        {/* Camera controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          maxDistance={100}
          minDistance={5}
          dampingFactor={0.1}
          enableDamping
        />
      </Canvas>

      {/* Feature status overlay */}
      <div className="absolute top-4 left-4 text-white">
        <div className="glass-panel p-4 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🔧 Progressive Galaxy</h3>
          <div className="text-sm space-y-1">
            <div>Stars: {displayStars.length}</div>
            <div>Fleets: {validFleets.length}</div>
            <div>Errors: {errorCount}</div>
            <div className="mt-2 text-xs">
              <div>✅ Basic Stars</div>
              <div>✅ Animations</div>
              <div>✅ Emissive Materials</div>
              <div>❌ Hyperlanes (disabled)</div>
              <div>❌ Nebulas (disabled)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressiveGalaxy3D;
