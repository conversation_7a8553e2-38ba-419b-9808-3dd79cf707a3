import React, { useRef, useEffect, useState, useMemo, Suspense } from 'react';
import { Can<PERSON>, useFrame, useThree, extend } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  Text,
  Html,
  Sparkles,
  Sphere,
  Ring,
  Torus,
  useTexture,
  shaderMaterial,
  Billboard,
  Trail,
  Float,
  Environment
} from '@react-three/drei';
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  Vignette,
  Noise,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';
import { useGameStore } from '../store/gameStore';
import { stellarApi } from '../services/stellarApi';
import type { Star } from '../types/stellar';
import SolarSystemView from './SolarSystemView';
import PlanetDetailView from './PlanetDetailView';

// Custom shader materials for stunning effects
const StarCoronaMaterial = shaderMaterial(
  {
    time: 0,
    color: new THREE.Color('#ffffff'),
    intensity: 1.0,
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    uniform float time;
    
    void main() {
      vUv = uv;
      vPosition = position;
      
      // Add subtle vertex displacement for corona effect
      vec3 newPosition = position;
      newPosition += normal * sin(time * 2.0 + position.x * 10.0) * 0.02;
      
      gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
    }
  `,
  // Fragment shader
  `
    uniform float time;
    uniform vec3 color;
    uniform float intensity;
    varying vec2 vUv;
    varying vec3 vPosition;
    
    void main() {
      vec2 center = vec2(0.5);
      float dist = distance(vUv, center);
      
      // Create corona effect
      float corona = 1.0 - smoothstep(0.0, 0.5, dist);
      corona = pow(corona, 2.0);
      
      // Add pulsing effect
      float pulse = sin(time * 3.0) * 0.3 + 0.7;
      corona *= pulse;
      
      // Add noise for realistic corona
      float noise = sin(vPosition.x * 20.0 + time) * sin(vPosition.y * 20.0 + time) * 0.1;
      corona += noise;
      
      vec3 finalColor = color * intensity;
      gl_FragColor = vec4(finalColor, corona * intensity);
    }
  `
);

const NebulaMaterial = shaderMaterial(
  {
    time: 0,
    color1: new THREE.Color('#ff6b9d'),
    color2: new THREE.Color('#4ecdc4'),
    opacity: 0.3,
  },
  // Vertex shader
  `
    varying vec2 vUv;
    varying vec3 vPosition;
    
    void main() {
      vUv = uv;
      vPosition = position;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  // Fragment shader
  `
    uniform float time;
    uniform vec3 color1;
    uniform vec3 color2;
    uniform float opacity;
    varying vec2 vUv;
    varying vec3 vPosition;
    
    // Noise function
    float noise(vec3 p) {
      return sin(p.x * 10.0) * sin(p.y * 10.0) * sin(p.z * 10.0);
    }
    
    void main() {
      vec3 pos = vPosition + time * 0.1;
      
      // Multi-octave noise for realistic nebula
      float n1 = noise(pos * 2.0) * 0.5;
      float n2 = noise(pos * 4.0) * 0.25;
      float n3 = noise(pos * 8.0) * 0.125;
      float totalNoise = n1 + n2 + n3;
      
      // Color mixing based on noise
      vec3 finalColor = mix(color1, color2, totalNoise + 0.5);
      
      // Fade edges
      float edgeFade = 1.0 - length(vUv - 0.5) * 2.0;
      edgeFade = smoothstep(0.0, 1.0, edgeFade);
      
      float alpha = (totalNoise + 0.5) * opacity * edgeFade;
      gl_FragColor = vec4(finalColor, alpha);
    }
  `
);

// Extend Three.js with our custom materials
extend({ StarCoronaMaterial, NebulaMaterial });

// Declare module augmentation for TypeScript
declare module '@react-three/fiber' {
  interface ThreeElements {
    starCoronaMaterial: any;
    nebulaMaterial: any;
  }
}

interface StunningStarSystemProps {
  star: Star;
  position: [number, number, number];
  isSelected: boolean;
  hasFleets: boolean;
  onClick: () => void;
  onHover: (star: Star | null) => void;
}

const StunningStarSystem: React.FC<StunningStarSystemProps> = ({
  star, position, isSelected, hasFleets, onClick, onHover
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Gentle pulsing animation for selected/hovered stars only
  useFrame((state) => {
    if (meshRef.current && (isSelected || hovered)) {
      const pulse = Math.sin(state.clock.elapsedTime * 2) * 0.1 + 1;
      meshRef.current.scale.setScalar(pulse);
    }
  });

  // Use stellar_color from database first, then fallback to spectral type
  const starColor = star.stellar_color || stellarApi.getStarColor(star.spectral_type, star.stellar_color);
  const starSize = Math.max(0.2, Math.min(1.0, stellarApi.getStarSize(star) * 0.5)); // Smaller, more realistic

  const handleClick = (e: any) => {
    e.stopPropagation();
    console.log('Star clicked:', star.name, star.star_id);
    onClick();
  };

  const handlePointerEnter = (e: any) => {
    e.stopPropagation();
    setHovered(true);
    onHover(star);
    document.body.style.cursor = 'pointer';
  };

  const handlePointerLeave = (e: any) => {
    e.stopPropagation();
    setHovered(false);
    onHover(null);
    document.body.style.cursor = 'auto';
  };

  return (
    <group position={position}>
      {/* Larger invisible clickable area for easier selection */}
      <mesh
        onClick={handleClick}
        onPointerEnter={handlePointerEnter}
        onPointerLeave={handlePointerLeave}
        visible={false}
      >
        <sphereGeometry args={[Math.max(starSize * 3, 2.0), 8, 8]} />
        <meshBasicMaterial transparent opacity={0} />
      </mesh>

      {/* Optimized star rendering - point-like with glow */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[starSize, 8, 8]} />
        <meshBasicMaterial
          color={starColor}
          transparent
          opacity={hovered ? 1.0 : 0.9}
        />
      </mesh>

      {/* Star glow effect - only for selected or hovered stars */}
      {(isSelected || hovered) && (
        <mesh scale={starSize * 4}>
          <sphereGeometry args={[1, 16, 16]} />
          <meshBasicMaterial
            color={starColor}
            transparent
            opacity={isSelected ? 0.3 : 0.2}
            side={THREE.BackSide}
          />
        </mesh>
      )}

      {/* Selection ring - simplified */}
      {isSelected && (
        <Ring args={[starSize * 3, starSize * 3.5, 32]}>
          <meshBasicMaterial color="#00FFFF" transparent opacity={0.6} side={THREE.DoubleSide} />
        </Ring>
      )}

      {/* Fleet indicators with enhanced visuals */}
      {hasFleets && (
        <Billboard>
          <Sparkles count={10} scale={2} size={1} speed={0.5} color="#FFD700" />
        </Billboard>
      )}

      {/* Star name label */}
      {(hovered || isSelected) && (
        <Billboard position={[0, starSize + 1, 0]}>
          <Text
            fontSize={0.5}
            color="#FFFFFF"
            anchorX="center"
            anchorY="middle"
            outlineWidth={0.02}
            outlineColor="#000000"
          >
            {star.name}
          </Text>
        </Billboard>
      )}
    </group>
  );
};

// Optimized star field for performance with many stars
interface OptimizedStarFieldProps {
  stars: Star[];
  onStarClick: (star: Star) => void;
  onStarHover: (star: Star | null) => void;
  selectedSystemId: number | null;
  maxDistance: number;
}

const OptimizedStarField: React.FC<OptimizedStarFieldProps> = ({
  stars, onStarClick, onStarHover, selectedSystemId, maxDistance
}) => {
  const pointsRef = useRef<THREE.Points>(null);
  const { camera, raycaster, mouse } = useThree();
  const [hoveredStarIndex, setHoveredStarIndex] = useState<number | null>(null);

  // Create a circular texture for the points to make them appear as circles instead of squares
  const starTexture = useMemo(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const context = canvas.getContext('2d')!;

    // Create a radial gradient for a circular star appearance
    const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
    gradient.addColorStop(0.2, 'rgba(255, 255, 255, 1)');
    gradient.addColorStop(0.4, 'rgba(255, 255, 255, 0.8)');
    gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.4)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    context.fillStyle = gradient;
    context.fillRect(0, 0, 64, 64);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    return texture;
  }, []);

  // Create geometry and materials for point-based stars
  const { positions, colors, sizes } = useMemo(() => {
    const positions = new Float32Array(stars.length * 3);
    const colors = new Float32Array(stars.length * 3);
    const sizes = new Float32Array(stars.length);

    stars.forEach((star, i) => {
      // Position
      const coords = stellarApi.getStarCoordinates(star);
      positions[i * 3] = coords[0];
      positions[i * 3 + 1] = coords[1];
      positions[i * 3 + 2] = coords[2];

      // Color from database or spectral type
      const colorHex = star.stellar_color || stellarApi.getStarColor(star.spectral_type, star.stellar_color);
      const color = new THREE.Color(colorHex);
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;

      // Size based on star properties
      sizes[i] = Math.max(2, Math.min(8, stellarApi.getStarSize(star) * 3));
    });

    return { positions, colors, sizes };
  }, [stars]);

  // Simple click handler for points
  const handleClick = (event: any) => {
    event.stopPropagation();
    console.log('🌟 OptimizedStarField: Points clicked - checking for star intersection');

    // For now, let's use a simple approach - find the closest star to camera center
    // This is a fallback until we get proper point intersection working
    if (stars.length > 0) {
      // Get the first star as a test
      const testStar = stars[0];
      console.log(`🌟 OptimizedStarField: Test clicking star: ${testStar.name} (ID: ${testStar.star_id})`);
      onStarClick(testStar);
    }
  };

  const handlePointerEnter = (event: any) => {
    event.stopPropagation();
    document.body.style.cursor = 'pointer';

    // For now, hover the first star as a test
    if (stars.length > 0 && hoveredStarIndex !== 0) {
      setHoveredStarIndex(0);
      onStarHover(stars[0]);
    }
  };

  const handlePointerLeave = (event: any) => {
    event.stopPropagation();
    document.body.style.cursor = 'auto';

    if (hoveredStarIndex !== null) {
      setHoveredStarIndex(null);
      onStarHover(null);
    }
  };

  return (
    <points
      ref={pointsRef}
      onClick={handleClick}
      onPointerEnter={handlePointerEnter}
      onPointerLeave={handlePointerLeave}
    >
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={stars.length}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={stars.length}
          array={colors}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-size"
          count={stars.length}
          array={sizes}
          itemSize={1}
        />
      </bufferGeometry>
      <pointsMaterial
        map={starTexture}
        size={3}
        sizeAttenuation={true}
        vertexColors={true}
        transparent={true}
        opacity={0.9}
        alphaTest={0.1}
        blending={THREE.AdditiveBlending}
      />
    </points>
  );
};

interface VolumetricNebulaProps {
  position: [number, number, number];
  color1: string;
  color2: string;
  scale?: number;
}

const VolumetricNebula: React.FC<VolumetricNebulaProps> = ({ 
  position, color1, color2, scale = 10 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current && meshRef.current.material) {
      (meshRef.current.material as any).time = state.clock.elapsedTime * 0.5;
    }
  });

  return (
    <mesh ref={meshRef} position={position} scale={scale}>
      <sphereGeometry args={[1, 32, 32]} />
      <nebulaMaterial
        color1={new THREE.Color(color1)}
        color2={new THREE.Color(color2)}
        opacity={0.4}
        transparent
        side={THREE.DoubleSide}
      />
    </mesh>
  );
};

const GalacticCore: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
      meshRef.current.rotation.z += 0.005;
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Central black hole */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[0.8, 64, 64]} />
        <meshStandardMaterial
          color="#000000"
          emissive="#4A0080"
          emissiveIntensity={1.2}
          roughness={0}
          metalness={1}
        />
      </mesh>
      
      {/* Accretion disk */}
      <Torus args={[2, 0.1, 16, 100]} rotation={[Math.PI / 2, 0, 0]}>
        <meshStandardMaterial
          color="#FF6B00"
          emissive="#FF6B00"
          emissiveIntensity={0.8}
          transparent
          opacity={0.7}
        />
      </Torus>
      
      {/* Energy jets */}
      <group>
        <mesh position={[0, 5, 0]} rotation={[0, 0, 0]}>
          <cylinderGeometry args={[0.1, 0.05, 10, 8]} />
          <meshBasicMaterial color="#00FFFF" transparent opacity={0.6} />
        </mesh>
        <mesh position={[0, -5, 0]} rotation={[Math.PI, 0, 0]}>
          <cylinderGeometry args={[0.1, 0.05, 10, 8]} />
          <meshBasicMaterial color="#00FFFF" transparent opacity={0.6} />
        </mesh>
      </group>
    </group>
  );
};

// Far galaxies background component
const FarGalaxies: React.FC<{ visible: boolean }> = ({ visible }) => {
  const groupRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += 0.0005; // Very slow rotation
    }
  });

  if (!visible) return null;

  // Generate realistic distant galaxies with proper types and orientations
  const galaxyData = useMemo(() => {
    const galaxies = [];
    const galaxyTypes = [
      { type: 'spiral', name: 'Andromeda', color: '#4a90e2', arms: 2 },
      { type: 'spiral', name: 'Whirlpool', color: '#5ba3f5', arms: 2 },
      { type: 'spiral', name: 'Pinwheel', color: '#6bb6ff', arms: 4 },
      { type: 'elliptical', name: 'M87', color: '#f5a623', eccentricity: 0.3 },
      { type: 'elliptical', name: 'M49', color: '#f5d623', eccentricity: 0.6 },
      { type: 'irregular', name: 'Large Magellanic Cloud', color: '#bd10e0' },
      { type: 'irregular', name: 'Small Magellanic Cloud', color: '#9013fe' },
      { type: 'spiral', name: 'Triangulum', color: '#50e3c2', arms: 2 },
      { type: 'spiral', name: 'Sombrero', color: '#7ed321', arms: 2 },
      { type: 'elliptical', name: 'Centaurus A', color: '#ff6b6b', eccentricity: 0.4 },
      { type: 'spiral', name: 'NGC 1300', color: '#4ecdc4', arms: 2 },
      { type: 'irregular', name: 'NGC 1427A', color: '#ff9ff3' },
      { type: 'spiral', name: 'NGC 2207', color: '#45b7d1', arms: 2 },
      { type: 'elliptical', name: 'NGC 4472', color: '#f39c12', eccentricity: 0.5 },
      { type: 'spiral', name: 'NGC 6946', color: '#e74c3c', arms: 4 }
    ];

    for (let i = 0; i < 15; i++) {
      const distance = 300 + Math.random() * 200;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      const x = distance * Math.sin(phi) * Math.cos(theta);
      const y = distance * Math.cos(phi);
      const z = distance * Math.sin(phi) * Math.sin(theta);

      const galaxyType = galaxyTypes[i % galaxyTypes.length];

      galaxies.push({
        x, y, z,
        size: 3 + Math.random() * 6,
        ...galaxyType,
        rotation: {
          x: Math.random() * Math.PI * 2,
          y: Math.random() * Math.PI * 2,
          z: Math.random() * Math.PI * 2
        },
        tilt: Math.random() * Math.PI / 3 // Random tilt up to 60 degrees
      });
    }
    return galaxies;
  }, []);

  // Galaxy rendering components
  const SpiralGalaxy = ({ galaxy, index }: { galaxy: any, index: number }) => (
    <group
      key={index}
      position={[galaxy.x, galaxy.y, galaxy.z]}
      rotation={[galaxy.rotation.x, galaxy.rotation.y, galaxy.rotation.z]}
    >
      {/* Central bulge */}
      <mesh>
        <sphereGeometry args={[galaxy.size * 0.3, 16, 16]} />
        <meshBasicMaterial
          color={galaxy.color}
          transparent
          opacity={0.8}
        />
      </mesh>

      {/* Spiral arms */}
      {Array.from({ length: galaxy.arms }).map((_, armIndex) => {
        const armRotation = (armIndex * Math.PI * 2) / galaxy.arms;
        return (
          <group key={armIndex} rotation={[0, armRotation, 0]}>
            {/* Create spiral arm with multiple segments */}
            {Array.from({ length: 8 }).map((_, segIndex) => {
              const radius = galaxy.size * 0.4 + (segIndex * galaxy.size * 0.15);
              const spiralAngle = segIndex * 0.3;
              const x = radius * Math.cos(spiralAngle);
              const z = radius * Math.sin(spiralAngle);
              const segmentSize = galaxy.size * 0.1 * (1 - segIndex * 0.1);

              return (
                <mesh key={segIndex} position={[x, 0, z]}>
                  <sphereGeometry args={[segmentSize, 8, 8]} />
                  <meshBasicMaterial
                    color={galaxy.color}
                    transparent
                    opacity={0.4 - segIndex * 0.04}
                  />
                </mesh>
              );
            })}
          </group>
        );
      })}

      {/* Galactic disk */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[galaxy.size * 0.3, galaxy.size * 1.2, 32]} />
        <meshBasicMaterial
          color={galaxy.color}
          transparent
          opacity={0.1}
          side={2} // DoubleSide
        />
      </mesh>

      {/* Dust lanes for edge-on view */}
      {Math.random() > 0.7 && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[galaxy.size * 0.5, galaxy.size * 0.7, 16]} />
          <meshBasicMaterial
            color="#8B4513"
            transparent
            opacity={0.3}
            side={2}
          />
        </mesh>
      )}
    </group>
  );

  const EllipticalGalaxy = ({ galaxy, index }: { galaxy: any, index: number }) => (
    <group
      key={index}
      position={[galaxy.x, galaxy.y, galaxy.z]}
      rotation={[galaxy.rotation.x, galaxy.rotation.y, galaxy.rotation.z]}
    >
      {/* Main elliptical body */}
      <mesh scale={[1, 1 - galaxy.eccentricity, 1]}>
        <sphereGeometry args={[galaxy.size, 16, 16]} />
        <meshBasicMaterial
          color={galaxy.color}
          transparent
          opacity={0.6}
        />
      </mesh>

      {/* Outer halo */}
      <mesh scale={[1.5, 1.5 - galaxy.eccentricity, 1.5]}>
        <sphereGeometry args={[galaxy.size, 12, 12]} />
        <meshBasicMaterial
          color={galaxy.color}
          transparent
          opacity={0.2}
        />
      </mesh>

      {/* Globular clusters around elliptical galaxy */}
      {Array.from({ length: 8 }).map((_, clusterIndex) => {
        const angle = (clusterIndex * Math.PI * 2) / 8;
        const distance = galaxy.size * (1.8 + Math.random() * 0.5);
        const x = distance * Math.cos(angle);
        const z = distance * Math.sin(angle);
        const y = (Math.random() - 0.5) * galaxy.size * 0.3;

        return (
          <mesh key={clusterIndex} position={[x, y, z]}>
            <sphereGeometry args={[galaxy.size * 0.05, 6, 6]} />
            <meshBasicMaterial
              color={galaxy.color}
              transparent
              opacity={0.6}
            />
          </mesh>
        );
      })}
    </group>
  );

  const IrregularGalaxy = ({ galaxy, index }: { galaxy: any, index: number }) => (
    <group
      key={index}
      position={[galaxy.x, galaxy.y, galaxy.z]}
      rotation={[galaxy.rotation.x, galaxy.rotation.y, galaxy.rotation.z]}
    >
      {/* Main irregular body with asymmetric shape */}
      <mesh scale={[1 + Math.random() * 0.5, 0.3 + Math.random() * 0.4, 1 + Math.random() * 0.5]}>
        <sphereGeometry args={[galaxy.size * 0.8, 12, 12]} />
        <meshBasicMaterial
          color={galaxy.color}
          transparent
          opacity={0.4}
        />
      </mesh>

      {/* Star-forming regions (bright clumps) */}
      {Array.from({ length: 3 + Math.floor(Math.random() * 4) }).map((_, clumpIndex) => {
        const offsetX = (Math.random() - 0.5) * galaxy.size * 1.5;
        const offsetY = (Math.random() - 0.5) * galaxy.size * 0.3;
        const offsetZ = (Math.random() - 0.5) * galaxy.size * 1.5;
        const clumpSize = galaxy.size * (0.15 + Math.random() * 0.25);

        return (
          <mesh key={clumpIndex} position={[offsetX, offsetY, offsetZ]}>
            <sphereGeometry args={[clumpSize, 8, 8]} />
            <meshBasicMaterial
              color={clumpIndex % 2 === 0 ? '#ff6b9d' : galaxy.color} // Some pink star-forming regions
              transparent
              opacity={0.5 + Math.random() * 0.3}
            />
          </mesh>
        );
      })}

      {/* Tidal streams/extensions */}
      {galaxy.name.includes('Magellanic') && (
        <mesh
          position={[galaxy.size * 1.2, 0, galaxy.size * 0.3]}
          scale={[2, 0.1, 0.3]}
        >
          <sphereGeometry args={[galaxy.size * 0.3, 8, 8]} />
          <meshBasicMaterial
            color={galaxy.color}
            transparent
            opacity={0.2}
          />
        </mesh>
      )}
    </group>
  );

  return (
    <group ref={groupRef}>
      {galaxyData.map((galaxy, index) => {
        switch (galaxy.type) {
          case 'spiral':
            return <SpiralGalaxy key={index} galaxy={galaxy} index={index} />;
          case 'elliptical':
            return <EllipticalGalaxy key={index} galaxy={galaxy} index={index} />;
          case 'irregular':
            return <IrregularGalaxy key={index} galaxy={galaxy} index={index} />;
          default:
            return null;
        }
      })}
    </group>
  );
};

const CameraController: React.FC<{ displayStars: any[]; onResetCamera: () => void }> = ({ displayStars, onResetCamera }) => {
  const { camera, controls } = useThree();
  const { selectedSystemId } = useGameStore();

  // Reset camera to galactic overview
  const resetToGalacticCenter = () => {
    if (controls) {
      // Reset OrbitControls target to galactic center
      controls.target.set(0, 0, 0);

      // Move camera to overview position
      const startPosition = camera.position.clone();
      const endPosition = new THREE.Vector3(50, 30, 50);

      let progress = 0;
      const animateCamera = () => {
        progress += 0.03;
        if (progress <= 1) {
          camera.position.lerpVectors(startPosition, endPosition, progress);
          controls.update();
          requestAnimationFrame(animateCamera);
        }
      };
      animateCamera();
    }
  };

  // Expose reset function to parent
  useEffect(() => {
    if (onResetCamera && onResetCamera.current) {
      onResetCamera.current = resetToGalacticCenter;
    }
  }, [onResetCamera]);

  useEffect(() => {
    if (selectedSystemId && displayStars.length > 0 && controls) {
      // Find the selected star
      const selectedStar = displayStars.find(star => star.star_id === selectedSystemId);
      if (selectedStar && selectedStar.position) {
        const [x, y, z] = selectedStar.position;

        // Set OrbitControls target to the selected star
        const startTarget = controls.target.clone();
        const endTarget = new THREE.Vector3(x, y, z);

        // Calculate a good camera position relative to the star
        const currentDistance = camera.position.distanceTo(controls.target);
        const optimalDistance = Math.max(8, Math.min(20, currentDistance));

        const direction = camera.position.clone().sub(controls.target).normalize();
        const newCameraPosition = endTarget.clone().add(direction.multiplyScalar(optimalDistance));

        // Animate both camera position and target
        const startPosition = camera.position.clone();

        let progress = 0;
        const animateCamera = () => {
          progress += 0.03;
          if (progress <= 1) {
            // Interpolate target
            controls.target.lerpVectors(startTarget, endTarget, progress);

            // Interpolate camera position
            camera.position.lerpVectors(startPosition, newCameraPosition, progress);

            controls.update();
            requestAnimationFrame(animateCamera);
          }
        };
        animateCamera();
      }
    }
  }, [selectedSystemId, camera, controls, displayStars]);

  return null;
};

const StunningGalaxy3D: React.FC = () => {
  console.log('🌌 StunningGalaxy3D: Component initializing');
  console.log('🕐 StunningGalaxy3D: Init timestamp:', new Date().toISOString());

  const {
    fleets,
    selectedSystemId,
    selectSystem
  } = useGameStore();

  // View state management
  const [currentView, setCurrentView] = useState<'galaxy' | 'solar_system' | 'planet_detail'>('galaxy');
  const [selectedStarId, setSelectedStarId] = useState<number | null>(null);
  const [selectedPlanetId, setSelectedPlanetId] = useState<number | null>(null);

  const [stars, setStars] = useState<Star[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hoveredStar, setHoveredStar] = useState<Star | null>(null);
  const [enablePostProcessing, setEnablePostProcessing] = useState(true);
  const resetCameraRef = useRef<() => void>(() => {});

  // Performance and visibility controls
  const [maxDistance, setMaxDistance] = useState(100); // Light-years - show all stars by default (we only have stars within 100ly)
  const [renderQuality, setRenderQuality] = useState<'ultra' | 'high' | 'medium' | 'low'>('high');
  const [showFarGalaxies, setShowFarGalaxies] = useState(true);
  // Optimization widget is now always visible

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Star[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Widget positioning state
  const [searchWidgetPos, setSearchWidgetPos] = useState({ x: 20, y: 20 });
  const [optimizationWidgetPos, setOptimizationWidgetPos] = useState({ x: 20, y: 280 }); // Position below search widget
  const [isDraggingSearch, setIsDraggingSearch] = useState(false);
  const [isDraggingOptimization, setIsDraggingOptimization] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Drag handlers for search widget
  const handleSearchMouseDown = (e: React.MouseEvent) => {
    setIsDraggingSearch(true);
    setDragOffset({
      x: e.clientX - searchWidgetPos.x,
      y: e.clientY - searchWidgetPos.y
    });
  };

  // Drag handlers for optimization widget
  const handleOptimizationMouseDown = (e: React.MouseEvent) => {
    setIsDraggingOptimization(true);
    setDragOffset({
      x: e.clientX - optimizationWidgetPos.x,
      y: e.clientY - optimizationWidgetPos.y
    });
  };

  // Global mouse move and up handlers with throttling
  React.useEffect(() => {
    let animationFrameId: number;

    const handleMouseMove = (e: MouseEvent) => {
      // Throttle using requestAnimationFrame to prevent performance issues
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }

      animationFrameId = requestAnimationFrame(() => {
        if (isDraggingSearch) {
          setSearchWidgetPos({
            x: Math.max(0, Math.min(window.innerWidth - 320, e.clientX - dragOffset.x)),
            y: Math.max(0, Math.min(window.innerHeight - 200, e.clientY - dragOffset.y))
          });
        }
        if (isDraggingOptimization) {
          setOptimizationWidgetPos({
            x: Math.max(0, Math.min(window.innerWidth - 280, e.clientX - dragOffset.x)),
            y: Math.max(0, Math.min(window.innerHeight - 400, e.clientY - dragOffset.y))
          });
        }
      });
    };

    const handleMouseUp = () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      setIsDraggingSearch(false);
      setIsDraggingOptimization(false);
    };

    if (isDraggingSearch || isDraggingOptimization) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDraggingSearch, isDraggingOptimization, dragOffset]);

  // Load stellar data with distance filtering
  useEffect(() => {
    const loadStars = async () => {
      try {
        setIsLoading(true);
        // Calculate appropriate limit based on distance and quality settings
        const limit = renderQuality === 'ultra' ? 5000 :
                     renderQuality === 'high' ? 3000 :
                     renderQuality === 'medium' ? 1500 : 800;

        const data = await stellarApi.getStars(maxDistance, limit);
        setStars(data.stars);
        console.log(`✅ Loaded stellar data: ${data.stars.length} stars within ${maxDistance} light-years (${renderQuality} quality)`);
      } catch (error) {
        console.error('❌ Failed to load stellar data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStars();
  }, [maxDistance, renderQuality]);

  // Search functionality
  useEffect(() => {
    if (searchQuery.trim().length >= 2) {
      const filtered = stars.filter(star =>
        star.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        star.proper_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        star.catalog_name?.toLowerCase().includes(searchQuery.toLowerCase())
      ).slice(0, 10); // Limit to 10 results
      setSearchResults(filtered);
      setShowSearchResults(true);
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  }, [searchQuery, stars]);

  // Function to zoom to a specific star
  const zoomToStar = (star: Star) => {
    const starData = displayStars.find(s => s.star_id === star.star_id);
    if (starData && starData.position) {
      // Select the star
      selectSystem(star.star_id);

      // Clear search
      setSearchQuery('');
      setShowSearchResults(false);

      console.log(`🔍 Zooming to star: ${star.name} at position:`, starData.position);
    }
  };

  // Convert stars to display format with REAL 3D astronomical positions
  const displayStars = useMemo(() => {
    if (!stars || stars.length === 0) return [];

    return stars.map((star) => {
      // Use real astronomical coordinates if available
      if (star.x_pc !== undefined && star.y_pc !== undefined && star.z_pc !== undefined) {
        // Scale parsecs to visual units (1 parsec = ~3 visual units for good visibility)
        const scale = 3.0;
        return {
          ...star,
          position: [star.x_pc * scale, star.z_pc * scale, star.y_pc * scale] as [number, number, number]
        };
      }

      // Fallback: Convert RA/Dec/Distance to 3D coordinates for older data
      if (star.ra_deg !== undefined && star.dec_deg !== undefined && star.distance_ly !== undefined) {
        const ra_rad = star.ra_deg * Math.PI / 180;
        const dec_rad = star.dec_deg * Math.PI / 180;
        const distance_pc = star.distance_ly * 0.306601; // Convert ly to parsecs
        const scale = 3.0;

        const x = distance_pc * Math.cos(dec_rad) * Math.cos(ra_rad) * scale;
        const y = distance_pc * Math.cos(dec_rad) * Math.sin(ra_rad) * scale;
        const z = distance_pc * Math.sin(dec_rad) * scale;

        return {
          ...star,
          position: [x, z, y] as [number, number, number]
        };
      }

      // Final fallback for Sol or stars without coordinates
      if (star.name === 'Sol' || star.distance_ly === 0) {
        return {
          ...star,
          position: [0, 0, 0] as [number, number, number] // Sol at origin
        };
      }

      // Default position if no coordinates available
      return {
        ...star,
        position: [0, 0, 0] as [number, number, number]
      };
    });
  }, [stars]);

  // Fleet positions mapped to star systems
  const fleetsBySystem = useMemo(() => {
    const fleetMap = new Map<string, any[]>();
    fleets?.forEach(fleet => {
      const systemFleets = fleetMap.get(fleet.system_id) || [];
      systemFleets.push(fleet);
      fleetMap.set(fleet.system_id, systemFleets);
    });
    return fleetMap;
  }, [fleets]);

  const handleStarClick = (star: Star) => {
    console.log(`🌟 Star clicked: ${star.name} (ID: ${star.star_id})`);

    // If already selected, zoom into solar system view
    if (star.star_id === selectedSystemId) {
      console.log(`🔍 Zooming into ${star.name} solar system`);
      setSelectedStarId(star.star_id);
      setCurrentView('solar_system');
    } else {
      // First click - select the star
      selectSystem(star.star_id);
    }
  };

  const handleBackToGalaxy = () => {
    console.log('🌌 Returning to galaxy view');
    setCurrentView('galaxy');
    setSelectedStarId(null);
    setSelectedPlanetId(null);
  };

  const handlePlanetClick = (planetId: number) => {
    console.log(`🪐 Navigating to planet detail view for planet ID: ${planetId}`);
    setSelectedPlanetId(planetId);
    setCurrentView('planet_detail');
  };

  const handlePlanetClickByName = (planetName: string) => {
    console.log(`🪐 Navigating to planet detail view for planet: ${planetName}`);
    // Convert planet name to ID (this would be better with a proper mapping)
    const planetNameToId: { [key: string]: number } = {
      'Mercury': 1, 'Venus': 2, 'Earth': 3, 'Mars': 4,
      'Jupiter': 5, 'Saturn': 6, 'Uranus': 7, 'Neptune': 8
    };
    const planetId = planetNameToId[planetName];
    if (planetId) {
      setSelectedPlanetId(planetId);
      setCurrentView('planet_detail');
    }
  };

  const handleBackToSolarSystem = () => {
    console.log('🌟 Returning to solar system view');
    setCurrentView('solar_system');
    setSelectedPlanetId(null);
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-white text-xl animate-pulse">
          🌌 Initializing Galactic Visualization...
        </div>
      </div>
    );
  }

  // Render planet detail view if selected
  if (currentView === 'planet_detail' && selectedPlanetId && selectedStarId) {
    return (
      <PlanetDetailView
        planetId={selectedPlanetId}
        starId={selectedStarId}
        onBack={handleBackToSolarSystem}
      />
    );
  }

  // Render solar system view if selected
  if (currentView === 'solar_system' && selectedStarId) {
    return (
      <SolarSystemView
        starId={selectedStarId}
        onBack={handleBackToGalaxy}
        onPlanetClick={handlePlanetClick}
      />
    );
  }

  // Render galaxy view
  return (
    <div className="w-full h-full relative">
      {/* Optimization Widget - Always visible, positioned below search widget */}
      <div
        className="absolute z-50 bg-gray-900 bg-opacity-95 rounded-lg border border-gray-600 text-white text-sm w-64"
        style={{
          left: `${optimizationWidgetPos.x}px`,
          top: `${optimizationWidgetPos.y}px`,
          cursor: isDraggingOptimization ? 'grabbing' : 'grab'
        }}
      >
          {/* Drag handle */}
          <div
            className="flex items-center justify-between p-2 bg-gray-800 bg-opacity-50 rounded-t-lg cursor-grab active:cursor-grabbing border-b border-gray-600"
            onMouseDown={handleOptimizationMouseDown}
          >
            <h3 className="font-bold text-cyan-400">🎮 Graphics Optimization</h3>
            <span className="text-gray-400 text-xs">⋮⋮</span>
          </div>

          <div className="p-4" onMouseDown={(e) => e.stopPropagation()}>

          {/* Distance Control */}
          <div className="mb-4">
            <label className="block text-xs text-gray-300 mb-1">
              🌌 Visibility Range: {maxDistance} light-years
            </label>
            <input
              type="range"
              min="10"
              max="100"
              value={maxDistance}
              onChange={(e) => setMaxDistance(Number(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>10ly</span>
              <span>100ly</span>
            </div>
          </div>

          {/* Render Quality */}
          <div className="mb-4">
            <label className="block text-xs text-gray-300 mb-2">🎨 Render Quality</label>
            <div className="grid grid-cols-2 gap-1">
              {(['low', 'medium', 'high', 'ultra'] as const).map((quality) => (
                <button
                  key={quality}
                  onClick={() => setRenderQuality(quality)}
                  className={`px-2 py-1 text-xs rounded ${
                    renderQuality === quality
                      ? 'bg-cyan-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {quality.charAt(0).toUpperCase() + quality.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Post Processing */}
          <div className="mb-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={enablePostProcessing}
                onChange={(e) => setEnablePostProcessing(e.target.checked)}
                className="rounded"
              />
              <span className="text-xs">✨ Post-Processing Effects</span>
            </label>
          </div>

          {/* Far Galaxies */}
          <div className="mb-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={showFarGalaxies}
                onChange={(e) => setShowFarGalaxies(e.target.checked)}
                className="rounded"
              />
              <span className="text-xs">🌌 Distant Galaxies</span>
            </label>
          </div>

          {/* Performance Info */}
          <div className="border-t border-gray-700 pt-3 mt-3">
            <div className="text-xs text-gray-400">
              <div>Stars: {displayStars.length}</div>
              <div>Quality: {renderQuality}</div>
              <div>Range: {maxDistance}ly</div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="border-t border-gray-700 pt-3 mt-3 space-y-1">
            <button
              onClick={() => resetCameraRef.current && resetCameraRef.current()}
              className="block w-full px-2 py-1 bg-blue-700 bg-opacity-80 text-white text-xs rounded hover:bg-blue-600"
            >
              🎯 Reset Camera
            </button>
            <button
              onClick={() => {
                setMaxDistance(30);
                setRenderQuality('medium');
                setEnablePostProcessing(false);
                setShowFarGalaxies(false);
              }}
              className="block w-full px-2 py-1 bg-green-700 bg-opacity-80 text-white text-xs rounded hover:bg-green-600"
            >
              ⚡ Performance Mode
            </button>
            <button
              onClick={() => {
                setMaxDistance(100);
                setRenderQuality('ultra');
                setEnablePostProcessing(true);
                setShowFarGalaxies(true);
              }}
              className="block w-full px-2 py-1 bg-purple-700 bg-opacity-80 text-white text-xs rounded hover:bg-purple-600"
            >
              🎨 Quality Mode
            </button>
          </div>
          </div>
        </div>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 z-10 bg-gray-900 bg-opacity-90 p-3 rounded-lg border border-gray-600 text-white text-sm">
        <div className="font-bold mb-1">🌟 Navigation:</div>
        <div>• Use search box to find stars</div>
        <div>• Click star to select</div>
        <div>• Click selected star again to zoom into system</div>
      </div>

      {/* Search interface */}
      <div
        className="absolute z-50 w-80"
        style={{
          left: `${searchWidgetPos.x}px`,
          top: `${searchWidgetPos.y}px`,
          cursor: isDraggingSearch ? 'grabbing' : 'grab'
        }}
      >
        <div className="glass-panel backdrop-blur-md bg-gray-900 bg-opacity-90 rounded-lg border border-gray-600">
          {/* Drag handle */}
          <div
            className="flex items-center justify-between p-2 bg-gray-800 bg-opacity-50 rounded-t-lg cursor-grab active:cursor-grabbing border-b border-gray-600"
            onMouseDown={handleSearchMouseDown}
          >
            <span className="text-cyan-400 text-sm font-bold">🔍 Star Search</span>
            <span className="text-gray-400 text-xs">⋮⋮</span>
          </div>

          <div className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <input
              type="text"
              placeholder="Search stars..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onMouseDown={(e) => e.stopPropagation()}
              className="bg-gray-800 border border-gray-600 rounded px-3 py-1 text-white text-sm focus:border-cyan-400 focus:outline-none flex-1"
            />
          </div>

          {/* Search results */}
          {showSearchResults && searchResults.length > 0 && (
            <div className="mt-2 max-h-48 overflow-y-auto" onMouseDown={(e) => e.stopPropagation()}>
              {searchResults.map((star) => (
                <div
                  key={star.star_id}
                  onClick={() => zoomToStar(star)}
                  className="cursor-pointer hover:bg-gray-700 p-2 rounded text-sm border-b border-gray-600 last:border-b-0"
                >
                  <div className="text-white font-medium">{star.name}</div>
                  <div className="text-gray-400 text-xs">
                    {star.distance_ly?.toFixed(1)} ly • {star.spectral_type || 'Unknown'}
                    {star.planet_count > 0 && ` • ${star.planet_count} planets`}
                  </div>
                </div>
              ))}
            </div>
          )}

          {showSearchResults && searchResults.length === 0 && searchQuery.length >= 2 && (
            <div className="mt-2 text-gray-400 text-sm">No stars found</div>
          )}
          </div>
        </div>
      </div>

      {/* Hovered star info - moved to bottom left */}
      {hoveredStar && !showSearchResults && (
        <div className="absolute bottom-20 left-4 z-10 bg-gray-900 bg-opacity-90 p-4 rounded-lg border border-gray-600 max-w-sm">
          <h3 className="text-lg font-bold text-white mb-2">{hoveredStar.name}</h3>
          <div className="text-sm text-gray-300 space-y-1">
            <div>Type: <span className="text-blue-300">{hoveredStar.spectral_type}</span></div>
            <div>Distance: <span className="text-green-300">{hoveredStar.distance_ly?.toFixed(1)} ly</span></div>
            <div>Mass: <span className="text-yellow-300">{hoveredStar.mass_solar?.toFixed(2)} M☉</span></div>
            {hoveredStar.planet_count > 0 && (
              <div>Planets: <span className="text-purple-300">{hoveredStar.planet_count}</span></div>
            )}
          </div>
        </div>
      )}

      <Canvas
        camera={{ position: [50, 30, 50], fov: 75 }}
        style={{ background: 'transparent' }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance",
          stencil: false,
          depth: true
        }}
        dpr={[1, 2]} // Adaptive pixel ratio
      >
        {/* Enhanced lighting setup */}
        <ambientLight intensity={0.2} color="#1a1a2e" />
        <pointLight position={[0, 0, 0]} intensity={3} color="#FFD700" distance={100} />
        <pointLight position={[30, 20, 30]} intensity={1.5} color="#4A90E2" distance={80} />
        <pointLight position={[-30, -20, -30]} intensity={1.5} color="#FF6B9D" distance={80} />
        <directionalLight position={[50, 50, 50]} intensity={0.5} color="#FFFFFF" />

        {/* Environment for realistic reflections */}
        <Environment preset="night" />

        {/* Enhanced background stars - quality dependent */}
        <Stars
          radius={200}
          depth={100}
          count={renderQuality === 'ultra' ? 8000 :
                 renderQuality === 'high' ? 5000 :
                 renderQuality === 'medium' ? 3000 : 1500}
          factor={6}
          saturation={0.1}
          fade
          speed={0.2}
        />

        {/* Far galaxies background */}
        <FarGalaxies visible={showFarGalaxies} />

        {/* Galactic center */}
        <GalacticCore />

        {/* Enhanced cosmic effects */}
        <Sparkles
          count={500}
          scale={[100, 100, 100]}
          size={3}
          speed={0.2}
          opacity={0.4}
          color="#FFD700"
        />

        {/* Render star systems - optimized based on count */}
        {displayStars.length > 5000 ? (
          // Use optimized point-based rendering for many stars
          <OptimizedStarField
            stars={displayStars}
            onStarClick={handleStarClick}
            onStarHover={setHoveredStar}
            selectedSystemId={selectedSystemId}
            maxDistance={maxDistance}
          />
        ) : (
          // Use detailed rendering for fewer stars
          displayStars.map((star) => (
            <StunningStarSystem
              key={star.star_id}
              star={star}
              position={star.position}
              isSelected={star.star_id === selectedSystemId}
              hasFleets={fleetsBySystem.has(star.star_id)}
              onClick={() => handleStarClick(star)}
              onHover={setHoveredStar}
            />
          ))
        )}

        {/* Enhanced camera controls */}
        <OrbitControls
          enablePan={true} // Enable panning to navigate to star systems
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={200}
          autoRotate={false} // Disable auto-rotate to allow manual navigation
          enableDamping={true}
          dampingFactor={0.05}
          rotateSpeed={0.5}
          zoomSpeed={1.0}
          panSpeed={1.0}
          maxPolarAngle={Math.PI}
          minPolarAngle={0}
          makeDefault
        />

        <CameraController displayStars={displayStars} onResetCamera={resetCameraRef} />

        {/* Post-processing effects for stunning visuals */}
        {enablePostProcessing && (
          <Suspense fallback={null}>
            <EffectComposer>
              <Bloom
                intensity={0.8}
                luminanceThreshold={0.2}
                luminanceSmoothing={0.9}
                radius={0.8}
              />
              <ChromaticAberration offset={[0.0005, 0.0005]} />
              <Vignette eskil={false} offset={0.1} darkness={0.3} />
              <Noise opacity={0.02} />
              <ToneMapping adaptive={true} resolution={256} />
            </EffectComposer>
          </Suspense>
        )}
      </Canvas>

      {/* Galaxy stats overlay */}
      <div className="absolute top-4 right-4 text-white z-50">
        <div data-testid="galaxy-stats" className="glass-panel p-4 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🌌 Galactic Database</h3>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-300">Stars Loaded:</span>
              <span className="text-yellow-400 font-bold">{displayStars.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Total Planets:</span>
              <span className="text-blue-400 font-bold">{displayStars.reduce((sum, star) => sum + Number(star.planet_count || 0), 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Active Fleets:</span>
              <span className="text-green-400 font-bold">{fleets?.length || 0}</span>
            </div>
            {hoveredStar && (
              <div className="mt-2 pt-2 border-t border-gray-600">
                <div className="text-yellow-300 font-semibold">{hoveredStar.name}</div>
                <div className="text-xs text-gray-400">
                  {hoveredStar.distance_ly?.toFixed(1)} ly • {hoveredStar.spectral_type || 'Unknown'}
                </div>
                {hoveredStar.planet_count > 0 && (
                  <div className="text-xs text-blue-300">{hoveredStar.planet_count} planets</div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Performance stats */}
      <div className="absolute bottom-4 right-4 text-xs text-gray-400">
        Rendering {displayStars.length} star systems
      </div>
    </div>
  );
};

export default StunningGalaxy3D;
