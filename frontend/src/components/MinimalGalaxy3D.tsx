import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Stars } from '@react-three/drei';
import * as THREE from 'three';

// Minimal rotating cube to test Three.js without complex logic
const RotatingCube: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    try {
      if (meshRef.current) {
        meshRef.current.rotation.x += 0.01;
        meshRef.current.rotation.y += 0.01;
      }
    } catch (error) {
      console.error('Animation error:', error);
    }
  });

  return (
    <mesh ref={meshRef}>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial color="#00ff00" />
    </mesh>
  );
};

// Simple star without complex animations
const SimpleStar: React.FC<{ position: [number, number, number] }> = ({ position }) => {
  return (
    <mesh position={position}>
      <sphereGeometry args={[0.5, 8, 8]} />
      <meshBasicMaterial color="#ffff00" />
    </mesh>
  );
};

const MinimalGalaxy3D: React.FC = () => {
  console.log('🌌 MinimalGalaxy3D: Rendering...');

  return (
    <div data-testid="minimal-galaxy-view" className="w-full h-full relative">
      <Canvas
        camera={{ position: [10, 10, 10], fov: 60 }}
        style={{ background: 'transparent' }}
        onError={(error) => {
          console.error('🚨 Canvas error:', error);
        }}
      >
        {/* Basic lighting */}
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />

        {/* Background stars */}
        <Stars
          radius={50}
          depth={20}
          count={1000}
          factor={2}
          saturation={0}
          fade
        />

        {/* Simple rotating cube */}
        <RotatingCube />

        {/* A few simple stars */}
        <SimpleStar position={[5, 0, 0]} />
        <SimpleStar position={[-5, 0, 0]} />
        <SimpleStar position={[0, 5, 0]} />
        <SimpleStar position={[0, -5, 0]} />

        {/* Camera controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          maxDistance={50}
          minDistance={5}
        />
      </Canvas>

      {/* Simple overlay */}
      <div className="absolute top-4 left-4 text-white">
        <div className="glass-panel p-4 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🧪 Minimal Galaxy Test</h3>
          <div className="text-sm space-y-1">
            <div>✅ Canvas loaded</div>
            <div>✅ Three.js working</div>
            <div>✅ Basic animations</div>
            <div>✅ Camera controls</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MinimalGalaxy3D;
