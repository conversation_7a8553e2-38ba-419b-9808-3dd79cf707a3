import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Stars } from '@react-three/drei';
import * as THREE from 'three';
import { useGameStore } from '../store/gameStore';

// Safe star component with minimal material properties
const SafeStar: React.FC<{ 
  star: any; 
  position: [number, number, number];
  isSelected?: boolean;
  onClick?: () => void;
}> = ({ star, position, isSelected = false, onClick }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  // Safe animation without material property access
  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
    }
  });

  const starColor = useMemo(() => {
    // Safe color mapping
    const colorMap: { [key: string]: string } = {
      'O': '#9bb0ff',
      'B': '#aabfff', 
      'A': '#cad7ff',
      'F': '#f8f7ff',
      'G': '#fff4ea',
      'K': '#ffd2a1',
      'M': '#ffad51'
    };
    return colorMap[star?.spectral_type?.[0]] || '#ffffff';
  }, [star?.spectral_type]);

  return (
    <group position={position} onClick={onClick}>
      {/* Main star - using only basic properties */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[0.5, 16, 16]} />
        <meshBasicMaterial color={starColor} />
      </mesh>
      
      {/* Selection indicator - simple ring */}
      {isSelected && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[1, 1.2, 16]} />
          <meshBasicMaterial color="#00FFFF" />
        </mesh>
      )}
    </group>
  );
};

// Safe fleet marker
const SafeFleetMarker: React.FC<{ 
  fleet: any; 
  systemPosition: [number, number, number];
}> = ({ fleet, systemPosition }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.02;
    }
  });

  return (
    <mesh 
      ref={meshRef} 
      position={[systemPosition[0], systemPosition[1] + 2, systemPosition[2]]}
    >
      <boxGeometry args={[0.3, 0.3, 0.3]} />
      <meshBasicMaterial color="#00FF00" />
    </mesh>
  );
};

const SafeGalaxy3D: React.FC = () => {
  const { stellarData, fleets, selectedSystemId, selectSystem } = useGameStore();

  // Safe data processing
  const displayStars = useMemo(() => {
    if (!Array.isArray(stellarData)) return [];
    
    return stellarData.slice(0, 100).map(star => {
      if (!star || typeof star.x !== 'number' || typeof star.y !== 'number' || typeof star.z !== 'number') {
        return null;
      }
      
      return {
        ...star,
        position3D: [star.x * 10, star.y * 10, star.z * 10] as [number, number, number]
      };
    }).filter(Boolean);
  }, [stellarData]);

  // Safe fleet processing
  const validFleets = useMemo(() => {
    if (!Array.isArray(fleets)) return [];
    return fleets.filter(fleet => fleet && fleet.id && fleet.system_id);
  }, [fleets]);

  console.log('🌌 SafeGalaxy3D: Rendering with', displayStars.length, 'stars and', validFleets.length, 'fleets');

  return (
    <div className="w-full h-full relative">
      <Canvas
        camera={{ position: [20, 15, 20], fov: 60 }}
        style={{ background: 'transparent' }}
        gl={{ 
          antialias: false, // Disable antialiasing to reduce shader complexity
          alpha: true,
          powerPreference: "high-performance"
        }}
        onError={(error) => {
          console.error('🚨 SafeGalaxy3D Canvas error:', error);
        }}
      >
        {/* Simple lighting */}
        <ambientLight intensity={0.6} />
        
        {/* Background stars */}
        <Stars
          radius={50}
          depth={20}
          count={500}
          factor={2}
          saturation={0}
          fade
        />

        {/* Safe star systems */}
        {displayStars.map((star, index) => {
          if (!star || !star.position3D) return null;
          
          return (
            <SafeStar
              key={star.star_id || index}
              star={star}
              position={star.position3D}
              isSelected={selectedSystemId === star.star_id?.toString()}
              onClick={() => star.star_id && selectSystem(star.star_id.toString())}
            />
          );
        })}

        {/* Safe fleet markers */}
        {validFleets.map(fleet => {
          const starId = fleet.system_id.replace('sys-', '');
          const star = displayStars.find(s => s && s.star_id && s.star_id.toString() === starId);
          if (!star || !star.position3D) return null;

          return (
            <SafeFleetMarker
              key={fleet.id}
              fleet={fleet}
              systemPosition={star.position3D}
            />
          );
        })}

        {/* Camera controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          maxDistance={100}
          minDistance={5}
          dampingFactor={0.1}
          enableDamping
        />
      </Canvas>

      {/* Safe overlay */}
      <div className="absolute top-4 left-4 text-white">
        <div className="glass-panel p-4 backdrop-blur-md">
          <h3 className="text-lg font-bold mb-2 text-cyan-400">🛡️ Safe Galaxy View</h3>
          <div className="text-sm space-y-1">
            <div>Stars: {displayStars.length}</div>
            <div>Fleets: {validFleets.length}</div>
            <div>Selected: {selectedSystemId || 'None'}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SafeGalaxy3D;
