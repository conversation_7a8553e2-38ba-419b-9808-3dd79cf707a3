import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PlanetDetailView from '../PlanetDetailView';

// Mock the stellar API
jest.mock('../../api/stellarApi', () => ({
  getStarDetail: jest.fn()
}));

// Mock Three.js components
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children }: any) => <div data-testid="canvas">{children}</div>,
  useFrame: jest.fn(),
  useThree: () => ({ camera: { position: { set: jest.fn() } } })
}));

jest.mock('@react-three/drei', () => ({
  OrbitControls: () => <div data-testid="orbit-controls" />,
  Stars: () => <div data-testid="stars" />,
  Sparkles: () => <div data-testid="sparkles" />,
  Float: ({ children }: any) => <div data-testid="float">{children}</div>,
  Html: ({ children }: any) => <div data-testid="html">{children}</div>,
  Ring: ({ children }: any) => <div data-testid="ring">{children}</div>
}));

// Mock Three.js
jest.mock('three', () => ({
  ...jest.requireActual('three'),
  BackSide: 1,
  DoubleSide: 2
}));

const mockEarthData = {
  planet_id: 3,
  name: 'Earth',
  composition: 'rocky',
  mass_earth: 1.0,
  radius_earth: 1.0,
  sma_au: 1.0,
  period_days: 365.25,
  has_atmosphere: true,
  atmosphere: 'N2, O2',
  atmospheric_effects: 'moderate',
  surface_temp_k: 288,
  dominant_color: 'Blue-Green',
  color_hex: '#6B93D6',
  raw_materials: 'Silicates, iron, aluminum, calcium, carbon, water (oceans), diverse mineral resources',
  material_category: 'Scientific',
  mineral_richness: 0.8,
  in_habitable_zone: true,
  exploration_status: 'explored',
  discovery_year: null,
  moons: [
    {
      moon_id: 1,
      planet_id: 3,
      name: 'Luna',
      distance_km: 384400,
      orbital_period_days: 27.3,
      diameter_km: 3474.8,
      mass_earth: 0.0123,
      radius_earth: 0.273,
      surface_composition: 'Anorthosite highlands, basaltic maria',
      raw_materials: 'Oxygen (oxides), silicon, aluminum, calcium, iron, magnesium, titanium, trace hydrogen at poles',
      material_category: 'Scientific',
      dominant_color: 'Gray',
      color_hex: '#C0C0C0',
      albedo: 0.136,
      visual_texture: 'cratered_maria',
      discovery_year: null,
      moon_type: 'regular',
      mineral_richness: 0.3,
      exploration_status: 'unexplored'
    }
  ]
};

const mockStarData = {
  star_id: 1,
  name: 'Sol',
  planets: [mockEarthData]
};

describe('PlanetDetailView', () => {
  const mockOnBack = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    const { getStarDetail } = require('../../api/stellarApi');
    getStarDetail.mockResolvedValue(mockStarData);
  });

  test('renders planet detail view with Earth data', async () => {
    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Earth')).toBeInTheDocument();
    });

    // Check for enhanced visual elements
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
    expect(screen.getByTestId('stars')).toBeInTheDocument();
    expect(screen.getByTestId('sparkles')).toBeInTheDocument();
  });

  test('displays moon information correctly', async () => {
    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Earth')).toBeInTheDocument();
    });

    // Check for moon section
    expect(screen.getByText(/Moons \(1\)/)).toBeInTheDocument();
    expect(screen.getByText('Luna')).toBeInTheDocument();
    expect(screen.getByText(/Period: 27\.3 days/)).toBeInTheDocument();
  });

  test('displays enhanced planet properties', async () => {
    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Earth')).toBeInTheDocument();
    });

    // Check for enhanced visual properties
    expect(screen.getByText(/Physical Properties/)).toBeInTheDocument();
    expect(screen.getByText(/Orbital Properties/)).toBeInTheDocument();
    expect(screen.getByText(/Raw Materials/)).toBeInTheDocument();
    
    // Check for habitability indicator
    expect(screen.getByText(/In Habitable Zone/)).toBeInTheDocument();
  });

  test('handles atmospheric effects correctly', async () => {
    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Earth')).toBeInTheDocument();
    });

    // Earth should show atmospheric information
    expect(screen.getByText(/N2, O2/)).toBeInTheDocument();
  });

  test('displays material properties and mineral richness', async () => {
    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Earth')).toBeInTheDocument();
    });

    // Check for material information
    expect(screen.getByText(/Silicates, iron, aluminum/)).toBeInTheDocument();
    expect(screen.getByText(/Mineral Richness: 80%/)).toBeInTheDocument();
  });

  test('handles gas giant composition correctly', async () => {
    const jupiterData = {
      ...mockEarthData,
      name: 'Jupiter',
      composition: 'gas_giant',
      has_atmosphere: true,
      atmospheric_effects: 'thick',
      dominant_color: 'Orange-Brown'
    };

    const mockJupiterStar = {
      ...mockStarData,
      planets: [jupiterData]
    };

    const { getStarDetail } = require('../../api/stellarApi');
    getStarDetail.mockResolvedValue(mockJupiterStar);

    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Jupiter')).toBeInTheDocument();
    });

    // Gas giants should show enhanced atmospheric effects
    expect(screen.getByText(/gas_giant/)).toBeInTheDocument();
  });

  test('handles loading and error states', async () => {
    const { getStarDetail } = require('../../api/stellarApi');
    getStarDetail.mockRejectedValue(new Error('API Error'));

    render(
      <PlanetDetailView
        planetId={3}
        starId={1}
        onBack={mockOnBack}
      />
    );

    // Should show loading initially
    expect(screen.getByText(/Loading planet data/)).toBeInTheDocument();

    // Should show error after API fails
    await waitFor(() => {
      expect(screen.getByText(/Error loading planet data/)).toBeInTheDocument();
    });
  });
});
