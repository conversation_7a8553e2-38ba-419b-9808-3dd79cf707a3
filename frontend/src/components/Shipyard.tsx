import React, { useState, useEffect } from 'react';
import { useGameStore } from '../store/gameStore';

interface ShipClass {
  id: string;
  name: string;
  description: string;
  cost: {
    energy: number;
    minerals: number;
    alloys: number;
  };
  stats: {
    supply: number;
    speed: number;
    firepower: number;
    defense: number;
  };
  buildTime: number; // in turns
}

const SHIP_CLASSES: ShipClass[] = [
  {
    id: 'scout',
    name: 'Scout Frigate',
    description: 'Fast, lightly armed vessel for exploration and reconnaissance.',
    cost: { energy: 100, minerals: 50, alloys: 25 },
    stats: { supply: 50, speed: 3, firepower: 1, defense: 1 },
    buildTime: 2
  },
  {
    id: 'corvette',
    name: 'Corvette',
    description: 'Small, agile warship for patrol and escort duties.',
    cost: { energy: 150, minerals: 75, alloys: 40 },
    stats: { supply: 75, speed: 2, firepower: 2, defense: 2 },
    buildTime: 3
  },
  {
    id: 'destroyer',
    name: 'Destroyer',
    description: 'Medium warship with balanced offensive and defensive capabilities.',
    cost: { energy: 300, minerals: 150, alloys: 80 },
    stats: { supply: 150, speed: 2, firepower: 4, defense: 3 },
    buildTime: 5
  },
  {
    id: 'cruiser',
    name: 'Cruiser',
    description: 'Heavy warship capable of independent operations.',
    cost: { energy: 500, minerals: 250, alloys: 150 },
    stats: { supply: 250, speed: 1, firepower: 6, defense: 5 },
    buildTime: 8
  },
  {
    id: 'battleship',
    name: 'Battleship',
    description: 'Massive capital ship with devastating firepower.',
    cost: { energy: 1000, minerals: 500, alloys: 300 },
    stats: { supply: 500, speed: 1, firepower: 12, defense: 10 },
    buildTime: 12
  }
];

const Shipyard: React.FC = () => {
  const { createFleet, selectedSystemId, systems, isLoading, error } = useGameStore();
  const [selectedShip, setSelectedShip] = useState<ShipClass | null>(null);
  const [fleetName, setFleetName] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [buildQueue, setBuildQueue] = useState<Array<{
    id: string;
    shipClass: ShipClass;
    quantity: number;
    systemId: string;
    turnsRemaining: number;
  }>>([]);

  // Mock resources - in a real game, this would come from the game state
  const [resources] = useState({
    energy: 2000,
    minerals: 1500,
    alloys: 800
  });

  const selectedSystem = systems.find(s => s.id === selectedSystemId);

  const calculateTotalCost = (shipClass: ShipClass, qty: number) => ({
    energy: shipClass.cost.energy * qty,
    minerals: shipClass.cost.minerals * qty,
    alloys: shipClass.cost.alloys * qty
  });

  const canAfford = (shipClass: ShipClass, qty: number) => {
    const totalCost = calculateTotalCost(shipClass, qty);
    return resources.energy >= totalCost.energy &&
           resources.minerals >= totalCost.minerals &&
           resources.alloys >= totalCost.alloys;
  };

  const handlePurchaseFleet = async () => {
    if (!selectedShip || !selectedSystemId || !fleetName.trim()) {
      return;
    }

    if (!canAfford(selectedShip, quantity)) {
      alert('Insufficient resources!');
      return;
    }

    try {
      // Create the fleet using the proper API
      await createFleet({
        empire_id: 'player-empire', // This should come from auth/game state
        system_id: selectedSystemId,
        stance: 'neutral',
        supply: selectedShip.stats.supply * quantity,
        // Additional metadata could be stored here
        metadata: {
          shipClass: selectedShip.id,
          shipCount: quantity,
          fleetName: fleetName
        }
      });

      // Add to build queue for visual feedback
      setBuildQueue(prev => [...prev, {
        id: `build-${Date.now()}`,
        shipClass: selectedShip,
        quantity,
        systemId: selectedSystemId,
        turnsRemaining: selectedShip.buildTime
      }]);

      // Reset form
      setSelectedShip(null);
      setFleetName('');
      setQuantity(1);

      alert(`Fleet "${fleetName}" ordered successfully! It will be ready in ${selectedShip.buildTime} turns.`);
    } catch (err) {
      console.error('Failed to create fleet:', err);
      alert('Failed to create fleet. Please try again.');
    }
  };

  return (
    <div className="text-white p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-cyan-400">🚀 Imperial Shipyard</h2>
        <div className="text-sm text-gray-400">
          {selectedSystem ? `Building at: ${selectedSystem.name}` : 'Select a system to build fleets'}
        </div>
      </div>

      {/* Resource Display */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-800 p-3 rounded-lg">
          <div className="text-yellow-400 font-semibold">⚡ Energy</div>
          <div className="text-xl">{resources.energy.toLocaleString()}</div>
        </div>
        <div className="bg-gray-800 p-3 rounded-lg">
          <div className="text-blue-400 font-semibold">🗿 Minerals</div>
          <div className="text-xl">{resources.minerals.toLocaleString()}</div>
        </div>
        <div className="bg-gray-800 p-3 rounded-lg">
          <div className="text-purple-400 font-semibold">🔩 Alloys</div>
          <div className="text-xl">{resources.alloys.toLocaleString()}</div>
        </div>
      </div>

      {!selectedSystemId && (
        <div className="bg-yellow-900 border border-yellow-600 rounded-lg p-4 mb-6">
          <div className="text-yellow-200">
            ⚠️ Please select a star system on the galaxy map to build fleets there.
          </div>
        </div>
      )}

      {/* Ship Classes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {SHIP_CLASSES.map(shipClass => {
          const totalCost = calculateTotalCost(shipClass, quantity);
          const affordable = canAfford(shipClass, quantity);

          return (
            <div
              key={shipClass.id}
              onClick={() => setSelectedShip(shipClass)}
              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                selectedShip?.id === shipClass.id
                  ? 'border-cyan-400 bg-cyan-900 bg-opacity-20'
                  : affordable
                  ? 'border-gray-600 hover:border-gray-500 bg-gray-800'
                  : 'border-red-600 bg-red-900 bg-opacity-20 opacity-60'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-bold text-lg">{shipClass.name}</h3>
                <div className="text-xs text-gray-400">{shipClass.buildTime} turns</div>
              </div>
              
              <p className="text-sm text-gray-300 mb-3">{shipClass.description}</p>
              
              <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                <div>Supply: {shipClass.stats.supply}</div>
                <div>Speed: {shipClass.stats.speed}</div>
                <div>Firepower: {shipClass.stats.firepower}</div>
                <div>Defense: {shipClass.stats.defense}</div>
              </div>
              
              <div className="border-t border-gray-600 pt-2">
                <div className="text-xs space-y-1">
                  <div className={`${totalCost.energy <= resources.energy ? 'text-yellow-400' : 'text-red-400'}`}>
                    ⚡ {totalCost.energy.toLocaleString()}
                  </div>
                  <div className={`${totalCost.minerals <= resources.minerals ? 'text-blue-400' : 'text-red-400'}`}>
                    🗿 {totalCost.minerals.toLocaleString()}
                  </div>
                  <div className={`${totalCost.alloys <= resources.alloys ? 'text-purple-400' : 'text-red-400'}`}>
                    🔩 {totalCost.alloys.toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Fleet Configuration */}
      {selectedShip && selectedSystemId && (
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-4 text-cyan-400">Configure Fleet</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Fleet Name</label>
              <input
                type="text"
                value={fleetName}
                onChange={(e) => setFleetName(e.target.value)}
                placeholder="Enter fleet name..."
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Quantity</label>
              <input
                type="number"
                min="1"
                max="10"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div className="flex items-end">
              <button
                onClick={handlePurchaseFleet}
                disabled={!fleetName.trim() || !canAfford(selectedShip, quantity) || isLoading}
                className={`w-full px-4 py-2 rounded font-semibold transition-colors ${
                  !fleetName.trim() || !canAfford(selectedShip, quantity) || isLoading
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-500 text-white'
                }`}
              >
                {isLoading ? 'Building...' : 'Build Fleet'}
              </button>
            </div>
          </div>
          
          {selectedShip && (
            <div className="mt-4 p-3 bg-gray-700 rounded">
              <div className="text-sm">
                <div className="font-semibold mb-2">Fleet Summary:</div>
                <div>• {quantity}x {selectedShip.name}</div>
                <div>• Total Supply: {selectedShip.stats.supply * quantity}</div>
                <div>• Build Time: {selectedShip.buildTime} turns</div>
                <div>• Location: {selectedSystem?.name}</div>
              </div>
            </div>
          )}
        </div>
      )}



      {/* Build Queue */}
      {buildQueue.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4 text-cyan-400">🔨 Build Queue</h3>
          <div className="space-y-2">
            {buildQueue.map(item => (
              <div key={item.id} className="flex justify-between items-center p-2 bg-gray-700 rounded">
                <div>
                  <div className="font-semibold">{item.quantity}x {item.shipClass.name}</div>
                  <div className="text-sm text-gray-400">
                    Building at: {systems.find(s => s.id === item.systemId)?.name}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-yellow-400 font-semibold">
                    {item.turnsRemaining} turns remaining
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-900 border border-red-600 rounded-lg p-4 mt-4">
          <div className="text-red-200">Error: {error}</div>
        </div>
      )}
    </div>
  );
};

export default Shipyard;
