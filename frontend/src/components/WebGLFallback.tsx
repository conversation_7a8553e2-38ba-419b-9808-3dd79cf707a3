import React from 'react';
import { stellarApi } from '../services/stellarApi';
import type { Star, StarDetail } from '../types/stellar';

interface WebGLFallbackProps {
  onRetry?: () => void;
}

const WebGLFallback: React.FC<WebGLFallbackProps> = ({ onRetry }) => {
  const checkWebGLSupport = () => {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  };

  const webglSupported = checkWebGLSupport();

  return (
    <div className="webgl-fallback" style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      textAlign: 'center',
      padding: '20px',
      zIndex: 1000
    }}>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '15px',
        padding: '40px',
        maxWidth: '600px',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h1 style={{
          fontSize: '2.5em',
          marginBottom: '20px',
          background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          🌌 Galactic Genesis
        </h1>
        
        <div style={{
          fontSize: '1.2em',
          marginBottom: '30px',
          lineHeight: '1.6'
        }}>
          {!webglSupported ? (
            <>
              <h2 style={{ color: '#ff6b6b', marginBottom: '20px' }}>
                ⚠️ WebGL Not Available
              </h2>
              <p style={{ marginBottom: '15px' }}>
                Your browser or system doesn't support WebGL, which is required for the 3D galaxy visualization.
              </p>
              <p style={{ marginBottom: '20px' }}>
                This could be due to:
              </p>
              <ul style={{ textAlign: 'left', marginBottom: '20px' }}>
                <li>Hardware acceleration disabled</li>
                <li>Outdated graphics drivers</li>
                <li>Browser security settings</li>
                <li>Virtual machine environment</li>
              </ul>
            </>
          ) : (
            <>
              <h2 style={{ color: '#ffa726', marginBottom: '20px' }}>
                🔧 3D Rendering Issue
              </h2>
              <p style={{ marginBottom: '15px' }}>
                WebGL is supported but the 3D context failed to initialize.
              </p>
            </>
          )}
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3 style={{ color: '#4ecdc4', marginBottom: '15px' }}>
            🛠️ Try These Solutions:
          </h3>
          <div style={{ textAlign: 'left', fontSize: '1em' }}>
            <p>• <strong>Enable Hardware Acceleration</strong> in your browser settings</p>
            <p>• <strong>Update your graphics drivers</strong></p>
            <p>• <strong>Try a different browser</strong> (Chrome, Firefox, Edge)</p>
            <p>• <strong>Disable browser extensions</strong> temporarily</p>
            <p>• <strong>Check for browser updates</strong></p>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
          {onRetry && (
            <button
              onClick={onRetry}
              style={{
                background: 'linear-gradient(45deg, #4ecdc4, #44a08d)',
                border: 'none',
                color: 'white',
                padding: '12px 24px',
                borderRadius: '25px',
                cursor: 'pointer',
                fontSize: '1em',
                fontWeight: 'bold',
                transition: 'transform 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
              onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
            >
              🔄 Retry
            </button>
          )}
          
          <button
            onClick={() => window.location.href = 'https://get.webgl.org/'}
            style={{
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              border: 'none',
              color: 'white',
              padding: '12px 24px',
              borderRadius: '25px',
              cursor: 'pointer',
              fontSize: '1em',
              fontWeight: 'bold',
              transition: 'transform 0.2s'
            }}
            onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
            onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
          >
            🔍 Test WebGL
          </button>
        </div>

        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'rgba(255, 255, 255, 0.05)',
          borderRadius: '10px',
          fontSize: '0.9em',
          color: '#ccc'
        }}>
          <p><strong>System Info:</strong></p>
          <p>WebGL Support: {webglSupported ? '✅ Available' : '❌ Not Available'}</p>
          <p>User Agent: {navigator.userAgent.substring(0, 50)}...</p>
        </div>
      </div>
    </div>
  );
};

export default WebGLFallback;
