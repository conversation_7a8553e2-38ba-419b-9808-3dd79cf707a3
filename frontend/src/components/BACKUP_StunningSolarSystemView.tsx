// BACKUP: Original StunningSolarSystemView.tsx
// This is a backup of the hardcoded solar system view that was used by the Solar button
// Created as backup before deletion to allow recovery if needed
// Date: 2025-09-20

import React, { useRef, useEffect, useState, useMemo, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import {
  OrbitControls,
  Float,
  Text,
  Html,
  Stars,
  Sparkles,
  MeshDistortMaterial,
  Ring
} from '@react-three/drei';
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';

// This component was hardcoded for Sol only and used by the Solar button
// It has been replaced by the universal SolarSystemView component
// Keep this backup for reference and potential recovery

interface StunningSolarSystemViewProps {
  onClose: () => void;
  onPlanetClick?: (planetName: string) => void;
}

// BACKUP CONTENT - This file contained the hardcoded solar system implementation
// The actual content has been moved to BACKUP_StunningSolarSystemView_FULL.tsx
// to avoid exceeding the 300 line limit

export default function BackupNotice() {
  return (
    <div className="w-full h-full flex items-center justify-center bg-black text-white">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">🚧 Component Backed Up</h2>
        <p className="text-gray-400">
          The original StunningSolarSystemView has been backed up.
        </p>
        <p className="text-gray-400">
          See BACKUP_StunningSolarSystemView_FULL.tsx for complete content.
        </p>
      </div>
    </div>
  );
}
