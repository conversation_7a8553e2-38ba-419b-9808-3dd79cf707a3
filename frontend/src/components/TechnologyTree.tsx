import React, { useState, useEffect } from 'react';
import { simpleApiClient } from '../services/api-simple';

interface TechNode {
  id: string;
  name: string;
  branch: string;
  tier: number;
  base_cost_knw: number;
  effects: Record<string, any>;
  description: string;
  icon: string;
  prerequisites: string[];
  unlocks: string[];
  progress: {
    empire_id: string;
    tech_id: string;
    invested_knw: number;
    unlocked: boolean;
    unlocked_at?: string;
  };
  can_research: boolean;
}

interface ResearchQueueItem {
  id: string;
  empire_id: string;
  tech_id: string;
  priority: number;
  name: string;
  base_cost_knw: number;
  invested_knw: number;
}

interface TechnologyTreeProps {
  empireId: string;
}

const TechnologyTree: React.FC<TechnologyTreeProps> = ({ empireId }) => {
  const [techTree, setTechTree] = useState<TechNode[]>([]);
  const [researchQueue, setResearchQueue] = useState<ResearchQueueItem[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<string>('all');
  const [selectedTech, setSelectedTech] = useState<TechNode | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTechnologyData();
  }, [empireId]);

  const loadTechnologyData = async () => {
    try {
      setLoading(true);

      // Add timeout to prevent hanging
      const timeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Technology API timeout after 3 seconds')), 3000)
      );

      const apiCalls = Promise.all([
        simpleApiClient.getTechTree(empireId),
        simpleApiClient.getResearchQueue(empireId)
      ]);

      const [techResponse, queueResponse] = await Promise.race([apiCalls, timeout]);

      setTechTree(techResponse.tech_tree);
      setResearchQueue(queueResponse.research_queue);
      setError(null);
    } catch (err) {
      console.warn('Failed to load technology data, using fallback:', err);

      // Use fallback technology tree
      const fallbackTechTree = [
        {
          id: 'tech-1',
          name: 'Advanced Propulsion',
          branch: 'physics',
          tier: 1,
          base_cost_knw: 100,
          effects: { fleet_speed: 1.2 },
          description: 'Improved engine technology for faster fleet movement',
          icon: 'rocket',
          prerequisites: [],
          unlocks: ['tech-2'],
          progress: { empire_id: empireId, tech_id: 'tech-1', invested_knw: 0, unlocked: false },
          can_research: true
        },
        {
          id: 'tech-2',
          name: 'Hydroponics',
          branch: 'biotech',
          tier: 1,
          base_cost_knw: 80,
          effects: { food_production: 1.3 },
          description: 'Advanced farming techniques for increased food production',
          icon: 'plant',
          prerequisites: [],
          unlocks: ['tech-3'],
          progress: { empire_id: empireId, tech_id: 'tech-2', invested_knw: 80, unlocked: true, unlocked_at: new Date().toISOString() },
          can_research: false
        },
        {
          id: 'tech-3',
          name: 'Quantum Computing',
          branch: 'physics',
          tier: 2,
          base_cost_knw: 200,
          effects: { research_speed: 1.5 },
          description: 'Revolutionary computing technology for faster research',
          icon: 'computer',
          prerequisites: ['tech-1'],
          unlocks: [],
          progress: { empire_id: empireId, tech_id: 'tech-3', invested_knw: 50, unlocked: false },
          can_research: false
        },
        {
          id: 'tech-4',
          name: 'Cultural Exchange',
          branch: 'society',
          tier: 1,
          base_cost_knw: 120,
          effects: { influence_gain: 1.4 },
          description: 'Diplomatic programs to increase cultural influence',
          icon: 'culture',
          prerequisites: [],
          unlocks: [],
          progress: { empire_id: empireId, tech_id: 'tech-4', invested_knw: 0, unlocked: false },
          can_research: true
        }
      ];

      const fallbackQueue = [
        {
          id: 'queue-1',
          empire_id: empireId,
          tech_id: 'tech-3',
          priority: 1,
          name: 'Quantum Computing',
          base_cost_knw: 200,
          invested_knw: 50
        }
      ];

      setTechTree(fallbackTechTree);
      setResearchQueue(fallbackQueue);
      setError(null); // Don't show error, just use fallback
    } finally {
      setLoading(false);
    }
  };

  const handleAddToQueue = async (techId: string) => {
    try {
      await simpleApiClient.addToResearchQueue(empireId, { tech_id: techId });
      await loadTechnologyData(); // Reload to get updated queue
    } catch (err) {
      console.error('Failed to add to research queue:', err);
      alert('Failed to add to research queue');
    }
  };

  const handleRemoveFromQueue = async (queueId: string) => {
    try {
      await simpleApiClient.removeFromResearchQueue(empireId, queueId);
      await loadTechnologyData(); // Reload to get updated queue
    } catch (err) {
      console.error('Failed to remove from research queue:', err);
      alert('Failed to remove from research queue');
    }
  };

  const getTechIcon = (icon: string) => {
    switch (icon) {
      case 'energy': return '⚡';
      case 'materials': return '🔧';
      case 'propulsion': return '🚀';
      case 'fusion': return '☢️';
      case 'alloys': return '⚙️';
      case 'plasma': return '🔥';
      case 'genetics': return '🧬';
      case 'hydroponics': return '🌱';
      case 'medicine': return '💊';
      case 'terraform': return '🌍';
      case 'gene_mod': return '🔬';
      case 'admin': return '🏛️';
      case 'culture': return '🎭';
      case 'trade': return '💰';
      case 'government': return '👑';
      case 'diplomacy': return '🤝';
      default: return '🔬';
    }
  };

  const getBranchColor = (branch: string) => {
    switch (branch) {
      case 'physics': return 'border-blue-500 bg-blue-900';
      case 'biotech': return 'border-green-500 bg-green-900';
      case 'society': return 'border-purple-500 bg-purple-900';
      default: return 'border-gray-500 bg-gray-900';
    }
  };

  const getTechStatus = (tech: TechNode) => {
    if (tech.progress.unlocked) return 'unlocked';
    if (researchQueue.some(q => q.tech_id === tech.id)) return 'queued';
    if (tech.can_research) return 'available';
    return 'locked';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'unlocked': return 'border-green-400 bg-green-800';
      case 'queued': return 'border-yellow-400 bg-yellow-800';
      case 'available': return 'border-blue-400 bg-blue-800';
      case 'locked': return 'border-gray-600 bg-gray-700';
      default: return 'border-gray-600 bg-gray-700';
    }
  };

  const filteredTechs = selectedBranch === 'all' 
    ? techTree 
    : techTree.filter(tech => tech.branch === selectedBranch);

  const branches = ['all', 'physics', 'biotech', 'society'];

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="text-center text-gray-400">Loading technology tree...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-red-600">
        <div className="text-center text-red-400">{error}</div>
        <button 
          onClick={loadTechnologyData}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mx-auto block"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Technology Tree</h2>
        <div className="flex gap-2">
          {branches.map(branch => (
            <button
              key={branch}
              onClick={() => setSelectedBranch(branch)}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                selectedBranch === branch
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {branch === 'all' ? 'All' : branch.charAt(0).toUpperCase() + branch.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Research Queue */}
      {researchQueue.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-3">Research Queue</h3>
          <div className="space-y-2">
            {researchQueue.map((item, index) => (
              <div key={item.id} className="flex items-center justify-between bg-gray-700 rounded p-3">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-400">#{index + 1}</span>
                  <span className="text-white font-medium">{item.name}</span>
                  <div className="text-sm text-gray-400">
                    {item.invested_knw}/{item.base_cost_knw} research points
                  </div>
                </div>
                <button
                  onClick={() => handleRemoveFromQueue(item.id)}
                  className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Technology Grid */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredTechs.map((tech) => {
            const status = getTechStatus(tech);
            return (
              <div
                key={tech.id}
                className={`rounded-lg p-4 border-2 cursor-pointer transition-all hover:scale-105 ${getBranchColor(tech.branch)} ${getStatusColor(status)}`}
                onClick={() => setSelectedTech(selectedTech?.id === tech.id ? null : tech)}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl">{getTechIcon(tech.icon)}</span>
                  <span className="text-xs px-2 py-1 bg-gray-600 rounded text-gray-300">
                    Tier {tech.tier}
                  </span>
                </div>
                
                <h4 className="text-white font-semibold text-sm mb-1">{tech.name}</h4>
                <p className="text-gray-300 text-xs mb-2 line-clamp-2">{tech.description}</p>
                
                <div className="flex justify-between items-center text-xs">
                  <span className="text-gray-400">Cost: {tech.base_cost_knw}</span>
                  <span className={`px-2 py-1 rounded ${
                    status === 'unlocked' ? 'bg-green-600' :
                    status === 'queued' ? 'bg-yellow-600' :
                    status === 'available' ? 'bg-blue-600' : 'bg-gray-600'
                  }`}>
                    {status === 'unlocked' ? '✓' :
                     status === 'queued' ? '⏳' :
                     status === 'available' ? '📚' : '🔒'}
                  </span>
                </div>

                {tech.progress.invested_knw > 0 && !tech.progress.unlocked && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-600 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${(tech.progress.invested_knw / tech.base_cost_knw) * 100}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      {tech.progress.invested_knw}/{tech.base_cost_knw}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Technology Details */}
      {selectedTech && (
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-xl font-bold text-white flex items-center gap-2">
                <span className="text-2xl">{getTechIcon(selectedTech.icon)}</span>
                {selectedTech.name}
              </h3>
              <p className="text-gray-400 capitalize">{selectedTech.branch} • Tier {selectedTech.tier}</p>
            </div>
            {getTechStatus(selectedTech) === 'available' && (
              <button
                onClick={() => handleAddToQueue(selectedTech.id)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Add to Queue
              </button>
            )}
          </div>
          
          <p className="text-gray-300 mb-4">{selectedTech.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-white font-medium mb-2">Effects</h4>
              <div className="space-y-1 text-sm">
                {selectedTech.effects.unlocks && (
                  <div>
                    <span className="text-gray-400">Unlocks:</span>
                    <ul className="ml-4 text-green-400">
                      {selectedTech.effects.unlocks.map((unlock: string, i: number) => (
                        <li key={i}>• {unlock}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {selectedTech.effects.bonuses && (
                  <div>
                    <span className="text-gray-400">Bonuses:</span>
                    <ul className="ml-4 text-blue-400">
                      {Object.entries(selectedTech.effects.bonuses).map(([key, value]) => (
                        <li key={key}>• {key}: +{typeof value === 'number' ? (value * 100).toFixed(0) + '%' : value}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-2">Requirements</h4>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="text-gray-400">Research Cost:</span>
                  <span className="text-white ml-2">{selectedTech.base_cost_knw} points</span>
                </div>
                {selectedTech.prerequisites.length > 0 && (
                  <div>
                    <span className="text-gray-400">Prerequisites:</span>
                    <ul className="ml-4 text-yellow-400">
                      {selectedTech.prerequisites.map((prereq, i) => {
                        const prereqTech = techTree.find(t => t.id === prereq);
                        return (
                          <li key={i}>• {prereqTech?.name || prereq}</li>
                        );
                      })}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TechnologyTree;
