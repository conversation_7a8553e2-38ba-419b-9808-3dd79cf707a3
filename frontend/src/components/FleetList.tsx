import React from 'react';
import { useGameStore } from '../store/gameStore';
import type { Fleet } from '../types/game';

const FleetCard: React.FC<{ fleet: Fleet; isSelected: boolean; onClick: () => void }> = ({ 
  fleet, 
  isSelected, 
  onClick 
}) => {
  const getStanceColor = (stance: string) => {
    switch (stance) {
      case 'aggressive': return 'text-neon-red';
      case 'defensive': return 'text-neon-green';
      default: return 'text-neon-cyan';
    }
  };

  const getSupplyColor = (supply: number) => {
    if (supply > 80) return 'text-neon-green';
    if (supply > 40) return 'text-neon-gold';
    return 'text-neon-red';
  };

  return (
    <div
      onClick={onClick}
      className={`p-3 rounded-lg border cursor-pointer transition-all ${
        isSelected
          ? 'border-neon-cyan bg-cyan-900 bg-opacity-20 neon-glow'
          : 'border-gray-600 hover:border-gray-500 bg-gray-800 bg-opacity-50'
      }`}
    >
      <div className="flex justify-between items-start mb-2">
        <div className="text-sm text-gray-400">Fleet {fleet.id.slice(0, 8)}</div>
        <div className={`text-xs font-semibold ${getStanceColor(fleet.stance)}`}>
          {fleet.stance.toUpperCase()}
        </div>
      </div>
      
      <div className="mb-2">
        <div className="text-white font-semibold">{fleet.system_name}</div>
        <div className="text-xs text-gray-400">System {fleet.system_id}</div>
      </div>
      
      <div className="flex justify-between items-center">
        <div className="text-xs text-gray-400">Supply:</div>
        <div className={`font-bold ${getSupplyColor(fleet.supply)}`}>
          {fleet.supply}
        </div>
      </div>
    </div>
  );
};

const FleetList: React.FC = () => {
  const { 
    fleets, 
    selectedFleetId, 
    selectFleet, 
    showOrderFormFor 
  } = useGameStore();

  const selectedFleet = Array.isArray(fleets) ? fleets.find(f => f && f.id === selectedFleetId) : null;
  const fleetCount = Array.isArray(fleets) ? fleets.length : 0;

  return (
    <div data-testid="fleet-panel" className="text-white">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-neon-cyan">Fleet Command</h3>
        <div className="text-sm text-gray-400">
          {fleetCount} fleet{fleetCount !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Fleet list */}
      <div className="space-y-2 mb-6 max-h-64 overflow-y-auto">
        {Array.isArray(fleets) && fleets.map((fleet) => {
          if (!fleet || !fleet.id) return null;
          return (
            <FleetCard
              key={fleet.id}
              fleet={fleet}
              isSelected={fleet.id === selectedFleetId}
              onClick={() => selectFleet(fleet.id)}
            />
          );
        })}
      </div>
      
      {/* Fleet actions */}
      {selectedFleet && (
        <div className="border-t border-gray-700 pt-4">
          <h4 className="text-md font-semibold mb-3 text-neon-cyan">Fleet Actions</h4>
          <div className="space-y-2">
            <button
              onClick={() => showOrderFormFor('move')}
              className="w-full px-3 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded transition-colors text-sm"
            >
              Move Fleet
            </button>
            <button
              onClick={() => showOrderFormFor('attack')}
              className="w-full px-3 py-2 bg-red-600 hover:bg-red-500 text-white rounded transition-colors text-sm"
            >
              Attack
            </button>
            <button
              onClick={() => showOrderFormFor('resupply')}
              className="w-full px-3 py-2 bg-green-600 hover:bg-green-500 text-white rounded transition-colors text-sm"
            >
              Resupply
            </button>
          </div>
          
          {/* Selected fleet details */}
          <div className="mt-4 p-3 bg-gray-800 bg-opacity-50 rounded">
            <div className="text-xs text-gray-400 mb-2">Selected Fleet</div>
            <div className="text-sm">
              <div>ID: {selectedFleet.id.slice(0, 8)}...</div>
              <div>Location: {selectedFleet.system_name}</div>
              <div>Supply: {selectedFleet.supply}</div>
              <div>Stance: {selectedFleet.stance}</div>
            </div>
          </div>
        </div>
      )}
      
      {fleets.length === 0 && (
        <div className="text-center text-gray-400 py-8">
          <p>No fleets available</p>
          <p className="text-sm mt-2">Fleets will appear here once loaded</p>
        </div>
      )}
    </div>
  );
};

export default FleetList;
