import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Stars, Sparkles } from '@react-three/drei';
import StunningGalaxy3D from './StunningGalaxy3D';
import Galaxy3D from './Galaxy3D';

const GraphicsComparison: React.FC = () => {
  const [showStunning, setShowStunning] = useState(true);

  return (
    <div className="h-screen w-screen bg-gray-900 relative">
      {/* Toggle Controls */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-20 flex space-x-4">
        <button
          onClick={() => setShowStunning(false)}
          className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
            !showStunning 
              ? 'bg-red-600 text-white shadow-lg' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          📱 Basic Graphics
        </button>
        <button
          onClick={() => setShowStunning(true)}
          className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
            showStunning 
              ? 'bg-purple-600 text-white shadow-lg cosmic-glow' 
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
          }`}
        >
          ✨ Stunning Graphics
        </button>
      </div>

      {/* Comparison Info */}
      <div className="absolute top-20 left-4 z-20 max-w-sm">
        <div className="glass-panel p-4 rounded-lg">
          <h3 className="text-lg font-bold text-white mb-2">
            {showStunning ? '✨ Stunning Mode' : '📱 Basic Mode'}
          </h3>
          <div className="text-sm text-gray-300 space-y-2">
            {showStunning ? (
              <>
                <div className="text-green-400">✅ Custom star coronas with shaders</div>
                <div className="text-green-400">✅ Volumetric nebulae (4 regions)</div>
                <div className="text-green-400">✅ Galactic core with black hole</div>
                <div className="text-green-400">✅ Post-processing effects</div>
                <div className="text-green-400">✅ 8,000 background stars</div>
                <div className="text-green-400">✅ Advanced lighting (5 sources)</div>
                <div className="text-green-400">✅ Planetary system visualization</div>
                <div className="text-green-400">✅ Interactive hover effects</div>
                <div className="text-green-400">✅ Cinematic camera controls</div>
                <div className="text-green-400">✅ Dynamic materials & animations</div>
              </>
            ) : (
              <>
                <div className="text-red-400">❌ Basic sphere materials</div>
                <div className="text-red-400">❌ No nebulae</div>
                <div className="text-red-400">❌ No galactic core</div>
                <div className="text-red-400">❌ No post-processing</div>
                <div className="text-yellow-400">⚠️ 5,000 background stars</div>
                <div className="text-yellow-400">⚠️ Basic lighting (3 sources)</div>
                <div className="text-red-400">❌ No planetary systems</div>
                <div className="text-yellow-400">⚠️ Basic hover effects</div>
                <div className="text-yellow-400">⚠️ Standard camera controls</div>
                <div className="text-red-400">❌ Static materials</div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Performance Stats */}
      <div className="absolute top-20 right-4 z-20 max-w-xs">
        <div className="glass-panel p-4 rounded-lg">
          <h3 className="text-lg font-bold text-white mb-2">📊 Performance</h3>
          <div className="text-sm text-gray-300 space-y-2">
            <div className="flex justify-between">
              <span>Render Quality:</span>
              <span className={showStunning ? 'text-purple-400' : 'text-blue-400'}>
                {showStunning ? 'Ultra High' : 'Standard'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Shader Complexity:</span>
              <span className={showStunning ? 'text-purple-400' : 'text-green-400'}>
                {showStunning ? 'Advanced' : 'Basic'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Particle Count:</span>
              <span className={showStunning ? 'text-purple-400' : 'text-blue-400'}>
                {showStunning ? '8,500+' : '5,000'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Post-Processing:</span>
              <span className={showStunning ? 'text-purple-400' : 'text-gray-400'}>
                {showStunning ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Memory Usage:</span>
              <span className={showStunning ? 'text-yellow-400' : 'text-green-400'}>
                {showStunning ? 'High' : 'Low'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Highlights */}
      {showStunning && (
        <div className="absolute bottom-4 left-4 z-20 max-w-md">
          <div className="glass-panel cosmic-glow p-4 rounded-lg">
            <h3 className="text-lg font-bold nebula-text mb-2">🌟 Stunning Features</h3>
            <div className="text-xs text-gray-300 grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span>Corona Shaders</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse"></div>
                <span>Volumetric Nebulae</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Bloom Effects</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <span>Galactic Core</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Planetary Systems</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                <span>Dynamic Lighting</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Render the appropriate galaxy component */}
      <div className="w-full h-full">
        {showStunning ? <StunningGalaxy3D /> : <Galaxy3D />}
      </div>

      {/* Instructions */}
      <div className="absolute bottom-4 right-4 z-20">
        <div className="glass-panel p-3 rounded-lg text-xs text-gray-400">
          <div>🖱️ Mouse: Orbit camera</div>
          <div>🔍 Scroll: Zoom in/out</div>
          <div>👆 Click: Select systems</div>
          <div>🎮 Toggle: Switch graphics modes</div>
        </div>
      </div>
    </div>
  );
};

export default GraphicsComparison;
