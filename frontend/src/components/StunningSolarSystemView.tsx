import React, { useRef, useState, useEffect, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  Float,
  Html,
  Ring,
  Sparkles,
  MeshDistortMaterial
} from '@react-three/drei';
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';

// Enhanced realistic solar system data with stunning visuals
const SOLAR_SYSTEM_DATA = {
  mercury: {
    distance: 0.39,
    radius: 0.383,
    color: '#8C7853',
    period: 88,
    name: 'Mercury',
    emissiveColor: '#B8860B',
    roughness: 0.9,
    metalness: 0.1,
    hasAtmosphere: false
  },
  venus: {
    distance: 0.72,
    radius: 0.949,
    color: '#FFC649',
    period: 225,
    name: 'Venus',
    emissiveColor: '#FFD700',
    roughness: 0.3,
    metalness: 0.0,
    hasAtmosphere: true,
    atmosphereColor: '#FFFF99'
  },
  earth: {
    distance: 1.0,
    radius: 1.0,
    color: '#6B93D6',
    period: 365,
    name: 'Earth',
    emissiveColor: '#4169E1',
    roughness: 0.7,
    metalness: 0.2,
    hasAtmosphere: true,
    atmosphereColor: '#87CEEB'
  },
  mars: {
    distance: 1.52,
    radius: 0.532,
    color: '#CD5C5C',
    period: 687,
    name: 'Mars',
    emissiveColor: '#B22222',
    roughness: 0.8,
    metalness: 0.1,
    hasAtmosphere: true,
    atmosphereColor: '#F4A460'
  },
  jupiter: {
    distance: 5.2,
    radius: 11.21,
    color: '#D2691E',
    period: 4333,
    name: 'Jupiter',
    emissiveColor: '#FF8C00',
    roughness: 0.2,
    metalness: 0.0,
    hasAtmosphere: true,
    atmosphereColor: '#DEB887',
    hasStorms: true
  },
  saturn: {
    distance: 9.5,
    radius: 9.45,
    color: '#F4A460',
    period: 10759,
    name: 'Saturn',
    emissiveColor: '#DAA520',
    roughness: 0.3,
    metalness: 0.0,
    hasAtmosphere: true,
    atmosphereColor: '#F5DEB3',
    hasRings: true
  },
  uranus: {
    distance: 19.2,
    radius: 4.01,
    color: '#4FD0E7',
    period: 30687,
    name: 'Uranus',
    emissiveColor: '#00CED1',
    roughness: 0.3,
    metalness: 0.0,
    hasAtmosphere: true,
    atmosphereColor: '#AFEEEE'
  },
  neptune: {
    distance: 30.1,
    radius: 3.88,
    color: '#4B70DD',
    period: 60190,
    name: 'Neptune',
    emissiveColor: '#1E90FF',
    roughness: 0.3,
    metalness: 0.0,
    hasAtmosphere: true,
    atmosphereColor: '#6495ED'
  }
};

// Comprehensive moon data for all planets (distances in planet radii, periods in Earth days)
const MOON_DATA = {
  earth: [
    { name: 'Moon', distance: 60.3, radius: 0.273, period: 27.3, color: '#C0C0C0' }
  ],
  mars: [
    { name: 'Phobos', distance: 2.76, radius: 0.0018, period: 0.32, color: '#8C7853' },
    { name: 'Deimos', distance: 6.94, radius: 0.00097, period: 1.26, color: '#8C7853' }
  ],
  jupiter: [
    // Inner moons
    { name: 'Metis', distance: 1.79, radius: 0.0034, period: 0.29, color: '#8C7853' },
    { name: 'Adrastea', distance: 1.81, radius: 0.0013, period: 0.30, color: '#8C7853' },
    { name: 'Amalthea', distance: 2.54, radius: 0.0134, period: 0.50, color: '#8C7853' },
    { name: 'Thebe', distance: 3.11, radius: 0.0078, period: 0.67, color: '#8C7853' },
    // Galilean moons
    { name: 'Io', distance: 5.90, radius: 0.286, period: 1.77, color: '#FFFF99' },
    { name: 'Europa', distance: 9.40, radius: 0.245, period: 3.55, color: '#87CEEB' },
    { name: 'Ganymede', distance: 15.0, radius: 0.413, period: 7.15, color: '#8C7853' },
    { name: 'Callisto', distance: 26.3, radius: 0.378, period: 16.69, color: '#696969' },
    // Major irregular moons (scaled distances)
    { name: 'Themisto', distance: 103, radius: 0.0063, period: 130, color: '#8C7853' },
    { name: 'Leda', distance: 155, radius: 0.0016, period: 241, color: '#8C7853' },
    { name: 'Himalia', distance: 160, radius: 0.0134, period: 251, color: '#8C7853' },
    { name: 'Lysithea', distance: 164, radius: 0.0028, period: 259, color: '#8C7853' },
    { name: 'Elara', distance: 165, radius: 0.0054, period: 260, color: '#8C7853' },
    { name: 'Ananke', distance: 295, radius: 0.0022, period: 631, color: '#8C7853' },
    { name: 'Carme', distance: 314, radius: 0.0036, period: 692, color: '#8C7853' },
    { name: 'Pasiphae', distance: 329, radius: 0.0047, period: 744, color: '#8C7853' },
    { name: 'Sinope', distance: 334, radius: 0.0030, period: 758, color: '#8C7853' }
  ],
  saturn: [
    // Inner moons
    { name: 'Pan', distance: 2.21, radius: 0.0022, period: 0.58, color: '#8C7853' },
    { name: 'Daphnis', distance: 2.26, radius: 0.0006, period: 0.59, color: '#8C7853' },
    { name: 'Atlas', distance: 2.28, radius: 0.0024, period: 0.60, color: '#8C7853' },
    { name: 'Prometheus', distance: 2.31, radius: 0.0069, period: 0.61, color: '#8C7853' },
    { name: 'Pandora', distance: 2.35, radius: 0.0068, period: 0.63, color: '#8C7853' },
    { name: 'Epimetheus', distance: 2.51, radius: 0.0092, period: 0.69, color: '#8C7853' },
    { name: 'Janus', distance: 2.51, radius: 0.0142, period: 0.69, color: '#8C7853' },
    // Major moons
    { name: 'Mimas', distance: 3.08, radius: 0.0311, period: 0.94, color: '#C0C0C0' },
    { name: 'Enceladus', distance: 3.95, radius: 0.0396, period: 1.37, color: '#FFFFFF' },
    { name: 'Tethys', distance: 4.88, radius: 0.0844, period: 1.89, color: '#C0C0C0' },
    { name: 'Dione', distance: 6.26, radius: 0.0884, period: 2.74, color: '#C0C0C0' },
    { name: 'Rhea', distance: 8.74, radius: 0.120, period: 4.52, color: '#C0C0C0' },
    { name: 'Titan', distance: 20.3, radius: 0.404, period: 15.95, color: '#FFA500' },
    { name: 'Hyperion', distance: 24.6, radius: 0.0213, period: 21.3, color: '#8C7853' },
    { name: 'Iapetus', distance: 59.1, radius: 0.115, period: 79.3, color: '#696969' },
    // Major irregular moons (scaled distances)
    { name: 'Phoebe', distance: 215, radius: 0.0168, period: 550, color: '#696969' }
  ],
  uranus: [
    // Inner moons
    { name: 'Cordelia', distance: 1.96, radius: 0.0032, period: 0.34, color: '#8C7853' },
    { name: 'Ophelia', distance: 2.12, radius: 0.0034, period: 0.38, color: '#8C7853' },
    { name: 'Bianca', distance: 2.31, radius: 0.0043, period: 0.43, color: '#8C7853' },
    { name: 'Cressida', distance: 2.44, radius: 0.0066, period: 0.46, color: '#8C7853' },
    { name: 'Desdemona', distance: 2.48, radius: 0.0057, period: 0.47, color: '#8C7853' },
    { name: 'Juliet', distance: 2.53, radius: 0.0084, period: 0.49, color: '#8C7853' },
    { name: 'Portia', distance: 2.61, radius: 0.0109, period: 0.51, color: '#8C7853' },
    { name: 'Rosalind', distance: 2.74, radius: 0.0058, period: 0.56, color: '#8C7853' },
    { name: 'Belinda', distance: 2.95, radius: 0.0068, period: 0.62, color: '#8C7853' },
    { name: 'Puck', distance: 3.40, radius: 0.0129, period: 0.76, color: '#8C7853' },
    // Major moons
    { name: 'Miranda', distance: 5.08, radius: 0.0371, period: 1.41, color: '#C0C0C0' },
    { name: 'Ariel', distance: 7.53, radius: 0.0908, period: 2.52, color: '#C0C0C0' },
    { name: 'Umbriel', distance: 10.4, radius: 0.0920, period: 4.14, color: '#696969' },
    { name: 'Titania', distance: 17.1, radius: 0.124, period: 8.71, color: '#C0C0C0' },
    { name: 'Oberon', distance: 22.8, radius: 0.120, period: 13.5, color: '#696969' },
    // Irregular moons (scaled distances)
    { name: 'Caliban', distance: 285, radius: 0.0060, period: 579, color: '#8C7853' },
    { name: 'Sycorax', distance: 486, radius: 0.0119, period: 1288, color: '#8C7853' },
    { name: 'Prospero', distance: 630, radius: 0.0040, period: 1978, color: '#8C7853' },
    { name: 'Setebos', distance: 695, radius: 0.0038, period: 2225, color: '#8C7853' }
  ],
  neptune: [
    // Inner moons
    { name: 'Naiad', distance: 1.93, radius: 0.0052, period: 0.29, color: '#8C7853' },
    { name: 'Thalassa', distance: 2.01, radius: 0.0065, period: 0.31, color: '#8C7853' },
    { name: 'Despina', distance: 2.15, radius: 0.0119, period: 0.33, color: '#8C7853' },
    { name: 'Galatea', distance: 2.43, radius: 0.0138, period: 0.43, color: '#8C7853' },
    { name: 'Larissa', distance: 3.04, radius: 0.0152, period: 0.55, color: '#8C7853' },
    { name: 'Hippocamp', distance: 4.11, radius: 0.0027, period: 0.95, color: '#8C7853' },
    { name: 'Proteus', distance: 4.75, radius: 0.0331, period: 1.12, color: '#8C7853' },
    // Major moon
    { name: 'Triton', distance: 14.3, radius: 0.212, period: 5.88, color: '#87CEEB' },
    // Irregular moon
    { name: 'Nereid', distance: 221, radius: 0.0268, period: 360, color: '#8C7853' }
  ]
};

// Scale factors for better visualization
const DISTANCE_SCALE = 8; // Scale orbital distances
const SIZE_SCALE = 0.3; // Scale planet sizes
const SUN_SIZE = 1.0; // Sun size - realistic relative to planets (Sun radius = 109 Earth radii, but scaled for visibility)
const MOON_DISTANCE_SCALE = 0.1; // Scale moon distances for visibility
const MOON_SIZE_SCALE = 0.05; // Scale moon sizes for visibility

// Beautiful orbital ring component
interface OrbitalRingProps {
  radius: number;
  color?: string;
  opacity?: number;
  segments?: number;
}

const OrbitalRing: React.FC<OrbitalRingProps> = ({
  radius,
  color = '#444444',
  opacity = 0.3,
  segments = 128
}) => {
  const ringRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (ringRef.current) {
      // Subtle pulsing effect
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 0.5) * 0.05;
      ringRef.current.material.opacity = opacity * pulse;
    }
  });

  return (
    <group position={[0, 0, 0]}> {/* Ensure ring is centered at origin (sun position) */}
      <mesh ref={ringRef} rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[radius - 0.03, radius + 0.03, segments]} />
        <meshBasicMaterial
          color={color}
          transparent
          opacity={opacity}
          side={THREE.DoubleSide}
        />
      </mesh>
      {/* Add a subtle inner glow */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[radius - 0.08, radius + 0.08, segments]} />
        <meshBasicMaterial
          color={color}
          transparent
          opacity={opacity * 0.2}
          side={THREE.DoubleSide}
        />
      </mesh>
    </group>
  );
};

interface EnhancedPlanetProps {
  planetData: any;
  orbitalRadius: number;
  time: number;
  timeSpeed: number;
  isPaused: boolean;
  onClick: () => void;
  isSelected?: boolean;
  isPlanetSpinning?: boolean;
}

const EnhancedPlanet: React.FC<EnhancedPlanetProps> = ({
  planetData,
  orbitalRadius,
  time,
  timeSpeed,
  isPaused,
  onClick,
  isSelected = false,
  isPlanetSpinning = true
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const atmosphereRef = useRef<THREE.Mesh>(null);
  const groupRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);

  // Planet size with proper scaling
  const planetSize = planetData.radius * SIZE_SCALE;

  // Synchronized orbital animation and planet rotation with time controls
  useFrame((state) => {
    if (groupRef.current && !isPaused) {
      // Use synchronized time for orbital motion
      const angle = (time / planetData.period) * 2 * Math.PI;
      groupRef.current.position.x = Math.cos(angle) * orbitalRadius;
      groupRef.current.position.z = Math.sin(angle) * orbitalRadius;
    }

    if (meshRef.current && isPlanetSpinning && !isPaused) {
      // Planet rotation synchronized with time speed
      meshRef.current.rotation.y += 0.005 * timeSpeed;
    }

    if (atmosphereRef.current && planetData.hasAtmosphere && !isPaused) {
      // Atmosphere animation synchronized with time speed
      atmosphereRef.current.rotation.y -= 0.002 * timeSpeed;
    }
  });

  return (
    <group ref={groupRef}>
      {/* Main planet with enhanced materials */}
      <Float speed={0.2} rotationIntensity={0.02} floatIntensity={0.01}>
        <mesh
          ref={meshRef}
          onClick={onClick}
          onPointerEnter={() => setHovered(true)}
          onPointerLeave={() => setHovered(false)}
          scale={isSelected ? 1.2 : 1}
        >
          <sphereGeometry args={[planetSize, 32, 32]} />
          <meshStandardMaterial
            color={planetData.color}
            emissive={planetData.emissiveColor || planetData.color}
            emissiveIntensity={hovered ? 0.4 : 0.15}
            roughness={planetData.roughness || 0.7}
            metalness={planetData.metalness || 0.1}
          />
        </mesh>
      </Float>

      {/* Enhanced atmospheric glow - only for planets with atmosphere */}
      {planetData.hasAtmosphere && (
        <mesh ref={atmosphereRef} scale={planetSize * 1.3}>
          <sphereGeometry args={[1, 16, 16]} />
          <meshBasicMaterial
            color={planetData.atmosphereColor || planetData.color}
            opacity={hovered ? 0.25 : 0.125}
            transparent
            side={THREE.BackSide}
          />
        </mesh>
      )}

      {/* Jupiter's Great Red Spot and storm bands */}
      {planetData.hasStorms && planetData.name === 'Jupiter' && (
        <group>
          {/* Storm bands */}
          <mesh scale={planetSize * 1.05}>
            <sphereGeometry args={[1, 32, 16]} />
            <meshBasicMaterial
              color="#8B4513"
              opacity={0.3}
              transparent
            />
          </mesh>
          {/* Great Red Spot */}
          <mesh position={[planetSize * 0.7, 0, 0]} scale={planetSize * 0.3}>
            <sphereGeometry args={[1, 8, 8]} />
            <meshBasicMaterial
              color="#CD5C5C"
              opacity={0.6}
              transparent
            />
          </mesh>
        </group>
      )}

      {/* Saturn's magnificent rings - only Saturn has prominent solid rings */}
      {planetData.hasRings && planetData.name === 'Saturn' && (
        <group rotation={[Math.PI / 2, 0, 0]}>
          {/* Main ring system */}
          <Ring args={[planetSize * 1.3, planetSize * 2.0, 64]}>
            <meshStandardMaterial
              color="#D4AF37"
              transparent
              opacity={0.8}
              side={THREE.DoubleSide}
              emissive="#B8860B"
              emissiveIntensity={0.1}
            />
          </Ring>
          {/* Cassini Division */}
          <Ring args={[planetSize * 2.1, planetSize * 2.3, 64]}>
            <meshStandardMaterial
              color="#CD853F"
              transparent
              opacity={0.6}
              side={THREE.DoubleSide}
              emissive="#A0522D"
              emissiveIntensity={0.05}
            />
          </Ring>
          {/* Outer ring */}
          <Ring args={[planetSize * 2.4, planetSize * 2.8, 64]}>
            <meshStandardMaterial
              color="#DEB887"
              transparent
              opacity={0.4}
              side={THREE.DoubleSide}
              emissive="#D2B48C"
              emissiveIntensity={0.03}
            />
          </Ring>
        </group>
      )}

      {/* Planet label on hover */}
      {hovered && (
        <Html position={[0, planetSize + 1.5, 0]} center>
          <div className="bg-black bg-opacity-90 text-white px-4 py-2 rounded-lg border border-cyan-400 shadow-lg">
            <div className="font-bold text-lg text-cyan-400 mb-1">
              {planetData.name.toUpperCase()}
            </div>
            <div className="text-sm space-y-1">
              <div>Distance: {planetData.distance} AU</div>
              <div>Radius: {planetData.radius.toFixed(2)} R⊕</div>
              <div>Period: {planetData.period} days</div>
            </div>
          </div>
        </Html>
      )}
    </group>
  );
};

// Moon component for realistic moon orbits
interface MoonProps {
  moonData: {
    name: string;
    distance: number;
    radius: number;
    period: number;
    color: string;
  };
  planetPosition: [number, number, number];
  planetRadius: number;
  timeSpeed: number;
  showMoons: boolean;
}

const Moon: React.FC<MoonProps> = ({ moonData, planetPosition, planetRadius, timeSpeed, showMoons }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current && showMoons) {
      const time = state.clock.elapsedTime * timeSpeed;
      const angle = (time / moonData.period) * 2 * Math.PI;
      const moonDistance = moonData.distance * planetRadius * MOON_DISTANCE_SCALE;

      // Calculate moon position relative to planet
      const x = planetPosition[0] + Math.cos(angle) * moonDistance;
      const z = planetPosition[2] + Math.sin(angle) * moonDistance;

      meshRef.current.position.set(x, planetPosition[1], z);
    }
  });

  if (!showMoons) return null;

  const moonSize = Math.max(moonData.radius * MOON_SIZE_SCALE, 0.02); // Minimum visible size

  return (
    <mesh
      ref={meshRef}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <sphereGeometry args={[moonSize, 16, 16]} />
      <meshStandardMaterial
        color={moonData.color}
        roughness={0.8}
        metalness={0.1}
      />

      {/* Moon label on hover */}
      {hovered && (
        <Html position={[0, moonSize + 0.5, 0]} center>
          <div className="bg-black bg-opacity-90 text-white px-2 py-1 rounded border border-gray-400 text-xs">
            <div className="font-bold text-gray-300">
              {moonData.name}
            </div>
            <div className="text-gray-400 text-xs">
              Period: {moonData.period.toFixed(1)}d
            </div>
          </div>
        </Html>
      )}
    </mesh>
  );
};

// Beautiful Sun component
interface StunningSunProps {
  isStarSpinning?: boolean;
}

const StunningSun: React.FC<StunningSunProps> = ({ isStarSpinning = true }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);
  const flareRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current && isStarSpinning) {
      meshRef.current.rotation.y += 0.002;
    }
    if (coronaRef.current && isStarSpinning) {
      coronaRef.current.rotation.y -= 0.001;
      coronaRef.current.rotation.z += 0.0005;
    }
    if (flareRef.current) {
      const time = state.clock.elapsedTime;
      flareRef.current.scale.setScalar(1 + Math.sin(time * 1.5) * 0.1);
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Main Sun */}
      <Float speed={0.1} rotationIntensity={0.01} floatIntensity={0.005}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[SUN_SIZE, 128, 128]} />
          <MeshDistortMaterial
            color="#FDB813"
            emissive="#FF6B00"
            emissiveIntensity={1.2}
            distort={0.15}
            speed={3}
            roughness={0}
            metalness={0}
          />
        </mesh>
      </Float>

      {/* Corona layers */}
      <mesh ref={coronaRef} scale={SUN_SIZE * 1.5}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshBasicMaterial
          color="#FFD700"
          transparent
          opacity={0.2}
          side={THREE.BackSide}
        />
      </mesh>

      <mesh ref={flareRef} scale={SUN_SIZE * 2.2}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#FF8C00"
          transparent
          opacity={0.1}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Solar wind particles - reduced for cleaner appearance */}
      <Sparkles
        count={50}
        scale={[SUN_SIZE * 3, SUN_SIZE * 3, SUN_SIZE * 3]}
        size={1}
        speed={0.3}
        opacity={0.3}
        color="#FFD700"
      />


    </group>
  );
};

// Time control component
interface TimeControlProps {
  timeSpeed: number;
  onTimeSpeedChange: (speed: number) => void;
  isPaused: boolean;
  onPauseToggle: () => void;
  isPlanetSpinning: boolean;
  onPlanetSpinToggle: () => void;
  isStarSpinning: boolean;
  onStarSpinToggle: () => void;
}

const TimeControl: React.FC<TimeControlProps> = ({
  timeSpeed,
  onTimeSpeedChange,
  isPaused,
  onPauseToggle,
  isPlanetSpinning,
  onPlanetSpinToggle,
  isStarSpinning,
  onStarSpinToggle
}) => {
  const speeds = [1, 10, 100, 1000]; // Realistic time speeds

  return (
    <div className="absolute bottom-4 left-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-blue-400">
      <div className="text-sm font-bold mb-2 text-blue-400">Time Control</div>
      <div className="flex items-center space-x-2 mb-2">
        <button
          onClick={onPauseToggle}
          className={`px-3 py-1 rounded text-xs ${
            isPaused ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          {isPaused ? '▶ Play' : '⏸ Pause'}
        </button>
      </div>
      <div className="text-xs mb-1">Speed: {timeSpeed}x</div>
      <div className="flex flex-wrap gap-1">
        {speeds.map(speed => (
          <button
            key={speed}
            onClick={() => onTimeSpeedChange(speed)}
            className={`px-2 py-1 rounded text-xs ${
              timeSpeed === speed
                ? 'bg-blue-600 text-white'
                : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
            }`}
          >
            {speed}x
          </button>
        ))}
      </div>

      {/* Rotation controls */}
      <div className="text-xs mb-1 text-blue-400">Rotation Control</div>
      <div className="flex gap-1">
        <button
          onClick={onPlanetSpinToggle}
          className={`px-2 py-1 rounded text-xs ${
            isPlanetSpinning
              ? 'bg-green-600 hover:bg-green-700'
              : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          🪐 {isPlanetSpinning ? 'Stop' : 'Start'}
        </button>
        <button
          onClick={onStarSpinToggle}
          className={`px-2 py-1 rounded text-xs ${
            isStarSpinning
              ? 'bg-orange-600 hover:bg-orange-700'
              : 'bg-gray-600 hover:bg-gray-700'
          }`}
        >
          ⭐ {isStarSpinning ? 'Stop' : 'Start'}
        </button>
      </div>
    </div>
  );
};

// Main stunning solar system component
interface StunningSolarSystemViewProps {
  onClose?: () => void;
  onPlanetClick?: (planetName: string) => void;
}

const StunningSolarSystemView: React.FC<StunningSolarSystemViewProps> = ({
  onClose,
  onPlanetClick
}) => {
  const [selectedPlanet, setSelectedPlanet] = useState<string | null>(null);
  const [timeSpeed, setTimeSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [showOrbitalTrails, setShowOrbitalTrails] = useState(true);
  const [enablePostProcessing, setEnablePostProcessing] = useState(true);
  const [showMoons, setShowMoons] = useState(true);
  const [isPlanetSpinning, setIsPlanetSpinning] = useState(true);
  const [isStarSpinning, setIsStarSpinning] = useState(true);

  // Time progression - 1x speed = 1 Earth year per real minute (365 days)
  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      // 1x speed = 1 Earth year per 60 seconds = 6.08 days per second
      // Make time progression visible: 1x = 6 days per second
      setCurrentTime(prev => prev + timeSpeed * 6.0);
    }, 16); // ~60fps

    return () => clearInterval(interval);
  }, [isPaused, timeSpeed]);

  // Create planets array from our solar system data
  const planets = Object.entries(SOLAR_SYSTEM_DATA).map(([name, data]) => ({
    name,
    ...data,
    orbitalRadius: data.distance * DISTANCE_SCALE
  }));

  return (
    <div className="relative w-full h-full bg-black overflow-hidden">
      {/* 3D Scene */}
      <Canvas
        camera={{
          position: [0, 30, 50],
          fov: 75,
          near: 0.1,
          far: 2000
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* Lighting setup */}
        <ambientLight intensity={0.05} />
        <pointLight position={[0, 0, 0]} intensity={3} color="#FDB813" decay={0.1} />

        {/* Camera controls with smooth movement */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={15}
          maxDistance={300}
          autoRotate={false}
          autoRotateSpeed={0.2}
          dampingFactor={0.05}
          enableDamping={true}
        />

        {/* Distant starfield background - far away from solar system */}
        <Stars
          radius={1000}
          depth={50}
          count={2000}
          factor={8}
          saturation={0}
          fade
          speed={0.1}
        />

        {/* Central Sun */}
        <StunningSun isStarSpinning={isStarSpinning} />

        {/* Orbital rings and planets */}
        {planets.map((planetData, index) => {
          // Get ring color based on planet type and selection
          const getRingColor = () => {
            if (selectedPlanet === planetData.name) return "#00FFFF"; // Cyan for selected

            // Subtle color variations based on planet
            switch (planetData.name) {
              case 'mercury': return "#FFA500";
              case 'venus': return "#FFD700";
              case 'earth': return "#4169E1";
              case 'mars': return "#CD5C5C";
              case 'jupiter': return "#DAA520";
              case 'saturn': return "#F4A460";
              case 'uranus': return "#4FD0E7";
              case 'neptune': return "#4169E1";
              default: return "#888888";
            }
          };

          return (
            <group key={planetData.name}>
              {/* Orbital trail - subtle and only when enabled */}
              {showOrbitalTrails && (
                <OrbitalRing
                  radius={planetData.orbitalRadius}
                  color={getRingColor()}
                  opacity={selectedPlanet === planetData.name ? 0.4 : 0.1}
                  segments={128}
                />
              )}

              {/* Planet */}
              <EnhancedPlanet
                planetData={planetData}
                orbitalRadius={planetData.orbitalRadius}
                time={currentTime}
                timeSpeed={timeSpeed}
                isPaused={isPaused}
                onClick={() => {
                  setSelectedPlanet(planetData.name);
                  console.log(`🪐 Planet clicked: ${planetData.name} - Navigating to planet detail view`);
                  if (onPlanetClick) {
                    onPlanetClick(planetData.name);
                  }
                }}
                isSelected={selectedPlanet === planetData.name}
                isPlanetSpinning={isPlanetSpinning}
              />

              {/* Moons for this planet */}
              {MOON_DATA[planetData.name as keyof typeof MOON_DATA]?.map((moonData, moonIndex) => {
                // Calculate current planet position
                const angle = (currentTime / planetData.period) * 2 * Math.PI;
                const planetPosition: [number, number, number] = [
                  Math.cos(angle) * planetData.orbitalRadius,
                  0,
                  Math.sin(angle) * planetData.orbitalRadius
                ];

                return (
                  <Moon
                    key={`${planetData.name}-${moonData.name}`}
                    moonData={moonData}
                    planetPosition={planetPosition}
                    planetRadius={planetData.radius * SIZE_SCALE}
                    timeSpeed={timeSpeed}
                    showMoons={showMoons}
                  />
                );
              })}
            </group>
          );
        })}

        {/* Post-processing effects for stunning visuals */}
        {enablePostProcessing && (
          <EffectComposer>
            <Bloom
              intensity={0.8}
              luminanceThreshold={0.1}
              luminanceSmoothing={0.9}
              height={400}
            />
            <ChromaticAberration
              offset={[0.001, 0.001]}
            />
            <ToneMapping
              adaptive={true}
              resolution={256}
              middleGrey={0.6}
              maxLuminance={16}
              averageLuminance={1}
              adaptationRate={1}
            />
          </EffectComposer>
        )}
      </Canvas>

      {/* Time Control */}
      <TimeControl
        timeSpeed={timeSpeed}
        onTimeSpeedChange={setTimeSpeed}
        isPaused={isPaused}
        onPauseToggle={() => setIsPaused(!isPaused)}
        isPlanetSpinning={isPlanetSpinning}
        onPlanetSpinToggle={() => setIsPlanetSpinning(!isPlanetSpinning)}
        isStarSpinning={isStarSpinning}
        onStarSpinToggle={() => setIsStarSpinning(!isStarSpinning)}
      />

      {/* Visual Options */}
      <div className="absolute bottom-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-blue-400">
        <div className="text-sm font-bold mb-2 text-blue-400">Visual Options</div>
        <div className="space-y-2">
          <label className="flex items-center space-x-2 text-xs">
            <input
              type="checkbox"
              checked={showOrbitalTrails}
              onChange={(e) => setShowOrbitalTrails(e.target.checked)}
              className="rounded"
            />
            <span>Orbital Rings</span>
          </label>
          <label className="flex items-center space-x-2 text-xs">
            <input
              type="checkbox"
              checked={enablePostProcessing}
              onChange={(e) => setEnablePostProcessing(e.target.checked)}
              className="rounded"
            />
            <span>Post-Processing</span>
          </label>
          <label className="flex items-center space-x-2 text-xs">
            <input
              type="checkbox"
              checked={showMoons}
              onChange={(e) => setShowMoons(e.target.checked)}
              className="rounded"
            />
            <span>Show Moons</span>
          </label>
        </div>
      </div>

      {/* System Info Panel */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-yellow-400 max-w-xs">
        <div className="text-lg font-bold mb-2 text-yellow-400">☉ Solar System</div>
        <div className="text-sm space-y-1">
          <div>🌟 Our Sun (G-class star)</div>
          <div>🌡️ 5,778 K surface temperature</div>
          <div>🪐 8 planets in orbit</div>
          <div>⏱️ Time: {(currentTime / 365).toFixed(2)} years ({currentTime.toFixed(0)} days)</div>
          {selectedPlanet && (
            <div className="mt-2 pt-2 border-t border-gray-600">
              <div className="text-cyan-400 font-bold">Selected: {selectedPlanet.toUpperCase()}</div>
            </div>
          )}
        </div>
      </div>

      {/* Close button */}
      {onClose && (
        <button
          onClick={onClose}
          className="absolute top-4 left-4 z-10 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white"
        >
          ✕ Close
        </button>
      )}
    </div>
  );
};

export default StunningSolarSystemView;
