import React, { useState, useEffect } from 'react';
import { useGameStore } from '../store/gameStore';
import { simpleApiClient } from '../services/api-simple';

const OrderForm: React.FC = () => {
  const {
    orderFormType,
    selectedFleetId,
    fleets,
    hideOrderForm,
    submitMoveOrder,
    submitAttackOrder,
    submitResupplyOrder,
  } = useGameStore();

  const [targetSystemId, setTargetSystemId] = useState('');
  const [targetFleetId, setTargetFleetId] = useState('');
  const [resupplyAmount, setResupplyAmount] = useState(20);
  const [availableSystems, setAvailableSystems] = useState<Array<{id: string, name: string}>>([]);
  const [loadingNeighbors, setLoadingNeighbors] = useState(false);

  const selectedFleet = fleets.find(f => f.id === selectedFleetId);
  const availableTargetFleets = fleets.filter(f => 
    f.id !== selectedFleetId && 
    f.system_id === selectedFleet?.system_id &&
    f.empire_id !== selectedFleet?.empire_id
  );

  // Load adjacent systems when fleet is selected
  useEffect(() => {
    const loadAdjacentSystems = async () => {
      if (selectedFleet && orderFormType === 'move') {
        setLoadingNeighbors(true);
        try {
          const response = await simpleApiClient.getSystemNeighbors(selectedFleet.system_id);
          setAvailableSystems(response.neighbors || []);
        } catch (error) {
          console.error('Failed to load adjacent systems:', error);
          // Fallback to empty array
          setAvailableSystems([]);
        } finally {
          setLoadingNeighbors(false);
        }
      }
    };

    loadAdjacentSystems();
  }, [selectedFleet, orderFormType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFleetId) return;

    try {
      switch (orderFormType) {
        case 'move':
          if (targetSystemId) {
            // Debug logging removed
            await submitMoveOrder({
              fleetId: selectedFleetId,
              toSystemId: targetSystemId,
            });
          }
          break;
        case 'attack':
          if (targetFleetId) {
            await submitAttackOrder({
              fleetId: selectedFleetId,
              targetFleetId: targetFleetId,
            });
          }
          break;
        case 'resupply':
          await submitResupplyOrder({
            fleetId: selectedFleetId,
            amount: resupplyAmount,
          });
          break;
      }
    } catch (error) {
      console.error('Failed to submit order:', error);
    }
  };

  const getFormTitle = () => {
    switch (orderFormType) {
      case 'move': return 'Move Fleet';
      case 'attack': return 'Attack Order';
      case 'resupply': return 'Resupply Fleet';
      default: return 'Fleet Order';
    }
  };

  const isFormValid = () => {
    switch (orderFormType) {
      case 'move': return targetSystemId !== '' && !loadingNeighbors && availableSystems.length > 0;
      case 'attack': return targetFleetId !== '';
      case 'resupply': return resupplyAmount > 0;
      default: return false;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="glass-panel p-6 max-w-md mx-4 w-full">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-bold text-neon-cyan">{getFormTitle()}</h3>
          <button
            onClick={hideOrderForm}
            className="text-gray-400 hover:text-white text-xl"
          >
            ×
          </button>
        </div>

        {selectedFleet && (
          <div className="mb-4 p-3 bg-gray-800 bg-opacity-50 rounded">
            <div className="text-sm text-gray-400 mb-1">Selected Fleet</div>
            <div className="text-white">
              <div>ID: {selectedFleet.id.slice(0, 8)}...</div>
              <div>Location: {selectedFleet.system_name}</div>
              <div>Supply: {selectedFleet.supply}</div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {orderFormType === 'move' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Destination System
              </label>
              {loadingNeighbors ? (
                <div className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-gray-400">
                  Loading adjacent systems...
                </div>
              ) : availableSystems.length > 0 ? (
                <select
                  value={targetSystemId}
                  onChange={(e) => setTargetSystemId(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-neon-cyan focus:outline-none"
                  required
                >
                  <option value="">Select destination...</option>
                  {availableSystems.map((system) => (
                    <option key={system.id} value={system.id}>
                      {system.name} ({system.id})
                    </option>
                  ))}
                </select>
              ) : (
                <div className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-gray-400">
                  No adjacent systems available for movement.
                </div>
              )}
              {selectedFleet && (
                <div className="text-xs text-gray-400 mt-1">
                  Current location: {selectedFleet.system_name} ({selectedFleet.system_id})
                </div>
              )}
            </div>
          )}

          {orderFormType === 'attack' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Target Fleet
              </label>
              {availableTargetFleets.length > 0 ? (
                <select
                  value={targetFleetId}
                  onChange={(e) => setTargetFleetId(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-neon-cyan focus:outline-none"
                  required
                >
                  <option value="">Select target...</option>
                  {availableTargetFleets.map((fleet) => (
                    <option key={fleet.id} value={fleet.id}>
                      Fleet {fleet.id.slice(0, 8)} (Supply: {fleet.supply})
                    </option>
                  ))}
                </select>
              ) : (
                <div className="text-gray-400 text-sm p-3 bg-gray-800 rounded">
                  No enemy fleets in this system to attack.
                </div>
              )}
            </div>
          )}

          {orderFormType === 'resupply' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Resupply Amount
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={resupplyAmount}
                onChange={(e) => setResupplyAmount(parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:border-neon-cyan focus:outline-none"
                required
              />
              <div className="text-xs text-gray-400 mt-1">
                Current supply: {selectedFleet?.supply || 0}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={hideOrderForm}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!isFormValid()}
              className={`px-4 py-2 rounded transition-colors ${
                isFormValid()
                  ? 'bg-neon-cyan hover:bg-cyan-400 text-black'
                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'
              }`}
            >
              Submit Order
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default OrderForm;
