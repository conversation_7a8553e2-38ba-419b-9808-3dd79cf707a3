import React, { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { wsService } from '../services/websocket';
import PortMonitor from './PortMonitor';

const GameHUD: React.FC = () => {
  const { turn, fleets, orders, refreshData } = useGameStore();
  const [showPortMonitor, setShowPortMonitor] = useState(false);

  const activeOrders = Array.isArray(orders) ? orders.filter(order => order && order.status === 'accepted').length : 0;
  const totalFleets = Array.isArray(fleets) ? fleets.length : 0;
  const totalSupply = Array.isArray(fleets) ? fleets.reduce((sum, fleet) => sum + (fleet?.supply || 0), 0) : 0;

  return (
    <div data-testid="main-nav" className="fixed top-0 left-0 right-0 h-16 glass-panel border-b border-gray-700 z-40">
      <div className="flex items-center justify-between h-full px-6">
        {/* Left side - Game title and turn */}
        <div className="flex items-center space-x-6">
          <h1 className="text-xl font-bold text-neon-cyan">GALACTIC GENESIS</h1>
          <div className="flex items-center space-x-2">
            <span className="text-gray-400">Turn:</span>
            <span className="text-neon-gold font-bold text-lg">{turn}</span>
          </div>
        </div>
        
        {/* Center - Game stats */}
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-neon-cyan rounded-full"></div>
            <span className="text-gray-400">Fleets:</span>
            <span className="text-white font-semibold">{totalFleets}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-neon-green rounded-full"></div>
            <span className="text-gray-400">Supply:</span>
            <span className="text-white font-semibold">{totalSupply}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-neon-purple rounded-full"></div>
            <span className="text-gray-400">Active Orders:</span>
            <span className="text-white font-semibold">{activeOrders}</span>
          </div>
        </div>
        
        {/* Right side - Connection status and actions */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${wsService.isConnected ? 'bg-neon-green' : 'bg-neon-red'}`}></div>
            <span className="text-gray-400 text-sm">
              {wsService.isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          <button
            onClick={() => window.open('http://localhost:8086', '_blank')}
            className="px-3 py-1 bg-blue-700 hover:bg-blue-600 text-white rounded text-sm transition-colors"
            title="Open health monitoring dashboard"
          >
            🏥 Health
          </button>

          <button
            onClick={() => setShowPortMonitor(true)}
            className="px-3 py-1 bg-purple-700 hover:bg-purple-600 text-white rounded text-sm transition-colors"
            title="Open port monitoring dashboard"
          >
            🔌 Ports
          </button>

          <button
            data-testid="tutorial-button"
            onClick={() => useGameStore.getState().showTutorial()}
            className="px-3 py-1 bg-neon-purple hover:bg-purple-600 text-white rounded text-sm transition-colors"
            title="Open game tutorial and reference guide"
          >
            📚 Help
          </button>

          <button
            onClick={refreshData}
            className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Port Monitor Modal */}
      <PortMonitor
        isOpen={showPortMonitor}
        onClose={() => setShowPortMonitor(false)}
      />
    </div>
  );
};

export default GameHUD;
