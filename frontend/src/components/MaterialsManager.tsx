import React, { useState, useEffect } from 'react';
import { simpleApiClient } from '../services/api-simple';

interface Material {
  id: string;
  name: string;
  category: string;
  density: number;
  base_value: number;
  rarity_factor: number;
  description: string;
}

interface MaterialDeposit {
  id: string;
  body_type: string;
  body_id: number;
  material_id: string;
  material_name: string;
  material_category: string;
  richness: number;
  accessibility: number;
  discovered: boolean;
  estimated_reserves?: number;
}

interface Station {
  id: string;
  name: string;
  station_type: string;
  station_type_name: string;
  station_category: string;
  system_id: string;
  empire_id: string;
  operational: boolean;
  max_storage_capacity: number;
  current_storage_used: number;
  construction_progress: number;
}

interface MaterialsManagerProps {
  empireId: string;
  systemId?: string;
}

export const MaterialsManager: React.FC<MaterialsManagerProps> = ({ empireId, systemId }) => {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [deposits, setDeposits] = useState<MaterialDeposit[]>([]);
  const [stations, setStations] = useState<Station[]>([]);
  const [stationTypes, setStationTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'materials' | 'deposits' | 'stations' | 'construction'>('materials');

  // Construction form state
  const [constructionForm, setConstructionForm] = useState({
    station_type_id: '',
    orbiting_body_type: '',
    orbiting_body_id: '',
    orbital_distance_km: '10000'
  });

  useEffect(() => {
    loadData();
  }, [empireId, systemId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [materialsRes, depositsRes, stationsRes, stationTypesRes] = await Promise.all([
        simpleApiClient.getMaterials(),
        simpleApiClient.getMaterialDeposits(systemId ? { discovered: true } : {}),
        simpleApiClient.getStations({ empire_id: empireId, ...(systemId && { system_id: systemId }) }),
        simpleApiClient.getStationTypes()
      ]);

      setMaterials(materialsRes.materials);
      setDeposits(depositsRes.deposits);
      setStations(stationsRes.stations);
      setStationTypes(stationTypesRes.station_types);
      setError(null);
    } catch (err) {
      console.error('Failed to load materials data:', err);
      setError('Failed to load materials data');
    } finally {
      setLoading(false);
    }
  };

  const handleConstructStation = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!systemId) {
      setError('System ID required for station construction');
      return;
    }

    try {
      await simpleApiClient.createStationConstructionOrder({
        empire_id: empireId,
        system_id: systemId,
        station_type_id: constructionForm.station_type_id,
        orbiting_body_type: constructionForm.orbiting_body_type || undefined,
        orbiting_body_id: constructionForm.orbiting_body_id ? parseInt(constructionForm.orbiting_body_id) : undefined,
        orbital_distance_km: parseInt(constructionForm.orbital_distance_km)
      });

      // Reset form and reload data
      setConstructionForm({
        station_type_id: '',
        orbiting_body_type: '',
        orbiting_body_id: '',
        orbital_distance_km: '10000'
      });
      setSuccessMessage('Construction order created successfully!');
      setError(null);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);

      await loadData();
    } catch (err) {
      console.error('Failed to create construction order:', err);
      setError('Failed to create construction order');
      setSuccessMessage(null);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      metal: 'bg-gray-600',
      volatile: 'bg-blue-600',
      silicate: 'bg-yellow-600',
      exotic: 'bg-purple-600',
      processed: 'bg-green-600'
    };
    return colors[category] || 'bg-gray-500';
  };

  const getRichnessColor = (richness: number) => {
    if (richness >= 0.8) return 'text-green-400';
    if (richness >= 0.6) return 'text-yellow-400';
    if (richness >= 0.4) return 'text-orange-400';
    return 'text-red-400';
  };

  if (loading) {
    return <div className="p-6 text-white">Loading materials data...</div>;
  }

  return (
    <div className="p-6 bg-gray-900 text-white" data-testid="materials-manager">
      <h2 className="text-2xl font-bold mb-6">Materials & Mining Management</h2>

      {error && (
        <div className="bg-red-600 text-white p-4 rounded mb-4" data-testid="error-message">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-600 text-white p-4 rounded mb-4" data-testid="success-message">
          {successMessage}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-4 mb-6 border-b border-gray-600">
        {[
          { key: 'materials', label: 'Material Types' },
          { key: 'deposits', label: 'Deposits' },
          { key: 'stations', label: 'Stations' },
          { key: 'construction', label: 'Build Station' }
        ].map(tab => (
          <button
            key={tab.key}
            data-testid={`${tab.key}-tab`}
            onClick={() => setActiveTab(tab.key as any)}
            className={`px-4 py-2 font-medium transition-colors ${
              activeTab === tab.key
                ? 'text-cyan-400 border-b-2 border-cyan-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Materials Tab */}
      {activeTab === 'materials' && (
        <div>
          {loading && <div data-testid="loading-indicator">Loading materials...</div>}
          <h3 className="text-lg font-semibold mb-4">Available Material Types</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {materials.map(material => (
              <div key={material.id} className="border border-gray-600 rounded-lg p-4" data-testid="material-card">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium" data-testid="material-name">{material.name}</h4>
                  <span
                    className={`px-2 py-1 rounded text-xs text-white ${getCategoryColor(material.category)}`}
                    data-testid="material-category"
                    data-category={material.category}
                  >
                    {material.category}
                  </span>
                </div>
                <p className="text-sm text-gray-400 mb-2" data-testid="material-description">{material.description}</p>
                <div className="text-xs text-gray-500">
                  <div data-testid="material-value">Value: {material.base_value} credits/unit</div>
                  <div>Density: {material.density} kg/m³</div>
                  <div>Rarity: {(material.rarity_factor * 100).toFixed(0)}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Deposits Tab */}
      {activeTab === 'deposits' && (
        <div>
          <h3 className="text-lg font-semibold mb-4">Material Deposits</h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-600" data-testid="deposits-table">
              <thead>
                <tr className="bg-gray-800">
                  <th className="border border-gray-600 p-2 text-left">Material</th>
                  <th className="border border-gray-600 p-2 text-left">Body</th>
                  <th className="border border-gray-600 p-2 text-left">Richness</th>
                  <th className="border border-gray-600 p-2 text-left">Accessibility</th>
                  <th className="border border-gray-600 p-2 text-left">Status</th>
                </tr>
              </thead>
              <tbody>
                {deposits.map(deposit => (
                  <tr key={deposit.id} className="hover:bg-gray-800" data-testid="deposit-row">
                    <td className="border border-gray-600 p-2">
                      <div className="flex items-center">
                        <span className={`w-3 h-3 rounded mr-2 ${getCategoryColor(deposit.material_category)}`}></span>
                        {deposit.material_name}
                      </div>
                    </td>
                    <td className="border border-gray-600 p-2">
                      {deposit.body_type} #{deposit.body_id}
                    </td>
                    <td className="border border-gray-600 p-2">
                      <span
                        className={getRichnessColor(deposit.richness)}
                        data-testid="richness-indicator"
                      >
                        {(deposit.richness * 100).toFixed(1)}%
                      </span>
                    </td>
                    <td className="border border-gray-600 p-2">
                      {(deposit.accessibility * 100).toFixed(1)}%
                    </td>
                    <td className="border border-gray-600 p-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        deposit.discovered ? 'bg-green-600' : 'bg-gray-600'
                      }`}>
                        {deposit.discovered ? 'Discovered' : 'Unknown'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Stations Tab */}
      {activeTab === 'stations' && (
        <div>
          <h3 className="text-lg font-semibold mb-4">Space Stations</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {stations.map(station => (
              <div key={station.id} className="border border-gray-600 rounded-lg p-4" data-testid="station-card">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium" data-testid="station-name">{station.name}</h4>
                  <span
                    className={`px-2 py-1 rounded text-xs ${
                      station.operational ? 'bg-green-600' : 'bg-yellow-600'
                    }`}
                    data-testid="station-status"
                  >
                    {station.operational ? 'Operational' : 'Under Construction'}
                  </span>
                </div>
                <p className="text-sm text-gray-400 mb-2" data-testid="station-type">{station.station_type_name}</p>
                <div className="text-xs text-gray-500">
                  <div>Category: {station.station_category}</div>
                  <div data-testid="station-storage">Storage: {station.current_storage_used}/{station.max_storage_capacity}</div>
                  {!station.operational && (
                    <div>Progress: {(station.construction_progress * 100).toFixed(1)}%</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Construction Tab */}
      {activeTab === 'construction' && (
        <div>
          <h3 className="text-lg font-semibold mb-4">Build New Station</h3>
          <form onSubmit={handleConstructStation} className="max-w-md space-y-4" data-testid="construction-form">
            <div>
              <label className="block text-sm font-medium mb-1">Station Type</label>
              <select
                value={constructionForm.station_type_id}
                onChange={(e) => setConstructionForm(prev => ({ ...prev, station_type_id: e.target.value }))}
                className="w-full p-2 bg-gray-800 border border-gray-600 rounded"
                data-testid="station-type-select"
                required
              >
                <option value="">Select station type...</option>
                {stationTypes.map(type => (
                  <option key={type.id} value={type.id} data-testid="station-type-option">
                    {type.name} ({type.category})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Orbiting Body Type (Optional)</label>
              <select
                value={constructionForm.orbiting_body_type}
                onChange={(e) => setConstructionForm(prev => ({ ...prev, orbiting_body_type: e.target.value }))}
                className="w-full p-2 bg-gray-800 border border-gray-600 rounded"
                data-testid="orbiting-body-type-select"
              >
                <option value="">Deep space</option>
                <option value="planet">Planet</option>
                <option value="moon">Moon</option>
                <option value="asteroid">Asteroid</option>
              </select>
            </div>

            {constructionForm.orbiting_body_type && (
              <div>
                <label className="block text-sm font-medium mb-1">Body ID</label>
                <input
                  type="number"
                  value={constructionForm.orbiting_body_id}
                  onChange={(e) => setConstructionForm(prev => ({ ...prev, orbiting_body_id: e.target.value }))}
                  className="w-full p-2 bg-gray-800 border border-gray-600 rounded"
                  data-testid="body-id-input"
                  placeholder="Enter body ID"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium mb-1">Orbital Distance (km)</label>
              <input
                type="number"
                value={constructionForm.orbital_distance_km}
                onChange={(e) => setConstructionForm(prev => ({ ...prev, orbital_distance_km: e.target.value }))}
                className="w-full p-2 bg-gray-800 border border-gray-600 rounded"
                data-testid="orbital-distance-input"
                required
              />
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors"
              data-testid="construct-button"
            >
              Order Construction
            </button>
          </form>
        </div>
      )}
    </div>
  );
};
