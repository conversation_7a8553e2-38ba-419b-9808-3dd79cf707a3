import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to console for debugging
    console.error('🚨 React Error Boundary caught an error:', error);
    console.error('📍 Error Info:', errorInfo);

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Update state with error details
    this.setState({
      error,
      errorInfo
    });

    // You can also log the error to an error reporting service here
    // Example: logErrorToService(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div style={{
          padding: '20px',
          fontFamily: 'monospace',
          backgroundColor: '#1a1a1a',
          color: '#ff4444',
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            maxWidth: '800px',
            width: '100%',
            backgroundColor: '#000',
            border: '2px solid #ff4444',
            borderRadius: '8px',
            padding: '20px'
          }}>
            <h1 style={{ color: '#ff4444', marginBottom: '20px' }}>
              🚨 Application Error
            </h1>
            
            <p style={{ color: '#ffaa00', marginBottom: '15px' }}>
              Something went wrong in the React component tree. This is likely a JavaScript error.
            </p>

            {this.state.error && (
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ color: '#ff6666' }}>Error Message:</h3>
                <pre style={{
                  backgroundColor: '#2a0000',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  color: '#ffcccc'
                }}>
                  {this.state.error.message}
                </pre>
              </div>
            )}

            {this.state.error?.stack && (
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ color: '#ff6666' }}>Stack Trace:</h3>
                <pre style={{
                  backgroundColor: '#2a0000',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  maxHeight: '200px',
                  fontSize: '12px',
                  color: '#ffcccc'
                }}>
                  {this.state.error.stack}
                </pre>
              </div>
            )}

            {this.state.errorInfo?.componentStack && (
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ color: '#ff6666' }}>Component Stack:</h3>
                <pre style={{
                  backgroundColor: '#2a0000',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  maxHeight: '200px',
                  fontSize: '12px',
                  color: '#ffcccc'
                }}>
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}

            <div style={{ marginTop: '30px' }}>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#ff4444',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                🔄 Reload Page
              </button>
              
              <button
                onClick={() => window.location.href = '/?debug=true'}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#4444ff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                🔧 Debug Mode
              </button>

              <button
                onClick={() => window.location.href = '/?simple=true'}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#44ff44',
                  color: 'black',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🧪 Simple Test
              </button>
            </div>

            <div style={{ marginTop: '20px', fontSize: '14px', color: '#888' }}>
              <p>💡 Troubleshooting tips:</p>
              <ul style={{ textAlign: 'left', paddingLeft: '20px' }}>
                <li>Check the browser console for additional error details</li>
                <li>Try refreshing the page</li>
                <li>Use Debug Mode to test individual components</li>
                <li>Check if all dependencies are properly installed</li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
