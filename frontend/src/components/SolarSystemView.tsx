import React, { useRef, useEffect, useState, useMemo, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import {
  OrbitControls,
  Float,
  Text,
  Html,
  Stars,
  Sparkles
} from '@react-three/drei';
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  ToneMapping
} from '@react-three/postprocessing';
import * as THREE from 'three';
import { stellarApi } from '../services/stellarApi';
import type { StarDetail, Planet, Moon } from '../types/stellar';

interface SolarSystemViewProps {
  starId: number;
  onBack: () => void;
  onPlanetClick?: (planetId: number) => void;
  timeSpeed?: number; // Time multiplier: 1x = real time, 10x = 10 times faster
}



interface OrbitingMoonProps {
  moon: Moon;
  planetPosition: [number, number, number];
  orbitalRadius: number;
  orbitalPeriod: number;
  time: number;
  planetSize: number;
  timeSpeed: number;
}

interface OrbitingPlanetProps {
  planet: Planet;
  orbitalRadius: number;
  orbitalPeriod: number;
  time: number;
  onClick: () => void;
  isPlanetSpinning: boolean;
  timeSpeed: number;
}

const OrbitingMoon: React.FC<OrbitingMoonProps> = ({
  moon,
  planetPosition,
  orbitalRadius,
  orbitalPeriod,
  time,
  planetSize,
  timeSpeed
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Calculate moon size relative to planet (realistic scaling)
  const moonSize = Math.max(0.05, (moon.radius_earth || moon.diameter_km / 12742) * planetSize * 0.8);

  // Moon color based on composition and database data
  const moonColor = moon.color_hex ||
    (moon.dominant_color === 'Yellow-Orange' ? '#FFFF99' :
     moon.dominant_color === 'Blue-White' ? '#B0E0E6' :
     moon.dominant_color === 'Gray-Brown' ? '#8B7D6B' :
     moon.dominant_color === 'Dark Gray' ? '#4F4F4F' :
     moon.dominant_color === 'Light Gray' ? '#D3D3D3' :
     moon.dominant_color === 'Pink-White' ? '#FFB6C1' :
     moon.dominant_color === 'Bright White' ? '#F8F8FF' :
     moon.dominant_color === 'Orange-Brown' ? '#CD853F' :
     '#C0C0C0'); // Default gray

  useFrame((state) => {
    if (meshRef.current && time > 0) {
      // Realistic orbital mechanics: 1x speed = real orbital period
      // At 1x speed: 1 real day = 1 second in simulation
      const realOrbitalPeriodDays = moon.orbital_period_days;
      const scaledTime = state.clock.elapsedTime * timeSpeed;

      // Convert to angle: complete orbit in orbital period
      // 1x speed = 1 day per second, so Luna (27.3 days) takes 27.3 seconds for full orbit
      const angle = (scaledTime / realOrbitalPeriodDays) * 2 * Math.PI;

      const x = planetPosition[0] + Math.cos(angle) * orbitalRadius;
      const z = planetPosition[2] + Math.sin(angle) * orbitalRadius;
      const y = planetPosition[1];

      meshRef.current.position.set(x, y, z);

      // Moon rotation (many moons are tidally locked)
      if (!moon.is_tidally_locked) {
        meshRef.current.rotation.y += 0.001 * timeSpeed;
      }
    }
  });

  return (
    <Float speed={0.5} rotationIntensity={0.1} floatIntensity={0.05}>
      <mesh
        ref={meshRef}
        onPointerEnter={() => setHovered(true)}
        onPointerLeave={() => setHovered(false)}
      >
        <sphereGeometry args={[moonSize, 16, 16]} />
        <meshStandardMaterial
          color={moonColor}
          roughness={0.9}
          metalness={0.1}
          emissive={hovered ? moonColor : '#000000'}
          emissiveIntensity={hovered ? 0.1 : 0}
        />
      </mesh>

      {/* Moon label when hovered */}
      {hovered && (
        <Html position={[0, moonSize + 0.3, 0]} center>
          <div className="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
            🌙 {moon.name}
            {moon.raw_materials && (
              <div className="text-xs text-gray-300 mt-1 max-w-48">
                {moon.raw_materials.substring(0, 50)}...
              </div>
            )}
          </div>
        </Html>
      )}
    </Float>
  );
};

const OrbitingPlanet: React.FC<OrbitingPlanetProps> = ({
  planet,
  orbitalRadius,
  orbitalPeriod,
  time,
  onClick,
  isPlanetSpinning,
  timeSpeed
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const groupRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);

  // Calculate planet size based on radius (Earth = 1.0)
  const planetSize = Math.max(0.1, Math.min(2.0, (planet.radius_earth || 0.5) * 0.3));

  // Get planet color - prioritize database color_hex, then dominant_color, then composition
  const getPlanetColor = (): string => {
    if (planet.color_hex) {
      return planet.color_hex;
    }

    if (planet.dominant_color) {
      switch (planet.dominant_color.toLowerCase()) {
        case 'gray': return '#8C7853';
        case 'yellow-orange': return '#FFC649';
        case 'blue-green': return '#6B93D6';
        case 'red-orange': return '#CD5C5C';
        case 'orange-brown': return '#D8CA9D';
        case 'golden-yellow': return '#FAD5A5';
        case 'cyan-blue': return '#4FD0E7';
        case 'deep blue': return '#4B70DD';
        default: break;
      }
    }

    // Fallback to composition-based colors
    switch (planet.composition.toLowerCase()) {
      case 'rocky': return '#8B4513';
      case 'gas_giant': return '#FFA500';
      case 'ice_giant': return '#4169E1';
      case 'super_earth': return '#228B22';
      default: return '#696969';
    }
  };

  const planetColor = getPlanetColor();

  // Enhanced material properties based on planet type and composition
  const getMaterialProperties = () => {
    if (planet.name === 'Earth') {
      return {
        color: '#4A90E2',
        emissive: '#001122',
        emissiveIntensity: hovered ? 0.4 : 0.15,
        roughness: 0.4,
        metalness: 0.1,
        atmosphereColor: '#87CEEB',
        hasAtmosphere: true
      };
    } else if (planet.composition === 'gas_giant') {
      return {
        color: planetColor,
        emissive: planetColor,
        emissiveIntensity: hovered ? 0.3 : 0.1,
        roughness: 0.1,
        metalness: 0.0,
        atmosphereColor: planetColor,
        hasAtmosphere: true
      };
    } else if (planet.composition === 'ice_giant') {
      return {
        color: planetColor,
        emissive: '#001144',
        emissiveIntensity: hovered ? 0.2 : 0.05,
        roughness: 0.2,
        metalness: 0.1,
        atmosphereColor: '#4169E1',
        hasAtmosphere: true
      };
    } else {
      // Rocky planets
      return {
        color: planetColor,
        emissive: planet.in_habitable_zone ? '#004400' : '#000000',
        emissiveIntensity: planet.in_habitable_zone ? (hovered ? 0.4 : 0.2) : 0.1,
        roughness: 0.7,
        metalness: 0.2,
        atmosphereColor: planet.in_habitable_zone ? '#87CEEB' : planetColor,
        hasAtmosphere: planet.in_habitable_zone || planet.name === 'Venus' || planet.name === 'Mars'
      };
    }
  };

  const materialProps = getMaterialProperties();

  // Calculate orbital position
  const angle = (time / orbitalPeriod) * Math.PI * 2;
  const x = Math.cos(angle) * orbitalRadius;
  const z = Math.sin(angle) * orbitalRadius;

  useFrame((state) => {
    if (meshRef.current && isPlanetSpinning) {
      // Planet rotation on its axis - scaled with time speed
      // Realistic day/night cycle: 1x speed = 1 day per second
      meshRef.current.rotation.y += (2 * Math.PI / 24) * timeSpeed * (1/60); // 24 hours per day, 60 fps
    }

    if (groupRef.current) {
      // Realistic orbital mechanics: 1x speed = real orbital period
      // At 1x speed: 1 real day = 1 second in simulation
      const realOrbitalPeriodDays = planet.period_days || 365; // Default to Earth if not specified
      const scaledTime = state.clock.elapsedTime * timeSpeed;

      // Convert to angle: complete orbit in orbital period
      // 1x speed = 1 day per second, so Earth (365 days) takes 365 seconds for full orbit
      const currentAngle = (scaledTime / realOrbitalPeriodDays) * 2 * Math.PI;

      groupRef.current.position.x = Math.cos(currentAngle) * orbitalRadius;
      groupRef.current.position.z = Math.sin(currentAngle) * orbitalRadius;
    }
  });

  return (
    <>
      {/* Orbital ring - only show for habitable zone planets */}
      {planet.in_habitable_zone && (
        <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
          <ringGeometry args={[orbitalRadius - 0.1, orbitalRadius + 0.1, 128]} />
          <meshBasicMaterial
            color="#00FF00"
            transparent
            opacity={0.15}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}

      {/* Orbiting planet */}
      <group ref={groupRef} position={[x, 0, z]}>
        <Float speed={0.2} rotationIntensity={0.02} floatIntensity={0.01}>
          <mesh
            ref={meshRef}
            onClick={onClick}
            onPointerEnter={() => setHovered(true)}
            onPointerLeave={() => setHovered(false)}
            scale={hovered ? 1.1 : 1}
          >
            <sphereGeometry args={[planetSize, 64, 64]} />
            <meshStandardMaterial
              color={materialProps.color}
              emissive={materialProps.emissive}
              emissiveIntensity={materialProps.emissiveIntensity}
              roughness={materialProps.roughness}
              metalness={materialProps.metalness}
            />
          </mesh>
        </Float>

        {/* Enhanced atmospheric glow - only for planets with atmosphere */}
        {materialProps.hasAtmosphere && (
          <mesh scale={planetSize * 1.3}>
            <sphereGeometry args={[1, 32, 32]} />
            <meshBasicMaterial
              color={materialProps.atmosphereColor}
              opacity={hovered ? 0.3 : 0.15}
              transparent
              side={THREE.BackSide}
            />
          </mesh>
        )}

        {/* Gas giant particle effects */}
        {planet.composition === 'gas_giant' && (
          <Sparkles
            count={20}
            scale={[planetSize * 2, planetSize * 2, planetSize * 2]}
            size={0.5}
            speed={0.2}
            color={materialProps.color}
          />
        )}

        {/* Planet label */}
        {hovered && (
          <Html position={[0, planetSize + 0.5, 0]} center>
            <div className="bg-black bg-opacity-90 text-white px-3 py-2 rounded text-sm border border-gray-600 max-w-64">
              <div className="font-bold text-yellow-300">{planet.name}</div>
              <div>Mass: {planet.mass_earth?.toFixed(2) || '?'} Earth</div>
              <div>Distance: {planet.sma_au?.toFixed(2) || '?'} AU</div>
              <div>Type: {planet.composition}</div>
              {planet.raw_materials && (
                <div className="text-xs text-gray-300 mt-1">
                  Materials: {planet.raw_materials.substring(0, 60)}...
                </div>
              )}
              {planet.moons && planet.moons.length > 0 && (
                <div className="text-cyan-400 text-xs mt-1">
                  🌙 {planet.moons.length} moon{planet.moons.length > 1 ? 's' : ''}
                </div>
              )}
              {planet.in_habitable_zone && (
                <div className="text-green-400 font-semibold">🌍 Habitable Zone</div>
              )}
            </div>
          </Html>
        )}

        {/* Render moons */}
        {planet.moons && planet.moons.map((moon, index) => {
          // Calculate moon orbital radius relative to planet size
          const moonOrbitalRadius = planetSize * (1.5 + index * 0.8); // Stagger moon orbits
          const moonOrbitalPeriod = Math.max(2, moon.orbital_period_days * 0.1); // Speed up for visibility

          return (
            <OrbitingMoon
              key={moon.moon_id}
              moon={moon}
              planetPosition={[x, 0, z]}
              orbitalRadius={moonOrbitalRadius}
              orbitalPeriod={moonOrbitalPeriod}
              time={time}
              planetSize={planetSize}
              timeSpeed={timeSpeed}
            />
          );
        })}
      </group>
    </>
  );
};

interface StarComponentProps {
  star: StarDetail;
  isStarSpinning: boolean;
}

const StarComponent: React.FC<StarComponentProps> = ({ star, isStarSpinning }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);

  // Calculate star size based on radius (Sun = 1.0)
  const starSize = Math.max(0.8, Math.min(3.0, (star.radius_solar || 1.0) * 1.2));

  // Get star color based on spectral type
  const getStarColor = (spectralType?: string): string => {
    if (!spectralType) return '#FFFFFF';
    const type = spectralType.charAt(0).toUpperCase();
    switch (type) {
      case 'O': return '#9BB0FF'; // Blue
      case 'B': return '#AABFFF'; // Blue-white
      case 'A': return '#CAD7FF'; // White
      case 'F': return '#F8F7FF'; // Yellow-white
      case 'G': return '#FFF4EA'; // Yellow (like our Sun)
      case 'K': return '#FFD2A1'; // Orange
      case 'M': return '#FFAD51'; // Red
      default: return '#FFFFFF';
    }
  };

  const starColor = getStarColor(star.spectral_type);

  useFrame((state) => {
    if (meshRef.current && isStarSpinning) {
      meshRef.current.rotation.y += 0.005;
    }

    if (coronaRef.current) {
      // Pulsing corona effect
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1;
      coronaRef.current.scale.setScalar(starSize * 1.5 * pulse);
    }
  });

  return (
    <group position={[0, 0, 0]}>
      {/* Enhanced star with stunning visuals */}
      <Float speed={0.2} rotationIntensity={0.05} floatIntensity={0.02}>
        <mesh ref={meshRef}>
          <sphereGeometry args={[starSize, 128, 128]} />
          <meshStandardMaterial
            color={starColor}
            emissive={starColor}
            emissiveIntensity={1.2}
            roughness={0}
            metalness={0}
          />
        </mesh>
      </Float>

      {/* Enhanced corona layers with better colors */}
      <mesh ref={coronaRef} scale={starSize * 1.5}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshBasicMaterial
          color="#FFD700"
          transparent
          opacity={0.2}
          side={THREE.BackSide}
        />
      </mesh>

      <mesh scale={starSize * 2.2}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#FF8C00"
          transparent
          opacity={0.1}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Solar wind particles for enhanced realism */}
      <Sparkles
        count={50}
        scale={[starSize * 3, starSize * 3, starSize * 3]}
        size={1}
        speed={0.3}
        color={starColor}
      />

      {/* Star label */}
      <Html position={[0, starSize + 1.5, 0]} center>
        <div className="bg-black bg-opacity-90 text-white px-4 py-3 rounded-lg border border-yellow-600">
          <div className="font-bold text-xl text-yellow-300">{star.name}</div>
          <div className="text-sm text-gray-300">{star.spectral_type}</div>
          <div className="text-sm text-gray-300">{star.distance_ly.toFixed(1)} ly</div>
        </div>
      </Html>
    </group>
  );
};

// Camera controller for smooth transitions and enhanced controls
interface CameraControllerProps {
  selectedPlanet?: string;
  onResetCamera: React.MutableRefObject<() => void>;
}

const CameraController: React.FC<CameraControllerProps> = ({ selectedPlanet, onResetCamera }) => {
  const { camera, controls } = useThree();

  // Reset camera to solar system overview
  const resetToSolarOverview = () => {
    if (controls) {
      // Reset OrbitControls target to solar center (star)
      controls.target.set(0, 0, 0);

      // Move camera to overview position
      const startPosition = camera.position.clone();
      const endPosition = new THREE.Vector3(20, 15, 20);

      let progress = 0;
      const animateCamera = () => {
        progress += 0.03;
        if (progress <= 1) {
          camera.position.lerpVectors(startPosition, endPosition, progress);
          controls.update();
          requestAnimationFrame(animateCamera);
        }
      };
      animateCamera();
    }
  };

  // Expose reset function to parent
  useEffect(() => {
    onResetCamera.current = resetToSolarOverview;
  }, [onResetCamera]);

  return null;
};

const SolarSystemView: React.FC<SolarSystemViewProps> = ({ starId, onBack, onPlanetClick }) => {
  const [starDetail, setStarDetail] = useState<StarDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeSpeed, setTimeSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);
  const [isPlanetSpinning, setIsPlanetSpinning] = useState(true);
  const [isStarSpinning, setIsStarSpinning] = useState(true);
  const resetCameraRef = useRef<() => void>(() => {});

  useEffect(() => {
    const loadStarDetail = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log(`🌟 SolarSystemView: Loading star detail for ID ${starId}`);
        const detail = await stellarApi.getStarDetail(starId);
        setStarDetail(detail);
        console.log(`✅ SolarSystemView: Loaded ${detail.name} with ${detail.planets.length} planets`);
      } catch (err) {
        console.error('❌ SolarSystemView: Failed to load star detail:', err);
        setError(err instanceof Error ? err.message : 'Failed to load star system');
      } finally {
        setLoading(false);
      }
    };

    loadStarDetail();

    // Cleanup function
    return () => {
      console.log('🧹 SolarSystemView: Cleaning up component');
      setStarDetail(null);
      setError(null);
    };
  }, [starId]);

  // Calculate orbital data for planets
  const planetOrbitalData = useMemo(() => {
    if (!starDetail?.planets) return [];

    return starDetail.planets.map((planet, index) => {
      const orbitalRadius = Math.max(2, (planet.sma_au || (index + 1)) * 4); // Scale for visibility
      const orbitalPeriod = Math.sqrt(Math.pow(planet.sma_au || (index + 1), 3)) * 2; // Simplified Kepler's law

      return {
        planet,
        orbitalRadius,
        orbitalPeriod: Math.max(5, orbitalPeriod), // Minimum period for visibility
      };
    });
  }, [starDetail]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="text-xl mb-2">Loading Solar System...</div>
          <div className="text-sm text-gray-400">Star ID: {starId}</div>
        </div>
      </div>
    );
  }

  if (error || !starDetail) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <div className="text-xl mb-2 text-red-400">Error Loading System</div>
          <div className="text-sm text-gray-400 mb-4">{error}</div>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
          >
            Back to Galaxy
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative bg-black">
      {/* Back button */}
      <button
        onClick={onBack}
        className="absolute top-4 left-4 z-10 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
      >
        ← Back to Galaxy
      </button>

      {/* Camera controls */}
      <div className="absolute top-4 right-4 z-10 space-y-2">
        <button
          onClick={() => resetCameraRef.current && resetCameraRef.current()}
          className="block w-full px-3 py-1 bg-blue-800 bg-opacity-80 text-white text-sm rounded border border-blue-600 hover:bg-blue-700"
        >
          🎯 Reset View
        </button>
      </div>

      {/* System info */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-90 text-white p-4 rounded-lg border border-gray-600">
        <h2 className="text-xl font-bold text-yellow-300">{starDetail.name} System</h2>
        <div className="text-sm text-gray-300">
          <div>🪐 {starDetail.planets.length} planets</div>
          <div>⭐ {starDetail.spectral_type} star</div>
          <div>📏 {starDetail.distance_ly.toFixed(1)} light-years</div>
        </div>
      </div>

      {/* Time controls */}
      <div className="absolute bottom-4 left-4 z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg border border-gray-600">
        <div className="text-sm font-bold mb-2">Time Control</div>
        <div className="flex items-center gap-2 mb-2">
          <button
            onClick={() => setIsPaused(!isPaused)}
            className="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs"
          >
            {isPaused ? '▶️' : '⏸️'}
          </button>
          <span className="text-xs">Speed: {timeSpeed}x</span>
        </div>
        <div className="flex gap-1 mb-2">
          {[0.1, 0.5, 1, 2, 5, 10].map(speed => (
            <button
              key={speed}
              onClick={() => setTimeSpeed(speed)}
              className={`px-2 py-1 rounded text-xs ${
                timeSpeed === speed
                  ? 'bg-yellow-600 text-black'
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            >
              {speed}x
            </button>
          ))}
        </div>

        {/* Rotation controls */}
        <div className="text-xs font-bold mb-1 text-gray-300">Rotation Control</div>
        <div className="flex gap-1">
          <button
            onClick={() => setIsPlanetSpinning(!isPlanetSpinning)}
            className={`px-2 py-1 rounded text-xs ${
              isPlanetSpinning
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-red-600 hover:bg-red-700'
            }`}
          >
            🪐 {isPlanetSpinning ? 'Stop' : 'Start'}
          </button>
          <button
            onClick={() => setIsStarSpinning(!isStarSpinning)}
            className={`px-2 py-1 rounded text-xs ${
              isStarSpinning
                ? 'bg-orange-600 hover:bg-orange-700'
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
          >
            ⭐ {isStarSpinning ? 'Stop' : 'Start'}
          </button>
        </div>
      </div>

      <Canvas
        camera={{ position: [20, 15, 20], fov: 60 }}
        style={{ background: 'transparent' }}
        gl={{
          preserveDrawingBuffer: true,
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onCreated={({ gl }) => {
          console.log('🎨 SolarSystemView: WebGL context created');
          gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }}
      >
        {/* Enhanced multi-source lighting for stunning visuals */}
        <ambientLight intensity={0.3} color="#ffffff" />

        {/* Main star light */}
        <pointLight
          position={[0, 0, 0]}
          intensity={4}
          color={starDetail ? starDetail.spectral_type?.charAt(0) === 'M' ? "#FFAD51" : "#FFD700" : "#FFD700"}
          distance={150}
          decay={2}
        />

        {/* Additional directional lights for better planet illumination */}
        <directionalLight
          position={[15, 10, 10]}
          intensity={0.6}
          color="#ffffff"
          castShadow
        />

        <directionalLight
          position={[-10, 5, 15]}
          intensity={0.3}
          color="#4169E1"
        />

        {/* Spot light for dramatic effect */}
        <spotLight
          position={[0, 20, 0]}
          intensity={0.5}
          angle={Math.PI / 3}
          penumbra={0.5}
          color="#FFD700"
          castShadow
        />

        {/* Distant starfield background */}
        <Stars
          radius={500}
          depth={50}
          count={2000}
          factor={8}
          saturation={0}
          fade
          speed={0.1}
        />

        {/* Enhanced camera controls */}
        <OrbitControls
          enablePan={true} // Enable panning to navigate around the solar system
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={150}
          autoRotate={false} // Disable auto-rotate to allow manual navigation
          enableDamping={true}
          dampingFactor={0.05}
          rotateSpeed={0.5}
          zoomSpeed={1.0}
          panSpeed={1.0}
          maxPolarAngle={Math.PI}
          minPolarAngle={0}
          makeDefault
        />

        {/* Star */}
        {starDetail && <StarComponent star={starDetail} isStarSpinning={isStarSpinning} />}

        {/* Orbiting Planets */}
        {planetOrbitalData.map((data, index) => (
          <OrbitingPlanet
            key={data.planet.planet_id}
            planet={data.planet}
            orbitalRadius={data.orbitalRadius}
            orbitalPeriod={data.orbitalPeriod}
            time={isPaused ? 0 : timeSpeed}
            isPlanetSpinning={isPlanetSpinning}
            timeSpeed={isPaused ? 0 : timeSpeed}
            onClick={() => {
              console.log(`🪐 Clicked planet: ${data.planet.name}`);
              if (onPlanetClick) {
                // Convert string planet_id to number for consistency
                const planetId = typeof data.planet.planet_id === 'string'
                  ? parseInt(data.planet.planet_id)
                  : data.planet.planet_id;
                console.log(`🔄 Converting planet ID: "${data.planet.planet_id}" (${typeof data.planet.planet_id}) → ${planetId} (${typeof planetId})`);
                onPlanetClick(planetId);
              }
            }}
          />
        ))}

        <CameraController onResetCamera={resetCameraRef} />

        {/* Enhanced post-processing effects for stunning visuals */}
        <Suspense fallback={null}>
          <EffectComposer>
            <Bloom
              intensity={0.3}
              luminanceThreshold={0.9}
              luminanceSmoothing={0.9}
              height={300}
            />
            <ChromaticAberration
              offset={[0.0005, 0.0005]}
            />
            <ToneMapping
              adaptive={true}
              resolution={256}
              middleGrey={0.6}
              maxLuminance={16.0}
              averageLuminance={1.0}
              adaptationRate={1.0}
            />
          </EffectComposer>
        </Suspense>
      </Canvas>
    </div>
  );
};

export default SolarSystemView;
