import React, { useRef, useState, useEffect, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  Float,
  Html,
  Ring,
  Sparkles
} from '@react-three/drei';
import * as THREE from 'three';
import { stellarApi } from '../services/stellarApi';
import type { Planet, Moon } from '../types/stellar';

interface PlanetDetailViewProps {
  planetId: number;
  starId: number;
  onBack: () => void;
}

// Moon data for major moons (this would come from database in full implementation)
const MAJOR_MOONS: { [key: string]: Moon[] } = {
  'Earth': [
    {
      moon_id: 1,
      planet_id: 3,
      name: 'Luna',
      mass_earth: 0.0123,
      radius_earth: 0.273,
      distance_planet_radii: 60.3,
      orbital_period_days: 27.3,
      composition: 'rocky',
      discovery_year: -3000
    }
  ],
  'Mars': [
    {
      moon_id: 2,
      planet_id: 4,
      name: '<PERSON><PERSON><PERSON>',
      mass_earth: 1.47e-8,
      radius_earth: 0.0018,
      distance_planet_radii: 2.76,
      orbital_period_days: 0.32,
      composition: 'rocky',
      discovery_year: 1877
    },
    {
      moon_id: 3,
      planet_id: 4,
      name: 'Deimos',
      mass_earth: 2.4e-9,
      radius_earth: 0.00098,
      distance_planet_radii: 6.94,
      orbital_period_days: 1.26,
      composition: 'rocky',
      discovery_year: 1877
    }
  ],
  'Jupiter': [
    {
      moon_id: 4,
      planet_id: 5,
      name: 'Io',
      mass_earth: 0.015,
      radius_earth: 0.286,
      distance_planet_radii: 5.9,
      orbital_period_days: 1.77,
      composition: 'rocky',
      discovery_year: 1610
    },
    {
      moon_id: 5,
      planet_id: 5,
      name: 'Europa',
      mass_earth: 0.008,
      radius_earth: 0.245,
      distance_planet_radii: 9.4,
      orbital_period_days: 3.55,
      composition: 'ice',
      discovery_year: 1610
    },
    {
      moon_id: 6,
      planet_id: 5,
      name: 'Ganymede',
      mass_earth: 0.025,
      radius_earth: 0.413,
      distance_planet_radii: 15.0,
      orbital_period_days: 7.15,
      composition: 'ice_rock',
      discovery_year: 1610
    },
    {
      moon_id: 7,
      planet_id: 5,
      name: 'Callisto',
      mass_earth: 0.018,
      radius_earth: 0.378,
      distance_planet_radii: 26.3,
      orbital_period_days: 16.69,
      composition: 'ice_rock',
      discovery_year: 1610
    }
  ],
  'Saturn': [
    {
      moon_id: 8,
      planet_id: 6,
      name: 'Titan',
      mass_earth: 0.0225,
      radius_earth: 0.404,
      distance_planet_radii: 20.4,
      orbital_period_days: 15.95,
      composition: 'ice_rock',
      discovery_year: 1655
    }
  ]
};

// TimeController component to handle time updates inside Canvas
const TimeController: React.FC<{
  timeSpeed: number;
  isPaused: boolean;
  onTimeUpdate: (time: number) => void;
}> = ({ timeSpeed, isPaused, onTimeUpdate }) => {
  useFrame((state) => {
    if (!isPaused) {
      onTimeUpdate(state.clock.elapsedTime * timeSpeed);
    }
  });
  return null;
};

interface OrbitingMoonProps {
  moon: Moon;
  planetRadius: number;
  time: number;
  timeSpeed: number;
  isPaused: boolean;
  onClick: () => void;
}

const OrbitingMoon: React.FC<OrbitingMoonProps> = ({
  moon,
  planetRadius,
  time,
  timeSpeed,
  isPaused,
  onClick
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const groupRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);

  // Enhanced moon size calculation for much better visibility
  const moonSize = Math.max(0.3, (moon.radius_earth || 0.27) * planetRadius * 0.8); // Much larger for visibility
  // Convert distance_km to planet radii (Earth radius ≈ 6371 km)
  const earthRadiusKm = 6371;
  const distanceInPlanetRadii = moon.distance_km / earthRadiusKm;
  // Scale orbital distance for better visibility (Luna at ~60 Earth radii becomes ~12 visual units)
  const orbitalDistance = Math.max(planetRadius * 4.0, distanceInPlanetRadii * planetRadius * 0.3);

  // Removed console.log to prevent spam

  // Realistic orbital animation with proper timing and controls
  useFrame((state) => {
    if (groupRef.current && !isPaused) {
      // Realistic orbital mechanics: 1x speed = real orbital period
      // At 1x speed: 1 real day = 1 second in simulation
      const realOrbitalPeriodDays = moon.orbital_period_days;
      const scaledTime = state.clock.elapsedTime * timeSpeed;

      // Convert to angle: complete orbit in orbital period
      // Luna (27.3 days) takes 27.3 seconds for full orbit at 1x speed
      const angle = (scaledTime / realOrbitalPeriodDays) * 2 * Math.PI;

      // Add slight orbital inclination for visual interest
      const inclination = 0.1;
      groupRef.current.position.x = Math.cos(angle) * orbitalDistance;
      groupRef.current.position.z = Math.sin(angle) * orbitalDistance;
      groupRef.current.position.y = Math.sin(angle) * orbitalDistance * inclination;
    }

    if (meshRef.current && !isPaused) {
      // Realistic moon rotation (tidally locked for most moons)
      meshRef.current.rotation.y += 0.001 * timeSpeed;
    }
  });

  const getMoonColor = (): string => {
    // Use database color if available
    if (moon.color_hex) return moon.color_hex;

    // Fallback based on dominant color
    if (moon.dominant_color) {
      switch (moon.dominant_color.toLowerCase()) {
        case 'gray': return '#C0C0C0';
        case 'white': return '#F5F5F5';
        case 'brown': return '#8B7355';
        case 'red': return '#CD5C5C';
        case 'yellow': return '#F0E68C';
        default: return '#A0A0A0';
      }
    }

    // Final fallback
    return '#A0A0A0';
  };

  return (
    <>
      {/* Orbital ring - more subtle and elegant */}
      <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
        <ringGeometry args={[orbitalDistance - 0.005, orbitalDistance + 0.005, 128]} />
        <meshBasicMaterial
          color="#888888"
          transparent
          opacity={hovered ? 0.4 : 0.15}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Orbiting moon */}
      <group ref={groupRef}>
        <Float speed={0.3} rotationIntensity={0.05} floatIntensity={0.02}>
          <mesh
            ref={meshRef}
            onClick={onClick}
            onPointerEnter={() => setHovered(true)}
            onPointerLeave={() => setHovered(false)}
            castShadow
            receiveShadow
          >
            <sphereGeometry args={[moonSize, 32, 32]} />
            <meshStandardMaterial
              color={getMoonColor()}
              roughness={moon.albedo ? (1 - moon.albedo) : 0.8}
              metalness={0.1}
              emissive={getMoonColor()}
              emissiveIntensity={hovered ? 0.3 : 0.1}
            />
          </mesh>

          {/* Enhanced moon glow for better visibility */}
          <mesh scale={moonSize * 1.3}>
            <sphereGeometry args={[1, 16, 16]} />
            <meshBasicMaterial
              color={getMoonColor()}
              transparent
              opacity={hovered ? 0.4 : 0.2}
              side={THREE.BackSide}
            />
          </mesh>
        </Float>

        {/* Moon label */}
        {hovered && (
          <Html position={[0, moonSize + 0.3, 0]} center>
            <div className="bg-black bg-opacity-90 text-white px-2 py-1 rounded text-xs border border-gray-500">
              <div className="font-bold text-cyan-300">{moon.name}</div>
              <div>Period: {moon.orbital_period_days.toFixed(1)} days</div>
              <div>Type: {moon.surface_composition || moon.moon_type || 'Unknown'}</div>
            </div>
          </Html>
        )}
      </group>
    </>
  );
};

// Distant Sun component for planet view
interface DistantSunProps {
  planet: Planet;
  starName?: string;
}

const DistantSun: React.FC<DistantSunProps> = ({ planet, starName = 'Sol' }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const coronaRef = useRef<THREE.Mesh>(null);

  // Calculate sun distance and size based on planet's orbital distance
  const auToVisualUnits = 25; // Reduced from 50 to make sun more visible
  const sunDistance = (planet.sma_au || 1.0) * auToVisualUnits;

  // Sun size calculation: Real sun is ~109 Earth radii, but scale for visibility
  const realSunRadius = 109; // Earth radii
  const sunSize = Math.max(4.0, realSunRadius * 0.05); // Larger and more visible

  // Removed console.log to prevent spam

  useFrame((state) => {
    if (meshRef.current && coronaRef.current) {
      // Gentle pulsing effect for the sun
      const time = state.clock.elapsedTime;
      const scale = 1 + Math.sin(time * 0.8) * 0.15;
      meshRef.current.scale.setScalar(scale);

      // Corona rotation and pulsing
      coronaRef.current.rotation.z += 0.005;
      const coronaScale = 1 + Math.sin(time * 1.2) * 0.1;
      coronaRef.current.scale.setScalar(coronaScale);
    }
  });

  return (
    <group position={[sunDistance * 0.7, sunDistance * 0.3, sunDistance * 0.2]}>
      {/* Main sun core */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[sunSize, 64, 64]} />
        <meshBasicMaterial
          color="#FDB813"
          emissive="#FF4500"
          emissiveIntensity={1.5}
        />
      </mesh>

      {/* Sun corona/atmosphere */}
      <mesh ref={coronaRef} scale={sunSize * 1.5}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#FFD700"
          transparent
          opacity={0.6}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Outer glow */}
      <mesh scale={sunSize * 2.0}>
        <sphereGeometry args={[1, 16, 16]} />
        <meshBasicMaterial
          color="#FFAA00"
          transparent
          opacity={0.3}
          side={THREE.BackSide}
        />
      </mesh>

      {/* Sun rays effect */}
      <mesh scale={sunSize * 2.5}>
        <sphereGeometry args={[1, 8, 8]} />
        <meshBasicMaterial
          color="#FFFF88"
          transparent
          opacity={0.2}
          side={THREE.BackSide}
        />
      </mesh>
    </group>
  );
};

interface PlanetComponentProps {
  planet: Planet;
  moons: Moon[];
  time: number;
  timeSpeed: number;
  isPaused: boolean;
  starName?: string;
}

const PlanetComponent: React.FC<PlanetComponentProps> = ({
  planet,
  moons,
  time,
  timeSpeed,
  isPaused,
  starName
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const atmosphereRef = useRef<THREE.Mesh>(null);
  const cloudRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // Enhanced planet size for stunning detail view
  const planetRadius = Math.max(2.0, planet.radius_earth * 2.5);

  // Enhanced rotation with realistic timing
  useFrame((state) => {
    if (meshRef.current) {
      // Realistic rotation based on planet's day length
      const rotationSpeed = (2 * Math.PI) / (planet.period_days || 1);
      meshRef.current.rotation.y += rotationSpeed * 0.01;
    }
    if (atmosphereRef.current) {
      // Atmosphere rotates slightly slower for dynamic effect
      atmosphereRef.current.rotation.y += 0.003;
      atmosphereRef.current.rotation.x += 0.001;
    }
    if (cloudRef.current) {
      // Clouds move at different speed for realism
      cloudRef.current.rotation.y += 0.004;
    }
  });

  // Enhanced planet color using database data
  const getPlanetColor = (): string => {
    // Prioritize database color_hex
    if (planet.color_hex) {
      return planet.color_hex;
    }

    // Use dominant_color mapping
    if (planet.dominant_color) {
      switch (planet.dominant_color.toLowerCase()) {
        case 'gray': return '#8C7853';
        case 'yellow-orange': return '#FFC649';
        case 'blue-green': return '#6B93D6';
        case 'red-orange': return '#CD5C5C';
        case 'orange-brown': return '#D8CA9D';
        case 'golden-yellow': return '#FAD5A5';
        case 'cyan-blue': return '#4FD0E7';
        case 'deep blue': return '#4B70DD';
        default: break;
      }
    }

    // Fallback to composition-based colors
    switch (planet.composition.toLowerCase()) {
      case 'rocky': return '#8B4513';
      case 'gas_giant': return '#FFA500';
      case 'ice_giant': return '#4169E1';
      default: return '#696969';
    }
  };

  // Enhanced material properties based on planet data
  const getMaterialProperties = () => {
    const baseColor = getPlanetColor();

    // Special handling for Earth to make it stunning
    if (planet.name === 'Earth') {
      return {
        color: '#1E90FF', // Vibrant blue for oceans
        roughness: 0.3,
        metalness: 0.2,
        emissive: '#0066CC',
        emissiveIntensity: 0.1,
        transparent: false,
        opacity: 1.0
      };
    }

    // Adjust material properties based on composition and visual texture
    switch (planet.composition.toLowerCase()) {
      case 'gas_giant':
        return {
          color: baseColor,
          roughness: 0.2,
          metalness: 0.0,
          emissive: baseColor,
          emissiveIntensity: 0.15,
          transparent: true,
          opacity: 0.95
        };
      case 'ice_giant':
        return {
          color: baseColor,
          roughness: 0.1,
          metalness: 0.3,
          emissive: baseColor,
          emissiveIntensity: 0.08,
          transparent: true,
          opacity: 0.9
        };
      case 'rocky':
        return {
          color: baseColor,
          roughness: 0.7,
          metalness: 0.15,
          emissive: '#000000',
          emissiveIntensity: 0.0,
          transparent: false,
          opacity: 1.0
        };
      default:
        return {
          color: baseColor,
          roughness: 0.6,
          metalness: 0.2,
          emissive: '#000000',
          emissiveIntensity: 0.0,
          transparent: false,
          opacity: 1.0
        };
    }
  };

  const materialProps = getMaterialProperties();

  return (
    <group position={[0, 0, 0]}>
      {/* Main planet */}
      <Float speed={0.1} rotationIntensity={0.01} floatIntensity={0.005}>
        <mesh ref={meshRef} castShadow receiveShadow>
          <sphereGeometry args={[planetRadius, 256, 256]} />
          <meshStandardMaterial
            color={materialProps.color}
            roughness={materialProps.roughness}
            metalness={materialProps.metalness}
            emissive={materialProps.emissive}
            emissiveIntensity={materialProps.emissiveIntensity}
            transparent={materialProps.transparent}
            opacity={materialProps.opacity}
          />
        </mesh>
      </Float>

      {/* Enhanced cloud layers for Earth-like planets */}
      {planet.name === 'Earth' && (
        <mesh ref={cloudRef} scale={planetRadius * 1.02}>
          <sphereGeometry args={[1, 128, 128]} />
          <meshStandardMaterial
            color="#FFFFFF"
            transparent
            opacity={0.4}
            roughness={0.8}
            metalness={0.0}
          />
        </mesh>
      )}

      {/* Enhanced atmosphere for planets that have one */}
      {planet.has_atmosphere && (
        <>
          {/* Outer atmosphere glow */}
          <mesh ref={atmosphereRef} scale={planetRadius * 1.4}>
            <sphereGeometry args={[1, 128, 128]} />
            <meshBasicMaterial
              color={planet.name === 'Earth' ? '#87CEEB' : materialProps.color}
              transparent
              opacity={planet.atmospheric_effects?.includes('thick') ? 0.25 : 0.15}
              side={THREE.BackSide}
            />
          </mesh>

          {/* Inner atmosphere layer */}
          <mesh scale={planetRadius * 1.2}>
            <sphereGeometry args={[1, 128, 128]} />
            <meshBasicMaterial
              color={planet.name === 'Earth' ? '#B0E0E6' : materialProps.color}
              transparent
              opacity={planet.atmospheric_effects?.includes('thick') ? 0.2 : 0.1}
              side={THREE.BackSide}
            />
          </mesh>

          {/* Atmospheric shimmer effect */}
          <mesh scale={planetRadius * 1.6}>
            <sphereGeometry args={[1, 64, 64]} />
            <meshBasicMaterial
              color="#FFFFFF"
              transparent
              opacity={0.05}
              side={THREE.BackSide}
            />
          </mesh>
        </>
      )}

      {/* Ring systems for gas giants (generic implementation) */}
      {(planet.composition === 'gas_giant' || planet.composition === 'ice_giant') &&
       planet.name.toLowerCase().includes('saturn') && (
        <group rotation={[Math.PI / 2, 0, 0]}>
          <Ring args={[planetRadius * 1.3, planetRadius * 2.5, 256]}>
            <meshStandardMaterial
              color="#D4AF37"
              transparent
              opacity={0.8}
              side={THREE.DoubleSide}
            />
          </Ring>
        </group>
      )}

      {/* Render moons with time controls */}
      {moons.map((moon) => (
        <OrbitingMoon
          key={moon.moon_id}
          moon={moon}
          planetRadius={planetRadius}
          time={time}
          timeSpeed={timeSpeed}
          isPaused={isPaused}
          onClick={() => {
            console.log(`🌙 Clicked moon: ${moon.name}`);
          }}
        />
      ))}

      {/* Distant sun for context */}
      <DistantSun planet={planet} starName={starName} />
    </group>
  );
};

const PlanetDetailView: React.FC<PlanetDetailViewProps> = ({
  planetId,
  starId,
  onBack
}) => {
  const [planet, setPlanet] = useState<Planet | null>(null);
  const [starName, setStarName] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [time, setTime] = useState(0);
  const [timeSpeed, setTimeSpeed] = useState(1);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    const loadPlanetDetail = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get planet data from the correct star system
        console.log(`🪐 PlanetDetailView: Loading planet ${planetId} from star ${starId}`);
        const starDetail = await stellarApi.getStarDetail(starId);

        // Handle both string and number planet IDs from API with robust matching
        console.log(`🔍 PlanetDetailView: Looking for planet ID ${planetId} (type: ${typeof planetId})`);
        console.log(`📋 Available planets:`, starDetail.planets.map(p => ({
          id: p.planet_id,
          name: p.name,
          idType: typeof p.planet_id
        })));

        const foundPlanet = starDetail.planets.find(p => {
          const apiPlanetId = typeof p.planet_id === 'string' ? parseInt(p.planet_id) : p.planet_id;
          const searchPlanetId = typeof planetId === 'string' ? parseInt(planetId) : planetId;
          const match = apiPlanetId === searchPlanetId;

          if (match) {
            console.log(`🎯 PlanetDetailView: Match found! API ID "${p.planet_id}" (${typeof p.planet_id}) matches search ID "${planetId}" (${typeof planetId})`);
          }

          return match;
        });

        if (foundPlanet) {
          console.log(`✅ PlanetDetailView: Successfully found planet ${foundPlanet.name} with ID ${foundPlanet.planet_id}`);
          setPlanet(foundPlanet);
          setStarName(starDetail.name);
        } else {
          console.error(`❌ PlanetDetailView: Planet with ID ${planetId} not found in star ${starId}`);
          console.error(`🔍 Search details: planetId=${planetId} (${typeof planetId})`);
          console.error(`📋 Available planets:`, starDetail.planets.map(p => ({
            id: p.planet_id,
            name: p.name,
            idType: typeof p.planet_id,
            parsedId: typeof p.planet_id === 'string' ? parseInt(p.planet_id) : p.planet_id
          })));
          throw new Error(`Planet with ID ${planetId} not found`);
        }
      } catch (err) {
        console.error('Failed to load planet detail:', err);
        setError(err instanceof Error ? err.message : 'Failed to load planet');
      } finally {
        setLoading(false);
      }
    };

    loadPlanetDetail();
  }, [planetId, starId]);

  const moons = useMemo(() => {
    if (!planet) return [];
    // Use real moon data from the API
    return planet.moons || [];
  }, [planet]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black">
        <div className="text-white text-xl animate-pulse">
          🪐 Loading Planet Details...
        </div>
      </div>
    );
  }

  if (error || !planet) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black">
        <div className="text-red-400 text-xl">
          ❌ {error || 'Planet not found'}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative bg-black">
      {/* Back button */}
      <button
        onClick={onBack}
        className="absolute top-4 left-4 z-10 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
      >
        ← Back to Solar System
      </button>

      {/* Enhanced Time controls - Fixed positioning */}
      <div
        className="fixed bottom-4 left-4 bg-black bg-opacity-95 text-white p-4 rounded-lg border-2 border-blue-500 shadow-lg"
        style={{ zIndex: 9999 }}
      >
        <div className="text-base font-bold mb-3 text-blue-300">🕐 Time Control</div>
        <div className="flex items-center gap-3 mb-3">
          <button
            onClick={() => setIsPaused(!isPaused)}
            className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm font-bold transition-colors"
          >
            {isPaused ? '▶️ Play' : '⏸️ Pause'}
          </button>
          <span className="text-sm font-bold text-yellow-300">Speed: {timeSpeed}x</span>
        </div>
        <div className="flex gap-1 mb-3 flex-wrap">
          {[0.1, 0.5, 1, 2, 5, 10].map(speed => (
            <button
              key={speed}
              onClick={() => setTimeSpeed(speed)}
              className={`px-3 py-1 rounded text-sm font-bold transition-colors ${
                timeSpeed === speed
                  ? 'bg-yellow-500 text-black shadow-md'
                  : 'bg-gray-700 hover:bg-gray-600 text-white'
              }`}
            >
              {speed}x
            </button>
          ))}
        </div>
        <div className="text-xs text-gray-300 border-t border-gray-600 pt-2">
          🌙 Moon orbits: Real astronomical timing<br/>
          1x = 1 day per second (Luna: 27.3s orbit)
        </div>
      </div>

      {/* Enhanced Planet info panel */}
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-95 text-white p-4 rounded-lg border border-cyan-400 max-w-md max-h-[80vh] overflow-y-auto">
        <h2 className="text-2xl font-bold text-cyan-400 mb-3">{planet.name}</h2>

        {/* Physical Characteristics */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-yellow-300 mb-2">🪐 Physical Properties</h3>
          <div className="text-sm space-y-1">
            <div><span className="text-gray-400">Mass:</span> {planet.mass_earth?.toFixed(3) || '?'} Earth masses</div>
            <div><span className="text-gray-400">Radius:</span> {planet.radius_earth?.toFixed(3) || '?'} Earth radii</div>
            <div><span className="text-gray-400">Type:</span> {planet.composition}</div>
            {planet.dominant_color && (
              <div><span className="text-gray-400">Color:</span> {planet.dominant_color}</div>
            )}
            {planet.surface_temp_k && (
              <div><span className="text-gray-400">Surface Temp:</span> {(planet.surface_temp_k - 273.15).toFixed(0)}°C</div>
            )}
          </div>
        </div>

        {/* Orbital Characteristics */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-blue-300 mb-2">🌌 Orbital Properties</h3>
          <div className="text-sm space-y-1">
            <div><span className="text-gray-400">Distance:</span> {planet.sma_au?.toFixed(2) || '?'} AU</div>
            <div><span className="text-gray-400">Period:</span> {planet.period_days?.toFixed(0) || '?'} days</div>
            {planet.eccentricity && (
              <div><span className="text-gray-400">Eccentricity:</span> {planet.eccentricity.toFixed(3)}</div>
            )}
          </div>
        </div>

        {/* Atmospheric Data */}
        {planet.atmosphere && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-green-300 mb-2">🌬️ Atmosphere</h3>
            <div className="text-sm">
              <div className="text-gray-300">{planet.atmosphere}</div>
              {planet.atmospheric_effects && (
                <div className="text-xs text-gray-400 mt-1">Effects: {planet.atmospheric_effects}</div>
              )}
            </div>
          </div>
        )}

        {/* Raw Materials */}
        {planet.raw_materials && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-orange-300 mb-2">⛏️ Raw Materials</h3>
            <div className="text-sm">
              <div className="text-gray-300">{planet.raw_materials}</div>
              {planet.material_category && (
                <div className="text-xs text-gray-400 mt-1">Category: {planet.material_category}</div>
              )}
              <div className="text-xs text-gray-400 mt-1">
                Mineral Richness: {(planet.mineral_richness * 100).toFixed(0)}%
              </div>
            </div>
          </div>
        )}

        {/* Moons */}
        {moons.length > 0 && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-purple-300 mb-2">🌙 Moons ({moons.length})</h3>
            <div className="text-sm space-y-2 max-h-32 overflow-y-auto">
              {moons.map((moon, index) => (
                <div key={index} className="border-l-2 border-purple-400 pl-2">
                  <div className="font-medium text-purple-200">{moon.name}</div>
                  {moon.raw_materials && (
                    <div className="text-xs text-gray-400">{moon.raw_materials.substring(0, 80)}...</div>
                  )}
                  <div className="text-xs text-gray-500">
                    Distance: {(moon.distance_km / 1000).toFixed(0)}k km •
                    Period: {moon.orbital_period_days.toFixed(1)} days
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Habitability */}
        {planet.in_habitable_zone && (
          <div className="mb-2">
            <div className="text-green-400 font-semibold text-center p-2 bg-green-900 bg-opacity-30 rounded">
              🌍 In Habitable Zone
            </div>
          </div>
        )}

        {/* Exploration Status */}
        <div className="text-xs text-gray-500 text-center mt-2">
          Status: {planet.exploration_status} •
          {planet.discovery_year && `Discovered: ${planet.discovery_year}`}
        </div>
      </div>

      {/* 3D Scene */}
      <Canvas camera={{ position: [5, 3, 5], fov: 75 }} shadows>
        {/* Time controller for animations */}
        <TimeController
          timeSpeed={timeSpeed}
          isPaused={isPaused}
          onTimeUpdate={setTime}
        />

        {/* Enhanced lighting setup for better planet visibility */}
        <ambientLight intensity={0.4} color="#404080" />
        <pointLight
          position={[10, 8, 10]}
          intensity={2.0}
          color="#FFF8DC"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <directionalLight
          position={[-8, -8, -4]}
          intensity={1.0}
          color="#87CEEB"
          castShadow
        />
        <spotLight
          position={[0, 15, 0]}
          angle={0.4}
          penumbra={0.3}
          intensity={0.8}
          color="#FFE4B5"
          castShadow
        />

        {/* Enhanced background stars */}
        <Stars radius={500} depth={100} count={2000} factor={6} saturation={0} fade speed={0.5} />

        {/* Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2.5}
          maxDistance={30}
          autoRotate={false}
          dampingFactor={0.05}
          enableDamping={true}
        />

        {/* Planet with moons and distant sun */}
        <PlanetComponent
          planet={planet}
          moons={moons}
          time={time}
          timeSpeed={timeSpeed}
          isPaused={isPaused}
          starName={starName}
        />

        {/* Enhanced cosmic effects */}
        <Sparkles
          count={300}
          scale={[100, 100, 100]}
          size={3}
          speed={0.2}
          opacity={0.4}
          color="#FFD700"
        />

        {/* Additional particle effects for gas giants */}
        {(planet.composition === 'gas_giant' || planet.composition === 'ice_giant') && (
          <Sparkles
            count={150}
            scale={[20, 20, 20]}
            size={1}
            speed={0.3}
            opacity={0.6}
            color={planet.composition === 'ice_giant' ? "#4169E1" : "#FFA500"}
          />
        )}
      </Canvas>
    </div>
  );
};

export default PlanetDetailView;
