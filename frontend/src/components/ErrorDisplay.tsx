import React from 'react';
import { useGameStore } from '../store/gameStore';

interface ErrorDisplayProps {
  error: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  const { setError } = useGameStore();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="glass-panel p-6 max-w-md mx-4">
        <div className="flex items-center mb-4">
          <div className="w-8 h-8 bg-neon-red rounded-full flex items-center justify-center mr-3">
            <span className="text-white font-bold">!</span>
          </div>
          <h3 className="text-lg font-bold text-neon-red">Error</h3>
        </div>
        
        <p className="text-gray-300 mb-6">{error}</p>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={() => setError(null)}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
          >
            Dismiss
          </button>
          <button
            onClick={() => {
              setError(null);
              window.location.reload();
            }}
            className="px-4 py-2 bg-neon-cyan hover:bg-cyan-400 text-black rounded transition-colors"
          >
            Reload
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
