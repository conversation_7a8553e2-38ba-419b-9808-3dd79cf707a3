import React, { useState, useEffect } from 'react';
import { portMonitorService } from '../services/portMonitor';
import type { PortInfo, PortScanResult } from '../services/portMonitor';

interface PortMonitorProps {
  isOpen: boolean;
  onClose: () => void;
}

const PortMonitor: React.FC<PortMonitorProps> = ({ isOpen, onClose }) => {
  const [portData, setPortData] = useState<PortScanResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [serviceAvailable, setServiceAvailable] = useState(false);
  const [killingPort, setKillingPort] = useState<number | null>(null);

  useEffect(() => {
    if (!isOpen) return;

    let unsubscribe: (() => void) | null = null;

    const initializePortMonitor = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if port monitor service is available
        const available = await portMonitorService.isAvailable();
        setServiceAvailable(available);

        if (!available) {
          setError('Port monitoring service is not available. Make sure the health dashboard is running on port 8086.');
          setLoading(false);
          return;
        }

        // Get initial port data
        const initialData = await portMonitorService.getPortScan();
        setPortData(initialData);

        // Subscribe to real-time updates
        unsubscribe = portMonitorService.subscribe((data) => {
          setPortData(data);
        });

        setLoading(false);
      } catch (err) {
        console.error('Failed to initialize port monitor:', err);
        setError(err instanceof Error ? err.message : 'Failed to load port data');
        setLoading(false);
      }
    };

    initializePortMonitor();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [isOpen]);

  const handleKillPort = async (port: number) => {
    if (!confirm(`Are you sure you want to kill the process on port ${port}?`)) {
      return;
    }

    try {
      setKillingPort(port);
      const result = await portMonitorService.killPort(port);
      
      if (result.success) {
        alert(`Successfully killed process on port ${port}`);
        // Refresh port data
        const newData = await portMonitorService.getPortScan();
        setPortData(newData);
      } else {
        alert(`Failed to kill port ${port}: ${result.message}`);
      }
    } catch (error) {
      console.error('Kill port failed:', error);
      alert(`Error killing port ${port}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setKillingPort(null);
    }
  };

  const getPortStatusColor = (port: PortInfo): string => {
    if (port.state === 'LISTEN') {
      return port.isExpected ? 'bg-green-500' : 'bg-yellow-500';
    }
    return port.isExpected ? 'bg-red-500' : 'bg-gray-500';
  };

  const getPortStatusText = (port: PortInfo): string => {
    if (port.state === 'LISTEN') {
      return port.isExpected ? 'Running' : 'Unexpected';
    }
    return port.isExpected ? 'Missing' : 'Closed';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-11/12 h-5/6 max-w-6xl border border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-lg">🔌</span>
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Port Monitor</h2>
              <p className="text-gray-400 text-sm">Real-time system port monitoring</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 h-full overflow-hidden">
          {loading && (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
                <p className="text-gray-400">Loading port data...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-900 border border-red-700 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-2">
                <span className="text-red-400">⚠️</span>
                <span className="text-red-200">{error}</span>
              </div>
              {!serviceAvailable && (
                <div className="mt-2 text-sm text-red-300">
                  <p>To enable port monitoring:</p>
                  <ol className="list-decimal list-inside mt-1 space-y-1">
                    <li>Navigate to the health dashboard: <code className="bg-red-800 px-1 rounded">http://localhost:8086</code></li>
                    <li>Or start the health dashboard service if it's not running</li>
                  </ol>
                </div>
              )}
            </div>
          )}

          {portData && (
            <>
              {/* Summary */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                <div className="bg-gray-800 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">{portData.summary.total}</div>
                  <div className="text-xs text-gray-400">Total Ports</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400">{portData.summary.listening}</div>
                  <div className="text-xs text-gray-400">Listening</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400">{portData.summary.expected}</div>
                  <div className="text-xs text-gray-400">Expected</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-400">{portData.summary.unexpected}</div>
                  <div className="text-xs text-gray-400">Unexpected</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-red-400">{portData.summary.missing}</div>
                  <div className="text-xs text-gray-400">Missing</div>
                </div>
              </div>

              {/* Last Update */}
              <div className="mb-4 text-sm text-gray-400">
                Last scan: {new Date(portData.timestamp).toLocaleString()}
              </div>

              {/* Port List */}
              <div className="bg-gray-800 rounded-lg overflow-hidden">
                <div className="max-h-96 overflow-y-auto">
                  <table className="w-full">
                    <thead className="bg-gray-700 sticky top-0">
                      <tr>
                        <th className="text-left p-3 text-gray-300">Status</th>
                        <th className="text-left p-3 text-gray-300">Port</th>
                        <th className="text-left p-3 text-gray-300">Service</th>
                        <th className="text-left p-3 text-gray-300">Protocol</th>
                        <th className="text-left p-3 text-gray-300">Process</th>
                        <th className="text-left p-3 text-gray-300">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {portData.ports.map((port, index) => (
                        <tr key={`${port.port}-${index}`} className="border-t border-gray-700 hover:bg-gray-750">
                          <td className="p-3">
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${getPortStatusColor(port)}`}></div>
                              <span className="text-sm text-gray-300">{getPortStatusText(port)}</span>
                            </div>
                          </td>
                          <td className="p-3">
                            <div className="font-semibold text-white">{port.port}</div>
                          </td>
                          <td className="p-3">
                            <div className="text-white">{port.service || 'Unknown'}</div>
                            {port.description && (
                              <div className="text-xs text-gray-400">{port.description}</div>
                            )}
                          </td>
                          <td className="p-3">
                            <span className="text-gray-300 uppercase text-sm">{port.protocol}</span>
                          </td>
                          <td className="p-3">
                            {port.processName && port.pid ? (
                              <div className="text-sm">
                                <div className="text-white">{port.processName}</div>
                                <div className="text-gray-400">PID: {port.pid}</div>
                              </div>
                            ) : (
                              <span className="text-gray-500">-</span>
                            )}
                          </td>
                          <td className="p-3">
                            {port.state === 'LISTEN' && (
                              <button
                                onClick={() => handleKillPort(port.port)}
                                disabled={killingPort === port.port}
                                className="px-2 py-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white text-xs rounded transition-colors"
                                title={`Kill process on port ${port.port}`}
                              >
                                {killingPort === port.port ? '...' : '🗑️ Kill'}
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PortMonitor;
