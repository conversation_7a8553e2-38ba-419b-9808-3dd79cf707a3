import React, { useRef, useEffect, useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { simpleApiClient } from '../services/api-simple';

// Default system positions for layout
const DEFAULT_SYSTEM_POSITIONS = [
  { id: 'sys-1', name: 'Sol', x: 400, y: 300 },
  { id: 'sys-2', name: 'Alpha Centauri', x: 600, y: 200 },
  { id: 'sys-3', name: 'Proxima', x: 300, y: 450 },
  { id: 'sys-4', name: '<PERSON>', x: 700, y: 400 },
  { id: 'sys-5', name: 'Sirius', x: 500, y: 500 },
];

const GalaxyMap: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });
  const [systemConnections, setSystemConnections] = useState<Map<string, string[]>>(new Map());
  const { fleets, systems, selectedFleetId, selectedSystemId, selectSystem, selectFleet } = useGameStore();

  // Use real systems with default positions for layout
  const displaySystems = systems.length > 0
    ? systems.map(sys => {
        const defaultPos = DEFAULT_SYSTEM_POSITIONS.find(p => p.id === sys.id);
        return {
          ...sys,
          x: defaultPos?.x || 400,
          y: defaultPos?.y || 300
        };
      })
    : DEFAULT_SYSTEM_POSITIONS;

  // Load system adjacency data
  useEffect(() => {
    const loadSystemConnections = async () => {
      const connections = new Map<string, string[]>();

      for (const system of displaySystems) {
        try {
          const response = await simpleApiClient.getSystemNeighbors(system.id);
          connections.set(system.id, response.neighbors.map(n => n.id));
        } catch (error) {
          console.warn(`Failed to load neighbors for ${system.id}:`, error);
          connections.set(system.id, []);
        }
      }

      setSystemConnections(connections);
    };

    if (displaySystems.length > 0) {
      loadSystemConnections();
    }
  }, [displaySystems]);

  // Update canvas size on window resize
  useEffect(() => {
    const updateCanvasSize = () => {
      const container = canvasRef.current?.parentElement;
      if (container) {
        setCanvasSize({
          width: container.clientWidth - 32, // Account for padding
          height: container.clientHeight - 32,
        });
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, []);

  // Draw the galaxy map
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw connections between systems based on real adjacency data
    ctx.strokeStyle = 'rgba(100, 100, 100, 0.3)';
    ctx.lineWidth = 1;
    displaySystems.forEach((system) => {
      const neighbors = systemConnections.get(system.id) || [];
      neighbors.forEach((neighborId) => {
        const neighbor = displaySystems.find(s => s.id === neighborId);
        if (neighbor) {
          ctx.beginPath();
          ctx.moveTo(system.x, system.y);
          ctx.lineTo(neighbor.x, neighbor.y);
          ctx.stroke();
        }
      });
    });

    // Draw systems
    displaySystems.forEach((system) => {
      const isSelected = system.id === selectedSystemId;
      const fleetsInSystem = fleets.filter(f => f.system_id === system.id);
      
      // System circle
      ctx.beginPath();
      ctx.arc(system.x, system.y, isSelected ? 12 : 8, 0, 2 * Math.PI);
      ctx.fillStyle = isSelected ? '#00ffff' : fleetsInSystem.length > 0 ? '#fbbf24' : '#6b7280';
      ctx.fill();
      
      if (isSelected) {
        ctx.strokeStyle = '#00ffff';
        ctx.lineWidth = 2;
        ctx.stroke();
      }

      // System name
      ctx.fillStyle = '#ffffff';
      ctx.font = '12px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(system.name, system.x, system.y - 20);

      // Fleet count indicator
      if (fleetsInSystem.length > 0) {
        ctx.fillStyle = '#00ff00';
        ctx.font = '10px sans-serif';
        ctx.fillText(`${fleetsInSystem.length}`, system.x, system.y + 25);
      }
    });

    // Draw fleet indicators
    fleets.forEach((fleet) => {
      const system = displaySystems.find(s => s.id === fleet.system_id);
      if (!system) return;

      const isSelected = fleet.id === selectedFleetId;
      const fleetIndex = fleets.filter(f => f.system_id === fleet.system_id).indexOf(fleet);
      
      // Position fleets around the system
      const angle = (fleetIndex * 60) * (Math.PI / 180);
      const radius = 20;
      const fleetX = system.x + Math.cos(angle) * radius;
      const fleetY = system.y + Math.sin(angle) * radius;

      // Fleet marker
      ctx.beginPath();
      ctx.arc(fleetX, fleetY, isSelected ? 6 : 4, 0, 2 * Math.PI);
      
      // Color based on supply level
      let fleetColor = '#10b981'; // green
      if (fleet.supply < 40) fleetColor = '#ef4444'; // red
      else if (fleet.supply < 80) fleetColor = '#fbbf24'; // yellow
      
      ctx.fillStyle = fleetColor;
      ctx.fill();
      
      if (isSelected) {
        ctx.strokeStyle = '#00ffff';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    });

  }, [fleets, displaySystems, selectedFleetId, selectedSystemId, canvasSize, systemConnections]);

  // Handle canvas clicks
  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Check if clicked on a system
    for (const system of displaySystems) {
      const distance = Math.sqrt(Math.pow(x - system.x, 2) + Math.pow(y - system.y, 2));
      if (distance <= 12) {
        selectSystem(system.id);
        return;
      }
    }

    // Check if clicked on a fleet
    for (const fleet of fleets) {
      const system = displaySystems.find(s => s.id === fleet.system_id);
      if (!system) continue;

      const fleetIndex = fleets.filter(f => f.system_id === fleet.system_id).indexOf(fleet);
      const angle = (fleetIndex * 60) * (Math.PI / 180);
      const radius = 20;
      const fleetX = system.x + Math.cos(angle) * radius;
      const fleetY = system.y + Math.sin(angle) * radius;

      const distance = Math.sqrt(Math.pow(x - fleetX, 2) + Math.pow(y - fleetY, 2));
      if (distance <= 6) {
        selectFleet(fleet.id);
        return;
      }
    }

    // Clear selection if clicked on empty space
    selectSystem(null);
    selectFleet(null);
  };

  return (
    <div className="h-full w-full p-4">
      <div className="h-full w-full relative glass-panel rounded-lg overflow-hidden">
        <canvas
          ref={canvasRef}
          width={canvasSize.width}
          height={canvasSize.height}
          onClick={handleCanvasClick}
          className="cursor-pointer"
          style={{ 
            background: 'radial-gradient(circle at 30% 40%, rgba(29, 78, 216, 0.15), transparent 50%), radial-gradient(circle at 80% 10%, rgba(139, 92, 246, 0.15), transparent 50%), radial-gradient(circle at 40% 80%, rgba(6, 182, 212, 0.1), transparent 50%)'
          }}
        />
        
        {/* Map legend */}
        <div className="absolute top-4 left-4 glass-panel p-3 text-xs text-white">
          <div className="font-semibold mb-2 text-neon-cyan">Galaxy Map</div>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
              <span>Empty System</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-gold rounded-full"></div>
              <span>System with Fleets</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-green rounded-full"></div>
              <span>Fleet (Healthy)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-neon-red rounded-full"></div>
              <span>Fleet (Low Supply)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GalaxyMap;
