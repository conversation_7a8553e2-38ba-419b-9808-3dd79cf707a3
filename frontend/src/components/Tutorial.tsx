import React, { useState } from 'react';
import { useGameStore } from '../store/gameStore';

// Tutorial version - increment when adding new features
const TUTORIAL_VERSION = '1.0.0';
const GAME_VERSION = 'Alpha 0.1.0';

interface TutorialStep {
  title: string;
  content: React.ReactNode;
  image?: string;
}

const tutorialSteps: TutorialStep[] = [
  {
    title: "Welcome to Galactic Genesis",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Welcome, Commander! You are about to embark on an epic journey to build a galactic empire. 
          Galactic Genesis is a 4X strategy game where you'll explore, expand, exploit, and exterminate 
          across the galaxy.
        </p>
        <div className="bg-gray-800 p-4 rounded-lg">
          <h4 className="text-neon-cyan font-semibold mb-2">Game Objectives:</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Command fleets across star systems</li>
            <li>• Manage supply lines and logistics</li>
            <li>• Engage in tactical combat</li>
            <li>• Expand your galactic influence</li>
          </ul>
        </div>
      </div>
    )
  },
  {
    title: "Understanding the Interface",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          The game interface is divided into several key areas:
        </p>
        <div className="grid grid-cols-1 gap-3">
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-cyan font-semibold">🎮 Top HUD</h5>
            <p className="text-sm text-gray-300">Shows current turn, fleet count, total supply, and connection status</p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-gold font-semibold">🚀 Left Panel - Fleet Command</h5>
            <p className="text-sm text-gray-300">Lists all your fleets with their status and allows fleet selection</p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-purple font-semibold">🗺️ Center - Galaxy Map</h5>
            <p className="text-sm text-gray-300">Interactive star map showing systems, fleets, and connections</p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-green font-semibold">📊 Right Panel - System Details</h5>
            <p className="text-sm text-gray-300">Shows information about selected systems and fleets</p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Fleet Management",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Fleets are the backbone of your empire. Each fleet has several important attributes:
        </p>
        <div className="space-y-3">
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-cyan font-semibold">Supply Level</h5>
            <p className="text-sm text-gray-300">
              Determines fleet effectiveness in combat. Low supply = weaker performance.
            </p>
            <div className="flex items-center space-x-2 mt-2">
              <div className="w-3 h-3 bg-neon-green rounded-full"></div>
              <span className="text-xs">80+ Supply (Healthy)</span>
              <div className="w-3 h-3 bg-neon-gold rounded-full"></div>
              <span className="text-xs">40-80 Supply (Caution)</span>
              <div className="w-3 h-3 bg-neon-red rounded-full"></div>
              <span className="text-xs">&lt;40 Supply (Critical)</span>
            </div>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-purple font-semibold">Fleet Stance</h5>
            <p className="text-sm text-gray-300">
              Affects combat performance: <span className="text-neon-red">Aggressive</span> (high damage), 
              <span className="text-neon-green"> Defensive</span> (damage reduction), 
              <span className="text-neon-cyan"> Neutral</span> (balanced)
            </p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Galaxy Map Navigation",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          The galaxy map is your strategic overview. Here's how to use it effectively:
        </p>
        <div className="space-y-3">
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-cyan font-semibold">🌟 Star Systems</h5>
            <p className="text-sm text-gray-300">
              Click on systems to select them. Systems with fleets appear in gold, empty systems in gray.
            </p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-gold font-semibold">🚀 Fleet Markers</h5>
            <p className="text-sm text-gray-300">
              Small colored dots around systems represent fleets. Click to select individual fleets.
            </p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-gray-400 font-semibold">🔗 System Connections</h5>
            <p className="text-sm text-gray-300">
              Gray lines show possible movement routes between nearby systems.
            </p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Issuing Orders",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Command your fleets by issuing orders. All orders are executed at the start of the next turn:
        </p>
        <div className="space-y-3">
          <div className="bg-blue-900 bg-opacity-50 p-3 rounded border border-blue-600">
            <h5 className="text-blue-300 font-semibold">📍 Move Orders</h5>
            <p className="text-sm text-gray-300">
              Send fleets to adjacent systems only. The game will show available destinations when you select a fleet. Moving consumes supply.
            </p>
          </div>
          <div className="bg-red-900 bg-opacity-50 p-3 rounded border border-red-600">
            <h5 className="text-red-300 font-semibold">⚔️ Attack Orders</h5>
            <p className="text-sm text-gray-300">
              Attack enemy fleets in the same system. Combat is resolved automatically with supply and stance affecting outcomes.
            </p>
          </div>
          <div className="bg-green-900 bg-opacity-50 p-3 rounded border border-green-600">
            <h5 className="text-green-300 font-semibold">🔋 Resupply Orders</h5>
            <p className="text-sm text-gray-300">
              Restore fleet supply. Essential for maintaining combat effectiveness.
            </p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Combat System",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Combat in Galactic Genesis is tactical and supply-based:
        </p>
        <div className="space-y-3">
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-red font-semibold">⚔️ Battle Resolution</h5>
            <p className="text-sm text-gray-300">
              Combat is automatic when attack orders are executed. Both fleets take damage based on their supply and stance.
            </p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-purple font-semibold">🎯 Damage Calculation</h5>
            <p className="text-sm text-gray-300">
              Higher supply = more damage dealt. Fleet stance provides modifiers: Aggressive (+damage), Defensive (-damage taken).
            </p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-gold font-semibold">💥 Fleet Destruction</h5>
            <p className="text-sm text-gray-300">
              Fleets are destroyed when their supply reaches zero. Plan your attacks carefully!
            </p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Turn-Based Gameplay",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Galactic Genesis operates on a turn-based system with real-time execution:
        </p>
        <div className="space-y-3">
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-cyan font-semibold">🕐 Turn Progression</h5>
            <p className="text-sm text-gray-300">
              Turns advance automatically every few seconds. Watch the turn counter in the top HUD.
            </p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-purple font-semibold">📋 Order Execution</h5>
            <p className="text-sm text-gray-300">
              Orders are queued and executed at the target turn. You'll see real-time updates as they happen.
            </p>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <h5 className="text-neon-green font-semibold">⚡ Live Updates</h5>
            <p className="text-sm text-gray-300">
              The interface updates in real-time via WebSocket. Watch fleets move and battles resolve live!
            </p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Strategic Tips",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Master these strategies to dominate the galaxy:
        </p>
        <div className="space-y-3">
          <div className="bg-blue-900 bg-opacity-30 p-3 rounded">
            <h5 className="text-blue-300 font-semibold">🔋 Supply Management</h5>
            <p className="text-sm text-gray-300">
              Keep fleets well-supplied. Low supply fleets are vulnerable and ineffective in combat.
            </p>
          </div>
          <div className="bg-purple-900 bg-opacity-30 p-3 rounded">
            <h5 className="text-purple-300 font-semibold">🎯 Tactical Positioning</h5>
            <p className="text-sm text-gray-300">
              Position fleets strategically. Control key systems and maintain supply lines.
            </p>
          </div>
          <div className="bg-green-900 bg-opacity-30 p-3 rounded">
            <h5 className="text-green-300 font-semibold">⚔️ Combat Timing</h5>
            <p className="text-sm text-gray-300">
              Attack when you have supply advantage. Use aggressive stance for decisive battles.
            </p>
          </div>
          <div className="bg-gold-900 bg-opacity-30 p-3 rounded">
            <h5 className="text-yellow-300 font-semibold">📊 Information Warfare</h5>
            <p className="text-sm text-gray-300">
              Monitor enemy fleet movements. Use the galaxy map to track threats and opportunities.
            </p>
          </div>
        </div>
      </div>
    )
  },
  {
    title: "Quick Reference Guide",
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Keep this reference handy while playing:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h5 className="text-neon-cyan font-semibold">🎮 Controls</h5>
            <div className="text-sm space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-400">Click System:</span>
                <span className="text-white">Select system</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Click Fleet:</span>
                <span className="text-white">Select fleet</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Fleet Actions:</span>
                <span className="text-white">Use left panel buttons</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h5 className="text-neon-purple font-semibold">📊 Status Colors</h5>
            <div className="text-sm space-y-1">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-neon-green rounded-full"></div>
                <span className="text-white">Healthy (80+ supply)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-neon-gold rounded-full"></div>
                <span className="text-white">Caution (40-80 supply)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-neon-red rounded-full"></div>
                <span className="text-white">Critical (&lt;40 supply)</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h5 className="text-neon-gold font-semibold">⚔️ Combat Tips</h5>
            <div className="text-sm space-y-1 text-gray-300">
              <div>• High supply = more damage</div>
              <div>• Aggressive stance = +damage</div>
              <div>• Defensive stance = -damage taken</div>
              <div>• 0 supply = fleet destroyed</div>
            </div>
          </div>

          <div className="space-y-3">
            <h5 className="text-neon-red font-semibold">🚨 Important</h5>
            <div className="text-sm space-y-1 text-gray-300">
              <div>• Orders execute next turn</div>
              <div>• Moving costs supply</div>
              <div>• Resupply regularly</div>
              <div>• Watch enemy movements</div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 p-4 rounded-lg mt-6">
          <h5 className="text-neon-cyan font-semibold mb-2">🎯 Victory Conditions</h5>
          <p className="text-sm text-gray-300">
            Currently in development: Destroy all enemy fleets, control key systems,
            or achieve technological supremacy. For now, focus on expanding your fleet
            presence and mastering the combat system!
          </p>
        </div>

        <div className="text-center mt-6 pt-4 border-t border-gray-700">
          <p className="text-xs text-gray-500">
            Galactic Genesis {GAME_VERSION} • Tutorial v{TUTORIAL_VERSION}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Tutorial last updated: December 2024
          </p>
        </div>
      </div>
    )
  }
];

const Tutorial: React.FC = () => {
  const { hideTutorial } = useGameStore();
  const [currentStep, setCurrentStep] = useState(0);

  const completeTutorial = () => {
    localStorage.setItem('galactic-genesis-tutorial-seen', 'true');
    hideTutorial();
  };

  const nextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentTutorial = tutorialSteps[currentStep];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="glass-panel max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-neon-cyan">{currentTutorial.title}</h2>
            <p className="text-sm text-gray-400">
              Step {currentStep + 1} of {tutorialSteps.length}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={completeTutorial}
              className="text-sm text-gray-400 hover:text-gray-300 underline"
            >
              Skip Tutorial
            </button>
            <button
              onClick={hideTutorial}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        {/* Progress bar */}
        <div className="px-6 pt-4">
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-neon-cyan h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentTutorial.content}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center p-6 border-t border-gray-700">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`px-4 py-2 rounded transition-colors ${
              currentStep === 0
                ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                : 'bg-gray-600 hover:bg-gray-500 text-white'
            }`}
          >
            Previous
          </button>

          <div className="flex space-x-2">
            {tutorialSteps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentStep ? 'bg-neon-cyan' : 'bg-gray-600 hover:bg-gray-500'
                }`}
              />
            ))}
          </div>

          {currentStep === tutorialSteps.length - 1 ? (
            <button
              onClick={completeTutorial}
              className="px-6 py-2 bg-neon-cyan hover:bg-cyan-400 text-black rounded transition-colors font-semibold"
            >
              Start Playing!
            </button>
          ) : (
            <button
              onClick={nextStep}
              className="px-4 py-2 bg-neon-cyan hover:bg-cyan-400 text-black rounded transition-colors"
            >
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Tutorial;
