import React from 'react';

const LoadingScreen: React.FC = () => {
  return (
    <div data-testid="loading-screen" className="h-screen w-screen bg-gray-900 starfield-bg flex items-center justify-center">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-neon-cyan mb-4 animate-pulse-slow">
            GALACTIC GENESIS
          </h1>
          <p className="text-xl text-gray-300">
            Initializing galactic empire...
          </p>
        </div>
        
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-neon-purple border-b-transparent rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
          </div>
        </div>
        
        <div className="text-gray-400">
          <p>Loading fleets, systems, and galactic data...</p>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
