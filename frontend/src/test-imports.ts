// Test file to verify imports work
import type { 
  Fleet, 
  MoveOrderPayload, 
  AttackOrderPayload, 
  ResupplyOrderPayload 
} from './types/game';

// This should compile without errors if exports are correct
const testFleet: Fleet = {
  id: 'test',
  empire_id: 'emp-1',
  system_id: 'sys-1',
  stance: 'neutral',
  supply: 100,
  system_name: 'Test System'
};

const testMovePayload: MoveOrderPayload = {
  fleetId: 'test',
  toSystemId: 'sys-2'
};

const testAttackPayload: AttackOrderPayload = {
  fleetId: 'test',
  targetFleetId: 'target'
};

const testResupplyPayload: ResupplyOrderPayload = {
  fleetId: 'test',
  amount: 50
};

console.log('All imports work correctly:', {
  testFleet,
  testMovePayload,
  testAttackPayload,
  testResupplyPayload
});
