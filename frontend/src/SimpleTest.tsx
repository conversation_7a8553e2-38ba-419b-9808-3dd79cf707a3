import React, { useState, useEffect } from 'react';

const SimpleTest: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    const runSimpleTests = async () => {
      addLog('🚀 Starting simple tests...');

      // Test 1: Basic React functionality
      addLog('✅ React is working');
      addLog('✅ useState hook working');
      addLog('✅ useEffect hook working');

      // Test 2: Try importing game store
      try {
        addLog('🎮 Testing game store import...');
        const gameStoreModule = await import('./store/gameStore');
        addLog('✅ Game store module imported');
        
        const { useGameStore } = gameStoreModule;
        addLog('✅ useGameStore hook extracted');
        
        const store = useGameStore.getState();
        addLog(`✅ Store accessed: isLoading=${store.isLoading}`);
        
      } catch (error) {
        addLog(`❌ Game store failed: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Test 3: Try importing services
      try {
        addLog('🔧 Testing services...');
        
        const stellarModule = await import('./services/stellarApi');
        addLog('✅ Stellar API imported');
        
        const wsModule = await import('./services/websocket');
        addLog('✅ WebSocket service imported');
        
      } catch (error) {
        addLog(`❌ Services failed: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Test 4: Try importing Three.js
      try {
        addLog('🎨 Testing Three.js...');
        
        const THREE = await import('three');
        addLog('✅ Three.js imported');
        
        const fiberModule = await import('@react-three/fiber');
        addLog('✅ React Three Fiber imported');
        
        const dreiModule = await import('@react-three/drei');
        addLog('✅ React Three Drei imported');
        
      } catch (error) {
        addLog(`❌ Three.js failed: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Test 5: Try importing main components
      try {
        addLog('🧩 Testing main components...');
        
        const Galaxy3DModule = await import('./components/Galaxy3D');
        addLog('✅ Galaxy3D imported');
        
        const GameHUDModule = await import('./components/GameHUD');
        addLog('✅ GameHUD imported');
        
      } catch (error) {
        addLog(`❌ Components failed: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Test 6: Environment check
      addLog('🔧 Environment variables:');
      addLog(`  NODE_ENV: ${import.meta.env.NODE_ENV || 'undefined'}`);
      addLog(`  VITE_API_URL: ${import.meta.env.VITE_API_URL || 'undefined'}`);
      addLog(`  VITE_WS_URL: ${import.meta.env.VITE_WS_URL || 'undefined'}`);
      addLog(`  VITE_DISABLE_WS: ${import.meta.env.VITE_DISABLE_WS || 'undefined'}`);

      addLog('🏁 Simple tests completed!');
      setIsLoading(false);
    };

    runSimpleTests();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#000', 
      color: '#0f0',
      minHeight: '100vh'
    }}>
      <h1>🧪 Simple System Test</h1>
      
      {isLoading && (
        <div style={{ color: '#ff0', marginBottom: '20px' }}>
          🔄 Running tests...
        </div>
      )}
      
      <div style={{ 
        backgroundColor: '#111', 
        padding: '15px', 
        border: '1px solid #333',
        maxHeight: '70vh',
        overflowY: 'auto'
      }}>
        {logs.map((log, index) => (
          <div key={index} style={{ 
            marginBottom: '5px',
            color: log.includes('❌') ? '#f44' : 
                   log.includes('⚠️') ? '#fa0' : '#0f0'
          }}>
            {log}
          </div>
        ))}
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <a href="/" style={{ color: '#0ff' }}>← Back to Main Game</a>
        <br />
        <a href="/?debug=true" style={{ color: '#0ff' }}>→ Basic Debug</a>
        <br />
        <a href="/?systemtest=true" style={{ color: '#0ff' }}>→ Full System Test</a>
      </div>
    </div>
  );
};

export default SimpleTest;
