// Debug: Add console log immediately
console.log('🚀 main.tsx: Starting execution...');
console.log('🌐 main.tsx: Location:', window.location.href);
console.log('🕐 main.tsx: Timestamp:', new Date().toISOString());
console.log('🖥️ main.tsx: User Agent:', navigator.userAgent);
console.log('📱 main.tsx: Screen:', `${screen.width}x${screen.height}`);
console.log('🎮 main.tsx: WebGL Support:', !!window.WebGLRenderingContext);
console.log('🔧 main.tsx: Environment:', {
  NODE_ENV: import.meta.env.NODE_ENV,
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD
});

import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import TestPage from './TestPage.tsx'
import MinimalApp from './MinimalApp.tsx'
import DebugTest from './DebugTest.tsx'
import SimpleTest from './SimpleTest.tsx'
import ErrorBoundary from './components/ErrorBoundary.tsx'
import MinimalGalaxyApp from './MinimalGalaxyApp.tsx'
import SafeApp from './SafeApp.tsx'
import UltraSafeApp from './UltraSafeApp.tsx'
import ProgressiveGalaxy3D from './components/ProgressiveGalaxy3D.tsx'
// import EnhancedGalaxy3D from './components/EnhancedGalaxy3D.tsx'
import EnhancedApp from './EnhancedApp.tsx'
import StunningApp from './StunningApp.tsx'
import GraphicsComparison from './components/GraphicsComparison.tsx'

try {
  console.log('📦 main.tsx: Dependencies imported successfully');
  console.log('🔍 main.tsx: Available components:', {
    App: !!App,
    TestPage: !!TestPage,
    MinimalApp: !!MinimalApp,
    StunningApp: !!StunningApp
  });

  // Check what to show based on URL params
  const urlParams = new URLSearchParams(window.location.search);
  const showDebug = urlParams.has('debug');
  const showMinimal = urlParams.has('minimal');
  const showTest = urlParams.has('test');
  const showSystemTest = urlParams.has('systemtest');
  const showSimpleTest = urlParams.has('simple');
  const showMinimalGalaxy = urlParams.has('mingalaxy');
  const showSafeMode = urlParams.has('safe');
  const showUltraSafe = urlParams.has('ultrasafe');
  const showProgressive = urlParams.has('progressive');
  const showEnhanced = urlParams.has('enhanced');
  const showStunning = urlParams.has('stunning');
  const showComparison = urlParams.has('compare');

  if (showDebug) {
    console.log('🔧 Debug mode activated');
    document.getElementById('root')!.innerHTML = `
      <div style="padding: 20px; font-family: monospace; background: #000; color: #0f0; min-height: 100vh;">
        <h1>🔧 Debug Mode</h1>
        <p>✅ HTML loaded</p>
        <p>✅ JavaScript executing</p>
        <p>✅ DOM manipulation working</p>
        <p>✅ Imports successful</p>
        <p>Timestamp: ${new Date().toISOString()}</p>
        <button onclick="alert('Button works!')">Test Button</button>
        <br><br>
        <a href="/" style="color: #0ff;">← Back to Main</a>
        <br>
        <a href="/?minimal=true" style="color: #0ff;">→ Minimal Test</a>
      </div>
    `;
  } else {
    console.log('📱 main.tsx: Loading React components...');

    let ComponentToRender = App;
    if (showMinimal) {
      ComponentToRender = MinimalApp;
    } else if (showTest) {
      ComponentToRender = TestPage;
    } else if (showSystemTest) {
      ComponentToRender = DebugTest;
    } else if (showSimpleTest) {
      ComponentToRender = SimpleTest;
    } else if (showMinimalGalaxy) {
      ComponentToRender = MinimalGalaxyApp;
    } else if (showSafeMode) {
      ComponentToRender = SafeApp;
    } else if (showUltraSafe) {
      ComponentToRender = UltraSafeApp;
    } else if (showProgressive) {
      ComponentToRender = ProgressiveGalaxy3D;
    } else if (showEnhanced) {
      ComponentToRender = EnhancedApp;
    } else if (showStunning) {
      ComponentToRender = StunningApp;
    } else if (showComparison) {
      ComponentToRender = GraphicsComparison;
    }

    console.log('🎯 main.tsx: Rendering component:', ComponentToRender.name);
    createRoot(document.getElementById('root')!).render(
      <ErrorBoundary>
        <ComponentToRender />
      </ErrorBoundary>
    );
    console.log('✅ main.tsx: Render complete');
  }

} catch (error) {
  console.error('💥 main.tsx: Fatal error:', error);
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
  document.getElementById('root')!.innerHTML = `
    <div style="padding: 20px; font-family: monospace; background: #000; color: #f00;">
      <h1>💥 Fatal Error</h1>
      <p>Error: ${errorMessage}</p>
      <p>Stack: ${errorStack}</p>
    </div>
  `;
}
