import React, { useState, useEffect } from 'react';
import { useGameStore } from './store/gameStore';
import StunningGalaxy3D from './components/StunningGalaxy3D';
import GameHUD from './components/GameHUD';
import FleetList from './components/FleetList';
import OrderForm from './components/OrderForm';
import Tutorial from './components/Tutorial';
import ResourceDisplay from './components/ResourceDisplay';
import ErrorBoundary from './components/ErrorBoundary';

const ErrorDisplay: React.FC<{ error: string }> = ({ error }) => (
  <div className="h-screen w-screen bg-gray-900 flex items-center justify-center">
    <div className="text-center p-8 bg-red-900 bg-opacity-50 rounded-lg border border-red-500 max-w-md">
      <h2 className="text-xl font-bold text-red-300 mb-4">🚨 System Error</h2>
      <p className="text-red-200 mb-4">{error}</p>
      <button 
        onClick={() => window.location.reload()} 
        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
      >
        Reload Application
      </button>
    </div>
  </div>
);

const LoadingScreen: React.FC = () => (
  <div className="h-screen w-screen bg-gray-900 starfield-bg flex items-center justify-center">
    <div className="text-center">
      <div className="text-4xl mb-4">🌌</div>
      <div className="text-xl text-white mb-2 animate-pulse">Galactic Genesis</div>
      <div className="text-sm text-gray-400">Initializing stunning galactic visualization...</div>
      <div className="mt-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
      </div>
    </div>
  </div>
);

const StunningApp: React.FC = () => {
  const {
    initialize,
    isLoading,
    error,
    showOrderForm,
    showTutorialModal,
    showOrderFormFor,
    hideOrderForm,
    showTutorial,
    hideTutorial
  } = useGameStore();
  
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const init = async () => {
      try {
        console.log('🚀 StunningApp: Initializing game...');
        await initialize();
        setIsInitialized(true);
        console.log('✅ StunningApp: Game initialized successfully');
      } catch (err) {
        console.error('❌ StunningApp: Failed to initialize:', err);
      }
    };

    init();
  }, [initialize]);

  // Show loading screen during initialization
  if (!isInitialized || isLoading) {
    return <LoadingScreen />;
  }

  // Show error screen if there's an error
  if (error) {
    return <ErrorDisplay error={error} />;
  }

  return (
    <ErrorBoundary>
      <div className="h-screen w-screen stunning-bg particle-bg overflow-hidden relative">
        {/* Enhanced background with animated gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 animate-pulse-slow"></div>
        <div className="absolute inset-0 bg-gradient-to-tl from-pink-900/10 via-transparent to-cyan-900/10"></div>
        
        {/* Game HUD - Enhanced top bar */}
        <div className="relative z-20">
          <GameHUD />
        </div>

        {/* Compact Resource Display */}
        <div className="absolute top-16 left-4 z-20">
          <div className="glass-panel cosmic-glow stellar-border p-3 rounded-lg hologram-effect">
            <ResourceDisplay empireId="emp-1" />
          </div>
        </div>

        {/* Main game area */}
        <div className="flex h-full pt-16 relative z-10">
          {/* Left sidebar - Enhanced Fleet management */}
          <div className="w-80 glass-panel cosmic-glow stellar-border m-4 p-4 overflow-y-auto backdrop-blur-md shadow-2xl hologram-effect">
            <div className="mb-4">
              <h2 className="text-lg font-bold nebula-text mb-2 flex items-center">
                🚀 Fleet Command
                <span className="ml-2 text-xs bg-blue-600 px-2 py-1 rounded">ENHANCED</span>
              </h2>
            </div>
            <FleetList />
          </div>

          {/* Center - Stunning Galaxy map */}
          <div className="flex-1 relative">
            <StunningGalaxy3D />
            
            {/* Overlay controls */}
            <div className="absolute top-4 left-4 z-10 space-y-2">
              <button
                onClick={() => showTutorial()}
                className="px-3 py-2 bg-blue-600/80 text-white text-sm rounded-lg border border-blue-400/50 hover:bg-blue-500/80 backdrop-blur-sm transition-all duration-200 shadow-lg"
              >
                📚 Tutorial
              </button>
              <button
                onClick={() => showOrderFormFor('move')}
                className="px-3 py-2 bg-green-600/80 text-white text-sm rounded-lg border border-green-400/50 hover:bg-green-500/80 backdrop-blur-sm transition-all duration-200 shadow-lg"
              >
                📋 Orders
              </button>
            </div>

            {/* Visual enhancement indicator */}
            <div className="absolute bottom-4 left-4 z-10">
              <div className="glass-panel px-3 py-2 text-xs text-gray-300 border border-purple-400/30">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                  <span>STUNNING GRAPHICS MODE</span>
                </div>
                <div className="text-purple-300 mt-1">
                  ✨ Enhanced shaders • 🌌 Volumetric nebulae • 🎨 Post-processing
                </div>
              </div>
            </div>
          </div>

          {/* Right sidebar - System info (when selected) */}
          <div className="w-80 glass-panel m-4 p-4 overflow-y-auto backdrop-blur-md border border-gray-600/50 shadow-2xl">
            <div className="mb-4">
              <h2 className="text-lg font-bold text-white mb-2 flex items-center">
                🌟 System Analysis
                <span className="ml-2 text-xs bg-purple-600 px-2 py-1 rounded">DETAILED</span>
              </h2>
            </div>
            
            <div className="text-gray-300 text-sm">
              <div className="mb-4 p-3 bg-gray-800/50 rounded border border-gray-600/30">
                <div className="text-yellow-300 font-semibold mb-2">🎯 Select a star system</div>
                <div className="text-xs text-gray-400">
                  Click on any star to view detailed information about the system, 
                  including stellar properties, planetary data, and habitability analysis.
                </div>
              </div>
              
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span>🌟 Total Systems:</span>
                  <span className="text-blue-300">29</span>
                </div>
                <div className="flex justify-between">
                  <span>🪐 Known Planets:</span>
                  <span className="text-green-300">14</span>
                </div>
                <div className="flex justify-between">
                  <span>🌍 Habitable Worlds:</span>
                  <span className="text-purple-300">3</span>
                </div>
                <div className="flex justify-between">
                  <span>📡 Scan Range:</span>
                  <span className="text-yellow-300">20 ly</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Modals */}
        {showOrderForm && (
          <div className="absolute inset-0 z-50 backdrop-blur-sm">
            <OrderForm />
          </div>
        )}
        
        {showTutorialModal && (
          <div className="absolute inset-0 z-50 backdrop-blur-sm">
            <Tutorial />
          </div>
        )}

        {/* Performance indicator */}
        <div className="absolute bottom-4 right-4 z-20 text-xs text-gray-500">
          <div className="glass-panel px-2 py-1 border border-gray-700/50">
            🎮 Stunning Mode Active
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default StunningApp;
