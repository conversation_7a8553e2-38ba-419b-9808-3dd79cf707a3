import React, { useState, useEffect } from 'react';
import MinimalGalaxy3D from './components/MinimalGalaxy3D';

const MinimalGalaxyApp: React.FC = () => {
  const [errorCount, setErrorCount] = useState(0);
  const [lastError, setLastError] = useState<string>('');

  useEffect(() => {
    // Monitor console errors
    const originalError = console.error;
    console.error = (...args) => {
      setErrorCount(prev => prev + 1);
      setLastError(args.join(' '));
      originalError(...args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  return (
    <div style={{ width: '100vw', height: '100vh', backgroundColor: '#000' }}>
      {/* Error monitoring */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        backgroundColor: errorCount > 0 ? '#ff4444' : '#444',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontFamily: 'monospace',
        fontSize: '12px'
      }}>
        <div>Errors: {errorCount}</div>
        {lastError && (
          <div style={{ maxWidth: '300px', wordBreak: 'break-word' }}>
            Last: {lastError.substring(0, 100)}...
          </div>
        )}
      </div>

      {/* Minimal Galaxy */}
      <MinimalGalaxy3D />
    </div>
  );
};

export default MinimalGalaxyApp;
