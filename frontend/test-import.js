// Test import to debug the module issue
console.log('Testing portMonitor imports...');

try {
  // Test dynamic import
  import('./src/services/portMonitor.ts').then(module => {
    console.log('✅ Dynamic import successful');
    console.log('Available exports:', Object.keys(module));
    console.log('PortInfo available:', 'PortInfo' in module);
    console.log('PortScanResult available:', 'PortScanResult' in module);
    console.log('portMonitorService available:', 'portMonitorService' in module);
  }).catch(error => {
    console.error('❌ Dynamic import failed:', error);
  });
} catch (error) {
  console.error('❌ Import test failed:', error);
}
