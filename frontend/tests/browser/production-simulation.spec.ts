import { test, expect } from '@playwright/test';

test.describe('Production Environment Simulation', () => {
  test('should simulate star.omnilyzer.ai production environment', async ({ page }) => {
    const consoleErrors: string[] = [];
    const stellarApiLogs: string[] = [];
    const pageErrors: string[] = [];
    
    // Capture all console messages
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        consoleErrors.push(text);
        console.log('🚨 Console Error:', text);
      }
      if (text.includes('StellarAPI') || text.includes('stellar')) {
        stellarApiLogs.push(`[${msg.type()}] ${text}`);
      }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      pageErrors.push(error.message);
      console.log('💥 Page Error:', error.message);
    });
    
    console.log('🌐 Simulating production environment (star.omnilyzer.ai)...');
    
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // Override the hostname to simulate production
    const productionSimulation = await page.evaluate(async () => {
      // Mock the window.location.hostname to simulate production
      Object.defineProperty(window.location, 'hostname', {
        writable: true,
        value: 'star.omnilyzer.ai'
      });
      
      try {
        // Import and create a new StellarAPI instance (this will detect the mocked hostname)
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi || module.default;
        
        // This should now use fallback data because hostname is 'star.omnilyzer.ai'
        const result = await stellarApi.getStars();
        
        return {
          success: true,
          hostname: window.location.hostname,
          hasStars: Array.isArray(result.stars),
          starsLength: result.stars ? result.stars.length : 0,
          hasTotal: typeof result.total === 'number',
          totalValue: result.total,
          firstStarName: result.stars && result.stars[0] ? result.stars[0].name : null,
          usedFallbackData: true,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hostname: window.location.hostname,
          hasStars: false,
          starsLength: 0,
          hasTotal: false,
          totalValue: 0,
          firstStarName: null,
          usedFallbackData: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🧪 PRODUCTION SIMULATION RESULT:');
    console.log('=================================');
    console.log(JSON.stringify(productionSimulation, null, 2));
    
    console.log('\n🌟 STELLAR API LOGS:');
    console.log('====================');
    stellarApiLogs.forEach(log => console.log(log));
    
    // Check for the specific errors that were reported in production
    const hasLengthError = [...consoleErrors, ...pageErrors].some(error => 
      error.includes('Cannot read properties of undefined') && 
      error.includes('reading \'length\'')
    );
    
    const hasStellarDataError = [...consoleErrors, ...pageErrors].some(error => 
      error.includes('Failed to load stellar data')
    );
    
    const hasTimeoutError = [...consoleErrors, ...pageErrors].some(error => 
      error.includes('timeout') || error.includes('Data refresh timeout')
    );
    
    console.log('\n📊 PRODUCTION ERROR ANALYSIS:');
    console.log('=============================');
    console.log(`Total console errors: ${consoleErrors.length}`);
    console.log(`Total page errors: ${pageErrors.length}`);
    console.log(`Has "length" error: ${hasLengthError}`);
    console.log(`Has stellar data error: ${hasStellarDataError}`);
    console.log(`Has timeout error: ${hasTimeoutError}`);
    
    console.log('\n📋 PRODUCTION SIMULATION SUMMARY:');
    console.log('==================================');
    
    if (hasLengthError) {
      console.log('🚨 CRITICAL: The "Cannot read properties of undefined (reading \'length\')" error is still present!');
    } else {
      console.log('✅ SUCCESS: No "length" property errors detected!');
    }
    
    if (productionSimulation.success) {
      console.log('✅ SUCCESS: Production fallback data is working correctly!');
      console.log(`✅ Loaded ${productionSimulation.starsLength} stars from fallback data`);
      console.log(`✅ First star: ${productionSimulation.firstStarName}`);
    } else {
      console.log('❌ FAILURE: Production fallback data is not working');
      console.log(`❌ Error: ${productionSimulation.error}`);
    }
    
    // The critical assertions
    expect(hasLengthError, 'The "Cannot read properties of undefined (reading \'length\')" error should be fixed').toBe(false);
    expect(productionSimulation.success, `Production simulation failed: ${productionSimulation.error}`).toBe(true);
    expect(productionSimulation.hasStars, 'Production should have fallback stars').toBe(true);
    expect(productionSimulation.starsLength, 'Production should have at least 8 fallback stars').toBeGreaterThanOrEqual(8);
    expect(productionSimulation.firstStarName, 'First star should be Sol').toBe('Sol');
  });

  test('should verify the page renders content and is not blank', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Check if the page has actual content
    const pageContent = await page.evaluate(() => {
      const body = document.body;
      const textContent = body.textContent || '';
      const hasCanvas = document.querySelector('canvas') !== null;
      const hasReactRoot = document.querySelector('#root') !== null;
      const elementCount = document.querySelectorAll('*').length;
      
      return {
        hasTextContent: textContent.trim().length > 0,
        textLength: textContent.trim().length,
        hasCanvas: hasCanvas,
        hasReactRoot: hasReactRoot,
        elementCount: elementCount,
        isBlankPage: textContent.trim().length < 50 && elementCount < 20
      };
    });
    
    console.log('\n📄 PAGE CONTENT ANALYSIS:');
    console.log('=========================');
    console.log(JSON.stringify(pageContent, null, 2));
    
    // Verify the page is not blank
    expect(pageContent.isBlankPage, 'Page should not be blank').toBe(false);
    expect(pageContent.hasReactRoot, 'Page should have React root element').toBe(true);
    expect(pageContent.elementCount, 'Page should have reasonable number of DOM elements').toBeGreaterThan(20);
    
    if (pageContent.hasCanvas) {
      console.log('✅ SUCCESS: Page has canvas element (3D galaxy should be rendering)');
    } else {
      console.log('⚠️ WARNING: No canvas element found (3D galaxy may not be rendering)');
    }
    
    if (pageContent.hasTextContent) {
      console.log('✅ SUCCESS: Page has text content');
    } else {
      console.log('❌ FAILURE: Page has no text content');
    }
  });

  test('should verify WebSocket and API error handling', async ({ page }) => {
    const networkErrors: string[] = [];
    const webSocketErrors: string[] = [];
    
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('WebSocket') || text.includes('timeout')) {
        webSocketErrors.push(text);
      }
    });
    
    page.on('requestfailed', request => {
      networkErrors.push(`${request.method()} ${request.url()}`);
    });
    
    await page.goto('/');
    await page.waitForTimeout(5000); // Wait longer for WebSocket attempts
    
    console.log('\n🔌 WEBSOCKET & API ERROR ANALYSIS:');
    console.log('==================================');
    console.log(`Network errors: ${networkErrors.length}`);
    console.log(`WebSocket errors: ${webSocketErrors.length}`);
    
    networkErrors.forEach(error => console.log('🌐 Network:', error));
    webSocketErrors.forEach(error => console.log('🔌 WebSocket:', error));
    
    // These errors are expected in development/test environment
    // The important thing is that they don't crash the app
    console.log('\n📋 ERROR HANDLING SUMMARY:');
    console.log('==========================');
    
    if (networkErrors.length > 0) {
      console.log('⚠️ Network errors detected (expected when backend services not running)');
    }
    
    if (webSocketErrors.length > 0) {
      console.log('⚠️ WebSocket errors detected (expected when real-time services not running)');
    }
    
    console.log('✅ App should handle these errors gracefully and continue functioning');
    
    // The app should handle errors gracefully - no assertions needed here
    // Just verify the page is still functional
    const isPageFunctional = await page.evaluate(() => {
      return document.body && document.body.children.length > 0;
    });
    
    expect(isPageFunctional, 'Page should remain functional despite network/WebSocket errors').toBe(true);
  });
});
