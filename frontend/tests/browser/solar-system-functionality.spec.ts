import { test, expect } from '@playwright/test';

test.describe('Solar System Functionality', () => {
  test('should load solar system view without errors', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Check for any JavaScript errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Wait for the page to load completely
    await page.waitForTimeout(5000);
    
    console.log('\n🌌 SOLAR SYSTEM LOAD TEST:');
    console.log('==========================');
    
    if (errors.length > 0) {
      console.log('❌ JavaScript errors found:');
      errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    } else {
      console.log('✅ No JavaScript errors detected');
    }
    
    // Check if the page has basic content
    const hasContent = await page.evaluate(() => {
      return document.body.textContent && document.body.textContent.length > 100;
    });
    
    console.log(`📄 Page has content: ${hasContent}`);
    
    // Check for canvas element (3D rendering)
    const hasCanvas = await page.evaluate(() => {
      return document.querySelector('canvas') !== null;
    });
    
    console.log(`🎨 Canvas element present: ${hasCanvas}`);
    
    // Check for specific UI elements
    const uiElements = await page.evaluate(() => {
      const elements = {
        hasTitle: document.querySelector('h1, h2, h3') !== null,
        hasButtons: document.querySelectorAll('button').length > 0,
        hasGalaxyText: document.body.textContent?.includes('GALACTIC') || false,
        hasHealthPanel: document.body.textContent?.includes('Health') || false,
        hasPortsPanel: document.body.textContent?.includes('Ports') || false
      };
      return elements;
    });
    
    console.log('🎛️ UI Elements:');
    Object.entries(uiElements).forEach(([key, value]) => {
      console.log(`   ${value ? '✅' : '❌'} ${key}: ${value}`);
    });
    
    // The test should pass if there are no critical errors
    expect(errors.filter(e => !e.includes('net::ERR_CONNECTION_REFUSED')).length, 'Should have no critical JavaScript errors').toBe(0);
    expect(hasContent, 'Page should have content').toBe(true);
    expect(hasCanvas, 'Should have canvas for 3D rendering').toBe(true);
  });

  test('should test rotation controls when available', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Try to find rotation control buttons
    const rotationControls = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      
      const planetSpinButton = buttons.find(btn => 
        btn.textContent?.includes('🪐') && 
        (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
      );
      
      const starSpinButton = buttons.find(btn => 
        btn.textContent?.includes('⭐') && 
        (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
      );
      
      const timeControls = buttons.filter(btn => 
        btn.textContent?.includes('▶') || 
        btn.textContent?.includes('⏸') ||
        btn.textContent?.match(/\d+x/)
      );
      
      return {
        hasPlanetSpinButton: !!planetSpinButton,
        hasStarSpinButton: !!starSpinButton,
        hasTimeControls: timeControls.length > 0,
        planetSpinText: planetSpinButton?.textContent || null,
        starSpinText: starSpinButton?.textContent || null,
        timeControlCount: timeControls.length,
        allButtons: buttons.map(btn => btn.textContent?.trim()).filter(text => text && text.length > 0)
      };
    });
    
    console.log('\n🎛️ ROTATION CONTROLS TEST:');
    console.log('===========================');
    console.log(`🪐 Planet spin button: ${rotationControls.hasPlanetSpinButton ? '✅' : '❌'} Found`);
    if (rotationControls.planetSpinText) {
      console.log(`   Text: "${rotationControls.planetSpinText}"`);
    }
    
    console.log(`⭐ Star spin button: ${rotationControls.hasStarSpinButton ? '✅' : '❌'} Found`);
    if (rotationControls.starSpinText) {
      console.log(`   Text: "${rotationControls.starSpinText}"`);
    }
    
    console.log(`⏰ Time controls: ${rotationControls.hasTimeControls ? '✅' : '❌'} Found (${rotationControls.timeControlCount} buttons)`);
    
    console.log('\n📋 All buttons found:');
    rotationControls.allButtons.forEach((text, index) => {
      console.log(`   ${index + 1}. "${text}"`);
    });
    
    // Test interaction if rotation controls are available
    if (rotationControls.hasPlanetSpinButton) {
      console.log('\n🧪 Testing planet spin button interaction...');
      
      const interactionResult = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const planetSpinButton = buttons.find(btn => 
          btn.textContent?.includes('🪐') && 
          (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
        ) as HTMLButtonElement;
        
        if (planetSpinButton) {
          const originalText = planetSpinButton.textContent;
          planetSpinButton.click();
          
          // Wait a moment for state change
          return new Promise(resolve => {
            setTimeout(() => {
              const newText = planetSpinButton.textContent;
              resolve({
                success: true,
                originalText,
                newText,
                textChanged: originalText !== newText
              });
            }, 100);
          });
        }
        
        return { success: false, error: 'Planet spin button not found' };
      });
      
      console.log('🔄 Interaction result:', interactionResult);
    }
    
    // The test passes if we can detect the UI structure
    expect(rotationControls.allButtons.length, 'Should have some buttons available').toBeGreaterThan(0);
  });

  test('should verify database-driven planets (no fallback)', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    const databaseTest = await page.evaluate(async () => {
      try {
        // Test the stellar API directly
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        console.log('🔍 Testing database-driven system...');
        
        // Test that fallback methods don't exist
        const hasFallbackMethods = 'getFallbackStars' in stellarApi || 'getFallbackPlanets' in stellarApi;
        
        // Test Sol system
        const solDetail = await stellarApi.getStarDetail(1);
        
        return {
          success: true,
          hasFallbackMethods,
          solName: solDetail.name,
          planetCount: solDetail.planets?.length || 0,
          planetNames: solDetail.planets?.map(p => p.name) || [],
          planetsFromDatabase: solDetail.planets?.length > 0,
          hasRealData: solDetail.planets?.some(p => p.mass_earth && p.radius_earth && p.sma_au) || false
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🗄️ DATABASE-DRIVEN TEST:');
    console.log('=========================');
    
    if (databaseTest.success) {
      console.log(`✅ Sol system loaded: ${databaseTest.solName}`);
      console.log(`📊 Planet count: ${databaseTest.planetCount}`);
      console.log(`🪐 Planets: ${databaseTest.planetNames.join(', ')}`);
      console.log(`🗄️ Fallback methods removed: ${!databaseTest.hasFallbackMethods ? '✅' : '❌'}`);
      console.log(`📡 Real database data: ${databaseTest.hasRealData ? '✅' : '❌'}`);
      
      if (databaseTest.planetCount === 8) {
        console.log('🎉 SUCCESS: All 8 planets loaded from database!');
      } else {
        console.log(`⚠️ Expected 8 planets, got ${databaseTest.planetCount}`);
      }
    } else {
      console.log(`❌ Database test failed: ${databaseTest.error}`);
    }
    
    expect(databaseTest.success, `Database test failed: ${databaseTest.error}`).toBe(true);
    expect(databaseTest.hasFallbackMethods, 'Fallback methods should be removed').toBe(false);
    expect(databaseTest.planetCount, 'Sol should have 8 planets from database').toBe(8);
    expect(databaseTest.hasRealData, 'Should have real astronomical data').toBe(true);
  });

  test('should verify no imaginary planets are displayed', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    const planetValidationTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        // Test multiple star systems to ensure no imaginary planets
        const testResults = [];
        
        // Test Sol (should have 8 planets)
        const sol = await stellarApi.getStarDetail(1);
        testResults.push({
          starName: sol.name,
          starId: 1,
          planetCount: sol.planets?.length || 0,
          expectedPlanets: 8,
          isValid: (sol.planets?.length || 0) === 8
        });
        
        // Test a few other stars (most should have 0 planets unless in database)
        for (let starId = 2; starId <= 5; starId++) {
          try {
            const star = await stellarApi.getStarDetail(starId);
            testResults.push({
              starName: star.name,
              starId,
              planetCount: star.planets?.length || 0,
              expectedPlanets: 'unknown',
              isValid: true // Any count is valid for other stars
            });
          } catch (error) {
            // Star might not exist in database, which is fine
            testResults.push({
              starName: `Star ${starId}`,
              starId,
              planetCount: 0,
              expectedPlanets: 0,
              isValid: true,
              error: 'Star not found (expected)'
            });
          }
        }
        
        return {
          success: true,
          testResults,
          solValid: testResults[0]?.isValid || false,
          totalStarsTested: testResults.length
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🚫 NO IMAGINARY PLANETS TEST:');
    console.log('==============================');
    
    if (planetValidationTest.success) {
      console.log(`🔍 Tested ${planetValidationTest.totalStarsTested} star systems:`);
      
      planetValidationTest.testResults.forEach((result, index) => {
        const status = result.isValid ? '✅' : '❌';
        const errorText = result.error ? ` (${result.error})` : '';
        console.log(`   ${status} ${result.starName}: ${result.planetCount} planets${errorText}`);
      });
      
      console.log(`\n🌟 Sol system validation: ${planetValidationTest.solValid ? '✅ PASSED' : '❌ FAILED'}`);
    } else {
      console.log(`❌ Planet validation failed: ${planetValidationTest.error}`);
    }
    
    expect(planetValidationTest.success, `Planet validation failed: ${planetValidationTest.error}`).toBe(true);
    expect(planetValidationTest.solValid, 'Sol system should have exactly 8 real planets').toBe(true);
  });
});
