import { test, expect } from '@playwright/test';

/**
 * <PERSON>mport and Module Error Detection Tests
 * These tests catch JavaScript import errors, module resolution issues, 
 * and TypeScript compilation problems that occur in the browser
 */

test.describe('Import and Module Resolution', () => {
  
  test('should load the main page without JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    
    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    // Navigate to the main page
    await page.goto('/');
    
    // Wait for the page to fully load
    await page.waitForLoadState('networkidle');
    
    // Check for any JavaScript errors
    expect(errors).toEqual([]);
    
    // Verify the page title loaded correctly
    await expect(page).toHaveTitle(/Galactic Genesis/);
  });
  
  test('should successfully import PortMonitor component without import/module errors', async ({ page }) => {
    const importErrors: string[] = [];
    const apiErrors: string[] = [];

    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();

        // Specifically track import/module errors (the ones we care about)
        if (text.includes('does not provide an export named') ||
            text.includes('import') ||
            text.includes('module') ||
            text.includes('export') ||
            text.includes('SyntaxError') ||
            text.includes('TypeError') && (text.includes('import') || text.includes('module'))) {
          importErrors.push(text);
        }
        // Track API errors separately (these are expected until backend is fixed)
        else if (text.includes('API Error') || text.includes('Failed to load resource')) {
          apiErrors.push(text);
        }
      }
    });

    // Capture page errors
    page.on('pageerror', error => {
      const message = error.message;

      if (message.includes('does not provide an export named') ||
          message.includes('import') ||
          message.includes('module') ||
          message.includes('export') ||
          message.includes('SyntaxError') ||
          message.includes('TypeError') && (message.includes('import') || message.includes('module'))) {
        importErrors.push(`Page Error: ${message}`);
      }
      else if (message.includes('API Error') || message.includes('Failed to load resource')) {
        apiErrors.push(`Page Error: ${message}`);
      }
    });

    // Navigate to the page
    await page.goto('/');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Wait a bit more for any async imports
    await page.waitForTimeout(2000);

    // Log what we found
    if (importErrors.length > 0) {
      console.log('❌ Import/Module errors detected:', importErrors);
    } else {
      console.log('✅ No import/module errors detected');
    }

    if (apiErrors.length > 0) {
      console.log('⚠️ API errors detected (expected until backend is fixed):', apiErrors);
    }

    // The key test: verify no import/module errors like the original issue
    expect(importErrors, `Import/Module errors found: ${importErrors.join(', ')}`).toEqual([]);
  });
  
  test('should verify portMonitorService is available and PortMonitor component loads', async ({ page }) => {
    const errors: string[] = [];
    const importErrors: string[] = [];

    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        errors.push(text);

        // Look for the specific import error you reported
        if (text.includes('does not provide an export named') ||
            text.includes('PortScanResult') ||
            text.includes('PortInfo')) {
          importErrors.push(text);
        }
      }
    });

    page.on('pageerror', error => {
      const message = error.message;
      errors.push(`Page Error: ${message}`);

      if (message.includes('does not provide an export named') ||
          message.includes('PortScanResult') ||
          message.includes('PortInfo')) {
        importErrors.push(`Page Error: ${message}`);
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Wait for components to load
    await page.waitForTimeout(3000);

    // Try to access the module in the browser console
    const moduleCheck = await page.evaluate(async () => {
      try {
        // Try to dynamically import the module
        const module = await import('/src/services/portMonitor.ts');

        // Check if the service is available (this should exist at runtime)
        const hasPortMonitorService = 'portMonitorService' in module;

        return {
          success: true,
          exports: Object.keys(module),
          hasPortMonitorService,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          exports: [],
          hasPortMonitorService: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    console.log('Module check result:', moduleCheck);

    // The key test: verify no import errors like the one you reported
    expect(importErrors, `Import errors found: ${importErrors.join(', ')}`).toEqual([]);

    // Verify the module import was successful
    expect(moduleCheck.success, `Module import failed: ${moduleCheck.error}`).toBe(true);

    // Verify the service is available (runtime value, not type)
    expect(moduleCheck.hasPortMonitorService, 'portMonitorService export not found').toBe(true);
  });
  
  test('should verify all expected modules can be imported', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test importing key modules
    const moduleTests = await page.evaluate(async () => {
      const results: Array<{module: string, success: boolean, error?: string}> = [];
      
      const modulesToTest = [
        '/src/services/portMonitor.ts',
        '/src/components/PortMonitor.tsx',
        '/src/services/healthDashboard.ts'
      ];
      
      for (const modulePath of modulesToTest) {
        try {
          await import(modulePath);
          results.push({ module: modulePath, success: true });
        } catch (error) {
          results.push({ 
            module: modulePath, 
            success: false, 
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
      
      return results;
    });
    
    console.log('Module import test results:', moduleTests);
    
    // Verify no console errors
    expect(errors).toEqual([]);
    
    // Verify all modules imported successfully
    for (const result of moduleTests) {
      expect(result.success, `Failed to import ${result.module}: ${result.error}`).toBe(true);
    }
  });
  
});
