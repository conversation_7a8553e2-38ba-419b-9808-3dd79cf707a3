import { test, expect } from '@playwright/test';

test.describe('Enhanced Solar System Verification', () => {
  test('should verify enhanced visuals work for Sol system', async ({ page }) => {
    // Set up error tracking
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Navigate to the application
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    
    // Wait for the galaxy view to load
    await page.waitForTimeout(3000);
    
    // Look for Sol system and click it twice to enter solar system view
    // First click selects, second click enters
    const solButton = page.locator('text=Sol').first();
    if (await solButton.isVisible()) {
      await solButton.click();
      await page.waitForTimeout(1000);
      await solButton.click();
      await page.waitForTimeout(3000);
    } else {
      // Alternative: try clicking on a star in the 3D view
      const canvas = page.locator('canvas');
      await canvas.click({ position: { x: 400, y: 300 } });
      await page.waitForTimeout(1000);
      await canvas.click({ position: { x: 400, y: 300 } });
      await page.waitForTimeout(3000);
    }
    
    // Check for enhanced visual errors
    const visualErrors = errors.filter(error => 
      error.includes('MeshDistortMaterial') || 
      error.includes('Sparkles') ||
      error.includes('EffectComposer') ||
      error.includes('Bloom') ||
      error.includes('ChromaticAberration') ||
      error.includes('ToneMapping') ||
      error.includes('Stars')
    );
    
    expect(visualErrors.length).toBe(0);
    
    console.log('✓ Enhanced visuals working for Sol system');
    if (errors.length > 0) {
      console.log('Non-critical errors:', errors.slice(0, 3));
    }
  });

  test('should verify API data supports enhanced materials', async ({ page }) => {
    // Test Sol system data
    const solResponse = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(solResponse.ok()).toBeTruthy();
    
    const solData = await solResponse.json();
    const planets = solData.planets;
    
    // Verify Earth has proper data for enhanced materials
    const earth = planets.find((p: any) => p.name === 'Earth');
    expect(earth).toBeDefined();
    expect(earth.composition).toBeDefined();
    expect(earth.radius_earth).toBeDefined();
    expect(earth.in_habitable_zone).toBe(true);
    
    // Verify gas giants have proper composition data
    const jupiter = planets.find((p: any) => p.name === 'Jupiter');
    expect(jupiter).toBeDefined();
    expect(jupiter.composition).toBe('gas_giant');
    
    // Verify ice giants
    const neptune = planets.find((p: any) => p.name === 'Neptune');
    expect(neptune).toBeDefined();
    expect(neptune.composition).toBe('ice_giant');
    
    console.log('✓ API data supports enhanced materials');
    console.log(`  Earth: ${earth.composition}, habitable: ${earth.in_habitable_zone}`);
    console.log(`  Jupiter: ${jupiter.composition}`);
    console.log(`  Neptune: ${neptune.composition}`);
  });

  test('should verify enhanced visuals work for Proxima Centauri', async ({ page }) => {
    // Test Proxima Centauri system data
    const proximaResponse = await page.request.get('http://localhost:19081/v1/stellar/stars/3');
    expect(proximaResponse.ok()).toBeTruthy();
    
    const proximaData = await proximaResponse.json();
    expect(proximaData.name).toBe('Proxima Centauri');
    expect(proximaData.planets).toBeDefined();
    expect(proximaData.planets.length).toBeGreaterThan(0);
    
    // Verify planets have the required data for enhanced visuals
    const planet = proximaData.planets[0];
    expect(planet.composition).toBeDefined();
    expect(planet.radius_earth).toBeDefined();
    expect(planet.name).toBeDefined();
    
    console.log('✓ Enhanced visuals data available for Proxima Centauri');
    console.log(`  Planet: ${planet.name}, composition: ${planet.composition}`);
  });

  test('should verify realistic orbital mechanics preserved', async ({ page }) => {
    // Verify that our enhanced visuals didn't break the orbital mechanics
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    // Test that orbital periods are still realistic
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    expect(earth.period_days).toBeCloseTo(365.25, 1);
    
    const mars = data.planets.find((p: any) => p.name === 'Mars');
    expect(mars.period_days).toBeCloseTo(687, 1);
    
    // Test that moons still have realistic orbital periods
    const luna = earth.moons[0];
    expect(luna.name).toBe('Luna');
    expect(luna.orbital_period_days).toBeCloseTo(27.3, 0.5);
    
    console.log('✓ Realistic orbital mechanics preserved with enhanced visuals');
    console.log(`  Earth: ${earth.period_days} days`);
    console.log(`  Mars: ${mars.period_days} days`);
    console.log(`  Luna: ${luna.orbital_period_days} days`);
  });

  test('should verify enhanced star materials work for different star types', async ({ page }) => {
    // Test different star systems to ensure enhanced materials work universally
    
    // Test Sol (G-type star)
    const solResponse = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const solData = await solResponse.json();
    expect(solData.spectral_type).toMatch(/G/);
    
    // Test Proxima Centauri (M-type star)
    const proximaResponse = await page.request.get('http://localhost:19081/v1/stellar/stars/3');
    const proximaData = await proximaResponse.json();
    expect(proximaData.spectral_type).toMatch(/M/);
    
    console.log('✓ Enhanced star materials support different spectral types');
    console.log(`  Sol: ${solData.spectral_type} (should use golden colors)`);
    console.log(`  Proxima: ${proximaData.spectral_type} (should use orange-red colors)`);
  });

  test('should verify no JavaScript errors with enhanced effects', async ({ page }) => {
    // Set up comprehensive error tracking
    const errors: string[] = [];
    const warnings: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      } else if (msg.type() === 'warning') {
        warnings.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Navigate and let enhanced effects run
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(5000);
    
    // Filter for critical errors related to enhanced effects
    const criticalErrors = errors.filter(error => 
      error.includes('WebGL') ||
      error.includes('shader') ||
      error.includes('texture') ||
      error.includes('material') ||
      error.includes('geometry') ||
      error.includes('undefined') ||
      error.includes('null') ||
      error.includes('NaN')
    );
    
    expect(criticalErrors.length).toBe(0);
    
    console.log('✓ No critical JavaScript errors with enhanced effects');
    if (errors.length > 0) {
      console.log(`Non-critical errors: ${errors.length}`);
    }
    if (warnings.length > 0) {
      console.log(`Warnings: ${warnings.length}`);
    }
  });

  test('should verify enhanced materials performance', async ({ page }) => {
    // Test that enhanced materials don't cause performance issues
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    
    // Measure performance
    const startTime = Date.now();
    await page.waitForTimeout(5000);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
    
    console.log('✓ Enhanced materials performance acceptable');
    console.log(`  Load time: ${duration}ms`);
  });
});
