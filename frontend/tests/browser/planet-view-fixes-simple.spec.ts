import { test, expect } from '@playwright/test';

test.describe('Planet View Fixes - Simple Verification', () => {
  test('should verify console log spam is fixed', async ({ page }) => {
    console.log('🔍 Testing console log spam fix...');

    const consoleMessages: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'log' && (msg.text().includes('🌙 Moon') || msg.text().includes('☀️ Distant sun'))) {
        consoleMessages.push(msg.text());
      }
    });

    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for any potential console messages
    await page.waitForTimeout(5000);
    
    // Should have minimal console messages (not thousands)
    expect(consoleMessages.length).toBeLessThan(50);
    console.log(`✅ Console log spam fixed: ${consoleMessages.length} messages (should be < 50)`);
  });

  test('should verify API returns correct AU distances', async ({ page }) => {
    console.log('🔍 Testing API AU distances...');

    // Test the API directly
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.status()).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('planets');
    expect(Array.isArray(data.planets)).toBe(true);

    if (data.planets.length > 0) {
      // Check Mercury
      const mercury = data.planets.find((p: any) => p.name === 'Mercury');
      if (mercury) {
        expect(mercury).toHaveProperty('sma_au');
        expect(typeof mercury.sma_au).toBe('number');
        expect(mercury.sma_au).toBeCloseTo(0.39, 1);
        console.log(`✅ Mercury AU distance: ${mercury.sma_au}`);
      }

      // Check Earth
      const earth = data.planets.find((p: any) => p.name === 'Earth');
      if (earth) {
        expect(earth).toHaveProperty('sma_au');
        expect(typeof earth.sma_au).toBe('number');
        expect(earth.sma_au).toBeCloseTo(1.0, 1);
        console.log(`✅ Earth AU distance: ${earth.sma_au}`);
      }

      // Check Mars
      const mars = data.planets.find((p: any) => p.name === 'Mars');
      if (mars) {
        expect(mars).toHaveProperty('sma_au');
        expect(typeof mars.sma_au).toBe('number');
        expect(mars.sma_au).toBeCloseTo(1.52, 1);
        console.log(`✅ Mars AU distance: ${mars.sma_au}`);
      }
    }

    console.log('✅ API AU distances are correct');
  });

  test('should verify moon orbital periods are realistic', async ({ page }) => {
    console.log('🔍 Testing moon orbital periods...');

    // Test the API directly
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.status()).toBe(200);

    const data = await response.json();
    
    if (data.planets && data.planets.length > 0) {
      const earth = data.planets.find((p: any) => p.name === 'Earth');
      if (earth && earth.moons && earth.moons.length > 0) {
        const luna = earth.moons.find((m: any) => m.name === 'Luna' || m.name === 'Moon');
        if (luna) {
          expect(luna).toHaveProperty('orbital_period_days');
          expect(typeof luna.orbital_period_days).toBe('number');
          expect(luna.orbital_period_days).toBeCloseTo(27.3, 2);
          console.log(`✅ Luna orbital period: ${luna.orbital_period_days} days`);
        }
      }
    }

    console.log('✅ Moon orbital periods are realistic');
  });

  test('should verify frontend loads without critical errors', async ({ page }) => {
    console.log('🔍 Testing frontend loading...');

    const jsErrors: string[] = [];
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });

    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Wait for the canvas to load
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(3000);

    // Check for critical errors
    const criticalErrors = jsErrors.filter(error => 
      error.includes('R3F: Hooks can only be used within the Canvas component') ||
      error.includes('Cannot read properties of undefined') ||
      error.includes('TypeError') ||
      error.includes('ReferenceError')
    );

    expect(criticalErrors.length).toBe(0);

    if (jsErrors.length === 0) {
      console.log('✅ No JavaScript errors detected');
    } else {
      console.log(`⚠️ Non-critical JavaScript errors: ${jsErrors.length}`);
      jsErrors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('✅ Frontend loads without critical errors');
  });

  test('should verify build compilation is successful', async ({ page }) => {
    console.log('🔍 Testing build compilation...');

    // This test verifies that the TypeScript compilation was successful
    // by checking that the page loads and basic functionality works
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check that the main elements are present
    const canvas = page.locator('canvas');
    await expect(canvas).toBeVisible({ timeout: 10000 });

    const gameTitle = page.locator('text=GALACTIC GENESIS');
    await expect(gameTitle).toBeVisible({ timeout: 5000 });

    console.log('✅ Build compilation successful - all components loaded');
  });
});
