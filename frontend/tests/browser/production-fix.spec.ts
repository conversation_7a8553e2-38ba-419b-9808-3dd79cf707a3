import { test, expect } from '@playwright/test';

test.describe('Production Bug Fix Verification', () => {
  test('should fix the "Cannot read properties of undefined (reading length)" error', async ({ page }) => {
    const consoleErrors: string[] = [];
    const stellarApiLogs: string[] = [];
    
    // Capture console messages
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        consoleErrors.push(text);
        console.log('🚨 Console Error:', text);
      }
      if (text.includes('StellarAPI') || text.includes('stellar')) {
        stellarApiLogs.push(`[${msg.type()}] ${text}`);
      }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      consoleErrors.push(`Page Error: ${error.message}`);
      console.log('💥 Page Error:', error.message);
    });
    
    console.log('🔍 Testing production fallback data fix...');
    
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Test the StellarAPI directly in the browser to simulate production
    const productionTest = await page.evaluate(async () => {
      try {
        // Import the StellarAPI
        const module = await import('/src/services/stellarApi.ts');
        const StellarApiClient = module.default || module.StellarApiClient;
        
        // Create a new instance with null baseUrl to simulate production
        const stellarApi = new StellarApiClient(null);
        
        // Try to call getStars - this should use fallback data
        const result = await stellarApi.getStars();
        
        return {
          success: true,
          hasStars: Array.isArray(result.stars),
          starsLength: result.stars ? result.stars.length : 0,
          hasTotal: typeof result.total === 'number',
          totalValue: result.total,
          firstStarName: result.stars && result.stars[0] ? result.stars[0].name : null,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hasStars: false,
          starsLength: 0,
          hasTotal: false,
          totalValue: 0,
          firstStarName: null,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🧪 PRODUCTION FALLBACK TEST RESULT:');
    console.log('====================================');
    console.log(JSON.stringify(productionTest, null, 2));
    
    console.log('\n🌟 STELLAR API LOGS:');
    console.log('====================');
    stellarApiLogs.forEach(log => console.log(log));
    
    // Check for the specific error that was reported
    const hasLengthError = consoleErrors.some(error => 
      error.includes('Cannot read properties of undefined') && 
      error.includes('reading \'length\'')
    );
    
    const hasStellarDataError = consoleErrors.some(error => 
      error.includes('Failed to load stellar data')
    );
    
    console.log('\n📊 ERROR ANALYSIS:');
    console.log('==================');
    console.log(`Total console errors: ${consoleErrors.length}`);
    console.log(`Has "length" error: ${hasLengthError}`);
    console.log(`Has stellar data error: ${hasStellarDataError}`);
    
    if (hasLengthError) {
      console.log('🚨 CRITICAL: The "Cannot read properties of undefined (reading \'length\')" error is still present!');
      console.log('This means the production fallback data fix did not work.');
    } else {
      console.log('✅ SUCCESS: No "length" property errors detected!');
    }
    
    // Verify the fix worked
    expect(productionTest.success, `Production fallback test failed: ${productionTest.error}`).toBe(true);
    expect(productionTest.hasStars, 'Fallback data should have stars array').toBe(true);
    expect(productionTest.starsLength, 'Fallback data should have stars').toBeGreaterThan(0);
    expect(productionTest.hasTotal, 'Fallback data should have total count').toBe(true);
    expect(productionTest.firstStarName, 'First star should be Sol').toBe('Sol');
    
    // The critical test: no "length" property errors
    expect(hasLengthError, 'The "Cannot read properties of undefined (reading \'length\')" error should be fixed').toBe(false);
  });

  test('should verify production environment detection works correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // Test environment detection
    const envTest = await page.evaluate(() => {
      const hostname = window.location.hostname;
      const isProduction = hostname === 'star.omnilyzer.ai';
      const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
      
      return {
        hostname,
        isProduction,
        isLocalhost,
        currentEnvironment: isProduction ? 'production' : isLocalhost ? 'development' : 'unknown'
      };
    });
    
    console.log('\n🌐 ENVIRONMENT DETECTION:');
    console.log('=========================');
    console.log(JSON.stringify(envTest, null, 2));
    
    // In our test environment, we should be on localhost
    expect(envTest.isLocalhost, 'Test should be running on localhost').toBe(true);
    expect(envTest.currentEnvironment, 'Environment should be development').toBe('development');
  });

  test('should test both development and production API paths', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // Test both API configurations
    const apiTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const StellarApiClient = module.default || module.StellarApiClient;
        
        // Test 1: Development mode (with API)
        const devApi = new StellarApiClient('http://localhost:19081/v1');
        let devResult;
        try {
          devResult = await devApi.getStars(20, 5);
        } catch (error) {
          devResult = { error: error instanceof Error ? error.message : String(error) };
        }
        
        // Test 2: Production mode (fallback data)
        const prodApi = new StellarApiClient(null);
        const prodResult = await prodApi.getStars(20, 5);
        
        return {
          development: {
            success: !devResult.error,
            starsCount: devResult.stars ? devResult.stars.length : 0,
            error: devResult.error || null
          },
          production: {
            success: Array.isArray(prodResult.stars),
            starsCount: prodResult.stars ? prodResult.stars.length : 0,
            usedFallback: true
          }
        };
      } catch (error) {
        return {
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🔄 API PATH TESTING:');
    console.log('====================');
    console.log(JSON.stringify(apiTest, null, 2));
    
    // Production fallback should always work
    expect(apiTest.production.success, 'Production fallback should work').toBe(true);
    expect(apiTest.production.starsCount, 'Production should have fallback stars').toBeGreaterThan(0);
    
    // Development might work or fail depending on API availability
    if (apiTest.development.success) {
      console.log('✅ Development API is available and working');
      expect(apiTest.development.starsCount, 'Development API should return stars').toBeGreaterThan(0);
    } else {
      console.log('⚠️ Development API not available (expected in some environments)');
      console.log('Error:', apiTest.development.error);
    }
  });
});
