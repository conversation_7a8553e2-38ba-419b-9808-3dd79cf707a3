import { test, expect } from '@playwright/test';

test.describe('Orbital Timing Verification', () => {
  test('should verify realistic orbital data in API', async ({ page }) => {
    // Test the API to ensure orbital periods are realistic
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const planets = data.planets;
    
    // Verify Earth has realistic orbital period
    const earth = planets.find((p: any) => p.name === 'Earth');
    expect(earth).toBeDefined();
    expect(earth.period_days).toBeCloseTo(365.25, 1);
    
    // Verify Mars has realistic orbital period (allow small tolerance)
    const mars = planets.find((p: any) => p.name === 'Mars');
    expect(mars).toBeDefined();
    expect(mars.period_days).toBeCloseTo(687, 1); // Allow 1 day tolerance
    
    // Verify Jupiter has realistic orbital period
    const jupiter = planets.find((p: any) => p.name === 'Jupiter');
    expect(jupiter).toBeDefined();
    expect(jupiter.period_days).toBeCloseTo(4333, 50); // Allow 50 day tolerance
    
    console.log('✓ Realistic planetary orbital periods verified in database');
    console.log(`  Earth: ${earth.period_days} days (expected ~365.25)`);
    console.log(`  Mars: ${mars.period_days} days (expected ~687)`);
    console.log(`  Jupiter: ${jupiter.period_days} days (expected ~4333)`);
  });

  test('should verify realistic moon orbital periods', async ({ page }) => {
    // Test moon orbital periods
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    // Test Earth's moon (Luna)
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    expect(earth.moons).toBeDefined();
    expect(earth.moons.length).toBe(1);
    
    const luna = earth.moons[0];
    expect(luna.name).toBe('Luna');
    expect(luna.orbital_period_days).toBeCloseTo(27.3, 0.5);
    
    // Test Jupiter's moons
    const jupiter = data.planets.find((p: any) => p.name === 'Jupiter');
    expect(jupiter.moons).toBeDefined();
    expect(jupiter.moons.length).toBe(4);
    
    const io = jupiter.moons.find((m: any) => m.name === 'Io');
    expect(io.orbital_period_days).toBeCloseTo(1.77, 0.1);
    
    const europa = jupiter.moons.find((m: any) => m.name === 'Europa');
    expect(europa.orbital_period_days).toBeCloseTo(3.55, 0.1);
    
    const ganymede = jupiter.moons.find((m: any) => m.name === 'Ganymede');
    expect(ganymede.orbital_period_days).toBeCloseTo(7.15, 0.1);
    
    const callisto = jupiter.moons.find((m: any) => m.name === 'Callisto');
    expect(callisto.orbital_period_days).toBeCloseTo(16.69, 0.2);
    
    console.log('✓ Realistic moon orbital periods verified');
    console.log(`  Luna: ${luna.orbital_period_days} days (expected ~27.3)`);
    console.log(`  Io: ${io.orbital_period_days} days (expected ~1.77)`);
    console.log(`  Europa: ${europa.orbital_period_days} days (expected ~3.55)`);
    console.log(`  Ganymede: ${ganymede.orbital_period_days} days (expected ~7.15)`);
    console.log(`  Callisto: ${callisto.orbital_period_days} days (expected ~16.69)`);
  });

  test('should verify orbital mechanics calculations are realistic', async ({ page }) => {
    // Test that the orbital mechanics formulas produce realistic results
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    // Test orbital speed calculations
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    const mars = data.planets.find((p: any) => p.name === 'Mars');
    const jupiter = data.planets.find((p: any) => p.name === 'Jupiter');
    
    // Verify that outer planets have longer orbital periods (Kepler's 3rd law)
    expect(mars.period_days).toBeGreaterThan(earth.period_days);
    expect(jupiter.period_days).toBeGreaterThan(mars.period_days);
    
    // Verify that outer planets have larger orbital radii
    expect(mars.sma_au).toBeGreaterThan(earth.sma_au);
    expect(jupiter.sma_au).toBeGreaterThan(mars.sma_au);
    
    console.log('✓ Orbital mechanics follow Kepler\'s laws');
    console.log(`  Earth: ${earth.sma_au} AU, ${earth.period_days} days`);
    console.log(`  Mars: ${mars.sma_au} AU, ${mars.period_days} days`);
    console.log(`  Jupiter: ${jupiter.sma_au} AU, ${jupiter.period_days} days`);
  });

  test('should verify no JavaScript errors in orbital calculations', async ({ page }) => {
    // Set up error tracking
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Navigate to the application and let it run for a bit
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(5000);
    
    // Check for orbital-related errors
    const orbitalErrors = errors.filter(error => 
      error.includes('orbital') || 
      error.includes('useFrame') || 
      error.includes('timeSpeed') ||
      error.includes('NaN') ||
      error.includes('undefined') ||
      error.includes('period_days')
    );
    
    expect(orbitalErrors.length).toBe(0);
    
    if (errors.length > 0) {
      console.log('Non-critical errors found:', errors.slice(0, 3)); // Show first 3 errors
    }
    
    console.log('✓ No orbital mechanics JavaScript errors detected');
  });

  test('should verify time scaling calculations are correct', async ({ page }) => {
    // Test the mathematical correctness of time scaling
    
    // At 1x speed: 1 real day = 1 second in simulation
    // So Luna (27.3 days) should take 27.3 seconds for full orbit
    // At 10x speed: Luna should take 2.73 seconds for full orbit
    
    const lunaRealPeriod = 27.3; // days
    const timeSpeed1x = 1;
    const timeSpeed10x = 10;
    
    const simulationPeriod1x = lunaRealPeriod / timeSpeed1x; // 27.3 seconds
    const simulationPeriod10x = lunaRealPeriod / timeSpeed10x; // 2.73 seconds
    
    expect(simulationPeriod1x).toBeCloseTo(27.3, 0.1);
    expect(simulationPeriod10x).toBeCloseTo(2.73, 0.1);
    
    // Test Earth orbital period
    const earthRealPeriod = 365.25; // days
    const earthSimulation1x = earthRealPeriod / timeSpeed1x; // 365.25 seconds = ~6 minutes
    const earthSimulation10x = earthRealPeriod / timeSpeed10x; // 36.525 seconds
    
    expect(earthSimulation1x).toBeCloseTo(365.25, 0.1);
    expect(earthSimulation10x).toBeCloseTo(36.525, 0.1);
    
    console.log('✓ Time scaling calculations are mathematically correct');
    console.log(`  Luna at 1x: ${simulationPeriod1x} seconds per orbit`);
    console.log(`  Luna at 10x: ${simulationPeriod10x} seconds per orbit`);
    console.log(`  Earth at 1x: ${earthSimulation1x} seconds per orbit (~${(earthSimulation1x/60).toFixed(1)} minutes)`);
    console.log(`  Earth at 10x: ${earthSimulation10x} seconds per orbit`);
  });
});
