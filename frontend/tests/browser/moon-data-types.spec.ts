import { test, expect } from '@playwright/test';

test.describe('Moon Data Type Validation', () => {
  test('should ensure all moon numeric fields are returned as numbers from API', async ({ page }) => {
    // Test the API directly to ensure data types are correct
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    expect(earth).toBeDefined();
    expect(earth.moons).toBeDefined();
    expect(earth.moons.length).toBeGreaterThan(0);
    
    const luna = earth.moons[0];
    expect(luna.name).toBe('Luna');
    
    // Verify all numeric fields are actually numbers, not strings
    expect(typeof luna.orbital_period_days).toBe('number');
    expect(typeof luna.diameter_km).toBe('number');
    expect(typeof luna.distance_km).toBe('number');
    
    // Verify the values are reasonable
    expect(luna.orbital_period_days).toBeCloseTo(27.3, 1);
    expect(luna.diameter_km).toBeCloseTo(3474.8, 1);
    expect(luna.distance_km).toBe(384400);
  });

  test('should render planet detail view with moon data without errors', async ({ page }) => {
    await page.goto('http://localhost:5174');
    
    // Wait for the galaxy to load
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // Click on the solar button to enter solar system view
    await page.click('button:has-text("solar")');
    await page.waitForTimeout(2000);
    
    // Click on Earth to enter planet detail view
    await page.click('canvas');
    await page.waitForTimeout(1000);
    
    // Look for Earth specifically and click it
    const earthFound = await page.evaluate(() => {
      // Try to find and click Earth in the solar system
      const canvas = document.querySelector('canvas');
      if (canvas) {
        // Simulate a click in the center area where Earth should be
        const rect = canvas.getBoundingClientRect();
        const event = new MouseEvent('click', {
          clientX: rect.left + rect.width * 0.6, // Earth's approximate position
          clientY: rect.top + rect.height * 0.5,
          bubbles: true
        });
        canvas.dispatchEvent(event);
        return true;
      }
      return false;
    });
    
    if (earthFound) {
      await page.waitForTimeout(2000);
      
      // Check if we're in planet detail view by looking for moon information
      const moonInfo = await page.locator('text=Luna').first();
      if (await moonInfo.isVisible()) {
        // Verify that moon orbital period is displayed correctly (no JavaScript errors)
        const periodText = await page.locator('text=/Period: \\d+\\.\\d+ days/').first();
        expect(await periodText.isVisible()).toBeTruthy();
        
        const periodValue = await periodText.textContent();
        expect(periodValue).toMatch(/Period: 27\.\d+ days/);
        
        // Verify no JavaScript errors occurred
        const errors = await page.evaluate(() => {
          return (window as any).jsErrors || [];
        });
        expect(errors.length).toBe(0);
      }
    }
  });

  test('should handle all planets with moons without type errors', async ({ page }) => {
    // Test API data for all planets with moons
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    const planetsWithMoons = data.planets.filter((p: any) => p.moons && p.moons.length > 0);
    expect(planetsWithMoons.length).toBeGreaterThan(0);
    
    for (const planet of planetsWithMoons) {
      console.log(`Testing planet: ${planet.name} with ${planet.moons.length} moons`);
      
      for (const moon of planet.moons) {
        // Verify all numeric fields are numbers
        if (moon.orbital_period_days !== null) {
          expect(typeof moon.orbital_period_days).toBe('number');
          expect(moon.orbital_period_days).toBeGreaterThan(0);
        }
        
        if (moon.diameter_km !== null) {
          expect(typeof moon.diameter_km).toBe('number');
          expect(moon.diameter_km).toBeGreaterThan(0);
        }
        
        if (moon.distance_km !== null) {
          expect(typeof moon.distance_km).toBe('number');
          expect(moon.distance_km).toBeGreaterThan(0);
        }
        
        if (moon.albedo !== null) {
          expect(typeof moon.albedo).toBe('number');
          expect(moon.albedo).toBeGreaterThanOrEqual(0);
          expect(moon.albedo).toBeLessThanOrEqual(1);
        }
      }
    }
  });
});
