import { test, expect } from '@playwright/test';

test.describe('Comprehensive Frontend Testing', () => {
  test('should load all major components without errors', async ({ page }) => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Capture console messages
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      } else if (msg.type() === 'warn') {
        warnings.push(msg.text());
      }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    // Navigate to the page
    await page.goto('/');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Check for critical elements
    await expect(page.locator('body')).toBeVisible();
    
    // Verify no critical JavaScript errors
    const criticalErrors = errors.filter(error => 
      !error.includes('Failed to load resource') && 
      !error.includes('net::ERR_CONNECTION_REFUSED') &&
      !error.includes('API Error')
    );
    
    expect(criticalErrors, `Critical JavaScript errors found: ${criticalErrors.join(', ')}`).toEqual([]);
    
    console.log(`✅ Frontend loaded successfully with ${errors.length} API errors (expected)`);
  });

  test('should verify all service modules can be imported', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const moduleTests = await page.evaluate(async () => {
      const modules = [
        '/src/services/portMonitor.ts',
        '/src/services/healthDashboard.ts',
        '/src/components/PortMonitor.tsx',
        '/src/components/StunningGalaxy3D.tsx'
      ];
      
      const results = [];
      
      for (const modulePath of modules) {
        try {
          const module = await import(modulePath);
          results.push({
            module: modulePath,
            success: true,
            exports: Object.keys(module),
            error: null
          });
        } catch (error) {
          results.push({
            module: modulePath,
            success: false,
            exports: [],
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
      
      return results;
    });
    
    console.log('Module import test results:', moduleTests);
    
    // Verify all modules imported successfully
    for (const result of moduleTests) {
      expect(result.success, `Module ${result.module} failed to import: ${result.error}`).toBe(true);
      expect(result.exports.length, `Module ${result.module} has no exports`).toBeGreaterThan(0);
    }
  });

  test('should verify TypeScript compilation works correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test TypeScript type checking by trying to use types
    const typeTest = await page.evaluate(async () => {
      try {
        // Import with type annotations
        const { portMonitorService } = await import('/src/services/portMonitor.ts');
        
        // Verify the service has expected methods
        const hasGetPortScan = typeof portMonitorService.getPortScan === 'function';
        const hasSubscribe = typeof portMonitorService.subscribe === 'function';
        
        return {
          success: true,
          hasGetPortScan,
          hasSubscribe,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hasGetPortScan: false,
          hasSubscribe: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    expect(typeTest.success, `TypeScript compilation test failed: ${typeTest.error}`).toBe(true);
    expect(typeTest.hasGetPortScan, 'portMonitorService.getPortScan method not found').toBe(true);
    expect(typeTest.hasSubscribe, 'portMonitorService.subscribe method not found').toBe(true);
  });

  test('should verify API connectivity and error handling', async ({ page }) => {
    const networkRequests: string[] = [];
    const networkErrors: string[] = [];
    
    // Monitor network requests
    page.on('request', request => {
      networkRequests.push(`${request.method()} ${request.url()}`);
    });
    
    page.on('requestfailed', request => {
      networkErrors.push(`Failed: ${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('Network requests made:', networkRequests.slice(0, 10)); // Show first 10
    console.log('Network errors:', networkErrors);
    
    // Verify that the app attempts to make API calls (even if they fail)
    const hasApiCalls = networkRequests.some(req => 
      req.includes('/api/') || req.includes(':8086') || req.includes(':19081')
    );
    
    expect(hasApiCalls, 'No API calls detected - frontend may not be trying to connect to backend').toBe(true);
  });

  test('should verify responsive design and viewport handling', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test different viewport sizes
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);
      
      // Verify the page is still functional
      const bodyVisible = await page.locator('body').isVisible();
      expect(bodyVisible, `Page not visible on ${viewport.name} viewport`).toBe(true);
      
      // Check for horizontal scrollbar (should not exist on mobile)
      const bodyWidth = await page.locator('body').boundingBox();
      if (bodyWidth) {
        expect(bodyWidth.width, `Horizontal overflow on ${viewport.name}`).toBeLessThanOrEqual(viewport.width + 20); // 20px tolerance
      }
    }
  });

  test('should verify CSS and styling loads correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check if CSS is loaded by verifying computed styles
    const stylesTest = await page.evaluate(() => {
      const body = document.body;
      const computedStyle = window.getComputedStyle(body);
      
      return {
        hasBackgroundColor: computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)',
        hasFontFamily: computedStyle.fontFamily !== '',
        hasMargin: computedStyle.margin !== '',
        bodyDisplay: computedStyle.display
      };
    });
    
    // Verify basic styling is applied
    expect(stylesTest.bodyDisplay, 'Body display style not set').not.toBe('');
    
    console.log('CSS styling test results:', stylesTest);
  });

  test('should verify performance metrics are reasonable', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Performance checks
    expect(loadTime, 'Page load time too slow').toBeLessThan(10000); // 10 seconds max
    
    // Check for memory leaks by counting DOM nodes
    const domNodeCount = await page.evaluate(() => {
      return document.querySelectorAll('*').length;
    });
    
    expect(domNodeCount, 'Too many DOM nodes - possible memory leak').toBeLessThan(5000);
    
    console.log(`✅ Performance: Load time ${loadTime}ms, DOM nodes: ${domNodeCount}`);
  });
});
