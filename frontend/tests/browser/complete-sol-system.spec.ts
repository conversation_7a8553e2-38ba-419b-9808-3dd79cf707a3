import { test, expect } from '@playwright/test';

test.describe('Complete Sol System with Rotation Controls', () => {
  test('should verify <PERSON> has all 8 planets with NASA data', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const solSystemTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        console.log('🌟 Testing complete Sol system...');
        const solDetail = await stellarApi.getStarDetail(1);
        
        const planets = solDetail.planets || [];
        const planetData = planets.map(p => ({
          name: p.name,
          mass_earth: p.mass_earth,
          radius_earth: p.radius_earth,
          sma_au: p.sma_au,
          period_days: p.period_days,
          rotation_hours: p.rotation_hours,
          composition: p.composition,
          atmosphere: p.atmosphere,
          surface_temp_k: p.surface_temp_k,
          moons: p.moons,
          in_habitable_zone: p.in_habitable_zone
        }));
        
        return {
          success: true,
          starName: solDetail.name,
          planetCount: planets.length,
          planetNames: planets.map(p => p.name),
          planetData,
          hasAllInnerPlanets: ['Mercury', 'Venus', 'Earth', 'Mars'].every(name => 
            planets.some(p => p.name === name)
          ),
          hasAllOuterPlanets: ['Jupiter', 'Saturn', 'Uranus', 'Neptune'].every(name => 
            planets.some(p => p.name === name)
          ),
          hasNASAData: planets.every(p => 
            p.mass_earth !== undefined && 
            p.radius_earth !== undefined && 
            p.sma_au !== undefined &&
            p.period_days !== undefined
          ),
          hasExtendedData: planets.some(p => 
            p.rotation_hours !== undefined && 
            p.atmosphere !== undefined && 
            p.surface_temp_k !== undefined &&
            p.moons !== undefined
          )
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🌍 COMPLETE SOL SYSTEM TEST RESULT:');
    console.log('===================================');
    console.log(JSON.stringify(solSystemTest, null, 2));
    
    if (solSystemTest.success) {
      console.log(`✅ Sol system: ${solSystemTest.starName}`);
      console.log(`📊 Planet count: ${solSystemTest.planetCount}`);
      console.log(`📊 Planet names: ${solSystemTest.planetNames.join(', ')}`);
      console.log(`📊 Has all inner planets: ${solSystemTest.hasAllInnerPlanets}`);
      console.log(`📊 Has all outer planets: ${solSystemTest.hasAllOuterPlanets}`);
      console.log(`📊 Has NASA data: ${solSystemTest.hasNASAData}`);
      console.log(`📊 Has extended data: ${solSystemTest.hasExtendedData}`);
      
      // Verify specific planet data
      const earth = solSystemTest.planetData.find(p => p.name === 'Earth');
      const jupiter = solSystemTest.planetData.find(p => p.name === 'Jupiter');
      const neptune = solSystemTest.planetData.find(p => p.name === 'Neptune');
      
      if (earth) {
        console.log(`🌍 Earth data: ${earth.mass_earth} Earth masses, ${earth.moons} moon(s), ${earth.atmosphere} atmosphere`);
      }
      if (jupiter) {
        console.log(`🪐 Jupiter data: ${jupiter.mass_earth} Earth masses, ${jupiter.moons} moons, ${jupiter.composition} composition`);
      }
      if (neptune) {
        console.log(`🔵 Neptune data: ${neptune.sma_au} AU, ${neptune.period_days} days, ${neptune.surface_temp_k}K`);
      }
    } else {
      console.log(`❌ Sol system test failed: ${solSystemTest.error}`);
    }
    
    expect(solSystemTest.success, `Sol system test failed: ${solSystemTest.error}`).toBe(true);
    expect(solSystemTest.planetCount, 'Sol should have 8 planets').toBe(8);
    expect(solSystemTest.hasAllInnerPlanets, 'Should have all inner planets').toBe(true);
    expect(solSystemTest.hasAllOuterPlanets, 'Should have all outer planets').toBe(true);
    expect(solSystemTest.hasNASAData, 'Should have NASA astronomical data').toBe(true);
    expect(solSystemTest.hasExtendedData, 'Should have extended planetary data').toBe(true);
  });

  test('should test rotation controls in solar system view', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Navigate to Sol system (assuming there's a way to click on Sol)
    const navigationTest = await page.evaluate(async () => {
      try {
        // Try to find and click Sol in the galaxy view
        const canvas = document.querySelector('canvas');
        if (!canvas) {
          return { success: false, error: 'No canvas found' };
        }
        
        // Simulate clicking on Sol (this is a simplified test)
        // In a real scenario, we'd need to find the exact coordinates of Sol
        const rect = canvas.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const clickEvent = new MouseEvent('click', {
          clientX: centerX,
          clientY: centerY,
          bubbles: true
        });
        canvas.dispatchEvent(clickEvent);
        
        return { success: true, message: 'Clicked on galaxy canvas' };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🎮 NAVIGATION TEST RESULT:');
    console.log('==========================');
    console.log(JSON.stringify(navigationTest, null, 2));
    
    // Wait for potential navigation
    await page.waitForTimeout(2000);
    
    // Check if rotation controls are present
    const rotationControlsTest = await page.evaluate(() => {
      try {
        // Look for rotation control buttons
        const planetSpinButton = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent?.includes('🪐') && (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
        );
        
        const starSpinButton = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent?.includes('⭐') && (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
        );
        
        const timeControlPanel = Array.from(document.querySelectorAll('div')).find(div => 
          div.textContent?.includes('Time Control') || div.textContent?.includes('Rotation Control')
        );
        
        return {
          success: true,
          hasPlanetSpinButton: !!planetSpinButton,
          hasStarSpinButton: !!starSpinButton,
          hasTimeControlPanel: !!timeControlPanel,
          planetSpinButtonText: planetSpinButton?.textContent || null,
          starSpinButtonText: starSpinButton?.textContent || null,
          controlPanelVisible: !!timeControlPanel
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🎛️ ROTATION CONTROLS TEST RESULT:');
    console.log('==================================');
    console.log(JSON.stringify(rotationControlsTest, null, 2));
    
    if (rotationControlsTest.success) {
      console.log(`✅ Has planet spin button: ${rotationControlsTest.hasPlanetSpinButton}`);
      console.log(`✅ Has star spin button: ${rotationControlsTest.hasStarSpinButton}`);
      console.log(`✅ Has control panel: ${rotationControlsTest.hasTimeControlPanel}`);
      
      if (rotationControlsTest.planetSpinButtonText) {
        console.log(`🪐 Planet button text: "${rotationControlsTest.planetSpinButtonText}"`);
      }
      if (rotationControlsTest.starSpinButtonText) {
        console.log(`⭐ Star button text: "${rotationControlsTest.starSpinButtonText}"`);
      }
    }
    
    expect(rotationControlsTest.success, `Rotation controls test failed: ${rotationControlsTest.error}`).toBe(true);
    
    // The test passes if we can verify the Sol system data, even if we can't navigate to the solar system view
    // This is because the rotation controls are part of the SolarSystemView component
    expect(navigationTest.success, 'Should be able to interact with the galaxy canvas').toBe(true);
  });

  test('should verify planet data accuracy against known values', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const planetAccuracyTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        const solDetail = await stellarApi.getStarDetail(1);
        const planets = solDetail.planets || [];
        
        // Test specific known values
        const earth = planets.find(p => p.name === 'Earth');
        const jupiter = planets.find(p => p.name === 'Jupiter');
        const mars = planets.find(p => p.name === 'Mars');
        const saturn = planets.find(p => p.name === 'Saturn');
        
        const accuracy = {
          earthMass: earth?.mass_earth === 1.0,
          earthRadius: earth?.radius_earth === 1.0,
          earthSMA: earth?.sma_au === 1.0,
          earthMoons: earth?.moons === 1,
          earthHabitable: earth?.in_habitable_zone === true,
          
          jupiterMassive: jupiter && jupiter.mass_earth > 300, // Jupiter is ~318 Earth masses
          jupiterGasGiant: jupiter?.composition === 'gas_giant',
          jupiterManyMoons: jupiter && jupiter.moons > 90, // Jupiter has 95+ moons
          
          marsTwoMoons: mars?.moons === 2, // Phobos and Deimos
          marsRocky: mars?.composition === 'rocky',
          
          saturnRings: saturn && saturn.moons > 140, // Saturn has 146+ moons/rings
          saturnGasGiant: saturn?.composition === 'gas_giant'
        };
        
        const accuracyScore = Object.values(accuracy).filter(Boolean).length;
        const totalTests = Object.keys(accuracy).length;
        
        return {
          success: true,
          accuracy,
          accuracyScore,
          totalTests,
          accuracyPercentage: (accuracyScore / totalTests) * 100,
          planetDetails: {
            earth: earth ? {
              mass: earth.mass_earth,
              radius: earth.radius_earth,
              sma: earth.sma_au,
              moons: earth.moons,
              habitable: earth.in_habitable_zone
            } : null,
            jupiter: jupiter ? {
              mass: jupiter.mass_earth,
              composition: jupiter.composition,
              moons: jupiter.moons
            } : null,
            mars: mars ? {
              moons: mars.moons,
              composition: mars.composition
            } : null,
            saturn: saturn ? {
              moons: saturn.moons,
              composition: saturn.composition
            } : null
          }
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🔬 PLANET ACCURACY TEST RESULT:');
    console.log('===============================');
    console.log(JSON.stringify(planetAccuracyTest, null, 2));
    
    if (planetAccuracyTest.success) {
      console.log(`📊 Accuracy score: ${planetAccuracyTest.accuracyScore}/${planetAccuracyTest.totalTests} (${planetAccuracyTest.accuracyPercentage.toFixed(1)}%)`);
      
      Object.entries(planetAccuracyTest.accuracy).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed}`);
      });
    }
    
    expect(planetAccuracyTest.success, `Planet accuracy test failed: ${planetAccuracyTest.error}`).toBe(true);
    expect(planetAccuracyTest.accuracyPercentage, 'Should have high accuracy for known planetary data').toBeGreaterThan(80);
  });
});
