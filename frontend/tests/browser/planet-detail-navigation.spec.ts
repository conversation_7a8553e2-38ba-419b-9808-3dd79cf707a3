import { test, expect } from '@playwright/test';

test.describe('Planet Detail View Navigation', () => {
  test('should test planet detail view implementation', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    console.log('\n🪐 PLANET DETAIL VIEW TEST:');
    console.log('============================');
    
    // Test the planet detail view component directly
    const planetDetailTest = await page.evaluate(async () => {
      try {
        // Test if PlanetDetailView component can be imported
        const module = await import('/src/components/PlanetDetailView.tsx');
        const PlanetDetailView = module.default;
        
        return {
          success: true,
          componentExists: !!PlanetDetailView,
          componentName: PlanetDetailView?.name || 'Unknown'
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log(`🔍 Planet Detail Component: ${planetDetailTest.componentExists ? '✅' : '❌'}`);
    if (planetDetailTest.componentName) {
      console.log(`   Component Name: ${planetDetailTest.componentName}`);
    }
    
    if (!planetDetailTest.success) {
      console.log(`❌ Component Error: ${planetDetailTest.error}`);
    }
    
    // Test navigation structure
    const navigationTest = await page.evaluate(() => {
      // Check if the main galaxy component supports planet navigation
      const buttons = Array.from(document.querySelectorAll('button'));
      const solarButton = buttons.find(btn => 
        btn.textContent?.toLowerCase().includes('solar')
      );
      
      return {
        hasSolarNavigation: !!solarButton,
        totalButtons: buttons.length,
        buttonTexts: buttons.map(btn => btn.textContent?.trim()).filter(text => text && text.length > 0)
      };
    });
    
    console.log('\n🧭 Navigation Structure:');
    console.log(`   Solar Navigation: ${navigationTest.hasSolarNavigation ? '✅' : '❌'}`);
    console.log(`   Total Buttons: ${navigationTest.totalButtons}`);
    console.log(`   Navigation Options: ${navigationTest.buttonTexts.slice(0, 5).join(', ')}...`);
    
    // Test planet data availability
    const planetDataTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        const solDetail = await stellarApi.getStarDetail(1);
        const planets = solDetail.planets || [];
        
        const planetSummary = planets.map(planet => ({
          id: planet.planet_id,
          name: planet.name,
          hasDetailData: !!(planet.mass_earth && planet.radius_earth && planet.sma_au)
        }));
        
        return {
          success: true,
          planetCount: planets.length,
          planetSummary,
          allPlanetsHaveData: planetSummary.every(p => p.hasDetailData)
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n📊 Planet Data Availability:');
    if (planetDataTest.success) {
      console.log(`   Planet Count: ${planetDataTest.planetCount}`);
      console.log(`   All Have Detail Data: ${planetDataTest.allPlanetsHaveData ? '✅' : '❌'}`);
      
      console.log('   Planet Summary:');
      planetDataTest.planetSummary?.forEach((planet, index) => {
        const status = planet.hasDetailData ? '✅' : '❌';
        console.log(`     ${status} ${planet.name} (ID: ${planet.id})`);
      });
    } else {
      console.log(`❌ Planet Data Error: ${planetDataTest.error}`);
    }
    
    // Test moon data structure
    const moonDataTest = await page.evaluate(async () => {
      try {
        // Test if moon data structure is available
        const module = await import('/src/components/PlanetDetailView.tsx');
        
        // Check if the component has moon data (this is a basic structural test)
        const componentString = module.default.toString();
        const hasMoonReferences = componentString.includes('moon') || 
                                 componentString.includes('Moon') ||
                                 componentString.includes('MAJOR_MOONS');
        
        return {
          success: true,
          hasMoonReferences,
          componentHasMoonSupport: hasMoonReferences
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🌙 Moon Data Support:');
    console.log(`   Moon References: ${moonDataTest.hasMoonReferences ? '✅' : '❌'}`);
    console.log(`   Component Moon Support: ${moonDataTest.componentHasMoonSupport ? '✅' : '❌'}`);
    
    // Test the zoom navigation concept
    const zoomNavigationTest = await page.evaluate(() => {
      // Test the concept of multi-level navigation
      const currentUrl = window.location.href;
      const hasMultiLevelNavigation = document.body.textContent?.includes('Galaxy') ||
                                     document.body.textContent?.includes('Solar') ||
                                     document.body.textContent?.includes('Planet');
      
      return {
        currentUrl,
        hasMultiLevelNavigation,
        navigationLevels: [
          { level: 'Galaxy', detected: document.body.textContent?.includes('Galaxy') || false },
          { level: 'Solar System', detected: document.body.textContent?.includes('Solar') || false },
          { level: 'Planet Detail', detected: document.body.textContent?.includes('Planet') || false }
        ]
      };
    });
    
    console.log('\n🔍 Zoom Navigation Concept:');
    console.log(`   Multi-Level Navigation: ${zoomNavigationTest.hasMultiLevelNavigation ? '✅' : '❌'}`);
    console.log('   Navigation Levels:');
    zoomNavigationTest.navigationLevels.forEach(level => {
      const status = level.detected ? '✅' : '❌';
      console.log(`     ${status} ${level.level}`);
    });
    
    // Assertions
    expect(planetDetailTest.success, `Planet detail component test failed: ${planetDetailTest.error}`).toBe(true);
    expect(planetDetailTest.componentExists, 'Planet detail component should exist').toBe(true);
    expect(navigationTest.hasSolarNavigation, 'Should have solar system navigation').toBe(true);
    expect(planetDataTest.success, `Planet data test failed: ${planetDataTest.error}`).toBe(true);
    expect(planetDataTest.planetCount, 'Should have 8 planets').toBe(8);
    expect(moonDataTest.success, `Moon data test failed: ${moonDataTest.error}`).toBe(true);
    expect(moonDataTest.hasMoonReferences, 'Planet detail view should support moons').toBe(true);
  });

  test('should test enhanced visual features', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    console.log('\n✨ ENHANCED VISUAL FEATURES TEST:');
    console.log('==================================');
    
    // Navigate to solar system to test enhanced visuals
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const solarButton = buttons.find(btn => 
        btn.textContent?.toLowerCase().includes('solar')
      );
      if (solarButton) solarButton.click();
    });
    
    await page.waitForTimeout(3000);
    
    // Test enhanced visual features
    const visualFeaturesTest = await page.evaluate(() => {
      const canvas = document.querySelector('canvas');
      const hasCanvas = !!canvas;
      
      // Check for enhanced visual controls
      const buttons = Array.from(document.querySelectorAll('button'));
      const hasHighQuality = buttons.some(btn => btn.textContent?.includes('High Quality'));
      const hasRotationControls = buttons.some(btn => btn.textContent?.includes('🪐')) &&
                                  buttons.some(btn => btn.textContent?.includes('⭐'));
      
      // Check for visual quality indicators
      const hasTimeControls = buttons.some(btn => btn.textContent?.includes('▶')) ||
                             buttons.some(btn => btn.textContent?.includes('⏸'));
      
      return {
        hasCanvas,
        hasHighQuality,
        hasRotationControls,
        hasTimeControls,
        canvasSize: canvas ? { width: canvas.width, height: canvas.height } : null,
        totalButtons: buttons.length
      };
    });
    
    console.log('🎨 Visual Features:');
    console.log(`   3D Canvas: ${visualFeaturesTest.hasCanvas ? '✅' : '❌'}`);
    console.log(`   High Quality Option: ${visualFeaturesTest.hasHighQuality ? '✅' : '❌'}`);
    console.log(`   Rotation Controls: ${visualFeaturesTest.hasRotationControls ? '✅' : '❌'}`);
    console.log(`   Time Controls: ${visualFeaturesTest.hasTimeControls ? '✅' : '❌'}`);
    
    if (visualFeaturesTest.canvasSize) {
      console.log(`   Canvas Size: ${visualFeaturesTest.canvasSize.width}x${visualFeaturesTest.canvasSize.height}`);
    }
    
    // Test Saturn rings implementation
    const saturnRingsTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        const solDetail = await stellarApi.getStarDetail(1);
        const saturn = solDetail.planets?.find(p => p.name === 'Saturn');
        
        return {
          success: true,
          hasSaturn: !!saturn,
          saturnIsGasGiant: saturn?.composition === 'gas_giant',
          saturnData: saturn ? {
            name: saturn.name,
            radius: saturn.radius_earth,
            composition: saturn.composition
          } : null
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n💍 Saturn Rings Implementation:');
    console.log(`   Saturn Present: ${saturnRingsTest.hasSaturn ? '✅' : '❌'}`);
    console.log(`   Is Gas Giant: ${saturnRingsTest.saturnIsGasGiant ? '✅' : '❌'}`);
    if (saturnRingsTest.saturnData) {
      console.log(`   Saturn Radius: ${saturnRingsTest.saturnData.radius} Earth radii`);
    }
    
    // Test smooth orbital mechanics
    const orbitalMechanicsTest = await page.evaluate(() => {
      // Test if the page is responsive and not frozen
      const startTime = performance.now();
      let animationFrameCount = 0;
      
      const testAnimation = () => {
        animationFrameCount++;
        if (performance.now() - startTime < 500) {
          requestAnimationFrame(testAnimation);
        }
      };
      
      requestAnimationFrame(testAnimation);
      
      return new Promise(resolve => {
        setTimeout(() => {
          const fps = (animationFrameCount / 0.5); // Frames per second over 0.5 seconds
          resolve({
            animationFrameCount,
            estimatedFPS: Math.round(fps),
            isSmooth: fps > 20 // Consider 20+ FPS as acceptable
          });
        }, 600);
      });
    });
    
    console.log('\n🔄 Orbital Mechanics Performance:');
    console.log(`   Animation Frames: ${orbitalMechanicsTest.animationFrameCount}`);
    console.log(`   Estimated FPS: ${orbitalMechanicsTest.estimatedFPS}`);
    console.log(`   Smooth Animation: ${orbitalMechanicsTest.isSmooth ? '✅' : '❌'}`);
    
    // Assertions
    expect(visualFeaturesTest.hasCanvas, 'Should have 3D canvas').toBe(true);
    expect(visualFeaturesTest.hasRotationControls, 'Should have rotation controls').toBe(true);
    expect(saturnRingsTest.success, `Saturn test failed: ${saturnRingsTest.error}`).toBe(true);
    expect(saturnRingsTest.hasSaturn, 'Should have Saturn in the system').toBe(true);
    expect(orbitalMechanicsTest.estimatedFPS, 'Should have reasonable FPS').toBeGreaterThan(10);
  });
});
