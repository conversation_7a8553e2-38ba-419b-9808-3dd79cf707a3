import { test, expect } from '@playwright/test';

test.describe('Sol Planets Debug', () => {
  test('should debug why Sol has no planets', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const solDebugTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        console.log('🔍 Debugging Sol planets...');
        
        // Test 1: Check if stellarApi exists
        if (!stellarApi) {
          return { error: 'stellarApi not found' };
        }
        
        // Test 2: Check if getStarDetail method exists
        if (typeof stellarApi.getStarDetail !== 'function') {
          return { error: 'getStarDetail method not found' };
        }
        
        // Test 3: Call getStarDetail for Sol
        console.log('🌟 Calling getStarDetail(1) for Sol...');
        const solDetail = await stellarApi.getStarDetail(1);
        console.log('📊 Sol detail result:', solDetail);
        
        // Test 4: Check the structure
        const result = {
          success: true,
          starName: solDetail.name,
          starId: solDetail.star_id,
          hasPlanetsProperty: 'planets' in solDetail,
          planetsType: typeof solDetail.planets,
          planetsIsArray: Array.isArray(solDetail.planets),
          planetCount: solDetail.planets ? solDetail.planets.length : 0,
          planetNames: solDetail.planets ? solDetail.planets.map(p => p.name) : [],
          rawPlanets: solDetail.planets,
          fullSolDetail: solDetail
        };
        
        console.log('🧪 Sol debug result:', result);
        return result;
      } catch (error) {
        console.error('❌ Sol debug error:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : null
        };
      }
    });
    
    console.log('\n🌍 SOL PLANETS DEBUG RESULT:');
    console.log('============================');
    console.log(JSON.stringify(solDebugTest, null, 2));
    
    if (solDebugTest.success) {
      console.log(`✅ Sol loaded: ${solDebugTest.starName} (ID: ${solDebugTest.starId})`);
      console.log(`📊 Has planets property: ${solDebugTest.hasPlanetsProperty}`);
      console.log(`📊 Planets type: ${solDebugTest.planetsType}`);
      console.log(`📊 Planets is array: ${solDebugTest.planetsIsArray}`);
      console.log(`📊 Planet count: ${solDebugTest.planetCount}`);
      console.log(`📊 Planet names: ${solDebugTest.planetNames.join(', ')}`);
      
      if (solDebugTest.planetCount === 0) {
        console.log('🚨 ISSUE: Sol has 0 planets!');
        console.log('🔍 Raw planets data:', solDebugTest.rawPlanets);
      }
    } else {
      console.log(`❌ Sol debug failed: ${solDebugTest.error}`);
    }
    
    // The test should pass if we can load Sol, regardless of planet count for now
    expect(solDebugTest.success, `Sol debug failed: ${solDebugTest.error}`).toBe(true);
    expect(solDebugTest.starName, 'Should be Sol').toBe('Sol');
  });

  test('should test getFallbackPlanets method directly', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const fallbackPlanetsTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        // Try to access the private method (this might not work)
        let fallbackPlanetsResult = null;
        let methodAccessible = false;
        
        try {
          // Try to access the private method
          const getFallbackPlanets = (stellarApi as any).getFallbackPlanets;
          if (typeof getFallbackPlanets === 'function') {
            methodAccessible = true;
            fallbackPlanetsResult = getFallbackPlanets.call(stellarApi, 1);
          }
        } catch (error) {
          console.log('⚠️ Cannot access private getFallbackPlanets method');
        }
        
        // Alternative: Test by creating a new instance with null baseUrl
        console.log('🧪 Testing fallback planets via production mode...');
        const StellarApiClient = module.default || (module as any).StellarApiClient;
        let prodApi;
        
        if (StellarApiClient) {
          prodApi = new StellarApiClient(null); // Force production mode
        } else {
          prodApi = stellarApi;
          (prodApi as any).baseUrl = null; // Force production mode
        }
        
        const solDetailProd = await prodApi.getStarDetail(1);
        
        return {
          success: true,
          methodAccessible,
          directFallbackPlanets: fallbackPlanetsResult,
          productionModePlanets: solDetailProd.planets,
          productionModePlanetCount: solDetailProd.planets ? solDetailPlanets.length : 0,
          productionModeStarName: solDetailProd.name
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🔧 FALLBACK PLANETS TEST RESULT:');
    console.log('=================================');
    console.log(JSON.stringify(fallbackPlanetsTest, null, 2));
    
    expect(fallbackPlanetsTest.success, `Fallback planets test failed: ${fallbackPlanetsTest.error}`).toBe(true);
  });

  test('should test the exact production scenario', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    const productionScenarioTest = await page.evaluate(async () => {
      try {
        // Simulate the exact production environment
        const module = await import('/src/services/stellarApi.ts');
        
        // Create a new StellarApiClient with null baseUrl (production mode)
        const StellarApiClient = module.default || (module as any).StellarApiClient;
        
        if (!StellarApiClient) {
          return { error: 'StellarApiClient constructor not found' };
        }
        
        console.log('🏭 Creating production mode StellarAPI...');
        const prodApi = new StellarApiClient(null);
        
        console.log('🌟 Testing getStarDetail(1) in production mode...');
        const solDetail = await prodApi.getStarDetail(1);
        
        console.log('📊 Production Sol detail:', solDetail);
        
        return {
          success: true,
          starName: solDetail.name,
          starId: solDetail.star_id,
          planetCount: solDetail.planets ? solDetail.planets.length : 0,
          planetNames: solDetail.planets ? solDetail.planets.map(p => p.name) : [],
          hasEarth: solDetail.planets ? solDetail.planets.some(p => p.name === 'Earth') : false,
          hasMercury: solDetail.planets ? solDetail.planets.some(p => p.name === 'Mercury') : false,
          planetsStructure: solDetail.planets
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : null
        };
      }
    });
    
    console.log('\n🏭 PRODUCTION SCENARIO TEST RESULT:');
    console.log('===================================');
    console.log(JSON.stringify(productionScenarioTest, null, 2));
    
    if (productionScenarioTest.success) {
      console.log(`✅ Production Sol: ${productionScenarioTest.starName}`);
      console.log(`📊 Planet count: ${productionScenarioTest.planetCount}`);
      console.log(`📊 Has Earth: ${productionScenarioTest.hasEarth}`);
      console.log(`📊 Has Mercury: ${productionScenarioTest.hasMercury}`);
      console.log(`📊 Planets: ${productionScenarioTest.planetNames.join(', ')}`);
      
      if (productionScenarioTest.planetCount > 0) {
        console.log('✅ SUCCESS: Sol has planets in production mode!');
      } else {
        console.log('🚨 ISSUE: Sol still has no planets in production mode!');
      }
    } else {
      console.log(`❌ Production scenario failed: ${productionScenarioTest.error}`);
    }
    
    expect(productionScenarioTest.success, `Production scenario failed: ${productionScenarioTest.error}`).toBe(true);
    expect(productionScenarioTest.planetCount, 'Sol should have planets in production mode').toBeGreaterThan(0);
  });
});
