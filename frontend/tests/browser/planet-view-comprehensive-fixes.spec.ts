import { test, expect } from '@playwright/test';

test.describe('Planet View Comprehensive Fixes Verification', () => {
  test('should verify enhanced moon visibility and realistic orbital timing', async ({ page }) => {
    console.log('🌙 Testing enhanced moon visibility and realistic orbital timing...');
    
    const errors: string[] = [];
    const logs: string[] = [];
    
    page.on('console', msg => {
      const text = msg.text();
      logs.push(text);
      if (msg.type() === 'error') {
        errors.push(text);
      }
    });
    
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // Look for enhanced moon logs with larger size calculations
    const moonLogs = logs.filter(log => 
      log.includes('🌙 Moon') && 
      log.includes('size=') && 
      log.includes('timeSpeed=')
    );
    
    console.log(`Found ${moonLogs.length} enhanced moon calculation logs`);
    
    // Verify moon size calculations are larger (should be >= 0.3)
    const sizeMatches = moonLogs.map(log => {
      const sizeMatch = log.match(/size=(\d+\.\d+)/);
      return sizeMatch ? parseFloat(sizeMatch[1]) : 0;
    });
    
    if (sizeMatches.length > 0) {
      const minSize = Math.min(...sizeMatches);
      console.log(`Minimum moon size: ${minSize}`);
      expect(minSize).toBeGreaterThanOrEqual(0.25); // Should be much larger now
    }
    
    // Verify no critical errors
    const criticalErrors = errors.filter(error => 
      error.includes('toFixed is not a function') ||
      error.includes('not found') ||
      error.includes('undefined')
    );
    
    expect(criticalErrors.length).toBe(0);
    console.log('✅ Enhanced moon visibility verified');
  });

  test('should verify visual sun rendering (not text window)', async ({ page }) => {
    console.log('☀️ Testing visual sun rendering...');
    
    const logs: string[] = [];
    const errors: string[] = [];
    
    page.on('console', msg => {
      const text = msg.text();
      logs.push(text);
      if (msg.type() === 'error') {
        errors.push(text);
      }
    });
    
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // Look for sun rendering logs with correct AU calculations
    const sunLogs = logs.filter(log => 
      log.includes('☀️ Distant sun') && 
      log.includes('AU=') &&
      log.includes('size=')
    );
    
    console.log(`Found ${sunLogs.length} sun rendering logs`);
    
    // Verify sun size calculations are larger (should be >= 3.0)
    const sunSizeMatches = sunLogs.map(log => {
      const sizeMatch = log.match(/size=(\d+\.\d+)/);
      return sizeMatch ? parseFloat(sizeMatch[1]) : 0;
    });
    
    if (sunSizeMatches.length > 0) {
      const minSunSize = Math.min(...sunSizeMatches);
      console.log(`Minimum sun size: ${minSunSize}`);
      expect(minSunSize).toBeGreaterThanOrEqual(3.0); // Should be larger now
    }
    
    // Verify no Three.js rendering errors
    const renderingErrors = errors.filter(error => 
      error.includes('Three') ||
      error.includes('WebGL') ||
      error.includes('material') ||
      error.includes('geometry')
    );
    
    expect(renderingErrors.length).toBe(0);
    console.log('✅ Visual sun rendering verified');
  });

  test('should verify time controls are visible and functional', async ({ page }) => {
    console.log('⏱️ Testing time controls visibility and functionality...');
    
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(2000);
    
    // Check for time control text in the page
    const pageContent = await page.textContent('body');
    
    // Time controls should be present in the DOM
    expect(pageContent).toContain('Time Control');
    expect(pageContent).toContain('Speed:');
    expect(pageContent).toContain('Moon orbits');
    
    console.log('✅ Time controls are present in the DOM');
  });

  test('should verify correct AU distances for different planets', async ({ page }) => {
    console.log('🪐 Testing correct AU distances for different planets...');
    
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    
    // Test that different planets have different AU distances
    const planetDistances = data.planets.map((p: any) => ({
      name: p.name,
      sma_au: p.sma_au
    }));
    
    console.log('Planet distances:', planetDistances);
    
    // Verify that not all planets have the same distance (should not all be 1.0)
    const uniqueDistances = new Set(planetDistances.map((p: any) => p.sma_au));
    expect(uniqueDistances.size).toBeGreaterThan(1);
    
    // Verify specific known distances
    const earth = planetDistances.find((p: any) => p.name === 'Earth');
    const mars = planetDistances.find((p: any) => p.name === 'Mars');
    
    if (earth && mars) {
      expect(earth.sma_au).toBeCloseTo(1.0, 0.1);
      expect(mars.sma_au).toBeGreaterThan(1.4);
      expect(mars.sma_au).toBeLessThan(1.6);
      console.log(`✅ Earth: ${earth.sma_au} AU, Mars: ${mars.sma_au} AU`);
    }
    
    console.log('✅ Correct AU distances verified');
  });

  test('should verify realistic moon orbital periods', async ({ page }) => {
    console.log('🔄 Testing realistic moon orbital periods...');
    
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    expect(earth).toBeDefined();
    expect(earth.moons).toBeDefined();
    expect(earth.moons.length).toBe(1);
    
    const luna = earth.moons[0];
    expect(luna.name).toBe('Luna');
    expect(luna.orbital_period_days).toBeCloseTo(27.3, 1);
    
    // Verify orbital timing calculations
    // At 1x speed: 1 day = 1 second, so Luna takes 27.3 seconds
    // At 10x speed: Luna takes 2.73 seconds
    const orbitTime1x = luna.orbital_period_days;
    const orbitTime10x = luna.orbital_period_days / 10;
    
    expect(orbitTime1x).toBeCloseTo(27.3, 1);
    expect(orbitTime10x).toBeCloseTo(2.73, 0.2);
    
    console.log(`✅ Luna orbital timing: ${orbitTime1x}s at 1x, ${orbitTime10x}s at 10x`);
  });

  test('should verify no compilation errors and clean rendering', async ({ page }) => {
    console.log('🔧 Testing for compilation errors and clean rendering...');
    
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(`Page error: ${error.message}`);
    });
    
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(5000);
    
    // Filter out known non-critical errors
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_') &&
      !error.toLowerCase().includes('warning') &&
      !error.includes('DevTools')
    );
    
    console.log(`Total errors: ${errors.length}, Critical errors: ${criticalErrors.length}`);
    
    if (criticalErrors.length > 0) {
      console.log('Critical errors found:', criticalErrors);
    }
    
    expect(criticalErrors.length).toBe(0);
    console.log('✅ No critical compilation or rendering errors');
  });
});
