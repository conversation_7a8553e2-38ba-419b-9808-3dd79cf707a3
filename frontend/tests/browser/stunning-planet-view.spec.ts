import { test, expect } from '@playwright/test';

test.describe('Stunning Planet View Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000); // Allow galaxy to load
  });

  test('should display stunning Earth with moon in planet detail view', async ({ page }) => {
    // Click on solar button to enter solar system view
    await page.click('button:has-text("solar")');
    await page.waitForTimeout(3000);

    // Try to navigate to Earth's planet detail view
    // We'll simulate clicking in the area where Earth should be
    const canvas = page.locator('canvas').first();
    await canvas.click({ position: { x: 400, y: 300 } });
    await page.waitForTimeout(2000);

    // Check if we're in planet detail view by looking for planet information
    const planetInfo = page.locator('text=/Physical Properties|Orbital Properties/').first();
    if (await planetInfo.isVisible()) {
      console.log('Successfully entered planet detail view');
      
      // Verify Earth-specific information is displayed
      const earthInfo = page.locator('text=Earth');
      if (await earthInfo.isVisible()) {
        console.log('Earth planet view confirmed');
        
        // Check for moon information
        const moonSection = page.locator('text=Moons');
        if (await moonSection.isVisible()) {
          console.log('Moon section found');
          
          // Look for Luna specifically
          const lunaInfo = page.locator('text=Luna');
          expect(await lunaInfo.isVisible()).toBeTruthy();
          
          // Verify moon orbital period is displayed correctly
          const moonPeriod = page.locator('text=/Period: 27\.\d+ days/');
          expect(await moonPeriod.isVisible()).toBeTruthy();
        }
      }
    }

    // Verify no JavaScript errors occurred
    const errors = await page.evaluate(() => {
      return (window as any).jsErrors || [];
    });
    expect(errors.length).toBe(0);
  });

  test('should verify API returns correct moon data for Earth', async ({ page }) => {
    // Test the API directly
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    
    expect(earth).toBeDefined();
    expect(earth.moons).toBeDefined();
    expect(earth.moons.length).toBe(1);
    
    const luna = earth.moons[0];
    expect(luna.name).toBe('Luna');
    expect(typeof luna.orbital_period_days).toBe('number');
    expect(luna.orbital_period_days).toBeCloseTo(27.3, 1);
    expect(typeof luna.distance_km).toBe('number');
    expect(luna.distance_km).toBe(384400);
    expect(luna.color_hex).toBe('#C0C0C0');
  });

  test('should handle all planets with enhanced visuals', async ({ page }) => {
    // Test API data for all planets
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    expect(data.planets.length).toBe(8);
    
    // Verify each planet has the required visual data
    for (const planet of data.planets) {
      expect(planet.name).toBeDefined();
      expect(planet.composition).toBeDefined();
      
      // Check for enhanced visual properties
      if (planet.dominant_color) {
        expect(typeof planet.dominant_color).toBe('string');
      }
      
      if (planet.color_hex) {
        expect(planet.color_hex).toMatch(/^#[0-9A-Fa-f]{6}$/);
      }
      
      // Verify atmospheric data
      expect(typeof planet.has_atmosphere).toBe('boolean');
      
      // Check moon data if present
      if (planet.moons && planet.moons.length > 0) {
        for (const moon of planet.moons) {
          expect(typeof moon.orbital_period_days).toBe('number');
          expect(typeof moon.distance_km).toBe('number');
          expect(moon.orbital_period_days).toBeGreaterThan(0);
          expect(moon.distance_km).toBeGreaterThan(0);
        }
      }
    }
  });

  test('should verify gas giants have enhanced visual effects', async ({ page }) => {
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    const gasGiants = data.planets.filter((p: any) => 
      p.composition === 'gas_giant' || p.composition === 'ice_giant'
    );
    
    expect(gasGiants.length).toBeGreaterThan(0);
    
    for (const planet of gasGiants) {
      console.log(`Testing gas giant: ${planet.name} (${planet.composition})`);
      
      // Gas giants should have atmospheric effects
      expect(planet.has_atmosphere).toBe(true);
      
      // Should have enhanced visual properties
      expect(planet.dominant_color).toBeDefined();
      
      // Jupiter should have moons
      if (planet.name === 'Jupiter') {
        expect(planet.moons).toBeDefined();
        expect(planet.moons.length).toBeGreaterThan(0);
      }
    }
  });

  test('should verify moon orbital mechanics data', async ({ page }) => {
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    const planetsWithMoons = data.planets.filter((p: any) => p.moons && p.moons.length > 0);
    
    for (const planet of planetsWithMoons) {
      console.log(`Testing ${planet.name} with ${planet.moons.length} moons`);
      
      for (const moon of planet.moons) {
        // Verify orbital data is realistic
        expect(moon.orbital_period_days).toBeGreaterThan(0);
        expect(moon.orbital_period_days).toBeLessThan(10000); // Reasonable upper bound
        
        expect(moon.distance_km).toBeGreaterThan(1000); // At least 1000 km from planet
        expect(moon.distance_km).toBeLessThan(10000000); // Less than 10 million km
        
        // Verify visual properties
        if (moon.albedo !== null) {
          expect(moon.albedo).toBeGreaterThanOrEqual(0);
          expect(moon.albedo).toBeLessThanOrEqual(1);
        }
        
        if (moon.color_hex) {
          expect(moon.color_hex).toMatch(/^#[0-9A-Fa-f]{6}$/);
        }
      }
    }
  });
});
