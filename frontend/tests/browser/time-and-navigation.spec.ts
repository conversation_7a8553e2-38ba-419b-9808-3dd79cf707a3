import { test, expect } from '@playwright/test';

test.describe('Time Synchronization and Planet Navigation', () => {
  test('should verify time synchronization and planet navigation work correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    console.log('\n⏰ TIME SYNCHRONIZATION & PLANET NAVIGATION TEST:');
    console.log('=================================================');
    
    // Navigate to solar system
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const solarButton = buttons.find(btn => 
        btn.textContent?.toLowerCase().includes('solar')
      );
      if (solarButton) solarButton.click();
    });
    
    await page.waitForTimeout(3000);
    
    // Test time synchronization
    const timeSyncTest = await page.evaluate(() => {
      // Get initial time display
      const timeElement = document.body.textContent?.match(/Time: ([\d.]+) years/);
      const initialTime = timeElement ? parseFloat(timeElement[1]) : 0;
      
      // Check if time controls are available
      const buttons = Array.from(document.querySelectorAll('button'));
      const timeButtons = buttons.filter(btn => 
        btn.textContent?.includes('1x') || 
        btn.textContent?.includes('10x') || 
        btn.textContent?.includes('100x') || 
        btn.textContent?.includes('1000x')
      );
      
      // Test 10x speed button
      const tenXButton = buttons.find(btn => btn.textContent?.includes('10x'));
      let speedChangeWorked = false;
      
      if (tenXButton) {
        tenXButton.click();
        speedChangeWorked = true;
      }
      
      return {
        initialTime,
        timeButtonsCount: timeButtons.length,
        speedChangeWorked,
        hasRealisticSpeeds: timeButtons.length >= 4,
        timeDisplayWorking: initialTime >= 0
      };
    });
    
    console.log('\n⏰ TIME SYNCHRONIZATION TEST:');
    console.log(`   Initial Time: ${timeSyncTest.initialTime} years`);
    console.log(`   Time Buttons Available: ${timeSyncTest.timeButtonsCount}/4`);
    console.log(`   Speed Change Works: ${timeSyncTest.speedChangeWorked ? '✅' : '❌'}`);
    console.log(`   Realistic Speeds: ${timeSyncTest.hasRealisticSpeeds ? '✅' : '❌'}`);
    console.log(`   Time Display Working: ${timeSyncTest.timeDisplayWorking ? '✅' : '❌'}`);
    
    // Wait a moment and check if time progresses
    await page.waitForTimeout(2000);
    
    const timeProgressTest = await page.evaluate(() => {
      const timeElement = document.body.textContent?.match(/Time: ([\d.]+) years/);
      const currentTime = timeElement ? parseFloat(timeElement[1]) : 0;
      
      return {
        currentTime,
        timeProgressed: currentTime > 0
      };
    });
    
    console.log(`   Time After 2s: ${timeProgressTest.currentTime} years`);
    console.log(`   Time Progressed: ${timeProgressTest.timeProgressed ? '✅' : '❌'}`);
    
    // Test planet navigation
    const planetNavTest = await page.evaluate(() => {
      const canvas = document.querySelector('canvas');
      if (!canvas) return { success: false, error: 'No canvas found' };
      
      // Try clicking on different areas of the canvas to hit planets
      const rect = canvas.getBoundingClientRect();
      const clickPositions = [
        { x: rect.left + rect.width * 0.3, y: rect.top + rect.height * 0.5, name: 'Left' },
        { x: rect.left + rect.width * 0.5, y: rect.top + rect.height * 0.5, name: 'Center' },
        { x: rect.left + rect.width * 0.7, y: rect.top + rect.height * 0.5, name: 'Right' },
      ];
      
      let clicksPerformed = 0;
      let navigationAttempted = false;
      
      // Listen for console logs to detect planet clicks
      const originalLog = console.log;
      let planetClickDetected = false;
      
      console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('Planet clicked') || message.includes('Navigating to planet')) {
          planetClickDetected = true;
        }
        originalLog.apply(console, args);
      };
      
      clickPositions.forEach((pos) => {
        const clickEvent = new MouseEvent('click', {
          clientX: pos.x,
          clientY: pos.y,
          bubbles: true
        });
        canvas.dispatchEvent(clickEvent);
        clicksPerformed++;
      });
      
      // Restore console.log
      console.log = originalLog;
      
      return {
        success: true,
        clicksPerformed,
        planetClickDetected,
        canvasClickable: true,
        navigationImplemented: planetClickDetected
      };
    });
    
    console.log('\n🪐 PLANET NAVIGATION TEST:');
    console.log(`   Canvas Clickable: ${planetNavTest.canvasClickable ? '✅' : '❌'}`);
    console.log(`   Clicks Performed: ${planetNavTest.clicksPerformed}`);
    console.log(`   Planet Click Detected: ${planetNavTest.planetClickDetected ? '✅' : '❌'}`);
    console.log(`   Navigation Implemented: ${planetNavTest.navigationImplemented ? '✅' : '❌'}`);
    
    // Test the demo navigation system
    const demoNavTest = await page.evaluate(async () => {
      try {
        // Check if SolarSystemDemo has proper navigation
        const module = await import('/src/components/SolarSystemDemo.tsx');
        const componentString = module.default.toString();
        
        const hasNavigationStates = componentString.includes('planet_detail') &&
                                   componentString.includes('solar_system');
        const hasPlanetDetailView = componentString.includes('PlanetDetailView');
        const hasProperHandlers = componentString.includes('handlePlanetClick') &&
                                 componentString.includes('handleBackToSolarSystem');
        
        return {
          success: true,
          hasNavigationStates,
          hasPlanetDetailView,
          hasProperHandlers,
          navigationFullyImplemented: hasNavigationStates && hasPlanetDetailView && hasProperHandlers
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🔄 DEMO NAVIGATION TEST:');
    console.log(`   Navigation States: ${demoNavTest.hasNavigationStates ? '✅' : '❌'}`);
    console.log(`   Planet Detail View: ${demoNavTest.hasPlanetDetailView ? '✅' : '❌'}`);
    console.log(`   Proper Handlers: ${demoNavTest.hasProperHandlers ? '✅' : '❌'}`);
    console.log(`   Navigation Complete: ${demoNavTest.navigationFullyImplemented ? '✅' : '❌'}`);
    
    // Test synchronized planet rotation
    const rotationSyncTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/components/StunningSolarSystemView.tsx');
        const componentString = module.default.toString();
        
        // Check for synchronized rotation
        const hasSyncedRotation = componentString.includes('rotation.y += 0.005 * timeSpeed') &&
                                 componentString.includes('timeSpeed') &&
                                 componentString.includes('isPaused');
        const hasSyncedOrbits = componentString.includes('time / planetData.period');
        
        return {
          success: true,
          hasSyncedRotation,
          hasSyncedOrbits,
          rotationSynchronized: hasSyncedRotation && hasSyncedOrbits
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🔄 ROTATION SYNCHRONIZATION TEST:');
    console.log(`   Synced Rotation: ${rotationSyncTest.hasSyncedRotation ? '✅' : '❌'}`);
    console.log(`   Synced Orbits: ${rotationSyncTest.hasSyncedOrbits ? '✅' : '❌'}`);
    console.log(`   Rotation Synchronized: ${rotationSyncTest.rotationSynchronized ? '✅' : '❌'}`);
    
    // Final summary
    const allSystemsWorking = 
      timeSyncTest.hasRealisticSpeeds &&
      timeSyncTest.timeDisplayWorking &&
      timeProgressTest.timeProgressed &&
      planetNavTest.navigationImplemented &&
      demoNavTest.navigationFullyImplemented &&
      rotationSyncTest.rotationSynchronized;
    
    console.log('\n🎯 FINAL SUMMARY:');
    console.log(`   Time Synchronization: ${timeSyncTest.hasRealisticSpeeds && timeProgressTest.timeProgressed ? '✅' : '❌'}`);
    console.log(`   Planet Navigation: ${planetNavTest.navigationImplemented && demoNavTest.navigationFullyImplemented ? '✅' : '❌'}`);
    console.log(`   Rotation Sync: ${rotationSyncTest.rotationSynchronized ? '✅' : '❌'}`);
    console.log(`   All Systems Working: ${allSystemsWorking ? '✅ COMPLETE SUCCESS!' : '❌ NEEDS WORK'}`);
    
    // Assertions
    expect(timeSyncTest.hasRealisticSpeeds, 'Should have realistic time speeds').toBe(true);
    expect(timeSyncTest.timeDisplayWorking, 'Time display should work').toBe(true);
    expect(timeProgressTest.timeProgressed, 'Time should progress').toBe(true);
    expect(planetNavTest.canvasClickable, 'Canvas should be clickable').toBe(true);
    expect(demoNavTest.success, `Demo navigation test failed: ${demoNavTest.error}`).toBe(true);
    expect(demoNavTest.navigationFullyImplemented, 'Demo navigation should be fully implemented').toBe(true);
    expect(rotationSyncTest.success, `Rotation sync test failed: ${rotationSyncTest.error}`).toBe(true);
    expect(rotationSyncTest.rotationSynchronized, 'Rotation should be synchronized with time').toBe(true);
    expect(allSystemsWorking, 'All systems should be working correctly').toBe(true);
  });
});
