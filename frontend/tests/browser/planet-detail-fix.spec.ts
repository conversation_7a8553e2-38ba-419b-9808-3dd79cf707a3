import { test, expect } from '@playwright/test';

test.describe('Planet Detail View Fix', () => {
  test('should fix the planet ID mismatch and load Venus correctly', async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5174');
    
    // Wait for the galaxy to load
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // Click on the solar button to enter solar system view
    const solarButton = page.locator('button:has-text("Solar")');
    if (await solarButton.isVisible({ timeout: 5000 })) {
      await solarButton.click();
      await page.waitForTimeout(2000);
    }
    
    // Wait for solar system to load
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(3000);
    
    // Capture console logs to track the planet click
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'log' && msg.text().includes('🪐')) {
        consoleLogs.push(msg.text());
      }
    });
    
    // Capture console errors to check for the planet not found error
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Try to click on Venus (should be the second planet)
    const canvas = page.locator('canvas').first();
    await canvas.click({ position: { x: 400, y: 350 } }); // Approximate position for Venus
    await page.waitForTimeout(1000);
    
    // Try a few more clicks to ensure we hit a planet
    await canvas.click({ position: { x: 450, y: 350 } });
    await page.waitForTimeout(1000);
    await canvas.click({ position: { x: 500, y: 350 } });
    await page.waitForTimeout(2000);
    
    // Check if we successfully navigated to planet detail view
    const planetDetailTest = await page.evaluate(async () => {
      try {
        // Check if we're in planet detail view
        const url = window.location.href;
        const hasBackButton = document.querySelector('button:has-text("Back"), button:has-text("← Back")') !== null;
        const hasPlanetContent = document.body.textContent?.includes('Planet') || false;
        
        return {
          success: true,
          url,
          hasBackButton,
          hasPlanetContent,
          bodyText: document.body.textContent?.substring(0, 200) || ''
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });
    
    console.log('🧪 PLANET DETAIL FIX TEST RESULT:');
    console.log('==================================');
    console.log(JSON.stringify(planetDetailTest, null, 2));
    
    console.log('\n📝 Console Logs:');
    consoleLogs.forEach(log => console.log(`   ${log}`));
    
    console.log('\n❌ Console Errors:');
    consoleErrors.forEach(error => console.log(`   ${error}`));
    
    // Check that we don't have the "Planet with ID X not found" error
    const hasPlanetNotFoundError = consoleErrors.some(error => 
      error.includes('Planet with ID') && error.includes('not found')
    );
    
    console.log(`\n🔍 Has "Planet not found" error: ${hasPlanetNotFoundError}`);
    
    // The test passes if we either successfully navigated to planet detail view
    // OR if we don't have the specific "Planet with ID X not found" error
    const testPassed = planetDetailTest.success && (!hasPlanetNotFoundError || planetDetailTest.hasBackButton);
    
    console.log(`\n🎯 Test Result: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
    
    expect(testPassed, 'Should either navigate to planet detail or not show planet ID error').toBe(true);
  });
  
  test('should test direct API planet ID format', async ({ page }) => {
    // Test the API directly to understand the planet ID format
    const apiTest = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:19081/v1/stellar/stars/1');
        const data = await response.json();
        
        const planets = data.planets || [];
        const firstPlanet = planets[0];
        const secondPlanet = planets[1]; // Venus
        
        return {
          success: true,
          planetCount: planets.length,
          firstPlanet: firstPlanet ? {
            planet_id: firstPlanet.planet_id,
            name: firstPlanet.name,
            idType: typeof firstPlanet.planet_id
          } : null,
          secondPlanet: secondPlanet ? {
            planet_id: secondPlanet.planet_id,
            name: secondPlanet.name,
            idType: typeof secondPlanet.planet_id
          } : null
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });
    
    console.log('🔬 API PLANET ID TEST RESULT:');
    console.log('=============================');
    console.log(JSON.stringify(apiTest, null, 2));
    
    expect(apiTest.success, 'Should successfully fetch planet data from API').toBe(true);
    expect(apiTest.planetCount, 'Should have planets in Sol system').toBeGreaterThan(0);
    
    if (apiTest.secondPlanet) {
      console.log(`\n🪐 Venus ID: ${apiTest.secondPlanet.planet_id} (${apiTest.secondPlanet.idType})`);
      expect(apiTest.secondPlanet.name, 'Second planet should be Venus').toBe('Venus');
    }
  });
});
