import { test, expect } from '@playwright/test';

/**
 * Frontend Health and Integration Tests
 * Comprehensive browser testing for the Galactic Genesis webapp
 */

test.describe('Frontend Health and Integration', () => {
  
  test('should load without any JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      } else if (msg.type() === 'warning') {
        warnings.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Log warnings for debugging but don't fail on them
    if (warnings.length > 0) {
      console.log('Warnings detected:', warnings);
    }
    
    // Fail on any JavaScript errors
    expect(errors, `JavaScript errors detected: ${errors.join(', ')}`).toEqual([]);
  });
  
  test('should have correct page structure', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for basic page structure
    await expect(page).toHaveTitle(/Galactic Genesis/);
    
    // Look for main content areas
    const mainContent = page.locator('body, #root, main, .app').first();
    await expect(mainContent).toBeVisible();
  });
  
  test('should load CSS and styling correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check that CSS is loaded by verifying computed styles
    const bodyStyles = await page.evaluate(() => {
      const body = document.body;
      const styles = window.getComputedStyle(body);
      return {
        margin: styles.margin,
        padding: styles.padding,
        fontFamily: styles.fontFamily
      };
    });
    
    // Verify that styles are applied (not default browser styles)
    expect(bodyStyles.fontFamily).not.toBe('');
  });
  
  test('should handle API connectivity', async ({ page }) => {
    const errors: string[] = [];
    const apiRequests: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('request', request => {
      const url = request.url();
      if (url.includes('api') || url.includes('8081') || url.includes('19081') || url.includes('8086')) {
        apiRequests.push(`${request.method()} ${url}`);
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for potential API calls
    await page.waitForTimeout(5000);
    
    console.log('API requests made:', apiRequests);
    
    // Verify no critical errors occurred
    expect(errors).toEqual([]);
  });
  
  test('should be responsive and interactive', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test basic interactivity
    const clickableElements = page.locator('button, a, [role="button"]');
    const count = await clickableElements.count();
    
    if (count > 0) {
      // Try clicking the first interactive element
      const firstElement = clickableElements.first();
      if (await firstElement.isVisible()) {
        await firstElement.click();
        // Wait a moment for any response
        await page.waitForTimeout(1000);
      }
    }
    
    // Verify page is still functional after interaction
    await expect(page).toHaveTitle(/Galactic Genesis/);
  });
  
  test('should handle viewport changes', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test different viewport sizes
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);
    
    await page.setViewportSize({ width: 1024, height: 768 });
    await page.waitForTimeout(500);
    
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Verify page is still functional
    await expect(page).toHaveTitle(/Galactic Genesis/);
  });
  
  test('should load all critical resources', async ({ page }) => {
    const failedRequests: string[] = [];
    
    page.on('requestfailed', request => {
      failedRequests.push(`${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Log failed requests for debugging
    if (failedRequests.length > 0) {
      console.log('Failed requests:', failedRequests);
    }
    
    // Allow some non-critical failures but not too many
    expect(failedRequests.length, `Too many failed requests: ${failedRequests.join(', ')}`).toBeLessThan(5);
  });
  
  test('should have working navigation and routing', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for navigation elements
    const navElements = page.locator('nav, .nav, [role="navigation"], a[href]');
    const navCount = await navElements.count();
    
    if (navCount > 0) {
      console.log(`Found ${navCount} navigation elements`);
      
      // Try clicking a navigation link if available
      const firstNav = navElements.first();
      if (await firstNav.isVisible()) {
        const href = await firstNav.getAttribute('href');
        if (href && href !== '#' && !href.startsWith('http')) {
          await firstNav.click();
          await page.waitForTimeout(1000);
        }
      }
    }
    
    // Verify page is still functional
    await expect(page).toHaveTitle(/Galactic Genesis/);
  });
  
});
