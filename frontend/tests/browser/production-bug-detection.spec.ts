import { test, expect } from '@playwright/test';

test.describe('Production Bug Detection', () => {
  test('should detect the exact production error and verify fix', async ({ page }) => {
    const consoleErrors: string[] = [];
    const pageErrors: string[] = [];
    const stellarApiLogs: string[] = [];
    
    // Capture all console messages
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error') {
        consoleErrors.push(text);
        console.log('🚨 Console Error:', text);
      }
      if (text.includes('StellarAPI') || text.includes('stellar') || text.includes('Failed to load stellar data')) {
        stellarApiLogs.push(`[${msg.type()}] ${text}`);
      }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      pageErrors.push(error.message);
      console.log('💥 Page Error:', error.message);
    });
    
    console.log('🔍 Testing for the exact production bug...');
    
    await page.goto('/');
    await page.waitForTimeout(5000); // Wait longer for all components to load
    
    // Test the exact scenario that's failing in production
    const productionBugTest = await page.evaluate(async () => {
      try {
        // Simulate the exact production environment
        const originalHostname = window.location.hostname;
        
        // Test 1: Import the stellarApi module
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        if (!stellarApi) {
          return { success: false, error: 'stellarApi not found in module exports' };
        }
        
        // Test 2: Force production mode by creating new instance with null baseUrl
        const StellarApiClient = module.default || (module as any).StellarApiClient;
        let prodApi;
        
        if (StellarApiClient) {
          prodApi = new StellarApiClient(null); // Force production mode
        } else {
          // Fallback: try to modify existing instance
          prodApi = stellarApi;
          (prodApi as any).baseUrl = null; // Force production mode
        }
        
        // Test 3: Call getStars() which should trigger the production fallback
        console.log('🧪 Testing getStars() in production mode...');
        const result = await prodApi.getStars();
        
        // Test 4: Check the result structure
        const hasStars = result && typeof result === 'object' && Array.isArray(result.stars);
        const starsLength = hasStars ? result.stars.length : 0;
        const hasTotal = result && typeof result.total === 'number';
        
        // Test 5: Try to access .length property (this is where the error occurs)
        let lengthAccessError = null;
        try {
          const testLength = result.stars.length; // This should work if fixed
          console.log('✅ Successfully accessed .length property:', testLength);
        } catch (error) {
          lengthAccessError = error instanceof Error ? error.message : String(error);
          console.log('❌ Error accessing .length property:', lengthAccessError);
        }
        
        return {
          success: true,
          hasStars,
          starsLength,
          hasTotal,
          totalValue: result.total,
          lengthAccessError,
          resultStructure: {
            type: typeof result,
            keys: result ? Object.keys(result) : [],
            starsType: result ? typeof result.stars : 'undefined',
            starsIsArray: result ? Array.isArray(result.stars) : false
          },
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hasStars: false,
          starsLength: 0,
          hasTotal: false,
          totalValue: 0,
          lengthAccessError: null,
          resultStructure: null,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🧪 PRODUCTION BUG TEST RESULT:');
    console.log('==============================');
    console.log(JSON.stringify(productionBugTest, null, 2));
    
    console.log('\n🌟 STELLAR API LOGS:');
    console.log('====================');
    stellarApiLogs.forEach(log => console.log(log));
    
    // Check for the specific error patterns from production
    const hasLengthError = [...consoleErrors, ...pageErrors].some(error => 
      error.includes('Cannot read properties of undefined') && 
      error.includes('reading \'length\'')
    );
    
    const hasStellarDataError = [...consoleErrors, ...pageErrors].some(error => 
      error.includes('Failed to load stellar data')
    );
    
    console.log('\n📊 PRODUCTION ERROR ANALYSIS:');
    console.log('=============================');
    console.log(`Console errors: ${consoleErrors.length}`);
    console.log(`Page errors: ${pageErrors.length}`);
    console.log(`Has "length" error: ${hasLengthError}`);
    console.log(`Has stellar data error: ${hasStellarDataError}`);
    console.log(`Length access error in test: ${productionBugTest.lengthAccessError || 'none'}`);
    
    // Print all errors for debugging
    if (consoleErrors.length > 0) {
      console.log('\n🚨 ALL CONSOLE ERRORS:');
      consoleErrors.forEach((error, i) => console.log(`${i + 1}. ${error}`));
    }
    
    if (pageErrors.length > 0) {
      console.log('\n💥 ALL PAGE ERRORS:');
      pageErrors.forEach((error, i) => console.log(`${i + 1}. ${error}`));
    }
    
    console.log('\n📋 PRODUCTION BUG SUMMARY:');
    console.log('===========================');
    
    if (productionBugTest.success) {
      console.log('✅ StellarAPI module loaded successfully');
      console.log(`✅ getStars() returned data: ${productionBugTest.starsLength} stars`);
      
      if (productionBugTest.lengthAccessError) {
        console.log('🚨 CRITICAL: Length access error detected!');
        console.log(`❌ Error: ${productionBugTest.lengthAccessError}`);
      } else {
        console.log('✅ No length access errors detected');
      }
    } else {
      console.log('❌ StellarAPI test failed');
      console.log(`❌ Error: ${productionBugTest.error}`);
    }
    
    // The critical test: verify the fix worked
    expect(productionBugTest.success, `Production test failed: ${productionBugTest.error}`).toBe(true);
    expect(productionBugTest.hasStars, 'Should have stars array').toBe(true);
    expect(productionBugTest.starsLength, 'Should have at least 8 fallback stars').toBeGreaterThanOrEqual(8);
    expect(productionBugTest.lengthAccessError, 'Should not have length access error').toBeNull();
    
    // Also verify no runtime errors in the page
    expect(hasLengthError, 'Should not have "Cannot read properties of undefined (reading \'length\')" error').toBe(false);
  });

  test('should test Sol system planets specifically', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Test Sol system planet data
    const solPlanetTest = await page.evaluate(async () => {
      try {
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        // Test getStarDetail for Sol (star_id: 1)
        const solDetail = await stellarApi.getStarDetail(1);
        
        return {
          success: true,
          starName: solDetail.name,
          hasPlanets: Array.isArray(solDetail.planets),
          planetCount: solDetail.planets ? solDetail.planets.length : 0,
          planetNames: solDetail.planets ? solDetail.planets.map(p => p.name) : [],
          error: null
        };
      } catch (error) {
        return {
          success: false,
          starName: null,
          hasPlanets: false,
          planetCount: 0,
          planetNames: [],
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🌍 SOL SYSTEM PLANET TEST:');
    console.log('==========================');
    console.log(JSON.stringify(solPlanetTest, null, 2));
    
    if (solPlanetTest.success) {
      console.log(`✅ Sol system loaded: ${solPlanetTest.starName}`);
      console.log(`✅ Planet count: ${solPlanetTest.planetCount}`);
      console.log(`✅ Planets: ${solPlanetTest.planetNames.join(', ')}`);
    } else {
      console.log(`❌ Sol system test failed: ${solPlanetTest.error}`);
    }
    
    // Verify Sol has planets
    expect(solPlanetTest.success, `Sol system test failed: ${solPlanetTest.error}`).toBe(true);
    expect(solPlanetTest.starName, 'Should be Sol').toBe('Sol');
    expect(solPlanetTest.hasPlanets, 'Sol should have planets').toBe(true);
    expect(solPlanetTest.planetCount, 'Sol should have at least 3 planets').toBeGreaterThanOrEqual(3);
  });

  test('should verify the app renders without hanging', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    
    // Wait for the app to load
    await page.waitForTimeout(5000);
    
    const loadTime = Date.now() - startTime;
    
    // Check if the app is actually rendering
    const renderingTest = await page.evaluate(() => {
      const body = document.body;
      const hasContent = body.textContent && body.textContent.trim().length > 100;
      const hasCanvas = document.querySelector('canvas') !== null;
      const hasTitle = document.title && document.title.includes('Galactic Genesis');
      const elementCount = document.querySelectorAll('*').length;
      
      return {
        hasContent,
        hasCanvas,
        hasTitle,
        elementCount,
        bodyText: body.textContent ? body.textContent.substring(0, 200) : '',
        isHanging: elementCount < 20 && !hasContent
      };
    });
    
    console.log('\n📱 APP RENDERING TEST:');
    console.log('======================');
    console.log(`Load time: ${loadTime}ms`);
    console.log(JSON.stringify(renderingTest, null, 2));
    
    // Verify the app is not hanging
    expect(renderingTest.isHanging, 'App should not be hanging with blank page').toBe(false);
    expect(renderingTest.hasTitle, 'Should have correct page title').toBe(true);
    expect(renderingTest.elementCount, 'Should have reasonable number of DOM elements').toBeGreaterThan(50);
    expect(loadTime, 'Load time should be reasonable').toBeLessThan(15000);
    
    if (renderingTest.hasCanvas) {
      console.log('✅ 3D canvas is present - galaxy should be rendering');
    } else {
      console.log('⚠️ No canvas found - 3D galaxy may not be rendering');
    }
  });
});
