import { test, expect } from '@playwright/test';

test.describe('Moon Rendering Verification', () => {
  test('should verify moon data types and prevent toFixed errors', async ({ page }) => {
    // Test the API directly to ensure all numeric fields are properly typed
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    
    // Test all planets with moons
    const planetsWithMoons = data.planets.filter((p: any) => p.moons && p.moons.length > 0);
    expect(planetsWithMoons.length).toBeGreaterThan(0);
    
    console.log(`Found ${planetsWithMoons.length} planets with moons`);
    
    for (const planet of planetsWithMoons) {
      console.log(`Testing ${planet.name} with ${planet.moons.length} moons`);
      
      for (const moon of planet.moons) {
        // Critical: These fields must be numbers to prevent .toFixed() errors
        expect(typeof moon.orbital_period_days).toBe('number');
        expect(typeof moon.distance_km).toBe('number');
        
        // Verify the values are reasonable
        expect(moon.orbital_period_days).toBeGreaterThan(0);
        expect(moon.distance_km).toBeGreaterThan(0);
        
        // Test that .toFixed() would work (this is what was failing before)
        expect(() => moon.orbital_period_days.toFixed(1)).not.toThrow();
        expect(() => (moon.distance_km / 1000).toFixed(0)).not.toThrow();
        
        // Verify other numeric fields if present
        if (moon.diameter_km !== null) {
          expect(typeof moon.diameter_km).toBe('number');
          expect(() => moon.diameter_km.toFixed(1)).not.toThrow();
        }
        
        if (moon.albedo !== null) {
          expect(typeof moon.albedo).toBe('number');
          expect(moon.albedo).toBeGreaterThanOrEqual(0);
          expect(moon.albedo).toBeLessThanOrEqual(1);
        }
        
        // Verify visual properties
        if (moon.color_hex) {
          expect(moon.color_hex).toMatch(/^#[0-9A-Fa-f]{6}$/);
        }
        
        console.log(`  ✓ ${moon.name}: period=${moon.orbital_period_days} days, distance=${moon.distance_km} km`);
      }
    }
  });

  test('should verify Earth Luna moon specifically', async ({ page }) => {
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    expect(earth).toBeDefined();
    expect(earth.moons).toBeDefined();
    expect(earth.moons.length).toBe(1);
    
    const luna = earth.moons[0];
    expect(luna.name).toBe('Luna');
    
    // These are the exact fields that were causing the .toFixed() error
    expect(typeof luna.orbital_period_days).toBe('number');
    expect(luna.orbital_period_days).toBeCloseTo(27.3, 1);
    
    expect(typeof luna.distance_km).toBe('number');
    expect(luna.distance_km).toBe(384400);
    
    // Verify the operations that were failing now work
    const periodText = `Period: ${luna.orbital_period_days.toFixed(1)} days`;
    expect(periodText).toBe('Period: 27.3 days');
    
    const distanceText = `Distance: ${(luna.distance_km / 1000).toFixed(0)}k km`;
    expect(distanceText).toBe('Distance: 384k km');
    
    console.log('✓ Luna moon data verified - no more .toFixed() errors!');
  });

  test('should verify Jupiter moons for complex orbital mechanics', async ({ page }) => {
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    const jupiter = data.planets.find((p: any) => p.name === 'Jupiter');
    expect(jupiter).toBeDefined();
    expect(jupiter.moons).toBeDefined();
    expect(jupiter.moons.length).toBe(4); // Io, Europa, Ganymede, Callisto
    
    const expectedMoons = ['Io', 'Europa', 'Ganymede', 'Callisto'];
    
    for (let i = 0; i < jupiter.moons.length; i++) {
      const moon = jupiter.moons[i];
      expect(expectedMoons).toContain(moon.name);
      
      // Verify orbital mechanics data
      expect(typeof moon.orbital_period_days).toBe('number');
      expect(typeof moon.distance_km).toBe('number');
      
      // Verify realistic values for Jupiter's moons
      expect(moon.orbital_period_days).toBeGreaterThan(1);
      expect(moon.orbital_period_days).toBeLessThan(20);
      expect(moon.distance_km).toBeGreaterThan(400000);
      expect(moon.distance_km).toBeLessThan(2000000);
      
      console.log(`  ✓ ${moon.name}: ${moon.orbital_period_days.toFixed(2)} days, ${(moon.distance_km / 1000).toFixed(0)}k km`);
    }
  });

  test('should verify enhanced planet visual properties', async ({ page }) => {
    const response = await page.request.get('http://localhost:19081/v1/stellar/stars/1');
    const data = await response.json();
    
    // Test Earth specifically for enhanced visuals
    const earth = data.planets.find((p: any) => p.name === 'Earth');
    expect(earth.dominant_color).toBe('Blue-Green');
    expect(earth.has_atmosphere).toBe(true);
    expect(earth.composition).toBe('rocky');
    
    // Test gas giants for enhanced effects
    const gasGiants = data.planets.filter((p: any) => 
      p.composition === 'gas_giant' || p.composition === 'ice_giant'
    );
    
    for (const planet of gasGiants) {
      expect(planet.has_atmosphere).toBe(true);
      expect(planet.dominant_color).toBeDefined();
      console.log(`✓ ${planet.name} (${planet.composition}): ${planet.dominant_color}`);
    }
  });

  test('should verify no JavaScript errors in planet rendering', async ({ page }) => {
    // Set up error tracking
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Navigate to the application
    await page.goto('http://localhost:5174');
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // Check that no errors occurred during initial load
    const relevantErrors = errors.filter(error => 
      error.includes('toFixed') || 
      error.includes('moon') || 
      error.includes('orbital_period_days')
    );
    
    expect(relevantErrors.length).toBe(0);
    
    if (errors.length > 0) {
      console.log('Non-critical errors found:', errors);
    }
    
    console.log('✓ No moon-related JavaScript errors detected');
  });
});
