import { test, expect } from '@playwright/test';

test.describe('Solar System Navigation and Controls', () => {
  test('should navigate to Sol system and test rotation controls', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    console.log('\n🌌 SOLAR SYSTEM NAVIGATION TEST:');
    console.log('================================');
    
    // Look for Sol system button or navigation
    const navigationResult = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const links = Array.from(document.querySelectorAll('a'));
      const allClickable = [...buttons, ...links];
      
      // Look for Sol, Solar, or Sun related navigation
      const solarNavigation = allClickable.find(el => {
        const text = el.textContent?.toLowerCase() || '';
        return text.includes('sol') || text.includes('solar') || text.includes('sun');
      });
      
      return {
        hasSolarNavigation: !!solarNavigation,
        solarNavigationText: solarNavigation?.textContent?.trim() || null,
        allNavigationOptions: allClickable.map(el => el.textContent?.trim()).filter(text => text && text.length > 0)
      };
    });
    
    console.log(`🔍 Solar navigation found: ${navigationResult.hasSolarNavigation ? '✅' : '❌'}`);
    if (navigationResult.solarNavigationText) {
      console.log(`   Text: "${navigationResult.solarNavigationText}"`);
    }
    
    console.log('\n📋 All navigation options:');
    navigationResult.allNavigationOptions.forEach((text, index) => {
      console.log(`   ${index + 1}. "${text}"`);
    });
    
    // Try to click on solar system navigation if available
    if (navigationResult.hasSolarNavigation) {
      console.log('\n🖱️ Clicking on solar system navigation...');
      
      await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const links = Array.from(document.querySelectorAll('a'));
        const allClickable = [...buttons, ...links];
        
        const solarNavigation = allClickable.find(el => {
          const text = el.textContent?.toLowerCase() || '';
          return text.includes('sol') || text.includes('solar') || text.includes('sun');
        }) as HTMLElement;
        
        if (solarNavigation) {
          solarNavigation.click();
        }
      });
      
      // Wait for navigation to complete
      await page.waitForTimeout(3000);
      
      // Check if we're now in solar system view
      const solarSystemViewResult = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        
        const planetSpinButton = buttons.find(btn => 
          btn.textContent?.includes('🪐') && 
          (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
        );
        
        const starSpinButton = buttons.find(btn => 
          btn.textContent?.includes('⭐') && 
          (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
        );
        
        const timeControls = buttons.filter(btn => 
          btn.textContent?.includes('▶') || 
          btn.textContent?.includes('⏸') ||
          btn.textContent?.match(/\d+x/)
        );
        
        // Check for solar system specific content
        const hasPlanetContent = document.body.textContent?.includes('Mercury') ||
                                document.body.textContent?.includes('Venus') ||
                                document.body.textContent?.includes('Earth') ||
                                document.body.textContent?.includes('Mars');
        
        return {
          hasPlanetSpinButton: !!planetSpinButton,
          hasStarSpinButton: !!starSpinButton,
          hasTimeControls: timeControls.length > 0,
          hasPlanetContent,
          planetSpinText: planetSpinButton?.textContent || null,
          starSpinText: starSpinButton?.textContent || null,
          timeControlCount: timeControls.length,
          currentUrl: window.location.href,
          allButtons: buttons.map(btn => btn.textContent?.trim()).filter(text => text && text.length > 0)
        };
      });
      
      console.log('\n🌟 SOLAR SYSTEM VIEW RESULT:');
      console.log('============================');
      console.log(`🌍 Current URL: ${solarSystemViewResult.currentUrl}`);
      console.log(`🪐 Planet content detected: ${solarSystemViewResult.hasPlanetContent ? '✅' : '❌'}`);
      console.log(`🪐 Planet spin button: ${solarSystemViewResult.hasPlanetSpinButton ? '✅' : '❌'}`);
      if (solarSystemViewResult.planetSpinText) {
        console.log(`   Text: "${solarSystemViewResult.planetSpinText}"`);
      }
      
      console.log(`⭐ Star spin button: ${solarSystemViewResult.hasStarSpinButton ? '✅' : '❌'}`);
      if (solarSystemViewResult.starSpinText) {
        console.log(`   Text: "${solarSystemViewResult.starSpinText}"`);
      }
      
      console.log(`⏰ Time controls: ${solarSystemViewResult.hasTimeControls ? '✅' : '❌'} (${solarSystemViewResult.timeControlCount} buttons)`);
      
      console.log('\n📋 All buttons in solar system view:');
      solarSystemViewResult.allButtons.forEach((text, index) => {
        console.log(`   ${index + 1}. "${text}"`);
      });
      
      // Test rotation control interaction if available
      if (solarSystemViewResult.hasPlanetSpinButton) {
        console.log('\n🧪 Testing planet spin control...');
        
        const interactionResult = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const planetSpinButton = buttons.find(btn => 
            btn.textContent?.includes('🪐') && 
            (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
          ) as HTMLButtonElement;
          
          if (planetSpinButton) {
            const originalText = planetSpinButton.textContent;
            planetSpinButton.click();
            
            return new Promise(resolve => {
              setTimeout(() => {
                const newText = planetSpinButton.textContent;
                resolve({
                  success: true,
                  originalText,
                  newText,
                  textChanged: originalText !== newText
                });
              }, 500);
            });
          }
          
          return { success: false, error: 'Planet spin button not found' };
        });
        
        console.log('🔄 Planet spin interaction:', interactionResult);
      }
      
      if (solarSystemViewResult.hasStarSpinButton) {
        console.log('\n🧪 Testing star spin control...');
        
        const starInteractionResult = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const starSpinButton = buttons.find(btn => 
            btn.textContent?.includes('⭐') && 
            (btn.textContent?.includes('Stop') || btn.textContent?.includes('Start'))
          ) as HTMLButtonElement;
          
          if (starSpinButton) {
            const originalText = starSpinButton.textContent;
            starSpinButton.click();
            
            return new Promise(resolve => {
              setTimeout(() => {
                const newText = starSpinButton.textContent;
                resolve({
                  success: true,
                  originalText,
                  newText,
                  textChanged: originalText !== newText
                });
              }, 500);
            });
          }
          
          return { success: false, error: 'Star spin button not found' };
        });
        
        console.log('⭐ Star spin interaction:', starInteractionResult);
      }
      
      // Verify we have the expected functionality
      expect(solarSystemViewResult.hasPlanetContent || solarSystemViewResult.hasPlanetSpinButton, 'Should be in solar system view with planet content or controls').toBe(true);
      
    } else {
      console.log('\n⚠️ No solar system navigation found - checking if we need to navigate differently');
      
      // Maybe we need to click on a star first
      const starClickResult = await page.evaluate(() => {
        // Look for clickable stars or star-related elements
        const canvas = document.querySelector('canvas');
        if (canvas) {
          // Try clicking in the center of the canvas (where Sol might be)
          const rect = canvas.getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          
          const clickEvent = new MouseEvent('click', {
            clientX: centerX,
            clientY: centerY,
            bubbles: true
          });
          
          canvas.dispatchEvent(clickEvent);
          return { clicked: true, x: centerX, y: centerY };
        }
        
        return { clicked: false };
      });
      
      console.log('🖱️ Canvas click attempt:', starClickResult);
      
      if (starClickResult.clicked) {
        await page.waitForTimeout(2000);
        
        // Check if anything changed after clicking
        const afterClickResult = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          return {
            buttonCount: buttons.length,
            buttonTexts: buttons.map(btn => btn.textContent?.trim()).filter(text => text && text.length > 0),
            hasNewContent: document.body.textContent?.includes('Mercury') || 
                          document.body.textContent?.includes('Venus') ||
                          document.body.textContent?.includes('planets')
          };
        });
        
        console.log('📊 After canvas click:', afterClickResult);
      }
    }
    
    // The test passes if we can navigate and find the expected structure
    expect(navigationResult.allNavigationOptions.length, 'Should have navigation options available').toBeGreaterThan(0);
  });

  test('should verify Sol system has all 8 planets in UI', async ({ page }) => {
    await page.goto('/');
    await page.waitForTimeout(3000);
    
    // Try to get to solar system view and verify planet display
    const planetDisplayTest = await page.evaluate(async () => {
      try {
        // Import the stellar API to get Sol data
        const module = await import('/src/services/stellarApi.ts');
        const stellarApi = module.stellarApi;
        
        const solDetail = await stellarApi.getStarDetail(1);
        
        // Check if planet names appear in the DOM
        const planetVisibility = solDetail.planets?.map(planet => {
          const planetNameInDOM = document.body.textContent?.includes(planet.name) || false;
          return {
            name: planet.name,
            visibleInDOM: planetNameInDOM,
            mass_earth: planet.mass_earth,
            composition: planet.composition
          };
        }) || [];
        
        return {
          success: true,
          solName: solDetail.name,
          planetCount: solDetail.planets?.length || 0,
          planetVisibility,
          anyPlanetsVisible: planetVisibility.some(p => p.visibleInDOM),
          allPlanetsVisible: planetVisibility.every(p => p.visibleInDOM)
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
    
    console.log('\n🪐 PLANET DISPLAY TEST:');
    console.log('=======================');
    
    if (planetDisplayTest.success) {
      console.log(`🌟 Star: ${planetDisplayTest.solName}`);
      console.log(`📊 Planet count: ${planetDisplayTest.planetCount}`);
      console.log(`👁️ Any planets visible: ${planetDisplayTest.anyPlanetsVisible ? '✅' : '❌'}`);
      console.log(`👁️ All planets visible: ${planetDisplayTest.allPlanetsVisible ? '✅' : '❌'}`);
      
      console.log('\n🪐 Planet visibility details:');
      planetDisplayTest.planetVisibility?.forEach((planet, index) => {
        const status = planet.visibleInDOM ? '✅' : '❌';
        console.log(`   ${status} ${planet.name} (${planet.mass_earth} Earth masses, ${planet.composition})`);
      });
      
    } else {
      console.log(`❌ Planet display test failed: ${planetDisplayTest.error}`);
    }
    
    expect(planetDisplayTest.success, `Planet display test failed: ${planetDisplayTest.error}`).toBe(true);
    expect(planetDisplayTest.planetCount, 'Sol should have 8 planets').toBe(8);
  });
});
