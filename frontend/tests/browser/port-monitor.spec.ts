import { test, expect } from '@playwright/test';

/**
 * Port Monitor Component Browser Tests
 * Real DOM interaction and API testing for the PortMonitor functionality
 */

test.describe('Port Monitor Component', () => {
  
  test('should render Port Monitor component without errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for the Port Monitor component
    const portMonitor = page.locator('[data-testid="port-monitor"], .port-monitor, h3:has-text("Port Monitor")').first();
    
    // Wait for the component to appear
    await expect(portMonitor).toBeVisible({ timeout: 10000 });
    
    // Verify no errors occurred during rendering
    expect(errors).toEqual([]);
  });
  
  test('should display port information', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for port data to load
    await page.waitForTimeout(3000);
    
    // Look for port information display
    const portInfo = page.locator('text=/Port|port|5174|8081|19081/').first();
    await expect(portInfo).toBeVisible({ timeout: 15000 });
    
    // Verify no errors occurred
    expect(errors).toEqual([]);
  });
  
  test('should handle port monitoring API calls', async ({ page }) => {
    const errors: string[] = [];
    const networkRequests: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('request', request => {
      const url = request.url();
      if (url.includes('health') || url.includes('port') || url.includes('8086')) {
        networkRequests.push(`${request.method()} ${url}`);
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for API calls to be made
    await page.waitForTimeout(5000);
    
    console.log('Network requests made:', networkRequests);
    
    // Verify some health/port related API calls were made
    expect(networkRequests.length, 'No health/port API calls detected').toBeGreaterThan(0);
    
    // Verify no errors occurred
    expect(errors).toEqual([]);
  });
  
  test('should update port status dynamically', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for initial load
    await page.waitForTimeout(2000);
    
    // Look for any status indicators or port listings
    const statusElements = page.locator('text=/status|Status|LISTEN|listening|running|active/i').first();
    
    // Wait for status information to appear
    await expect(statusElements).toBeVisible({ timeout: 15000 });
    
    // Wait for potential updates
    await page.waitForTimeout(3000);
    
    // Verify no errors occurred during updates
    expect(errors).toEqual([]);
  });
  
  test('should handle refresh functionality', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for refresh button or similar control
    const refreshButton = page.locator('button:has-text("Refresh"), button:has-text("Update"), [data-testid="refresh"]').first();
    
    // If refresh button exists, click it
    if (await refreshButton.isVisible({ timeout: 5000 })) {
      await refreshButton.click();
      await page.waitForTimeout(2000);
    }
    
    // Verify no errors occurred
    expect(errors).toEqual([]);
  });
  
  test('should display expected ports (5174, 8081, 19081, etc.)', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for port data to load
    await page.waitForTimeout(5000);
    
    // Check for expected Galactic Genesis ports
    const expectedPorts = ['5174', '8081', '19081', '8086'];
    const foundPorts: string[] = [];
    
    for (const port of expectedPorts) {
      const portElement = page.locator(`text=${port}`).first();
      if (await portElement.isVisible({ timeout: 2000 })) {
        foundPorts.push(port);
      }
    }
    
    console.log('Found ports:', foundPorts);
    
    // Verify at least some expected ports are displayed
    expect(foundPorts.length, `Expected to find some of these ports: ${expectedPorts.join(', ')}, but found: ${foundPorts.join(', ')}`).toBeGreaterThan(0);
    
    // Verify no errors occurred
    expect(errors).toEqual([]);
  });
  
});
