<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: rgba(0, 255, 0, 0.2); border: 1px solid #00ff00; }
        .error { background: rgba(255, 0, 0, 0.2); border: 1px solid #ff0000; }
        .loading { background: rgba(255, 255, 0, 0.2); border: 1px solid #ffff00; }
        button {
            background: #00ffff;
            color: black;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #00cccc; }
        pre {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 Galactic Genesis - API Connection Test</h1>
        
        <div class="test-section">
            <h2>1. API Base URL Detection</h2>
            <div id="url-status" class="status loading">Testing...</div>
            <pre id="url-details"></pre>
        </div>
        
        <div class="test-section">
            <h2>2. Stars List API</h2>
            <button onclick="testStarsList()">Test Stars List</button>
            <div id="stars-status" class="status">Click button to test</div>
            <pre id="stars-details"></pre>
        </div>
        
        <div class="test-section">
            <h2>3. Star Detail API</h2>
            <button onclick="testStarDetail()">Test Star Detail (Alpha Centauri B)</button>
            <div id="detail-status" class="status">Click button to test</div>
            <pre id="detail-details"></pre>
        </div>
        
        <div class="test-section">
            <h2>4. WebGL Context Test</h2>
            <div id="webgl-status" class="status loading">Testing...</div>
            <canvas id="webgl-test" width="100" height="100" style="border: 1px solid #333;"></canvas>
        </div>
    </div>

    <script>
        // Test URL detection
        function testUrlDetection() {
            const hostname = window.location.hostname;
            const apiUrl = `http://${hostname}:19081/v1`;
            
            document.getElementById('url-details').textContent = 
                `Hostname: ${hostname}\nAPI URL: ${apiUrl}`;
            
            document.getElementById('url-status').className = 'status success';
            document.getElementById('url-status').textContent = '✅ URL detection working';
        }

        // Test stars list
        async function testStarsList() {
            const statusEl = document.getElementById('stars-status');
            const detailsEl = document.getElementById('stars-details');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '⏳ Testing stars list API...';
            
            try {
                const hostname = window.location.hostname;
                const url = `http://${hostname}:19081/v1/stellar/stars?limit=5`;
                
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                statusEl.className = 'status success';
                statusEl.textContent = `✅ Stars list loaded: ${data.stars.length} stars (${endTime - startTime}ms)`;
                
                detailsEl.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ Stars list failed: ${error.message}`;
                detailsEl.textContent = error.stack || error.toString();
            }
        }

        // Test star detail
        async function testStarDetail() {
            const statusEl = document.getElementById('detail-status');
            const detailsEl = document.getElementById('detail-details');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '⏳ Testing star detail API...';
            
            try {
                const hostname = window.location.hostname;
                const url = `http://${hostname}:19081/v1/stellar/stars/3`;
                
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                statusEl.className = 'status success';
                statusEl.textContent = `✅ Star detail loaded: ${data.name} with ${data.planets.length} planets (${endTime - startTime}ms)`;
                
                detailsEl.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ Star detail failed: ${error.message}`;
                detailsEl.textContent = error.stack || error.toString();
            }
        }

        // Test WebGL
        function testWebGL() {
            const statusEl = document.getElementById('webgl-status');
            const canvas = document.getElementById('webgl-test');
            
            try {
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    // Test basic WebGL functionality
                    gl.clearColor(0.0, 0.5, 0.0, 1.0);
                    gl.clear(gl.COLOR_BUFFER_BIT);
                    
                    const vendor = gl.getParameter(gl.VENDOR);
                    const renderer = gl.getParameter(gl.RENDERER);
                    
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ WebGL working - ${vendor} ${renderer}`;
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '❌ WebGL not supported';
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ WebGL error: ${error.message}`;
            }
        }

        // Run tests on load
        window.onload = function() {
            testUrlDetection();
            testWebGL();
        };
    </script>
</body>
</html>
