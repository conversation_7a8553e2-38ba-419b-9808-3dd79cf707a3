<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galactic Genesis - Stunning Graphics Demo</title>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
            background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #7877c6, #ff6b9d, #4ecdc4, #ffd60a);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 8s ease-in-out infinite;
            margin-bottom: 10px;
        }
        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .subtitle {
            font-size: 1.2rem;
            color: #a0a0a0;
            margin-bottom: 30px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 25px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(120, 119, 198, 0.3);
            border-color: rgba(120, 119, 198, 0.5);
        }
        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        .demo-card:hover::before {
            left: 100%;
        }
        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffffff;
        }
        .card-description {
            color: #b0b0b0;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .demo-button {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(45deg, #7877c6, #ff6b9d);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .demo-button:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(120, 119, 198, 0.4);
        }
        .demo-button.secondary {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
        }
        .demo-button.tertiary {
            background: linear-gradient(45deg, #ffd60a, #ff8500);
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 12px;
            width: 30px;
        }
        .feature-text {
            color: #e0e0e0;
        }
        .tech-specs {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 25px;
            margin-top: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .spec-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .spec-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .spec-label {
            color: #a0a0a0;
        }
        .spec-value {
            color: #4ecdc4;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌌 Galactic Genesis</h1>
            <p class="subtitle">Stunning Graphics Overhaul Demo</p>
            <p style="color: #888; font-size: 0.9rem;">Experience the dramatic transformation from basic 3D to cinematic space visualization</p>
        </div>

        <div class="demo-grid">
            <div class="demo-card">
                <span class="card-icon">✨</span>
                <h3 class="card-title">Stunning Mode</h3>
                <p class="card-description">
                    Experience the full power of our graphics overhaul with custom shaders, 
                    volumetric nebulae, galactic core, and advanced post-processing effects.
                </p>
                <a href="/?stunning=true" class="demo-button">Launch Stunning Galaxy</a>
            </div>

            <div class="demo-card">
                <span class="card-icon">⚖️</span>
                <h3 class="card-title">Graphics Comparison</h3>
                <p class="card-description">
                    Side-by-side comparison tool to toggle between basic and stunning graphics modes. 
                    See the dramatic difference in real-time.
                </p>
                <a href="/?compare=true" class="demo-button secondary">Compare Graphics</a>
            </div>

            <div class="demo-card">
                <span class="card-icon">📱</span>
                <h3 class="card-title">Basic Mode</h3>
                <p class="card-description">
                    The original galaxy map with standard 3D rendering. 
                    Functional and efficient for lower-end hardware.
                </p>
                <a href="/" class="demo-button tertiary">View Basic Mode</a>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-item">
                <span class="feature-icon">🎨</span>
                <span class="feature-text">Custom Star Corona Shaders</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🌌</span>
                <span class="feature-text">Volumetric Nebulae (4 regions)</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">⚫</span>
                <span class="feature-text">Galactic Core with Black Hole</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✨</span>
                <span class="feature-text">Advanced Post-Processing</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🌟</span>
                <span class="feature-text">8,000+ Background Stars</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">💡</span>
                <span class="feature-text">5-Point Dynamic Lighting</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🪐</span>
                <span class="feature-text">Planetary System Visualization</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🎮</span>
                <span class="feature-text">Interactive Hover Effects</span>
            </div>
        </div>

        <div class="tech-specs">
            <h3 style="color: #ffffff; margin-bottom: 20px;">🔧 Technical Specifications</h3>
            <div class="spec-grid">
                <div class="spec-item">
                    <span class="spec-label">Render Engine:</span>
                    <span class="spec-value">Three.js + React Fiber</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Shader Language:</span>
                    <span class="spec-value">GLSL ES 3.0</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Post-Processing:</span>
                    <span class="spec-value">5 Effect Pipeline</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Particle Count:</span>
                    <span class="spec-value">8,500+ Objects</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Stellar Data:</span>
                    <span class="spec-value">29 Real Stars</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Planetary Data:</span>
                    <span class="spec-value">14 Confirmed Exoplanets</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Performance:</span>
                    <span class="spec-value">Adaptive Quality</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">Memory Usage:</span>
                    <span class="spec-value">Optimized</span>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; color: #888;">
            <p>🚀 Built with React, Three.js, and cutting-edge WebGL technology</p>
            <p>⚡ Optimized for modern browsers with WebGL 2.0 support</p>
        </div>
    </div>
</body>
</html>
