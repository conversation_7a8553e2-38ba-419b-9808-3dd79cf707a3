# Galactic Genesis - Frontend

A React TypeScript frontend for the Galactic Genesis 4X strategy game.

## 🚀 Features

- **🗺️ Interactive Galaxy Map**: 2D star map with clickable systems and fleets
- **🚀 Fleet Management**: View, select, and command your fleets
- **📋 Order System**: Submit move, attack, and resupply orders
- **⚡ Real-time Updates**: Live WebSocket updates for fleet movements and battles
- **🎨 Space Theme**: Dark UI with neon accents and starfield background
- **📱 Responsive Design**: Works on desktop and tablet
- **📚 Interactive Tutorial**: Comprehensive game guide with step-by-step instructions
- **🎮 Quick Reference**: Always-accessible help system

## 🛠️ Tech Stack

- **React 18** + TypeScript
- **Vite** (fast build tool)
- **Tailwind CSS** (styling)
- **Zustand** (state management)
- **Socket.io** (WebSocket client)
- **Canvas API** (galaxy map rendering)

## 🏁 Getting Started

### Prerequisites

- Node.js 18+
- Backend services running on `http://localhost:19080`

### Installation

```bash
npm install
```

### Development

```bash
npm run dev
```

The app will be available at `http://localhost:5173`

### Environment Variables

Create a `.env` file:

```env
VITE_API_URL=http://localhost:19080
VITE_WS_URL=ws://localhost:19080
```

## 🎮 Game Interface

### Main Layout

- **Top Bar**: Game HUD with turn counter, fleet stats, and connection status
- **Left Sidebar**: Fleet list and management controls
- **Center**: Interactive galaxy map
- **Right Sidebar**: System details and information

### Controls

- **Click systems** to select and view details
- **Click fleets** to select and issue orders
- **Use fleet action buttons** to submit orders
- **Real-time updates** show live game state changes

### Fleet Actions

1. **Move**: Send fleet to adjacent system
2. **Attack**: Attack enemy fleets in same system
3. **Resupply**: Restore fleet supply

## 📚 Tutorial System

### Features

- **Auto-launch**: Shows automatically for new players
- **Step-by-step guide**: 9 comprehensive tutorial steps
- **Interactive navigation**: Jump to any step, skip, or complete
- **Quick reference**: Always-accessible help from the header
- **Version tracking**: Tutorial updates with new game features

### Tutorial Content

1. **Welcome & Objectives**: Game introduction and goals
2. **Interface Overview**: UI layout and components
3. **Fleet Management**: Understanding fleet attributes
4. **Galaxy Map**: Navigation and interaction
5. **Order System**: How to command fleets
6. **Combat Mechanics**: Battle resolution and tactics
7. **Turn-based Gameplay**: Understanding game flow
8. **Strategic Tips**: Advanced gameplay advice
9. **Quick Reference**: Handy reference guide

### Development Tools

Access these functions in the browser console:

```javascript
// Reset tutorial for testing
resetTutorial()

// Show tutorial manually
showTutorial()
```

## 🔧 Development

### Project Structure

```
src/
├── components/          # React components
│   ├── GalaxyMap.tsx   # Interactive star map
│   ├── FleetList.tsx   # Fleet management
│   ├── OrderForm.tsx   # Order submission
│   ├── Tutorial.tsx    # Game tutorial system
│   └── GameHUD.tsx     # Top bar interface
├── services/           # API and WebSocket clients
├── store/              # Zustand state management
├── types/              # TypeScript definitions
└── App.tsx             # Main application
```

### Building

```bash
npm run build
```

## 🌟 Future Enhancements

- 3D galaxy visualization
- Advanced fleet formations
- Diplomacy interface
- Technology tree
- Battle animations
- Mobile optimization
- Multiplayer support
