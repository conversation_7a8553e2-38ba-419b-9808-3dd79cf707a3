# Page snapshot

```yaml
- generic [ref=e3]:
  - generic [ref=e5]:
    - generic [ref=e6]:
      - heading "GALACTIC GENESIS" [level=1] [ref=e7]
      - generic [ref=e8]:
        - generic [ref=e9]: "Turn:"
        - generic [ref=e10]: "1"
    - generic [ref=e11]:
      - generic [ref=e12]:
        - generic [ref=e14]: "Fleets:"
        - generic [ref=e15]: "0"
      - generic [ref=e16]:
        - generic [ref=e18]: "Supply:"
        - generic [ref=e19]: "0"
      - generic [ref=e20]:
        - generic [ref=e22]: "Active Orders:"
        - generic [ref=e23]: "0"
    - generic [ref=e24]:
      - generic [ref=e27]: Disconnected
      - button "🏥 Health" [ref=e28] [cursor=pointer]
      - button "🔌 Ports" [ref=e29] [cursor=pointer]
      - button "📚 Help" [ref=e30] [cursor=pointer]
      - button "Refresh" [ref=e31] [cursor=pointer]
  - generic [ref=e33]:
    - heading "Empire Resources" [level=3] [ref=e34]
    - generic [ref=e35]:
      - generic [ref=e36]:
        - generic [ref=e37]: ⚡
        - generic [ref=e38]: energy
        - generic [ref=e39]: "1000"
        - generic [ref=e41]: "+50"
      - generic [ref=e42]:
        - generic [ref=e43]: ⛏️
        - generic [ref=e44]: minerals
        - generic [ref=e45]: "800"
        - generic [ref=e47]: "+30"
      - generic [ref=e48]:
        - generic [ref=e49]: 🌾
        - generic [ref=e50]: food
        - generic [ref=e51]: "500"
        - generic [ref=e53]: "+25"
      - generic [ref=e54]:
        - generic [ref=e55]: 🔬
        - generic [ref=e56]: research
        - generic [ref=e57]: "200"
        - generic [ref=e59]: "+15"
  - generic [ref=e60]:
    - generic [ref=e62]:
      - generic [ref=e63]:
        - heading "Fleet Command" [level=3] [ref=e64]
        - generic [ref=e65]: 0 fleets
      - generic [ref=e66]:
        - paragraph [ref=e67]: No fleets available
        - paragraph [ref=e68]: Fleets will appear here once loaded
    - generic [ref=e70]:
      - generic [ref=e71]:
        - generic [ref=e72]:
          - heading "🎮 Graphics Optimization" [level=3] [ref=e73]
          - generic [ref=e74]: ⋮⋮
        - generic [ref=e75]:
          - generic [ref=e76]:
            - generic [ref=e77]: "🌌 Visibility Range: 100 light-years"
            - slider [ref=e78] [cursor=pointer]: "100"
            - generic [ref=e79]:
              - generic [ref=e80]: 10ly
              - generic [ref=e81]: 100ly
          - generic [ref=e82]:
            - generic [ref=e83]: 🎨 Render Quality
            - generic [ref=e84]:
              - button "Low" [ref=e85] [cursor=pointer]
              - button "Medium" [ref=e86] [cursor=pointer]
              - button "High" [ref=e87] [cursor=pointer]
              - button "Ultra" [ref=e88] [cursor=pointer]
          - generic [ref=e90]:
            - checkbox "✨ Post-Processing Effects" [checked] [ref=e91]
            - generic [ref=e92]: ✨ Post-Processing Effects
          - generic [ref=e94]:
            - checkbox "🌌 Distant Galaxies" [checked] [ref=e95]
            - generic [ref=e96]: 🌌 Distant Galaxies
          - generic [ref=e98]:
            - generic [ref=e99]: "Stars: 3000"
            - generic [ref=e100]: "Quality: high"
            - generic [ref=e101]: "Range: 100ly"
          - generic [ref=e102]:
            - button "🎯 Reset Camera" [ref=e103] [cursor=pointer]
            - button "⚡ Performance Mode" [ref=e104] [cursor=pointer]
            - button "🎨 Quality Mode" [ref=e105] [cursor=pointer]
      - generic [ref=e106]:
        - generic [ref=e107]: "🌟 Navigation:"
        - generic [ref=e108]: • Use search box to find stars
        - generic [ref=e109]: • Click star to select
        - generic [ref=e110]: • Click selected star again to zoom into system
      - generic [ref=e112]:
        - generic [ref=e113]:
          - generic [ref=e114]: 🔍 Star Search
          - generic [ref=e115]: ⋮⋮
        - textbox "Search stars..." [ref=e118]
      - generic [ref=e123]:
        - heading "🌌 Galactic Database" [level=3] [ref=e124]
        - generic [ref=e125]:
          - generic [ref=e126]:
            - generic [ref=e127]: "Stars Loaded:"
            - generic [ref=e128]: "3000"
          - generic [ref=e129]:
            - generic [ref=e130]: "Total Planets:"
            - generic [ref=e131]: "11100"
          - generic [ref=e132]:
            - generic [ref=e133]: "Active Fleets:"
            - generic [ref=e134]: "0"
      - generic [ref=e135]: Rendering 3000 star systems
    - generic [ref=e136]:
      - button "🏛️ Colonies" [ref=e137] [cursor=pointer]:
        - generic [ref=e138] [cursor=pointer]: 🏛️
        - generic [ref=e139] [cursor=pointer]: Colonies
      - button "🔬 Tech" [ref=e140] [cursor=pointer]:
        - generic [ref=e141] [cursor=pointer]: 🔬
        - generic [ref=e142] [cursor=pointer]: Tech
      - button "💰 Market" [ref=e143] [cursor=pointer]:
        - generic [ref=e144] [cursor=pointer]: 💰
        - generic [ref=e145] [cursor=pointer]: Market
  - generic [ref=e147]:
    - generic [ref=e148]:
      - generic [ref=e149]:
        - heading "Welcome to Galactic Genesis" [level=2] [ref=e150]
        - paragraph [ref=e151]: Step 1 of 9
      - generic [ref=e152]:
        - button "Skip Tutorial" [ref=e153] [cursor=pointer]
        - button "×" [ref=e154] [cursor=pointer]
    - generic [ref=e159]:
      - paragraph [ref=e160]: Welcome, Commander! You are about to embark on an epic journey to build a galactic empire. Galactic Genesis is a 4X strategy game where you'll explore, expand, exploit, and exterminate across the galaxy.
      - generic [ref=e161]:
        - heading "Game Objectives:" [level=4] [ref=e162]
        - list [ref=e163]:
          - listitem [ref=e164]: • Command fleets across star systems
          - listitem [ref=e165]: • Manage supply lines and logistics
          - listitem [ref=e166]: • Engage in tactical combat
          - listitem [ref=e167]: • Expand your galactic influence
    - generic [ref=e168]:
      - button "Previous" [disabled] [ref=e169]
      - generic [ref=e170]:
        - button [ref=e171] [cursor=pointer]
        - button [ref=e172] [cursor=pointer]
        - button [ref=e173] [cursor=pointer]
        - button [ref=e174] [cursor=pointer]
        - button [ref=e175] [cursor=pointer]
        - button [ref=e176] [cursor=pointer]
        - button [ref=e177] [cursor=pointer]
        - button [ref=e178] [cursor=pointer]
        - button [ref=e179] [cursor=pointer]
      - button "Next" [ref=e180] [cursor=pointer]
```