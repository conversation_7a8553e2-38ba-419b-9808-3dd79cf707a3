#!/bin/bash

echo "🚀 FINAL COMPREHENSIVE GALACTIC GENESIS TEST SUITE"
echo "=================================================="

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing $test_name... "
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo "✅ PASS"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ FAIL"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Test API Gateway Health
run_test "API Gateway Health" "curl -s http://localhost:19081/v1/health | grep -q 'ok'"

# Test Frontend Availability
run_test "Frontend Availability" "curl -s http://localhost:5174 | grep -q 'html'"

# Test Database Connectivity
run_test "Database Connectivity" "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c 'SELECT 1;' | grep -q '1'"

# Test Stellar Data Count
echo -n "Testing Stellar Data Count (should be 122)... "
STAR_COUNT=$(curl -s "http://localhost:19081/v1/stellar/stars?limit=200" | jq '.stars | length')
if [ "$STAR_COUNT" = "122" ]; then
    echo "✅ PASS ($STAR_COUNT stars)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL (got $STAR_COUNT, expected 122)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test Stellar Colors
echo -n "Testing Stellar Colors... "
COLOR_COUNT=$(curl -s "http://localhost:19081/v1/stellar/stars?limit=200" | jq '.stars[] | select(.stellar_color != null) | .stellar_color' | wc -l)
if [ "$COLOR_COUNT" = "122" ]; then
    echo "✅ PASS (all $COLOR_COUNT stars have colors)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL (only $COLOR_COUNT stars have colors)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test 3D Coordinates
echo -n "Testing 3D Coordinates... "
COORD_COUNT=$(curl -s "http://localhost:19081/v1/stellar/stars?limit=200" | jq '.stars[] | select(.x_pc != null and .y_pc != null and .z_pc != null) | .name' | wc -l)
if [ "$COORD_COUNT" -ge "120" ]; then
    echo "✅ PASS ($COORD_COUNT stars have 3D coordinates)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL (only $COORD_COUNT stars have 3D coordinates)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test Spectral Types
echo -n "Testing Spectral Type Diversity... "
SPECTRAL_TYPES=$(curl -s "http://localhost:19081/v1/stellar/stars?limit=200" | jq -r '.stars[] | .spectral_type' | cut -c1 | sort | uniq | wc -l)
if [ "$SPECTRAL_TYPES" -ge "4" ]; then
    echo "✅ PASS ($SPECTRAL_TYPES different spectral classes)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL (only $SPECTRAL_TYPES spectral classes)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test Distance Distribution
echo -n "Testing Distance Distribution... "
NEARBY_STARS=$(curl -s "http://localhost:19081/v1/stellar/stars?max_distance=10" | jq '.stars | length')
DISTANT_STARS=$(curl -s "http://localhost:19081/v1/stellar/stars?max_distance=50" | jq '.stars | length')
if [ "$NEARBY_STARS" -ge "25" ] && [ "$DISTANT_STARS" -ge "120" ]; then
    echo "✅ PASS ($NEARBY_STARS within 10ly, $DISTANT_STARS within 50ly)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL ($NEARBY_STARS within 10ly, $DISTANT_STARS within 50ly)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test Planet Data
echo -n "Testing Planet Data... "
PLANET_COUNT=$(curl -s "http://localhost:19081/v1/stellar/stars/153" | jq '.planets | length')
if [ "$PLANET_COUNT" -ge "1" ]; then
    echo "✅ PASS (Sol has $PLANET_COUNT planets)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL (Sol has $PLANET_COUNT planets)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test API Performance
echo -n "Testing API Performance... "
START_TIME=$(date +%s%N)
curl -s "http://localhost:19081/v1/stellar/stars?limit=100" > /dev/null
END_TIME=$(date +%s%N)
RESPONSE_TIME=$(( (END_TIME - START_TIME) / 1000000 )) # Convert to milliseconds
if [ "$RESPONSE_TIME" -lt "2000" ]; then
    echo "✅ PASS (${RESPONSE_TIME}ms response time)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ FAIL (${RESPONSE_TIME}ms response time, too slow)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test Build Status
run_test "Frontend Build" "[ -f frontend/dist/index.html ]"

# Summary
echo ""
echo "🌌 FINAL TEST RESULTS"
echo "===================="
echo "✅ Passed: $PASSED_TESTS"
echo "❌ Failed: $FAILED_TESTS"
echo "📊 Total:  $TOTAL_TESTS"
echo ""

if [ "$FAILED_TESTS" -eq "0" ]; then
    echo "🎉 ALL TESTS PASSED! Galactic Genesis is ready for production!"
    echo ""
    echo "🌟 STELLAR DATABASE SUMMARY:"
    echo "- Total Stars: 122 (within 50 light years)"
    echo "- All stars have realistic colors based on spectral type"
    echo "- 3D coordinates for accurate galaxy positioning"
    echo "- Diverse spectral types (O, B, A, F, G, K, M, D, L, T)"
    echo "- Scientifically accurate stellar properties from real catalogs"
    echo ""
    echo "🚀 SERVICES RUNNING:"
    echo "- Frontend: http://localhost:5174"
    echo "- API Gateway: http://localhost:19081"
    echo "- Database: PostgreSQL on port 5433"
    echo ""
    exit 0
else
    echo "⚠️  Some tests failed. Please review the issues above."
    exit 1
fi
