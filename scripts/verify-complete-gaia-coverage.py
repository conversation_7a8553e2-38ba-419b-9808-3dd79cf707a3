#!/usr/bin/env python3
"""
Verify Complete GAIA Coverage Script
Verifies that we have imported ALL stars from ALL GAIA databases within 100 light-years
"""

import psycopg2
import psycopg2.extras
import requests
import math
import logging
import time
import json
from typing import Dict, List, Optional, Tuple
from astroquery.gaia import Gaia
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'gg',
    'user': 'gg',
    'password': 'ggpassword'
}

def get_db_connection():
    """Get database connection"""
    return psycopg2.connect(**DB_CONFIG)

def get_gaia_table_list():
    """Get list of all available GAIA tables"""
    logger.info("🔍 Discovering all available GAIA tables...")
    
    try:
        # Get all available tables
        tables = Gaia.load_tables(only_names=True)
        
        gaia_tables = []
        for table in tables:
            if 'gaia' in table.lower():
                gaia_tables.append(table)
        
        logger.info(f"Found {len(gaia_tables)} GAIA tables:")
        for table in gaia_tables:
            logger.info(f"  📋 {table}")
        
        return gaia_tables
        
    except Exception as e:
        logger.error(f"❌ Error getting GAIA tables: {e}")
        # Fallback to known tables
        return [
            'gaiadr3.gaia_source',
            'gaiadr3.gaia_source_lite',
            'gaiaedr3.gaia_source',
            'gaiadr2.gaia_source',
            'gaiadr1.gaia_source'
        ]

def query_gaia_table_for_nearby_stars(table_name: str):
    """Query a specific GAIA table for nearby stars"""
    logger.info(f"🌌 Querying {table_name} for stars within 100 light-years...")
    
    # Base query for stars within 100 light-years (parallax > 10 mas)
    base_query = f"""
    SELECT 
        source_id,
        ra, dec, parallax, parallax_error,
        pmra, pmdec, pmra_error, pmdec_error,
        phot_g_mean_mag, phot_bp_mean_mag, phot_rp_mean_mag,
        bp_rp, ruwe
    FROM {table_name}
    WHERE parallax > 10.0 
    AND parallax_error/parallax < 0.2 
    AND ruwe < 1.4
    AND phot_g_mean_mag IS NOT NULL
    """
    
    # Add additional fields if available in DR3
    if 'dr3' in table_name.lower():
        query = f"""
        SELECT 
            source_id,
            ra, dec, parallax, parallax_error,
            pmra, pmdec, pmra_error, pmdec_error,
            phot_g_mean_mag, phot_bp_mean_mag, phot_rp_mean_mag,
            bp_rp, phot_bp_rp_excess_factor,
            ruwe, astrometric_excess_noise, astrometric_excess_noise_sig,
            astrometric_chi2_al, astrometric_n_good_obs_al,
            teff_gspphot, teff_gspphot_lower, teff_gspphot_upper,
            logg_gspphot, logg_gspphot_lower, logg_gspphot_upper,
            mh_gspphot, mh_gspphot_lower, mh_gspphot_upper,
            distance_gspphot, distance_gspphot_lower, distance_gspphot_upper,
            azero_gspphot, ag_gspphot, ebpminrp_gspphot,
            mass_gspphot, mass_gspphot_lower, mass_gspphot_upper,
            radius_gspphot, radius_gspphot_lower, radius_gspphot_upper,
            lum_gspphot, lum_gspphot_lower, lum_gspphot_upper,
            dr2_radial_velocity, dr2_radial_velocity_error,
            phot_variable_flag, has_epoch_photometry, has_epoch_rv,
            in_qso_candidates, in_galaxy_candidates, non_single_star,
            duplicated_source, phot_proc_mode
        FROM {table_name}
        WHERE parallax > 10.0 
        AND parallax_error/parallax < 0.2 
        AND ruwe < 1.4
        AND phot_g_mean_mag IS NOT NULL
        AND NOT in_qso_candidates
        AND NOT in_galaxy_candidates
        ORDER BY parallax DESC
        """
    else:
        query = base_query + " ORDER BY parallax DESC"
    
    try:
        logger.info(f"Executing query on {table_name}...")
        job = Gaia.launch_job_async(query, dump_to_file=False)
        results = job.get_results()
        
        logger.info(f"✅ Retrieved {len(results)} stars from {table_name}")
        return results, table_name
        
    except Exception as e:
        logger.error(f"❌ Error querying {table_name}: {e}")
        return None, table_name

def verify_gaia_coverage():
    """Verify complete GAIA coverage by querying all available tables"""
    logger.info("🚀 Starting comprehensive GAIA coverage verification...")
    
    # Get all GAIA tables
    gaia_tables = get_gaia_table_list()
    
    all_results = {}
    total_unique_stars = set()
    
    for table in gaia_tables:
        try:
            results, table_name = query_gaia_table_for_nearby_stars(table)
            if results is not None:
                all_results[table_name] = results
                
                # Track unique source IDs
                for star in results:
                    source_id = star.get('source_id')
                    if source_id:
                        total_unique_stars.add(int(source_id))
                
                logger.info(f"📊 {table_name}: {len(results)} stars")
            else:
                logger.warning(f"⚠️ Failed to query {table_name}")
                
        except Exception as e:
            logger.error(f"❌ Error processing {table}: {e}")
            continue
    
    logger.info(f"🌟 Total unique stars across all GAIA tables: {len(total_unique_stars)}")
    
    return all_results, total_unique_stars

def check_local_database_coverage():
    """Check what we have in our local database"""
    logger.info("🔍 Checking local database coverage...")
    
    with get_db_connection() as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
            # Count stars by source
            cursor.execute("""
                SELECT 
                    src,
                    COUNT(*) as count,
                    COUNT(CASE WHEN gaia_source_id IS NOT NULL THEN 1 END) as with_gaia_id,
                    MIN(distance_ly) as min_distance,
                    MAX(distance_ly) as max_distance
                FROM stars 
                GROUP BY src
                ORDER BY count DESC
            """)
            
            sources = cursor.fetchall()
            logger.info("📊 Local database sources:")
            total_local = 0
            gaia_ids_local = set()
            
            for source in sources:
                logger.info(f"  {source['src']}: {source['count']} stars (GAIA IDs: {source['with_gaia_id']}, range: {source['min_distance']:.1f}-{source['max_distance']:.1f} ly)")
                total_local += source['count']
            
            # Get all GAIA source IDs in local database
            cursor.execute("SELECT gaia_source_id FROM stars WHERE gaia_source_id IS NOT NULL")
            local_gaia_ids = cursor.fetchall()
            for row in local_gaia_ids:
                gaia_ids_local.add(int(row['gaia_source_id']))
            
            logger.info(f"📈 Total local stars: {total_local}")
            logger.info(f"🆔 Local GAIA source IDs: {len(gaia_ids_local)}")
            
            return total_local, gaia_ids_local

def compare_coverage(gaia_unique_stars, local_gaia_ids):
    """Compare GAIA coverage with local database"""
    logger.info("🔄 Comparing GAIA coverage with local database...")
    
    missing_from_local = gaia_unique_stars - local_gaia_ids
    extra_in_local = local_gaia_ids - gaia_unique_stars
    
    logger.info(f"📊 Coverage Analysis:")
    logger.info(f"  GAIA total unique stars: {len(gaia_unique_stars)}")
    logger.info(f"  Local GAIA source IDs: {len(local_gaia_ids)}")
    logger.info(f"  Missing from local: {len(missing_from_local)}")
    logger.info(f"  Extra in local: {len(extra_in_local)}")
    
    coverage_percentage = (len(local_gaia_ids) / len(gaia_unique_stars)) * 100 if gaia_unique_stars else 0
    logger.info(f"  Coverage percentage: {coverage_percentage:.1f}%")
    
    if missing_from_local:
        logger.warning(f"⚠️ Missing {len(missing_from_local)} GAIA stars from local database")
        if len(missing_from_local) <= 10:
            logger.info(f"Missing source IDs: {list(missing_from_local)[:10]}")
    
    if extra_in_local:
        logger.info(f"ℹ️ Local database has {len(extra_in_local)} additional GAIA IDs")
    
    return missing_from_local, extra_in_local, coverage_percentage

def get_expected_star_count_within_100ly():
    """Calculate expected number of stars within 100 light-years"""
    logger.info("📐 Calculating expected star count within 100 light-years...")
    
    # Stellar density in the solar neighborhood: ~0.004 stars per cubic light-year
    stellar_density = 0.004
    
    # Volume of sphere with radius 100 ly: (4/3) * π * r³
    volume_100ly = (4/3) * math.pi * (100 ** 3)
    
    expected_stars = volume_100ly * stellar_density
    
    logger.info(f"📊 Expected stars within 100 ly:")
    logger.info(f"  Volume: {volume_100ly:,.0f} cubic light-years")
    logger.info(f"  Stellar density: {stellar_density} stars/ly³")
    logger.info(f"  Expected total: {expected_stars:,.0f} stars")
    
    # GAIA completeness varies by magnitude
    # GAIA is ~100% complete to G=17, ~50% to G=20
    # Most stars within 100 ly should be brighter than G=17
    gaia_completeness = 0.95  # 95% completeness for nearby stars
    
    expected_gaia_detections = expected_stars * gaia_completeness
    logger.info(f"  Expected GAIA detections: {expected_gaia_detections:,.0f} stars")
    
    return expected_stars, expected_gaia_detections

def main():
    """Main execution function"""
    logger.info("🚀 Starting complete GAIA coverage verification...")
    
    try:
        # Step 1: Calculate expected star count
        expected_total, expected_gaia = get_expected_star_count_within_100ly()
        
        # Step 2: Check local database
        local_total, local_gaia_ids = check_local_database_coverage()
        
        # Step 3: Query all GAIA databases
        logger.info("🌌 Querying all GAIA databases...")
        gaia_results, gaia_unique_stars = verify_gaia_coverage()
        
        # Step 4: Compare coverage
        missing, extra, coverage_pct = compare_coverage(gaia_unique_stars, local_gaia_ids)
        
        # Step 5: Final assessment
        logger.info("🎯 FINAL ASSESSMENT:")
        logger.info(f"  Expected stars (theoretical): {expected_total:,.0f}")
        logger.info(f"  Expected GAIA detections: {expected_gaia:,.0f}")
        logger.info(f"  Actual GAIA stars found: {len(gaia_unique_stars):,}")
        logger.info(f"  Local database total: {local_total:,}")
        logger.info(f"  Local GAIA coverage: {coverage_pct:.1f}%")
        
        if coverage_pct >= 95:
            logger.info("✅ EXCELLENT: Near-complete GAIA coverage achieved!")
        elif coverage_pct >= 80:
            logger.info("✅ GOOD: Substantial GAIA coverage achieved!")
        elif coverage_pct >= 50:
            logger.warning("⚠️ PARTIAL: Moderate GAIA coverage, room for improvement")
        else:
            logger.error("❌ INCOMPLETE: Significant gaps in GAIA coverage")
        
        # Recommendations
        if len(missing) > 0:
            logger.info(f"📋 RECOMMENDATION: Import {len(missing)} missing GAIA stars")
        
        if len(gaia_unique_stars) < expected_gaia * 0.8:
            logger.warning("⚠️ GAIA query may be incomplete - consider broader search criteria")
        
        logger.info("✅ GAIA coverage verification completed!")
        
    except Exception as e:
        logger.error(f"❌ Error during verification: {e}")
        raise

if __name__ == "__main__":
    main()
