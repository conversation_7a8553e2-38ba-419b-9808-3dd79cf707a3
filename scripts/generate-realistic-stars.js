#!/usr/bin/env node

/**
 * Generate Realistic Stellar Data
 * Creates scientifically accurate stellar data for nearby stars
 * Uses real astronomical principles and known stellar properties
 */

const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5433,
  database: 'gg',
  user: 'gg',
  password: 'ggpassword'
});

/**
 * Convert celestial coordinates to 3D Cartesian
 */
function celestialToCartesian(ra_deg, dec_deg, distance_ly) {
  const ra_rad = ra_deg * Math.PI / 180;
  const dec_rad = dec_deg * Math.PI / 180;
  
  // Convert to parsecs for better scaling
  const distance_pc = distance_ly * 0.306601;
  
  const x = distance_pc * Math.cos(dec_rad) * Math.cos(ra_rad);
  const y = distance_pc * Math.cos(dec_rad) * Math.sin(ra_rad);
  const z = distance_pc * Math.sin(dec_rad);
  
  return { x, y, z };
}

/**
 * Get stellar color from spectral type
 */
function getSpectralColor(spectralType) {
  const type = spectralType.charAt(0).toUpperCase();
  const colors = {
    'O': '#9bb0ff',  // Blue
    'B': '#aabfff',  // Blue-white  
    'A': '#cad7ff',  // White
    'F': '#f8f7ff',  // Yellow-white
    'G': '#fff4ea',  // Yellow (like our Sun)
    'K': '#ffd2a1',  // Orange
    'M': '#ffad51',  // Red
    'L': '#ff6600',  // Brown dwarf
    'T': '#cc3300'   // Brown dwarf
  };
  return colors[type] || '#ffffff';
}

/**
 * Real nearby star systems with accurate data
 */
const REAL_NEARBY_STARS = [
  // Sol (our Sun) - reference point
  {
    name: 'Sol',
    proper_name: 'Sun',
    ra_deg: 0.0,
    dec_deg: 0.0,
    distance_ly: 0.0,
    spectral_type: 'G2V',
    mass_solar: 1.0,
    radius_solar: 1.0,
    teff_k: 5778,
    luminosity_solar: 1.0,
    mag_v: 4.83,
    color_bv: 0.656,
    absolute_magnitude: 4.83
  },
  
  // Alpha Centauri System
  {
    name: 'Alpha Centauri A',
    proper_name: 'Rigil Kentaurus',
    ra_deg: 219.9020625,
    dec_deg: -60.8339583,
    distance_ly: 4.37,
    spectral_type: 'G2V',
    mass_solar: 1.1,
    radius_solar: 1.22,
    teff_k: 5790,
    luminosity_solar: 1.52,
    mag_v: -0.01,
    color_bv: 0.71,
    absolute_magnitude: 4.38
  },
  {
    name: 'Alpha Centauri B',
    proper_name: 'Toliman',
    ra_deg: 219.9020625,
    dec_deg: -60.8339583,
    distance_ly: 4.37,
    spectral_type: 'K1V',
    mass_solar: 0.91,
    radius_solar: 0.86,
    teff_k: 5260,
    luminosity_solar: 0.50,
    mag_v: 1.35,
    color_bv: 0.88,
    absolute_magnitude: 5.71
  },
  {
    name: 'Proxima Centauri',
    proper_name: 'Alpha Centauri C',
    ra_deg: 217.4289167,
    dec_deg: -62.6795556,
    distance_ly: 4.24,
    spectral_type: 'M5.5Ve',
    mass_solar: 0.12,
    radius_solar: 0.14,
    teff_k: 3042,
    luminosity_solar: 0.0017,
    mag_v: 11.13,
    color_bv: 1.90,
    absolute_magnitude: 15.60
  },
  
  // Barnard's Star
  {
    name: "Barnard's Star",
    proper_name: "Barnard's Star",
    ra_deg: 269.4520833,
    dec_deg: 4.6933056,
    distance_ly: 5.96,
    spectral_type: 'M4.0V',
    mass_solar: 0.14,
    radius_solar: 0.20,
    teff_k: 3134,
    luminosity_solar: 0.0035,
    mag_v: 9.53,
    color_bv: 1.74,
    absolute_magnitude: 13.22
  },
  
  // Wolf 359
  {
    name: 'Wolf 359',
    proper_name: 'Wolf 359',
    ra_deg: 164.1206250,
    dec_deg: 7.0094444,
    distance_ly: 7.86,
    spectral_type: 'M6.0V',
    mass_solar: 0.09,
    radius_solar: 0.16,
    teff_k: 2800,
    luminosity_solar: 0.0014,
    mag_v: 13.54,
    color_bv: 2.01,
    absolute_magnitude: 16.55
  },
  
  // Sirius System
  {
    name: 'Sirius A',
    proper_name: 'Sirius',
    ra_deg: 101.2870833,
    dec_deg: -16.7161111,
    distance_ly: 8.66,
    spectral_type: 'A1V',
    mass_solar: 2.063,
    radius_solar: 1.711,
    teff_k: 9940,
    luminosity_solar: 25.4,
    mag_v: -1.46,
    color_bv: 0.009,
    absolute_magnitude: 1.42
  },
  {
    name: 'Sirius B',
    proper_name: 'Sirius B',
    ra_deg: 101.2870833,
    dec_deg: -16.7161111,
    distance_ly: 8.66,
    spectral_type: 'DA2',
    mass_solar: 0.978,
    radius_solar: 0.0084,
    teff_k: 25200,
    luminosity_solar: 0.056,
    mag_v: 8.44,
    color_bv: -0.03,
    absolute_magnitude: 11.18
  },
  
  // Epsilon Eridani
  {
    name: 'Epsilon Eridani',
    proper_name: 'Ran',
    ra_deg: 53.2326250,
    dec_deg: -9.4583333,
    distance_ly: 10.52,
    spectral_type: 'K2V',
    mass_solar: 0.82,
    radius_solar: 0.735,
    teff_k: 5084,
    luminosity_solar: 0.34,
    mag_v: 3.73,
    color_bv: 0.887,
    absolute_magnitude: 6.19
  },
  
  // Procyon System
  {
    name: 'Procyon A',
    proper_name: 'Procyon',
    ra_deg: 114.8254167,
    dec_deg: 5.2250000,
    distance_ly: 11.46,
    spectral_type: 'F5IV-V',
    mass_solar: 1.499,
    radius_solar: 2.048,
    teff_k: 6530,
    luminosity_solar: 6.93,
    mag_v: 0.34,
    color_bv: 0.42,
    absolute_magnitude: 2.66
  },
  
  // 61 Cygni System
  {
    name: '61 Cygni A',
    proper_name: '61 Cygni A',
    ra_deg: 316.1416667,
    dec_deg: 38.3441667,
    distance_ly: 11.40,
    spectral_type: 'K5.0V',
    mass_solar: 0.70,
    radius_solar: 0.665,
    teff_k: 4374,
    luminosity_solar: 0.153,
    mag_v: 5.21,
    color_bv: 1.18,
    absolute_magnitude: 7.49
  },
  
  // Vega
  {
    name: 'Vega',
    proper_name: 'Vega',
    ra_deg: 279.2341667,
    dec_deg: 38.7836111,
    distance_ly: 25.04,
    spectral_type: 'A0Va',
    mass_solar: 2.135,
    radius_solar: 2.362,
    teff_k: 9602,
    luminosity_solar: 40.12,
    mag_v: 0.026,
    color_bv: 0.00,
    absolute_magnitude: 0.582
  },
  
  // Altair
  {
    name: 'Altair',
    proper_name: 'Altair',
    ra_deg: 297.6958333,
    dec_deg: 8.8683333,
    distance_ly: 16.73,
    spectral_type: 'A7V',
    mass_solar: 1.79,
    radius_solar: 1.63,
    teff_k: 7550,
    luminosity_solar: 10.6,
    mag_v: 0.77,
    color_bv: 0.22,
    absolute_magnitude: 2.21
  }
];

/**
 * Insert realistic stellar data
 */
async function insertRealisticStars() {
  console.log('🌟 Inserting realistic stellar data...');
  
  try {
    // Clear existing data
    await pool.query('DELETE FROM planets WHERE star_id IN (SELECT star_id FROM stars)');
    await pool.query('DELETE FROM stars');
    
    let inserted = 0;
    
    for (const star of REAL_NEARBY_STARS) {
      // Calculate 3D coordinates
      const coords = celestialToCartesian(star.ra_deg, star.dec_deg, star.distance_ly);
      
      // Get stellar color
      const stellarColor = getSpectralColor(star.spectral_type);
      
      await pool.query(`
        INSERT INTO stars (
          src, src_key, name, proper_name, catalog_name,
          ra_deg, dec_deg, distance_ly, x_pc, y_pc, z_pc,
          spectral_type, mass_solar, radius_solar, teff_k, 
          luminosity_solar, mag_v, color_bv, absolute_magnitude,
          stellar_color, discovery_status, is_colonizable,
          data_quality_score
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
          $16, $17, $18, $19, $20, $21, $22, $23
        )
      `, [
        'realistic', star.name.toLowerCase().replace(/\s+/g, '_'),
        star.name, star.proper_name, star.name,
        star.ra_deg, star.dec_deg, star.distance_ly,
        coords.x, coords.y, coords.z,
        star.spectral_type, star.mass_solar, star.radius_solar, star.teff_k,
        star.luminosity_solar, star.mag_v, star.color_bv, star.absolute_magnitude,
        stellarColor, 'known', true, 1.0
      ]);
      
      inserted++;
      console.log(`✅ Inserted ${star.name} (${star.spectral_type}, ${star.distance_ly} ly)`);
    }
    
    console.log(`🌌 Successfully inserted ${inserted} realistic stars`);
    
    // Update statistics
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total_stars,
        COUNT(*) FILTER (WHERE distance_ly <= 10) as stars_10ly,
        COUNT(*) FILTER (WHERE distance_ly <= 25) as stars_25ly,
        COUNT(*) FILTER (WHERE spectral_type LIKE 'G%') as g_type_stars,
        COUNT(*) FILTER (WHERE spectral_type LIKE 'M%') as m_type_stars,
        COUNT(*) FILTER (WHERE spectral_type LIKE 'A%') as a_type_stars,
        COUNT(*) FILTER (WHERE spectral_type LIKE 'K%') as k_type_stars,
        AVG(distance_ly) as avg_distance
      FROM stars
    `);
    
    console.log('\n🌌 REALISTIC STELLAR DATABASE COMPLETE!');
    console.log('=====================================');
    console.log(`✅ Total stars: ${stats.rows[0].total_stars}`);
    console.log(`✅ Stars within 10 ly: ${stats.rows[0].stars_10ly}`);
    console.log(`✅ Stars within 25 ly: ${stats.rows[0].stars_25ly}`);
    console.log(`✅ G-type stars (Sun-like): ${stats.rows[0].g_type_stars}`);
    console.log(`✅ M-type stars (Red dwarfs): ${stats.rows[0].m_type_stars}`);
    console.log(`✅ A-type stars (Hot): ${stats.rows[0].a_type_stars}`);
    console.log(`✅ K-type stars (Orange): ${stats.rows[0].k_type_stars}`);
    console.log(`✅ Average distance: ${parseFloat(stats.rows[0].avg_distance).toFixed(2)} ly`);
    
  } catch (error) {
    console.error('❌ Error inserting stellar data:', error);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Generating realistic stellar database...');
  
  try {
    await insertRealisticStars();
  } catch (error) {
    console.error('❌ Generation failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, celestialToCartesian, getSpectralColor };
