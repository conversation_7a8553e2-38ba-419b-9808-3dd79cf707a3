#!/bin/bash
# GAIA DR3 Integration Master Script for Galactic Genesis
# Downloads and integrates GAIA DR3 stellar data and NASA exoplanet data

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "scripts" ]; then
    error "Please run this script from the project root directory"
    exit 1
fi

log "🌌 Starting GAIA DR3 Integration for Galactic Genesis"

# Step 1: Setup Python virtual environment and dependencies
log "📦 Setting up Python virtual environment..."
if [ ! -d "venv-gaia" ]; then
    python3 -m venv venv-gaia
    success "Virtual environment created"
fi

log "📦 Installing Python dependencies..."
source venv-gaia/bin/activate
pip install -r scripts/requirements-gaia.txt
success "Python dependencies installed in virtual environment"

# Step 2: Check database connectivity
log "🔌 Checking database connectivity..."
if ! PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c "SELECT 1;" &> /dev/null; then
    error "Cannot connect to database. Please ensure PostgreSQL is running on port 5433"
    exit 1
fi
success "Database connection verified"

# Step 3: Apply database migrations
log "🗄️ Applying database migrations..."
log "   • GAIA DR3 stellar columns..."
PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -f db/sql/012_gaia_dr3_stellar_columns.sql
log "   • NASA exoplanet columns..."
PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -f db/sql/013_nasa_exoplanet_columns.sql
log "   • Enhanced moons table..."
PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -f db/sql/014_enhanced_moons_columns.sql
success "Database migrations applied"

# Step 4: Download GAIA DR3 and NASA data
log "📡 Downloading GAIA DR3 and NASA Exoplanet Archive data..."
log "⚠️  This may take 10-30 minutes depending on your internet connection"

if source venv-gaia/bin/activate && python scripts/gaia-dr3-downloader.py; then
    success "Data download completed"
else
    error "Data download failed"
    exit 1
fi

# Step 5: Compare local data with authoritative sources
log "🔍 Comparing local data with authoritative sources..."
if source venv-gaia/bin/activate && python scripts/data-comparison-validator.py; then
    success "Data comparison completed"
else
    error "Data comparison failed"
    exit 1
fi

# Step 6: Apply database updates
log "🔄 Applying database updates..."
if source venv-gaia/bin/activate && python scripts/database-migration-system.py; then
    success "Database updates completed"
else
    error "Database updates failed"
    exit 1
fi

# Step 7: Verify data integrity
log "✅ Verifying data integrity..."

# Check star count
STAR_COUNT=$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM stars;")
log "Total stars in database: $STAR_COUNT"

# Check planet count
PLANET_COUNT=$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM planets;")
log "Total planets in database: $PLANET_COUNT"

# Check moon count
MOON_COUNT=$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM moons;")
log "Total moons in database: $MOON_COUNT"

# Check GAIA data
GAIA_COUNT=$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3';")
log "Stars from GAIA DR3: $GAIA_COUNT"

# Check NASA data
NASA_COUNT=$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM planets WHERE src = 'nasa_exoplanet_archive';")
log "Planets from NASA Archive: $NASA_COUNT"

# Step 8: Test API endpoints
log "🧪 Testing API endpoints..."

# Check if API Gateway is running
if ! curl -s http://localhost:19081/v1/health > /dev/null; then
    warning "API Gateway not running. Starting it..."
    cd services/api-gateway
    npm start &
    API_PID=$!
    cd ../..
    sleep 5
else
    success "API Gateway is running"
fi

# Test stellar data endpoint
STELLAR_RESPONSE=$(curl -s "http://localhost:19081/v1/stellar/stars?limit=5")
if echo "$STELLAR_RESPONSE" | jq -e '.stars | length' > /dev/null 2>&1; then
    STELLAR_COUNT=$(echo "$STELLAR_RESPONSE" | jq '.stars | length')
    success "Stellar API working: returned $STELLAR_COUNT stars"
else
    error "Stellar API test failed"
fi

# Test planetary data endpoint
PLANET_RESPONSE=$(curl -s "http://localhost:19081/v1/stellar/systems/1/planets")
if echo "$PLANET_RESPONSE" | jq -e '.planets' > /dev/null 2>&1; then
    success "Planetary API working"
else
    warning "Planetary API test inconclusive (may be normal if no planets for star 1)"
fi

# Step 9: Generate final report
log "📊 Generating final integration report..."

cat > gaia_integration_report.md << EOF
# GAIA DR3 Integration Report

## Summary
- **Integration Date**: $(date)
- **Total Stars**: $STAR_COUNT
- **Total Planets**: $PLANET_COUNT  
- **Total Moons**: $MOON_COUNT
- **GAIA DR3 Stars**: $GAIA_COUNT
- **NASA Archive Planets**: $NASA_COUNT

## Data Sources
- **GAIA DR3**: European Space Agency's third data release
- **NASA Exoplanet Archive**: Confirmed exoplanets database
- **Manual**: Solar System and well-known objects

## Files Generated
- \`gaia_stellar_data.json\`: Raw GAIA stellar data
- \`nasa_planet_data.json\`: Raw NASA planetary data
- \`stellar_comparison_results.json\`: Stellar data comparison results
- \`planetary_comparison_results.json\`: Planetary data comparison results
- \`comparison_report.json\`: Summary comparison report
- \`migration_report.json\`: Database migration summary

## Database Changes
- Added moons table with Solar System moons
- Updated stellar data with GAIA DR3 information
- Added confirmed exoplanets from NASA Archive
- Created backup tables for safety

## Next Steps
1. Review comparison reports for any manual interventions needed
2. Test frontend galaxy view with new stellar data
3. Verify planetary system displays work correctly
4. Consider adding more detailed moon data for gas giants

EOF

success "Integration report generated: gaia_integration_report.md"

# Step 10: Cleanup and final status
log "🧹 Cleaning up temporary processes..."

if [ ! -z "$API_PID" ]; then
    kill $API_PID 2>/dev/null || true
fi

# Final success message
echo ""
echo "🎉 GAIA DR3 Integration Complete!"
echo ""
echo "📈 Database now contains:"
echo "   • $STAR_COUNT stars (including $GAIA_COUNT from GAIA DR3)"
echo "   • $PLANET_COUNT planets (including $NASA_COUNT from NASA Archive)"
echo "   • $MOON_COUNT moons"
echo ""
echo "📁 Generated files:"
echo "   • gaia_integration_report.md"
echo "   • *.json data and comparison files"
echo "   • Database backup tables (stars_backup, planets_backup, moons_backup)"
echo ""
echo "🚀 Your Galactic Genesis database is now powered by real astronomical data!"
echo ""

success "GAIA DR3 integration completed successfully"
