#!/usr/bin/env bash
set -euo pipefail

# Comprehensive test suite for Galactic Genesis
# Tests all critical functionality as requested by the user

echo "🚀 GALACTIC GENESIS COMPREHENSIVE TEST SUITE"
echo "============================================="

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Helper function to run tests
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing $test_name... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo "✅ PASS"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ FAIL"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Helper function to check HTTP endpoints
check_http() {
    local url="$1"
    local expected_code="${2:-200}"
    curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "^$expected_code$"
}

# Helper function to check JSON structure
check_json() {
    local url="$1"
    local jq_filter="$2"
    curl -s "$url" | jq -e "$jq_filter" >/dev/null 2>&1
}

echo ""
echo "📋 1. UNIT TESTS"
echo "=================="

# Frontend build test
run_test "Frontend Build" "cd frontend && npm run build"

# API Gateway build test  
run_test "API Gateway Build" "cd services/api-gateway && npm run build"

echo ""
echo "🔗 2. INTEGRATION TESTS"
echo "======================="

# Port availability tests
run_test "Frontend Port 5174" "check_http http://localhost:5174"
run_test "API Gateway Port 19081" "check_http http://localhost:19081/v1/health"

# Database connectivity
run_test "Database Connection" "psql -h localhost -p 5433 -U gg -d gg -c 'SELECT 1;' >/dev/null 2>&1"

echo ""
echo "🌌 3. STELLAR API TESTS"
echo "======================"

# Stellar API endpoints
run_test "Stellar Stars List" "check_http http://localhost:19081/v1/stellar/stars"
run_test "Stars JSON Structure" "check_json http://localhost:19081/v1/stellar/stars '.stars | type == \"array\" and length > 0'"

# Test realistic stellar data
run_test "Sol Star Data" "check_json 'http://localhost:19081/v1/stellar/stars?limit=20' '.stars[] | select(.name == \"Sol\") | .stellar_color != null'"
run_test "Proxima Centauri Data" "check_json 'http://localhost:19081/v1/stellar/stars?limit=20' '.stars[] | select(.name == \"Proxima Centauri\") | .spectral_type == \"M5.5Ve\"'"
run_test "3D Coordinates" "check_json 'http://localhost:19081/v1/stellar/stars?limit=20' '.stars[] | select(.name == \"Sol\") | .x_pc == 0'"

echo ""
echo "🎮 4. FRONTEND FUNCTIONALITY"
echo "============================"

# Frontend static files
run_test "Frontend HTML" "check_http http://localhost:5174"
run_test "Frontend CSS" "check_http http://localhost:5174/assets/index-CBO0dXTP.css"
run_test "Frontend JS Bundle" "check_http http://localhost:5174/assets/index-BWd-fLj4.js"

echo ""
echo "🔍 5. SMOKE TESTS"
echo "================="

# Run stellar smoke test
echo "Running stellar smoke test..."
if ./scripts/stellar-smoke.sh >/dev/null 2>&1; then
    run_test "Stellar Smoke Test" "true"
else
    run_test "Stellar Smoke Test" "false"
fi

echo ""
echo "📊 6. PERFORMANCE TESTS"
echo "======================"

# API response time tests
run_test "API Response Time < 1s" "timeout 1s curl -s http://localhost:19081/v1/stellar/stars >/dev/null"
run_test "Frontend Load Time < 3s" "timeout 3s curl -s http://localhost:5174 >/dev/null"

echo ""
echo "🎯 7. GALAXY MAP TESTS"
echo "====================="

# Test that realistic stellar data is available for galaxy map
run_test "Multiple Star Systems" "check_json 'http://localhost:19081/v1/stellar/stars?limit=50' '.stars | length >= 10'"
run_test "Stellar Colors Available" "check_json 'http://localhost:19081/v1/stellar/stars?limit=20' '.stars | map(select(.stellar_color != null)) | length >= 5'"
run_test "3D Positioning Data" "check_json 'http://localhost:19081/v1/stellar/stars?limit=20' '.stars | map(select(.x_pc != null and .y_pc != null and .z_pc != null)) | length >= 5'"

echo ""
echo "🏁 FINAL RESULTS"
echo "================"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo "🎉 ALL TESTS PASSED! 🎉"
    echo "✅ Website is fully functional"
    echo "✅ All ports are listening"
    echo "✅ Database is connected"
    echo "✅ Realistic galaxy map data is available"
    echo "✅ Frontend and API are working correctly"
    echo ""
    echo "🌌 GALACTIC GENESIS IS READY FOR USE! 🌌"
    exit 0
else
    echo ""
    echo "❌ SOME TESTS FAILED"
    echo "Please check the failed tests above and fix any issues."
    exit 1
fi
