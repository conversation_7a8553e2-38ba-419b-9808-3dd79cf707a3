#!/usr/bin/env node

// Script to generate fallback stellar data for production deployment
// This ensures the production site has access to all 84 star systems

const fs = require('fs');
const path = require('path');

async function generateFallbackData() {
  try {
    console.log('🌟 Generating fallback stellar data...');
    
    // Read the stellar data from the API export
    const stellarDataPath = '/tmp/stellar_data.json';
    if (!fs.existsSync(stellarDataPath)) {
      console.error('❌ Stellar data file not found. Please run:');
      console.error('curl -s "http://localhost:19081/v1/stellar/stars?max_distance=20&limit=100" > /tmp/stellar_data.json');
      process.exit(1);
    }
    
    const stellarData = JSON.parse(fs.readFileSync(stellarDataPath, 'utf8'));
    console.log(`📊 Loaded ${stellarData.stars.length} stars from API`);
    
    // Generate TypeScript code for the fallback data
    const fallbackCode = `  // Fallback data for production when API is not available
  // Generated from real stellar database with ${stellarData.stars.length} star systems
  private getFallbackStars(): { stars: Star[]; total: number } {
    const fallbackStars: Star[] = [
${stellarData.stars.map(star => `      {
        star_id: ${star.star_id},
        name: '${star.name.replace(/'/g, "\\'")}',
        ra_deg: ${star.ra_deg},
        dec_deg: ${star.dec_deg},
        distance_ly: ${star.distance_ly},
        spectral_type: '${star.spectral_type || 'Unknown'}',
        mass_solar: ${star.mass_solar || 1.0},
        radius_solar: ${star.radius_solar || 1.0},
        teff_k: ${star.teff_k || 5778},
        luminosity_solar: ${star.luminosity_solar || 1.0},
        mag_v: ${star.mag_v || 0.0},
        discovery_status: '${star.discovery_status || 'known'}',
        is_colonizable: ${star.is_colonizable || true},
        planet_count: ${star.planet_count || 0}
      }`).join(',\n')}
    ];

    console.log('🔄 Using fallback stellar data:', fallbackStars.length, 'stars');
    return { stars: fallbackStars, total: fallbackStars.length };
  }`;
    
    // Write the fallback code to a file
    const outputPath = '/tmp/fallback_stellar_data.ts';
    fs.writeFileSync(outputPath, fallbackCode);
    
    console.log(`✅ Generated fallback data with ${stellarData.stars.length} stars`);
    console.log(`📁 Saved to: ${outputPath}`);
    console.log('');
    console.log('🔧 Next steps:');
    console.log('1. Copy the generated code from the file above');
    console.log('2. Replace the getFallbackStars() method in frontend/src/services/stellarApi.ts');
    console.log('3. Rebuild and redeploy the frontend');
    
  } catch (error) {
    console.error('❌ Error generating fallback data:', error);
    process.exit(1);
  }
}

generateFallbackData();
