-- Fix Stellar Database: Remove Duplicates and Add Solar System Planets
-- This script addresses two main issues:
-- 1. Duplicate stars from multiple import sources (manual, comprehensive, extended)
-- 2. Missing Solar system planets due to incorrect star_id references

BEGIN;

-- Create a temporary table to track which stars to keep (prefer manual > comprehensive > extended)
CREATE TEMP TABLE stars_to_keep AS
SELECT DISTINCT ON (name) 
    star_id,
    name,
    src,
    CASE 
        WHEN src = 'manual' THEN 1
        WHEN src = 'comprehensive' THEN 2
        WHEN src = 'extended' THEN 3
        ELSE 4
    END as priority
FROM stars 
WHERE name IS NOT NULL
ORDER BY name, priority;

-- Create a temporary table for stars to delete
CREATE TEMP TABLE stars_to_delete AS
SELECT s.star_id, s.name, s.src
FROM stars s
LEFT JOIN stars_to_keep stk ON s.star_id = stk.star_id
WHERE s.name IS NOT NULL 
AND stk.star_id IS NULL;

-- Show what will be deleted
SELECT 
    'Stars to be deleted' as action,
    COUNT(*) as count,
    array_agg(DISTINCT src) as sources
FROM stars_to_delete;

-- Update any planet references to point to the kept stars
UPDATE planets 
SET star_id = (
    SELECT stk.star_id 
    FROM stars_to_delete std
    JOIN stars s ON std.star_id = s.star_id
    JOIN stars_to_keep stk ON s.name = stk.name
    WHERE planets.star_id = std.star_id
)
WHERE star_id IN (SELECT star_id FROM stars_to_delete);

-- Update any star_neighbors references
UPDATE star_neighbors 
SET star_id = (
    SELECT stk.star_id 
    FROM stars_to_delete std
    JOIN stars s ON std.star_id = s.star_id
    JOIN stars_to_keep stk ON s.name = stk.name
    WHERE star_neighbors.star_id = std.star_id
)
WHERE star_id IN (SELECT star_id FROM stars_to_delete);

UPDATE star_neighbors 
SET neighbor_star_id = (
    SELECT stk.star_id 
    FROM stars_to_delete std
    JOIN stars s ON std.star_id = s.star_id
    JOIN stars_to_keep stk ON s.name = stk.name
    WHERE star_neighbors.neighbor_star_id = std.star_id
)
WHERE neighbor_star_id IN (SELECT star_id FROM stars_to_delete);

-- Delete duplicate stars
DELETE FROM stars WHERE star_id IN (SELECT star_id FROM stars_to_delete);

-- Get the Sol star_id for inserting planets
DO $$
DECLARE
    sol_star_id INTEGER;
BEGIN
    SELECT star_id INTO sol_star_id FROM stars WHERE name = 'Sol' LIMIT 1;

    IF sol_star_id IS NULL THEN
        RAISE EXCEPTION 'Sol star not found in database';
    END IF;

    -- Check if Solar System planets already exist
    IF NOT EXISTS (SELECT 1 FROM planets WHERE star_id = sol_star_id AND name = 'Earth') THEN
        -- Insert Solar System planets (using only essential columns)
        INSERT INTO planets (
            star_id, src, src_key, name, mass_earth, radius_earth, sma_au, period_days,
            eccentricity, inclination_deg, eq_temp_k, composition,
            habitability_score, in_habitable_zone, has_atmosphere, has_water,
            mineral_richness, energy_potential, discovery_method, discovery_year,
            is_colonized, is_exploitable, exploration_status
        ) VALUES
        -- Mercury
        (sol_star_id, 'manual', 'mercury', 'Mercury', 0.055, 0.383, 0.387, 87.97, 0.206, 7.0, 440, 'rocky', 0.0, false, false, false, 0.7, 0.8, 'observation', 1631, false, true, 'known'),

        -- Venus
        (sol_star_id, 'manual', 'venus', 'Venus', 0.815, 0.949, 0.723, 224.7, 0.007, 3.4, 737, 'rocky', 0.0, false, true, false, 0.9, 0.6, 'observation', 1610, false, true, 'known'),

        -- Earth
        (sol_star_id, 'manual', 'earth', 'Earth', 1.0, 1.0, 1.0, 365.26, 0.017, 0.0, 288, 'rocky', 1.0, true, true, true, 0.8, 0.7, 'observation', -4000, false, false, 'known'),

        -- Mars
        (sol_star_id, 'manual', 'mars', 'Mars', 0.107, 0.532, 1.524, 686.98, 0.094, 1.9, 210, 'rocky', 0.1, false, true, true, 0.8, 0.6, 'observation', 1610, false, true, 'known'),

        -- Jupiter
        (sol_star_id, 'manual', 'jupiter', 'Jupiter', 317.8, 11.21, 5.204, 4332.59, 0.049, 1.3, 165, 'gas_giant', 0.0, false, true, false, 0.3, 0.9, 'observation', 1610, false, true, 'known'),

        -- Saturn
        (sol_star_id, 'manual', 'saturn', 'Saturn', 95.2, 9.45, 9.537, 10759.22, 0.057, 2.5, 134, 'gas_giant', 0.0, false, true, false, 0.4, 0.8, 'observation', 1610, false, true, 'known'),

        -- Uranus
        (sol_star_id, 'manual', 'uranus', 'Uranus', 14.5, 4.01, 19.165, 30688.5, 0.046, 0.8, 76, 'ice_giant', 0.0, false, true, false, 0.5, 0.7, 'observation', 1781, false, true, 'known'),

        -- Neptune
        (sol_star_id, 'manual', 'neptune', 'Neptune', 17.1, 3.88, 30.178, 60182, 0.009, 1.8, 72, 'ice_giant', 0.0, false, true, false, 0.6, 0.8, 'observation', 1846, false, true, 'known');

        RAISE NOTICE 'Solar system planets inserted for Sol star_id: %', sol_star_id;
    ELSE
        RAISE NOTICE 'Solar system planets already exist for Sol star_id: %', sol_star_id;
    END IF;
END $$;

COMMIT;

-- Display final summary
SELECT 
    'Final star count' as metric,
    COUNT(*) as count
FROM stars
UNION ALL
SELECT 
    'Final planet count' as metric,
    COUNT(*) as count
FROM planets
UNION ALL
SELECT 
    'Solar system planets' as metric,
    COUNT(*) as count
FROM planets p
JOIN stars s ON p.star_id = s.star_id
WHERE s.name = 'Sol'
UNION ALL
SELECT 
    'Duplicate stars remaining' as metric,
    COUNT(*) as count
FROM (
    SELECT name, COUNT(*) as cnt
    FROM stars 
    WHERE name IS NOT NULL 
    GROUP BY name 
    HAVING COUNT(*) > 1
) duplicates;
