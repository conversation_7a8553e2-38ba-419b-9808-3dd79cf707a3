#!/usr/bin/env bash
set -euo pipefail

# Comprehensive unit/type-check test runner for all services.
# Usage:
#   bash scripts/unit-tests.sh [--no-typecheck] [--services LIST]
# Options:
#   --no-typecheck   Skip TypeScript type-checks (default: type-checks run)
#   --services LIST  Comma-separated list among: gateway,orders,fleets,dispatcher (default: all)
#
# Behavior:
# - Runs tsc --noEmit for selected services (unless --no-typecheck)
# - Runs each service's test suite (npm test) sequentially
# - Aggregates results and exits non-zero on any failure

ROOT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
SERVICES=(gateway orders fleets dispatcher)
DO_TYPECHECK=true

# Parse args
while [[ $# -gt 0 ]]; do
  case "$1" in
    --no-typecheck) DO_TYPECHECK=false; shift;;
    --services)
      IFS=',' read -r -a SERVICES <<<"$2"; shift 2;;
    -h|--help)
      echo "Usage: $0 [--no-typecheck] [--services LIST]"; exit 0;;
    *) echo "Unknown arg: $1"; exit 1;;
  esac
done

# Map service key -> path
path_for() {
  case "$1" in
    gateway) echo "services/api-gateway";;
    orders) echo "services/orders-svc";;
    fleets) echo "services/fleets-svc";;
    dispatcher) echo "services/event-dispatcher";;
    *) echo "?";;
  esac
}

pass=0; fail=0
run_typecheck() {
  local key="$1"; local dir; dir=$(path_for "$key")
  if [[ "$dir" == "?" ]]; then echo "[skip] unknown service '$key'"; return 0; fi
  echo "[tsc] $key ($dir)"
  ( cd "$ROOT_DIR/$dir" && npx -y tsc -p tsconfig.json --noEmit ) && pass=$((pass+1)) || { echo "[fail] tsc $key"; fail=$((fail+1)); }
}
run_tests() {
  local key="$1"; local dir; dir=$(path_for "$key")
  if [[ "$dir" == "?" ]]; then echo "[skip] unknown service '$key'"; return 0; fi
  echo "[test] $key ($dir)"
  ( cd "$ROOT_DIR/$dir" && npm test --silent ) && pass=$((pass+1)) || { echo "[fail] test $key"; fail=$((fail+1)); }
}

echo "Services: ${SERVICES[*]}  (typecheck: $DO_TYPECHECK)"

if $DO_TYPECHECK; then
  for s in "${SERVICES[@]}"; do run_typecheck "$s"; done
fi
for s in "${SERVICES[@]}"; do run_tests "$s"; done

echo "---"
echo "Passed steps: $pass  Failed steps: $fail"
if [[ $fail -ne 0 ]]; then exit 1; fi

