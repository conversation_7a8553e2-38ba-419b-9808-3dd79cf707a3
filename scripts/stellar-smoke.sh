#!/usr/bin/env bash
set -euo pipefail

# Stellar-specific smoke test for solar system functionality
# Tests frontend, API gateway, and stellar database endpoints

FRONTEND_URL=${FRONTEND_URL:-http://localhost:5174}
GATEWAY_URL=${GATEWAY_URL:-http://localhost:19081}

pass=0; fail=0
_print() { printf "%s\n" "$*"; }

_check() {
  local name="$1" url="$2" expected_code="${3:-200}"
  local resp code
  resp=$(curl -sS -w "\n%{http_code}" "$url" || echo -e "\nERROR")
  code=${resp##*$'\n'}
  if [[ "$code" == "$expected_code" ]]; then
    _print "✔ $name ($code)"
    pass=$((pass+1))
  else
    _print "✖ $name failed (expected $expected_code, got $code)"
    fail=$((fail+1))
  fi
}

_check_json() {
  local name="$1" url="$2" jq_filter="$3"
  local resp
  resp=$(curl -sS "$url" 2>/dev/null || echo '{}')
  if command -v jq >/dev/null 2>&1 && echo "$resp" | jq -e "$jq_filter" >/dev/null 2>&1; then
    _print "✔ $name (JSON structure valid)"
    pass=$((pass+1))
  else
    _print "✖ $name (JSON structure invalid or jq not available)"
    fail=$((fail+1))
  fi
}

_print "🌌 Stellar System Smoke Test"
_print "Frontend: $FRONTEND_URL"
_print "Gateway: $GATEWAY_URL"
_print ""

# 1) Frontend accessibility
_check "Frontend HTML" "$FRONTEND_URL"

# 2) API Gateway health
_check "API Gateway Health" "$GATEWAY_URL/v1/health"

# 3) Stellar endpoints
_check "Stellar Stars List" "$GATEWAY_URL/v1/stellar/stars"
_check_json "Stars JSON Structure" "$GATEWAY_URL/v1/stellar/stars" '.stars | type == "array" and length > 0'

# 4) Sol system detail (find Sol's star_id dynamically)
SOL_ID=$(curl -s "$GATEWAY_URL/v1/stellar/stars?limit=20" | jq -r '.stars[] | select(.name == "Sol") | .star_id' | head -1)
if [ -n "$SOL_ID" ] && [ "$SOL_ID" != "null" ]; then
  _check "Sol System Detail" "$GATEWAY_URL/v1/stellar/stars/$SOL_ID"
  _check_json "Sol Planets" "$GATEWAY_URL/v1/stellar/stars/$SOL_ID" '.planets | type == "array"'

  # 5) Planet atmosphere data (if planets exist)
  _check_json "Earth Atmosphere" "$GATEWAY_URL/v1/stellar/stars/$SOL_ID" '.planets[]? | select(.name == "Earth")? | .has_atmosphere == true'
  _check_json "Mercury No Atmosphere" "$GATEWAY_URL/v1/stellar/stars/$SOL_ID" '.planets[]? | select(.name == "Mercury")? | .has_atmosphere == false'
else
  echo "✖ Sol System Detail failed (Sol not found in database)"
  echo "✖ Sol Planets (Sol not found)"
  echo "✖ Earth Atmosphere (Sol not found)"
  echo "✖ Mercury No Atmosphere (Sol not found)"
fi

# 6) Other star systems (find Proxima Centauri dynamically)
PROXIMA_ID=$(curl -s "$GATEWAY_URL/v1/stellar/stars?limit=20" | jq -r '.stars[] | select(.name == "Proxima Centauri") | .star_id' | head -1)
if [ -n "$PROXIMA_ID" ] && [ "$PROXIMA_ID" != "null" ]; then
  _check "Proxima Centauri System" "$GATEWAY_URL/v1/stellar/stars/$PROXIMA_ID"
  _check_json "Proxima Planets" "$GATEWAY_URL/v1/stellar/stars/$PROXIMA_ID" '.planets | type == "array"'
else
  echo "✖ Proxima Centauri System failed (Proxima Centauri not found in database)"
  echo "✖ Proxima Planets (Proxima Centauri not found)"
fi

# 7) Database connectivity
_print "Testing database connectivity..."
if docker exec gg_postgres psql -U gg -d gg -c "SELECT COUNT(*) FROM stars;" >/dev/null 2>&1; then
  _print "✔ Database connectivity"
  pass=$((pass+1))
else
  _print "✖ Database connectivity failed"
  fail=$((fail+1))
fi

_print ""
_print "---"
_print "🌟 Stellar Smoke Test Results:"
_print "Passed: $pass  Failed: $fail"

if [[ $fail -eq 0 ]]; then
  _print "🎉 All stellar system tests passed!"
  exit 0
else
  _print "❌ Some tests failed. Check the output above."
  exit 1
fi
