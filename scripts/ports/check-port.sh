#!/bin/bash

# 🚨 GALACTIC GENESIS PORT CHECKER
# NEVER CHANGE PORTS - ONLY DIAGNOSE

PORT=$1

if [ -z "$PORT" ]; then
    echo "❌ Usage: $0 <PORT_NUMBER>"
    echo "Example: $0 5177"
    exit 1
fi

echo "🔍 CHECKING PORT $PORT STATUS"
echo "================================"

# Check if port is in use
if netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
    echo "🚨 PORT $PORT IS IN USE:"
    echo ""
    netstat -tlnp 2>/dev/null | grep ":$PORT " | while read line; do
        echo "  $line"
    done
    echo ""
    
    # Get process details
    PID=$(netstat -tlnp 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1 | head -1)
    if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
        echo "📋 PROCESS DETAILS:"
        ps -p $PID -o pid,ppid,cmd --no-headers 2>/dev/null || echo "  Process details not available"
    fi
    
    echo ""
    echo "⚠️  CRITICAL: Port $PORT is busy!"
    echo "⚠️  DO NOT change to a different port!"
    echo "⚠️  Use kill-port.sh to free this port if needed"
    echo "⚠️  ASK USER before making any changes!"
    
else
    echo "✅ PORT $PORT IS FREE"
    echo ""
    echo "🎯 Port $PORT is available for use"
fi

echo ""
echo "🔒 REMEMBER: Never change configured ports without user approval!"
