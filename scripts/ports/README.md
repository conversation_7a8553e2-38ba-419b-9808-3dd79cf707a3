# 🚨 GALACTIC GENESIS PORT MANAGEMENT - CRITICAL RULES

## ⚠️ ABSOLUTE PORT RULES - NO EXCEPTIONS

### 🔒 **RULE #1: NEVER CHANGE CONFIGURED PORTS**
- **NEVER** change port numbers without explicit user approval
- **ALWAYS** use the agreed/configured ports listed below
- **FAIL FAST** if a port is busy - do not reassign to different port

### 🔒 **RULE #2: PORT CONFLICT RESOLUTION PROTOCOL**
1. **STOP** - Do not change the port
2. **DIAGNOSE** - Use scripts in `/scripts/ports/` to identify what's using the port
3. **ASK** - Request user approval before any port changes
4. **DOCUMENT** - Update this file if ports are officially changed

### 🔒 **RULE #3: AGREED PORT CONFIGURATION**
```
GALACTIC GENESIS OFFICIAL PORTS - DO NOT CHANGE:
==============================================
Frontend (React):           5177
API Gateway:                19081
Orders Service:              8081
Fleets Service:              8082
Colonies Service:            8083
Tech Service:                8084
Market Service:              8085
Health Dashboard:            8086
Event Dispatcher:            8090
PostgreSQL Database:         5433
NATS Message Broker:         4222
==============================================
```

## 🛠️ **PORT DIAGNOSTIC TOOLS**

### Check Port Usage:
```bash
./scripts/ports/check-port.sh <PORT_NUMBER>
```

### Find Process Using Port:
```bash
./scripts/ports/find-process.sh <PORT_NUMBER>
```

### Kill Process on Port:
```bash
./scripts/ports/kill-port.sh <PORT_NUMBER>
```

### Verify All Galactic Genesis Ports:
```bash
./scripts/ports/verify-all-ports.sh
```

## 🚨 **EMERGENCY PROTOCOL**
If a configured port is busy:
1. Run `./scripts/ports/check-port.sh <PORT>`
2. Identify the conflicting process
3. **ASK USER** before killing or changing anything
4. Document the resolution

## 📝 **PORT CHANGE LOG**
- 2025-01-XX: Initial port configuration established
- **ALL FUTURE CHANGES REQUIRE USER APPROVAL**
