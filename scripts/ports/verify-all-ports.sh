#!/bin/bash

# 🚨 GALACTIC GENESIS PORT VERIFICATION
# Checks all configured ports against agreed configuration

echo "🌌 GALACTIC GENESIS PORT VERIFICATION"
echo "====================================="
echo ""

# Agreed port configuration
declare -A EXPECTED_PORTS=(
    ["5174"]="Frontend (React)"
    ["19081"]="API Gateway"
    ["8081"]="Orders Service"
    ["8082"]="Fleets Service"
    ["8083"]="Colonies Service"
    ["8084"]="Tech Service"
    ["8085"]="Market Service"
    ["8086"]="Health Dashboard"
    ["8090"]="Event Dispatcher"
    ["5433"]="PostgreSQL Database"
    ["4222"]="NATS Message Broker"
)

VIOLATIONS=0
TOTAL_PORTS=${#EXPECTED_PORTS[@]}
LISTENING_PORTS=0

echo "📋 CHECKING $TOTAL_PORTS CONFIGURED PORTS:"
echo ""

for port in "${!EXPECTED_PORTS[@]}"; do
    service="${EXPECTED_PORTS[$port]}"
    
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        echo "✅ Port $port: $service - LISTENING"
        ((LISTENING_PORTS++))
    else
        echo "❌ Port $port: $service - NOT LISTENING"
        ((VIOLATIONS++))
    fi
done

echo ""
echo "📊 SUMMARY:"
echo "  Total Configured Ports: $TOTAL_PORTS"
echo "  Listening Ports: $LISTENING_PORTS"
echo "  Missing Services: $VIOLATIONS"

if [ $VIOLATIONS -eq 0 ]; then
    echo ""
    echo "🎉 ALL PORTS CONFIGURED CORRECTLY!"
    echo "🔒 No port violations detected"
else
    echo ""
    echo "⚠️  $VIOLATIONS PORT VIOLATIONS DETECTED!"
    echo "⚠️  Some services are not running on expected ports"
    echo "⚠️  Use individual port scripts to diagnose issues"
fi

echo ""
echo "🚨 REMEMBER: NEVER change these port numbers without user approval!"
echo "🚨 If ports are busy, use kill-port.sh or ask user for guidance!"
