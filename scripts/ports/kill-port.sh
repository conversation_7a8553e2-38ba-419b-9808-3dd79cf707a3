#!/bin/bash

# 🚨 GALACTIC GENESIS PORT KILLER
# ONLY use after user approval!

PORT=$1

if [ -z "$PORT" ]; then
    echo "❌ Usage: $0 <PORT_NUMBER>"
    echo "Example: $0 5177"
    echo ""
    echo "⚠️  WARNING: This will kill processes using the specified port!"
    echo "⚠️  ONLY use with explicit user approval!"
    exit 1
fi

echo "🚨 ATTEMPTING TO FREE PORT $PORT"
echo "================================"

# Check if port is in use
if ! netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
    echo "✅ Port $PORT is already free"
    exit 0
fi

echo "🔍 Current processes using port $PORT:"
netstat -tlnp 2>/dev/null | grep ":$PORT " | while read line; do
    echo "  $line"
done

echo ""
echo "⚠️  WARNING: About to kill processes using port $PORT"
echo "⚠️  This action should only be performed with user approval!"
echo ""

# Get PIDs using the port
PIDS=$(netstat -tlnp 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1 | grep -v '^-$' | sort -u)

if [ -z "$PIDS" ]; then
    echo "❌ No PIDs found for port $PORT"
    exit 1
fi

echo "🎯 Found PIDs: $PIDS"
echo ""

for PID in $PIDS; do
    if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
        echo "🔫 Killing process $PID..."
        kill -9 $PID 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "✅ Process $PID killed successfully"
        else
            echo "❌ Failed to kill process $PID"
        fi
    fi
done

echo ""
echo "🔍 Verifying port $PORT is now free..."
sleep 1

if netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
    echo "❌ Port $PORT is still in use!"
    netstat -tlnp 2>/dev/null | grep ":$PORT "
else
    echo "✅ Port $PORT is now free!"
fi

echo ""
echo "🔒 REMEMBER: Always get user approval before killing processes!"
