#!/usr/bin/env python3
"""
Database Migration and Update System for Galactic Genesis
Safely updates local database with GAIA DR3 and NASA Exoplanet Archive data
"""

import os
import sys
import json
import psycopg2
import psycopg2.extras
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
import math
import numpy as np
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_migration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """Handles safe database updates with GAIA and NASA data"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.conn = None
        self.setup_database_connection()
        
    def setup_database_connection(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = False
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
    
    def create_backup_tables(self):
        """Create backup tables before migration"""
        logger.info("🔄 Creating backup tables...")
        
        backup_queries = [
            "DROP TABLE IF EXISTS stars_backup CASCADE;",
            "CREATE TABLE stars_backup AS SELECT * FROM stars;",
            "DROP TABLE IF EXISTS planets_backup CASCADE;", 
            "CREATE TABLE planets_backup AS SELECT * FROM planets;",
            "DROP TABLE IF EXISTS moons_backup CASCADE;",
            "CREATE TABLE moons_backup AS SELECT * FROM moons;"
        ]
        
        try:
            with self.conn.cursor() as cur:
                for query in backup_queries:
                    cur.execute(query)
                self.conn.commit()
            logger.info("✅ Backup tables created successfully")
        except Exception as e:
            logger.error(f"❌ Backup creation failed: {e}")
            self.conn.rollback()
            raise
    
    def apply_moons_migration(self):
        """Apply moons table migration"""
        logger.info("🌙 Applying moons table migration...")
        
        try:
            with open('db/sql/011_moons_table.sql', 'r') as f:
                migration_sql = f.read()
            
            with self.conn.cursor() as cur:
                cur.execute(migration_sql)
                self.conn.commit()
            
            logger.info("✅ Moons table migration applied successfully")
        except Exception as e:
            logger.error(f"❌ Moons migration failed: {e}")
            self.conn.rollback()
            raise
    
    def get_stellar_color(self, spectral_type: Optional[str]) -> str:
        """Get stellar color based on spectral type"""
        if not spectral_type:
            return '#ffffff'
        
        spec_class = spectral_type[0].upper()
        color_map = {
            'O': '#9bb0ff',  # Blue
            'B': '#aabfff',  # Blue-white
            'A': '#cad7ff',  # White
            'F': '#f8f7ff',  # Yellow-white
            'G': '#fff4ea',  # Yellow
            'K': '#ffd2a1',  # Orange
            'M': '#ffad51',  # Red
            'L': '#8b0000',  # Brown dwarf
            'T': '#4b0000',  # Brown dwarf
            'D': '#ffffff'   # White dwarf
        }
        return color_map.get(spec_class, '#ffffff')
    
    def convert_to_cartesian(self, ra_deg: float, dec_deg: float, distance_pc: float) -> Tuple[float, float, float]:
        """Convert RA/Dec/Distance to Cartesian coordinates"""
        ra_rad = math.radians(ra_deg)
        dec_rad = math.radians(dec_deg)
        
        x = distance_pc * math.cos(dec_rad) * math.cos(ra_rad)
        y = distance_pc * math.cos(dec_rad) * math.sin(ra_rad)
        z = distance_pc * math.sin(dec_rad)
        
        return x, y, z
    
    def update_stellar_data(self):
        """Update stellar data with GAIA DR3 information"""
        logger.info("⭐ Updating stellar data with GAIA DR3...")
        
        try:
            # Load comparison results
            with open('stellar_comparison_results.json', 'r') as f:
                comparison_results = json.load(f)
            
            # Load GAIA data
            with open('gaia_stellar_data.json', 'r') as f:
                gaia_data = json.load(f)
            
            gaia_dict = {star['source_id']: star for star in gaia_data}
            
            updates_made = 0
            additions_made = 0
            
            with self.conn.cursor() as cur:
                for result in comparison_results:
                    if result['recommended_action'] == 'update_from_auth' and result['authoritative_id']:
                        # Update existing star with GAIA data
                        gaia_star = gaia_dict.get(result['authoritative_id'])
                        if gaia_star:
                            stellar_color = self.get_stellar_color(gaia_star.get('spectral_type'))
                            x_pc, y_pc, z_pc = self.convert_to_cartesian(
                                gaia_star['ra'], gaia_star['dec'], gaia_star['distance_pc']
                            )
                            
                            update_query = """
                            UPDATE stars SET
                                src = 'gaia_dr3',
                                src_key = %s,
                                ra_deg = %s,
                                dec_deg = %s,
                                distance_ly = %s,
                                spectral_type = %s,
                                stellar_mass = %s,
                                stellar_radius = %s,
                                stellar_color = %s,
                                x_pc = %s,
                                y_pc = %s,
                                z_pc = %s,
                                updated_at = now()
                            WHERE star_id = %s
                            """
                            
                            cur.execute(update_query, (
                                gaia_star['source_id'],
                                gaia_star['ra'],
                                gaia_star['dec'],
                                gaia_star['distance_ly'],
                                gaia_star.get('spectral_type'),
                                gaia_star.get('stellar_mass'),
                                gaia_star.get('stellar_radius'),
                                stellar_color,
                                x_pc, y_pc, z_pc,
                                int(result['local_id'])
                            ))
                            updates_made += 1
                    
                    elif result['recommended_action'] == 'add_to_local' and result['authoritative_id']:
                        # Add new star from GAIA
                        gaia_star = gaia_dict.get(result['authoritative_id'])
                        if gaia_star and gaia_star['distance_ly'] <= 100.0:
                            stellar_color = self.get_stellar_color(gaia_star.get('spectral_type'))
                            x_pc, y_pc, z_pc = self.convert_to_cartesian(
                                gaia_star['ra'], gaia_star['dec'], gaia_star['distance_pc']
                            )
                            
                            insert_query = """
                            INSERT INTO stars (
                                src, src_key, name, ra_deg, dec_deg, distance_ly,
                                spectral_type, stellar_mass, stellar_radius, stellar_color,
                                x_pc, y_pc, z_pc
                            ) VALUES (
                                'gaia_dr3', %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            ) ON CONFLICT (src, src_key) DO NOTHING
                            """
                            
                            # Generate name from GAIA source ID
                            star_name = f"Gaia DR3 {gaia_star['source_id']}"
                            
                            cur.execute(insert_query, (
                                gaia_star['source_id'],
                                star_name,
                                gaia_star['ra'],
                                gaia_star['dec'],
                                gaia_star['distance_ly'],
                                gaia_star.get('spectral_type'),
                                gaia_star.get('stellar_mass'),
                                gaia_star.get('stellar_radius'),
                                stellar_color,
                                x_pc, y_pc, z_pc
                            ))
                            additions_made += 1
                
                self.conn.commit()
            
            logger.info(f"✅ Stellar data updated: {updates_made} updates, {additions_made} additions")
            
        except Exception as e:
            logger.error(f"❌ Stellar data update failed: {e}")
            self.conn.rollback()
            raise
    
    def update_planetary_data(self):
        """Update planetary data with NASA Exoplanet Archive information"""
        logger.info("🪐 Updating planetary data with NASA Exoplanet Archive...")
        
        try:
            # Load comparison results
            with open('planetary_comparison_results.json', 'r') as f:
                comparison_results = json.load(f)
            
            # Load NASA data
            with open('nasa_planet_data.json', 'r') as f:
                nasa_data = json.load(f)
            
            nasa_dict = {planet['pl_name']: planet for planet in nasa_data}
            
            updates_made = 0
            additions_made = 0
            
            with self.conn.cursor() as cur:
                for result in comparison_results:
                    if result['recommended_action'] == 'update_from_auth' and result['authoritative_id']:
                        # Update existing planet with NASA data
                        nasa_planet = nasa_dict.get(result['authoritative_id'])
                        if nasa_planet:
                            # Determine composition from mass and radius
                            composition = self.determine_planet_composition(
                                nasa_planet.get('pl_masse'), nasa_planet.get('pl_rade')
                            )
                            
                            update_query = """
                            UPDATE planets SET
                                src = 'nasa_exoplanet_archive',
                                src_key = %s,
                                name = %s,
                                mass_earth = %s,
                                radius_earth = %s,
                                sma_au = %s,
                                period_days = %s,
                                eccentricity = %s,
                                inclination_deg = %s,
                                eq_temp_k = %s,
                                composition = %s,
                                discovery_method = %s,
                                discovery_year = %s,
                                discovery_facility = %s,
                                updated_at = now()
                            WHERE planet_id = %s
                            """
                            
                            cur.execute(update_query, (
                                nasa_planet['pl_name'],
                                nasa_planet['pl_name'],
                                nasa_planet.get('pl_masse'),
                                nasa_planet.get('pl_rade'),
                                nasa_planet.get('pl_orbsmax'),
                                nasa_planet.get('pl_orbper'),
                                nasa_planet.get('pl_orbeccen'),
                                nasa_planet.get('pl_orbincl'),
                                nasa_planet.get('pl_eqt'),
                                composition,
                                nasa_planet.get('discoverymethod'),
                                nasa_planet.get('disc_year'),
                                nasa_planet.get('disc_facility'),
                                int(result['local_id'])
                            ))
                            updates_made += 1
                    
                    elif result['recommended_action'] == 'add_to_local' and result['authoritative_id']:
                        # Add new planet from NASA
                        nasa_planet = nasa_dict.get(result['authoritative_id'])
                        if nasa_planet:
                            # Find host star
                            star_query = """
                            SELECT star_id FROM stars 
                            WHERE ABS(ra_deg - %s) < 0.01 AND ABS(dec_deg - %s) < 0.01
                            ORDER BY (ABS(ra_deg - %s) + ABS(dec_deg - %s))
                            LIMIT 1
                            """
                            
                            cur.execute(star_query, (
                                nasa_planet['ra'], nasa_planet['dec'],
                                nasa_planet['ra'], nasa_planet['dec']
                            ))
                            
                            star_result = cur.fetchone()
                            if star_result:
                                star_id = star_result[0]
                                
                                composition = self.determine_planet_composition(
                                    nasa_planet.get('pl_masse'), nasa_planet.get('pl_rade')
                                )
                                
                                insert_query = """
                                INSERT INTO planets (
                                    star_id, src, src_key, name, mass_earth, radius_earth,
                                    sma_au, period_days, eccentricity, inclination_deg,
                                    eq_temp_k, composition, discovery_method, discovery_year,
                                    discovery_facility
                                ) VALUES (
                                    %s, 'nasa_exoplanet_archive', %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                ) ON CONFLICT (star_id, name) DO NOTHING
                                """
                                
                                cur.execute(insert_query, (
                                    star_id,
                                    nasa_planet['pl_name'],
                                    nasa_planet['pl_name'],
                                    nasa_planet.get('pl_masse'),
                                    nasa_planet.get('pl_rade'),
                                    nasa_planet.get('pl_orbsmax'),
                                    nasa_planet.get('pl_orbper'),
                                    nasa_planet.get('pl_orbeccen'),
                                    nasa_planet.get('pl_orbincl'),
                                    nasa_planet.get('pl_eqt'),
                                    composition,
                                    nasa_planet.get('discoverymethod'),
                                    nasa_planet.get('disc_year'),
                                    nasa_planet.get('disc_facility')
                                ))
                                additions_made += 1
                
                self.conn.commit()
            
            logger.info(f"✅ Planetary data updated: {updates_made} updates, {additions_made} additions")
            
        except Exception as e:
            logger.error(f"❌ Planetary data update failed: {e}")
            self.conn.rollback()
            raise
    
    def determine_planet_composition(self, mass_earth: Optional[float], radius_earth: Optional[float]) -> str:
        """Determine planet composition from mass and radius"""
        if not mass_earth or not radius_earth:
            return 'unknown'
        
        # Calculate density relative to Earth
        density_ratio = mass_earth / (radius_earth ** 3)
        
        if density_ratio > 0.8:  # Rocky like Earth or denser
            return 'rocky'
        elif density_ratio > 0.3:  # Between rocky and gas
            if radius_earth > 2.0:
                return 'sub_neptune'
            else:
                return 'super_earth'
        elif radius_earth > 4.0:  # Large and low density
            return 'gas_giant'
        else:
            return 'ice_giant'
    
    def add_solar_system_planets(self):
        """Add missing Solar System planets if not present"""
        logger.info("🌍 Adding Solar System planets...")
        
        solar_planets = [
            ('Mercury', 0.055, 0.383, 0.387, 87.97, 0.206, 7.0, 440),
            ('Venus', 0.815, 0.949, 0.723, 224.7, 0.007, 3.4, 737),
            ('Earth', 1.0, 1.0, 1.0, 365.26, 0.017, 0.0, 288),
            ('Mars', 0.107, 0.532, 1.524, 686.98, 0.094, 1.9, 210),
            ('Jupiter', 317.8, 11.21, 5.204, 4332.6, 0.049, 1.3, 165),
            ('Saturn', 95.2, 9.45, 9.582, 10759.2, 0.057, 2.5, 134),
            ('Uranus', 14.5, 4.01, 19.20, 30688.5, 0.046, 0.8, 76),
            ('Neptune', 17.1, 3.88, 30.05, 60182, 0.010, 1.8, 72)
        ]
        
        try:
            with self.conn.cursor() as cur:
                # Get Sol's star_id
                cur.execute("SELECT star_id FROM stars WHERE name = 'Sol'")
                sol_result = cur.fetchone()
                
                if sol_result:
                    sol_star_id = sol_result[0]
                    
                    for planet_data in solar_planets:
                        name, mass_earth, radius_earth, sma_au, period_days, eccentricity, inclination, temp = planet_data
                        
                        # Check if planet already exists
                        cur.execute(
                            "SELECT planet_id FROM planets WHERE star_id = %s AND name = %s",
                            (sol_star_id, name)
                        )
                        
                        if not cur.fetchone():
                            composition = 'gas_giant' if mass_earth > 10 else 'rocky'
                            
                            insert_query = """
                            INSERT INTO planets (
                                star_id, src, name, mass_earth, radius_earth, sma_au,
                                period_days, eccentricity, inclination_deg, eq_temp_k,
                                composition, discovery_year, discovery_method
                            ) VALUES (
                                %s, 'manual', %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                            """
                            
                            cur.execute(insert_query, (
                                sol_star_id, name, mass_earth, radius_earth, sma_au,
                                period_days, eccentricity, inclination, temp, composition,
                                None, 'Direct observation'
                            ))
                            
                            logger.info(f"✅ Added Solar System planet: {name}")
                
                self.conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Solar System planets addition failed: {e}")
            self.conn.rollback()
            raise
    
    def generate_migration_report(self) -> Dict:
        """Generate comprehensive migration report"""
        logger.info("📊 Generating migration report...")
        
        try:
            with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                # Count stars by source
                cur.execute("SELECT src, COUNT(*) as count FROM stars GROUP BY src ORDER BY count DESC")
                star_sources = cur.fetchall()
                
                # Count planets by source
                cur.execute("SELECT src, COUNT(*) as count FROM planets GROUP BY src ORDER BY count DESC")
                planet_sources = cur.fetchall()
                
                # Count moons
                cur.execute("SELECT COUNT(*) as count FROM moons")
                moon_count = cur.fetchone()['count']
                
                # Distance distribution
                cur.execute("""
                SELECT 
                    CASE 
                        WHEN distance_ly <= 10 THEN '0-10 ly'
                        WHEN distance_ly <= 25 THEN '10-25 ly'
                        WHEN distance_ly <= 50 THEN '25-50 ly'
                        WHEN distance_ly <= 100 THEN '50-100 ly'
                        ELSE '100+ ly'
                    END as distance_range,
                    COUNT(*) as count
                FROM stars 
                GROUP BY distance_range
                ORDER BY MIN(distance_ly)
                """)
                distance_distribution = cur.fetchall()
                
                report = {
                    'migration_timestamp': datetime.now().isoformat(),
                    'stellar_data': {
                        'total_stars': sum(row['count'] for row in star_sources),
                        'sources': {row['src']: row['count'] for row in star_sources},
                        'distance_distribution': {row['distance_range']: row['count'] for row in distance_distribution}
                    },
                    'planetary_data': {
                        'total_planets': sum(row['count'] for row in planet_sources),
                        'sources': {row['src']: row['count'] for row in planet_sources}
                    },
                    'moon_data': {
                        'total_moons': moon_count
                    }
                }
                
                return report
                
        except Exception as e:
            logger.error(f"❌ Report generation failed: {e}")
            raise

def main():
    """Main execution function"""
    db_config = {
        'host': 'localhost',
        'port': 5433,
        'database': 'gg',
        'user': 'gg',
        'password': 'ggpassword'
    }
    
    try:
        migrator = DatabaseMigrator(db_config)
        
        # Create backups
        migrator.create_backup_tables()
        
        # Apply moons migration
        migrator.apply_moons_migration()
        
        # Update data
        migrator.update_stellar_data()
        migrator.update_planetary_data()
        migrator.add_solar_system_planets()
        
        # Generate report
        report = migrator.generate_migration_report()
        
        with open('migration_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info("✅ Database migration completed successfully")
        logger.info(f"📈 Final report: {report}")
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
