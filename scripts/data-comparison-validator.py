#!/usr/bin/env python3
"""
Data Comparison and Validation System for Galactic Genesis
Compares local stellar/planetary data with GAIA DR3 and NASA Exoplanet Archive data
"""

import os
import sys
import json
import psycopg2
import psycopg2.extras
from typing import Dict, List, Optional, Tuple, Set
import logging
from dataclasses import dataclass
import math
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_comparison.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ComparisonResult:
    """Result of comparing local vs authoritative data"""
    local_id: str
    authoritative_id: str
    match_type: str  # 'exact', 'close', 'different', 'missing_local', 'missing_auth'
    discrepancies: List[str]
    confidence_score: float
    recommended_action: str  # 'keep_local', 'update_from_auth', 'manual_review'

class DataComparator:
    """Compares local database with authoritative sources"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.conn = None
        self.setup_database_connection()
        
    def setup_database_connection(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = False
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
    
    def load_local_stars(self) -> List[Dict]:
        """Load all stars from local database"""
        query = """
        SELECT star_id, name, ra_deg, dec_deg, distance_ly, spectral_type, 
               stellar_mass, stellar_radius, stellar_color, src, src_key,
               x_pc, y_pc, z_pc, created_at, updated_at
        FROM stars 
        ORDER BY distance_ly
        """
        
        with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute(query)
            return cur.fetchall()
    
    def load_local_planets(self) -> List[Dict]:
        """Load all planets from local database"""
        query = """
        SELECT p.planet_id, p.star_id, p.name, p.src, p.src_key,
               p.mass_earth, p.radius_earth, p.sma_au, p.period_days,
               p.eccentricity, p.inclination_deg, p.eq_temp_k,
               p.discovery_method, p.discovery_year, p.discovery_facility,
               s.name as star_name, s.ra_deg, s.dec_deg
        FROM planets p
        JOIN stars s ON p.star_id = s.star_id
        ORDER BY s.distance_ly, p.sma_au
        """
        
        with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute(query)
            return cur.fetchall()
    
    def load_gaia_data(self) -> List[Dict]:
        """Load GAIA data from JSON file"""
        try:
            with open('gaia_stellar_data.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("⚠️ GAIA data file not found")
            return []
    
    def load_nasa_data(self) -> List[Dict]:
        """Load NASA exoplanet data from JSON file"""
        try:
            with open('nasa_planet_data.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("⚠️ NASA planet data file not found")
            return []
    
    def calculate_angular_distance(self, ra1: float, dec1: float, ra2: float, dec2: float) -> float:
        """Calculate angular distance between two celestial coordinates in degrees"""
        ra1_rad = math.radians(ra1)
        dec1_rad = math.radians(dec1)
        ra2_rad = math.radians(ra2)
        dec2_rad = math.radians(dec2)
        
        # Haversine formula
        dra = ra2_rad - ra1_rad
        ddec = dec2_rad - dec1_rad
        
        a = (math.sin(ddec/2)**2 + 
             math.cos(dec1_rad) * math.cos(dec2_rad) * math.sin(dra/2)**2)
        c = 2 * math.asin(math.sqrt(a))
        
        return math.degrees(c)
    
    def find_stellar_matches(self, local_stars: List[Dict], gaia_stars: List[Dict]) -> List[ComparisonResult]:
        """Find matches between local and GAIA stellar data"""
        logger.info("🔍 Comparing local stellar data with GAIA DR3...")
        
        results = []
        matched_gaia_ids = set()
        
        for local_star in local_stars:
            best_match = None
            best_distance = float('inf')
            best_confidence = 0.0
            
            local_ra = local_star.get('ra_deg')
            local_dec = local_star.get('dec_deg')
            
            if not local_ra or not local_dec:
                continue
                
            # Find closest GAIA star by position
            for gaia_star in gaia_stars:
                if gaia_star['source_id'] in matched_gaia_ids:
                    continue
                    
                angular_dist = self.calculate_angular_distance(
                    local_ra, local_dec, gaia_star['ra'], gaia_star['dec']
                )
                
                if angular_dist < best_distance:
                    best_distance = angular_dist
                    best_match = gaia_star
            
            if best_match and best_distance < 0.01:  # Within 36 arcseconds
                matched_gaia_ids.add(best_match['source_id'])
                
                # Calculate confidence and find discrepancies
                confidence, discrepancies = self.compare_stellar_properties(local_star, best_match)
                
                # Determine recommended action
                if confidence > 0.9:
                    action = 'keep_local'
                elif confidence > 0.7:
                    action = 'update_from_auth'
                else:
                    action = 'manual_review'
                
                results.append(ComparisonResult(
                    local_id=str(local_star['star_id']),
                    authoritative_id=best_match['source_id'],
                    match_type='close' if best_distance > 0.001 else 'exact',
                    discrepancies=discrepancies,
                    confidence_score=confidence,
                    recommended_action=action
                ))
            else:
                # No match found
                results.append(ComparisonResult(
                    local_id=str(local_star['star_id']),
                    authoritative_id='',
                    match_type='missing_auth',
                    discrepancies=['No GAIA match found'],
                    confidence_score=0.0,
                    recommended_action='manual_review'
                ))
        
        # Find GAIA stars not in local database
        for gaia_star in gaia_stars:
            if gaia_star['source_id'] not in matched_gaia_ids:
                results.append(ComparisonResult(
                    local_id='',
                    authoritative_id=gaia_star['source_id'],
                    match_type='missing_local',
                    discrepancies=['Star not in local database'],
                    confidence_score=1.0,
                    recommended_action='add_to_local'
                ))
        
        logger.info(f"✅ Stellar comparison complete: {len(results)} comparisons")
        return results
    
    def compare_stellar_properties(self, local_star: Dict, gaia_star: Dict) -> Tuple[float, List[str]]:
        """Compare properties between local and GAIA stellar data"""
        discrepancies = []
        confidence_factors = []
        
        # Distance comparison
        local_dist = local_star.get('distance_ly', 0)
        gaia_dist = gaia_star.get('distance_ly', 0)
        if local_dist and gaia_dist:
            dist_diff = abs(local_dist - gaia_dist) / max(local_dist, gaia_dist)
            if dist_diff > 0.1:  # 10% difference
                discrepancies.append(f"Distance: local={local_dist:.2f}ly, GAIA={gaia_dist:.2f}ly")
            confidence_factors.append(max(0, 1 - dist_diff))
        
        # Spectral type comparison
        local_spec = local_star.get('spectral_type', '')
        gaia_spec = gaia_star.get('spectral_type', '')
        if local_spec and gaia_spec:
            if local_spec[0] != gaia_spec[0]:  # Different spectral class
                discrepancies.append(f"Spectral type: local={local_spec}, GAIA={gaia_spec}")
                confidence_factors.append(0.5)
            else:
                confidence_factors.append(0.9)
        
        # Mass comparison
        local_mass = local_star.get('stellar_mass')
        gaia_mass = gaia_star.get('stellar_mass')
        if local_mass and gaia_mass:
            mass_diff = abs(local_mass - gaia_mass) / max(local_mass, gaia_mass)
            if mass_diff > 0.2:  # 20% difference
                discrepancies.append(f"Mass: local={local_mass:.2f}M☉, GAIA={gaia_mass:.2f}M☉")
            confidence_factors.append(max(0, 1 - mass_diff))
        
        # Calculate overall confidence
        confidence = np.mean(confidence_factors) if confidence_factors else 0.5
        
        return confidence, discrepancies
    
    def find_planetary_matches(self, local_planets: List[Dict], nasa_planets: List[Dict]) -> List[ComparisonResult]:
        """Find matches between local and NASA planetary data"""
        logger.info("🔍 Comparing local planetary data with NASA Exoplanet Archive...")
        
        results = []
        matched_nasa_names = set()
        
        for local_planet in local_planets:
            best_match = None
            best_confidence = 0.0
            
            local_name = local_planet.get('name', '').lower()
            local_star_ra = local_planet.get('ra_deg')
            local_star_dec = local_planet.get('dec_deg')
            
            # Try name matching first
            for nasa_planet in nasa_planets:
                nasa_name = nasa_planet.get('pl_name', '').lower()
                
                if nasa_name in matched_nasa_names:
                    continue
                
                # Check for name similarity
                if local_name and nasa_name and (
                    local_name == nasa_name or 
                    local_name in nasa_name or 
                    nasa_name in local_name
                ):
                    best_match = nasa_planet
                    best_confidence = 0.9
                    break
                
                # Check for positional match
                if local_star_ra and local_star_dec:
                    angular_dist = self.calculate_angular_distance(
                        local_star_ra, local_star_dec,
                        nasa_planet['ra'], nasa_planet['dec']
                    )
                    if angular_dist < 0.01:  # Same star system
                        confidence = 0.7
                        if confidence > best_confidence:
                            best_match = nasa_planet
                            best_confidence = confidence
            
            if best_match:
                matched_nasa_names.add(best_match['pl_name'])
                
                # Find discrepancies
                discrepancies = self.compare_planetary_properties(local_planet, best_match)
                
                # Determine action
                if best_confidence > 0.8 and len(discrepancies) < 3:
                    action = 'update_from_auth'
                elif best_confidence > 0.6:
                    action = 'manual_review'
                else:
                    action = 'keep_local'
                
                results.append(ComparisonResult(
                    local_id=str(local_planet['planet_id']),
                    authoritative_id=best_match['pl_name'],
                    match_type='exact' if best_confidence > 0.8 else 'close',
                    discrepancies=discrepancies,
                    confidence_score=best_confidence,
                    recommended_action=action
                ))
            else:
                results.append(ComparisonResult(
                    local_id=str(local_planet['planet_id']),
                    authoritative_id='',
                    match_type='missing_auth',
                    discrepancies=['No NASA match found'],
                    confidence_score=0.0,
                    recommended_action='manual_review'
                ))
        
        # Find NASA planets not in local database
        for nasa_planet in nasa_planets:
            if nasa_planet['pl_name'] not in matched_nasa_names:
                results.append(ComparisonResult(
                    local_id='',
                    authoritative_id=nasa_planet['pl_name'],
                    match_type='missing_local',
                    discrepancies=['Planet not in local database'],
                    confidence_score=1.0,
                    recommended_action='add_to_local'
                ))
        
        logger.info(f"✅ Planetary comparison complete: {len(results)} comparisons")
        return results
    
    def compare_planetary_properties(self, local_planet: Dict, nasa_planet: Dict) -> List[str]:
        """Compare properties between local and NASA planetary data"""
        discrepancies = []
        
        # Mass comparison
        local_mass = local_planet.get('mass_earth')
        nasa_mass = nasa_planet.get('pl_masse')
        if local_mass and nasa_mass:
            mass_diff = abs(local_mass - nasa_mass) / max(local_mass, nasa_mass)
            if mass_diff > 0.2:
                discrepancies.append(f"Mass: local={local_mass:.2f}M⊕, NASA={nasa_mass:.2f}M⊕")
        
        # Radius comparison
        local_radius = local_planet.get('radius_earth')
        nasa_radius = nasa_planet.get('pl_rade')
        if local_radius and nasa_radius:
            radius_diff = abs(local_radius - nasa_radius) / max(local_radius, nasa_radius)
            if radius_diff > 0.2:
                discrepancies.append(f"Radius: local={local_radius:.2f}R⊕, NASA={nasa_radius:.2f}R⊕")
        
        # Orbital period comparison
        local_period = local_planet.get('period_days')
        nasa_period = nasa_planet.get('pl_orbper')
        if local_period and nasa_period:
            period_diff = abs(local_period - nasa_period) / max(local_period, nasa_period)
            if period_diff > 0.1:
                discrepancies.append(f"Period: local={local_period:.2f}d, NASA={nasa_period:.2f}d")
        
        return discrepancies
    
    def generate_comparison_report(self, stellar_results: List[ComparisonResult], 
                                 planetary_results: List[ComparisonResult]) -> Dict:
        """Generate comprehensive comparison report"""
        report = {
            'stellar_analysis': {
                'total_comparisons': len(stellar_results),
                'exact_matches': len([r for r in stellar_results if r.match_type == 'exact']),
                'close_matches': len([r for r in stellar_results if r.match_type == 'close']),
                'missing_local': len([r for r in stellar_results if r.match_type == 'missing_local']),
                'missing_auth': len([r for r in stellar_results if r.match_type == 'missing_auth']),
                'update_recommended': len([r for r in stellar_results if r.recommended_action == 'update_from_auth']),
                'manual_review': len([r for r in stellar_results if r.recommended_action == 'manual_review'])
            },
            'planetary_analysis': {
                'total_comparisons': len(planetary_results),
                'exact_matches': len([r for r in planetary_results if r.match_type == 'exact']),
                'close_matches': len([r for r in planetary_results if r.match_type == 'close']),
                'missing_local': len([r for r in planetary_results if r.match_type == 'missing_local']),
                'missing_auth': len([r for r in planetary_results if r.match_type == 'missing_auth']),
                'update_recommended': len([r for r in planetary_results if r.recommended_action == 'update_from_auth']),
                'manual_review': len([r for r in planetary_results if r.recommended_action == 'manual_review'])
            }
        }
        
        return report

def main():
    """Main execution function"""
    db_config = {
        'host': 'localhost',
        'port': 5433,
        'database': 'gg',
        'user': 'gg',
        'password': 'ggpassword'
    }
    
    try:
        comparator = DataComparator(db_config)
        
        # Load all data
        local_stars = comparator.load_local_stars()
        local_planets = comparator.load_local_planets()
        gaia_stars = comparator.load_gaia_data()
        nasa_planets = comparator.load_nasa_data()
        
        logger.info(f"📊 Loaded: {len(local_stars)} local stars, {len(gaia_stars)} GAIA stars")
        logger.info(f"📊 Loaded: {len(local_planets)} local planets, {len(nasa_planets)} NASA planets")
        
        # Perform comparisons
        stellar_results = comparator.find_stellar_matches(local_stars, gaia_stars)
        planetary_results = comparator.find_planetary_matches(local_planets, nasa_planets)
        
        # Generate report
        report = comparator.generate_comparison_report(stellar_results, planetary_results)
        
        # Save results
        with open('stellar_comparison_results.json', 'w') as f:
            json.dump([vars(r) for r in stellar_results], f, indent=2, default=str)
            
        with open('planetary_comparison_results.json', 'w') as f:
            json.dump([vars(r) for r in planetary_results], f, indent=2, default=str)
            
        with open('comparison_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info("✅ Comparison complete. Results saved to JSON files.")
        logger.info(f"📈 Report: {report}")
        
    except Exception as e:
        logger.error(f"❌ Comparison failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
