#!/bin/bash

# Comprehensive test runner for Materials System
# Runs smoke tests, Cypress E2E tests, and Playwright cross-browser tests

set -e

echo "🧪 Galactic Genesis - Materials System Test Suite"
echo "================================================="

# Configuration
FRONTEND_URL="${FRONTEND_URL:-http://localhost:5174}"
API_BASE_URL="${API_BASE_URL:-http://localhost:19081}"
MATERIALS_SVC_URL="${MATERIALS_SVC_URL:-http://localhost:8086}"

# Test flags
RUN_SMOKE="${RUN_SMOKE:-true}"
RUN_CYPRESS="${RUN_CYPRESS:-true}"
RUN_PLAYWRIGHT="${RUN_PLAYWRIGHT:-true}"
HEADLESS="${HEADLESS:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

log() {
    echo -e "${2}${1}${NC}"
}

# Check if services are running
check_services() {
    log "🔍 Checking service availability..." $BLUE
    
    # Check frontend
    if curl -s "$FRONTEND_URL" >/dev/null; then
        log "✅ Frontend is running at $FRONTEND_URL" $GREEN
    else
        log "❌ Frontend is not running at $FRONTEND_URL" $RED
        log "💡 Start with: cd frontend && npm run dev" $YELLOW
        return 1
    fi
    
    # Check API Gateway
    if curl -s "$API_BASE_URL/v1/health" >/dev/null; then
        log "✅ API Gateway is running at $API_BASE_URL" $GREEN
    else
        log "❌ API Gateway is not running at $API_BASE_URL" $RED
        log "💡 Start with: cd services/api-gateway && npm start" $YELLOW
        return 1
    fi
    
    # Check Materials Service (optional)
    if curl -s "$MATERIALS_SVC_URL/health" >/dev/null; then
        log "✅ Materials Service is running at $MATERIALS_SVC_URL" $GREEN
    else
        log "⚠️  Materials Service is not running at $MATERIALS_SVC_URL" $YELLOW
        log "💡 Start with: cd services/materials-svc && npm start" $YELLOW
    fi
    
    return 0
}

# Run smoke tests
run_smoke_tests() {
    log "\n🔥 Running Smoke Tests..." $BLUE
    log "=========================" $BLUE
    
    if [ ! -f "tests/smoke/materials-system-smoke.js" ]; then
        log "❌ Materials smoke test file not found" $RED
        return 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "📦 Installing dependencies..." $YELLOW
        npm install
    fi
    
    # Run smoke tests
    if node tests/smoke/materials-system-smoke.js; then
        log "✅ Smoke tests passed!" $GREEN
        return 0
    else
        log "❌ Smoke tests failed!" $RED
        return 1
    fi
}

# Run Cypress E2E tests
run_cypress_tests() {
    log "\n🌲 Running Cypress E2E Tests..." $BLUE
    log "===============================" $BLUE
    
    # Check if Cypress is installed
    if ! command -v npx >/dev/null 2>&1; then
        log "❌ npx not found. Please install Node.js" $RED
        return 1
    fi
    
    if [ ! -f "cypress/e2e/materials-system.cy.js" ]; then
        log "❌ Cypress test file not found" $RED
        return 1
    fi
    
    # Install Cypress if not already installed
    if [ ! -d "node_modules/cypress" ]; then
        log "📦 Installing Cypress..." $YELLOW
        npm install cypress --save-dev
    fi
    
    # Run Cypress tests
    if [ "$HEADLESS" = "true" ]; then
        log "🤖 Running Cypress in headless mode..." $CYAN
        if npx cypress run --spec "cypress/e2e/materials-system.cy.js" --browser chrome; then
            log "✅ Cypress tests passed!" $GREEN
            return 0
        else
            log "❌ Cypress tests failed!" $RED
            return 1
        fi
    else
        log "🖥️  Opening Cypress in interactive mode..." $CYAN
        npx cypress open --e2e
        return 0
    fi
}

# Run Playwright tests
run_playwright_tests() {
    log "\n🎭 Running Playwright Cross-Browser Tests..." $BLUE
    log "===========================================" $BLUE
    
    # Check if Playwright is installed
    if [ ! -d "node_modules/@playwright" ]; then
        log "📦 Installing Playwright..." $YELLOW
        npm install @playwright/test
        npx playwright install
    fi
    
    if [ ! -f "tests/playwright/materials-system.spec.js" ]; then
        log "❌ Playwright test file not found" $RED
        return 1
    fi
    
    # Run Playwright tests
    if [ "$HEADLESS" = "true" ]; then
        log "🤖 Running Playwright in headless mode..." $CYAN
        if npx playwright test tests/playwright/materials-system.spec.js; then
            log "✅ Playwright tests passed!" $GREEN
            return 0
        else
            log "❌ Playwright tests failed!" $RED
            log "💡 View report with: npx playwright show-report" $YELLOW
            return 1
        fi
    else
        log "🖥️  Running Playwright with UI..." $CYAN
        npx playwright test tests/playwright/materials-system.spec.js --ui
        return 0
    fi
}

# Generate test report
generate_report() {
    log "\n📊 Generating Test Report..." $BLUE
    log "============================" $BLUE
    
    REPORT_DIR="test-results"
    mkdir -p "$REPORT_DIR"
    
    REPORT_FILE="$REPORT_DIR/materials-system-test-report.md"
    
    cat > "$REPORT_FILE" << EOF
# Materials System Test Report

**Generated:** $(date)
**Test Suite:** Galactic Genesis Materials System

## Test Configuration
- Frontend URL: $FRONTEND_URL
- API Base URL: $API_BASE_URL
- Materials Service URL: $MATERIALS_SVC_URL
- Headless Mode: $HEADLESS

## Test Results Summary

| Test Type | Status | Details |
|-----------|--------|---------|
| Smoke Tests | $SMOKE_STATUS | Basic functionality and API connectivity |
| Cypress E2E | $CYPRESS_STATUS | End-to-end user workflows |
| Playwright Cross-Browser | $PLAYWRIGHT_STATUS | Multi-browser compatibility |

## Test Coverage

### Smoke Tests
- ✅ Materials Service Health Check
- ✅ API Gateway Endpoints
- ✅ Database Schema Validation
- ✅ Material Distribution
- ✅ Order Creation

### Cypress E2E Tests
- ✅ Materials Management UI
- ✅ Material Categories Display
- ✅ Deposits Table Functionality
- ✅ Station Construction Workflow
- ✅ Form Validation
- ✅ Error Handling
- ✅ Responsive Design

### Playwright Cross-Browser Tests
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari/WebKit
- ✅ Mobile Chrome
- ✅ Mobile Safari
- ✅ Microsoft Edge

## Recommendations

EOF

    if [ "$SMOKE_STATUS" = "❌ FAILED" ]; then
        echo "- Fix smoke test failures before proceeding with E2E tests" >> "$REPORT_FILE"
    fi
    
    if [ "$CYPRESS_STATUS" = "❌ FAILED" ]; then
        echo "- Review Cypress test failures for UI/UX issues" >> "$REPORT_FILE"
    fi
    
    if [ "$PLAYWRIGHT_STATUS" = "❌ FAILED" ]; then
        echo "- Check cross-browser compatibility issues" >> "$REPORT_FILE"
    fi
    
    echo "" >> "$REPORT_FILE"
    echo "For detailed test results, check the individual test outputs and artifacts in the test-results directory." >> "$REPORT_FILE"
    
    log "📄 Test report generated: $REPORT_FILE" $GREEN
}

# Main execution
main() {
    log "🚀 Starting Materials System Test Suite..." $BLUE
    
    # Check services first
    if ! check_services; then
        log "❌ Service check failed. Please start required services." $RED
        exit 1
    fi
    
    # Initialize status variables
    SMOKE_STATUS="⏭️  SKIPPED"
    CYPRESS_STATUS="⏭️  SKIPPED"
    PLAYWRIGHT_STATUS="⏭️  SKIPPED"
    
    OVERALL_SUCCESS=true
    
    # Run smoke tests
    if [ "$RUN_SMOKE" = "true" ]; then
        if run_smoke_tests; then
            SMOKE_STATUS="✅ PASSED"
        else
            SMOKE_STATUS="❌ FAILED"
            OVERALL_SUCCESS=false
        fi
    fi
    
    # Run Cypress tests
    if [ "$RUN_CYPRESS" = "true" ]; then
        if run_cypress_tests; then
            CYPRESS_STATUS="✅ PASSED"
        else
            CYPRESS_STATUS="❌ FAILED"
            OVERALL_SUCCESS=false
        fi
    fi
    
    # Run Playwright tests
    if [ "$RUN_PLAYWRIGHT" = "true" ]; then
        if run_playwright_tests; then
            PLAYWRIGHT_STATUS="✅ PASSED"
        else
            PLAYWRIGHT_STATUS="❌ FAILED"
            OVERALL_SUCCESS=false
        fi
    fi
    
    # Generate report
    generate_report
    
    # Final summary
    log "\n🏁 Test Suite Complete!" $BLUE
    log "========================" $BLUE
    log "Smoke Tests: $SMOKE_STATUS" $NC
    log "Cypress E2E: $CYPRESS_STATUS" $NC
    log "Playwright: $PLAYWRIGHT_STATUS" $NC
    
    if [ "$OVERALL_SUCCESS" = true ]; then
        log "\n🎉 All enabled tests passed!" $GREEN
        exit 0
    else
        log "\n💥 Some tests failed. Check the output above for details." $RED
        exit 1
    fi
}

# Handle command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-smoke)
            RUN_SMOKE="false"
            shift
            ;;
        --no-cypress)
            RUN_CYPRESS="false"
            shift
            ;;
        --no-playwright)
            RUN_PLAYWRIGHT="false"
            shift
            ;;
        --headed)
            HEADLESS="false"
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --no-smoke      Skip smoke tests"
            echo "  --no-cypress    Skip Cypress E2E tests"
            echo "  --no-playwright Skip Playwright tests"
            echo "  --headed        Run tests in headed mode (with browser UI)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            log "Unknown option: $1" $RED
            exit 1
            ;;
    esac
done

# Run main function
main
