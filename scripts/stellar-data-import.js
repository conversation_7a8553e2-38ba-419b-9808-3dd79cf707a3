#!/usr/bin/env node

/**
 * Stellar Data Import Pipeline
 * Downloads and imports comprehensive stellar data from multiple authoritative sources
 * 
 * Sources:
 * 1. HYG Database (Hipparcos + Yale + Gliese) - nearby stars
 * 2. NASA Exoplanet Archive - confirmed exoplanets
 * 3. JPL Solar System Data - moons and planetary data
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const zlib = require('zlib');
const { Pool } = require('pg');
const csv = require('csv-parser');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5433,
  database: 'gg',
  user: 'gg',
  password: 'ggpassword'
});

// Configuration
const CONFIG = {
  MAX_DISTANCE_LY: 50,
  DATA_DIR: path.join(__dirname, '../data/stellar'),
  SOURCES: {
    // Use a smaller, more reliable dataset for now
    NEARBY_STARS: 'https://raw.githubusercontent.com/astronexus/HYG-Database/master/hygdata_v3.csv',
    NASA_EXOPLANETS: 'https://exoplanetarchive.ipac.caltech.edu/TAP/sync?query=select+*+from+ps&format=csv',
    NASA_COMPOSITE: 'https://exoplanetarchive.ipac.caltech.edu/TAP/sync?query=select+*+from+pscomppars&format=csv'
  }
};

// Ensure data directory exists
if (!fs.existsSync(CONFIG.DATA_DIR)) {
  fs.mkdirSync(CONFIG.DATA_DIR, { recursive: true });
}

/**
 * Download file from URL
 */
async function downloadFile(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(CONFIG.DATA_DIR, filename);
    const file = fs.createWriteStream(filePath);
    
    console.log(`📥 Downloading ${filename}...`);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded ${filename}`);
        resolve(filePath);
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete partial file
      reject(err);
    });
  });
}

/**
 * Convert celestial coordinates to 3D Cartesian
 */
function celestialToCartesian(ra_deg, dec_deg, distance_ly) {
  if (!ra_deg || !dec_deg || !distance_ly) return { x: 0, y: 0, z: 0 };
  
  const ra_rad = ra_deg * Math.PI / 180;
  const dec_rad = dec_deg * Math.PI / 180;
  
  // Convert to parsecs for better scaling (1 ly = 0.306601 pc)
  const distance_pc = distance_ly * 0.306601;
  
  // Standard astronomical coordinate conversion
  const x = distance_pc * Math.cos(dec_rad) * Math.cos(ra_rad);
  const y = distance_pc * Math.cos(dec_rad) * Math.sin(ra_rad);
  const z = distance_pc * Math.sin(dec_rad);
  
  return { x, y, z };
}

/**
 * Get stellar color from spectral type
 */
function getSpectralColor(spectralType) {
  if (!spectralType) return '#ffffff';
  
  const type = spectralType.charAt(0).toUpperCase();
  const colors = {
    'O': '#9bb0ff',  // Blue
    'B': '#aabfff',  // Blue-white  
    'A': '#cad7ff',  // White
    'F': '#f8f7ff',  // Yellow-white
    'G': '#fff4ea',  // Yellow (like our Sun)
    'K': '#ffd2a1',  // Orange
    'M': '#ffad51',  // Red
    'L': '#ff6600',  // Brown dwarf
    'T': '#cc3300',  // Brown dwarf
    'Y': '#990000'   // Brown dwarf
  };
  
  return colors[type] || '#ffffff';
}

/**
 * Clean and deduplicate existing stellar data
 */
async function cleanExistingData() {
  console.log('🧹 Cleaning existing stellar data...');
  
  try {
    // Remove duplicates based on name and coordinates
    await pool.query(`
      DELETE FROM stars s1 
      USING stars s2 
      WHERE s1.star_id > s2.star_id 
        AND s1.name = s2.name 
        AND ABS(s1.ra_deg - s2.ra_deg) < 0.01 
        AND ABS(s1.dec_deg - s2.dec_deg) < 0.01
    `);
    
    // Remove planets for deleted stars
    await pool.query(`
      DELETE FROM planets 
      WHERE star_id NOT IN (SELECT star_id FROM stars)
    `);
    
    console.log('✅ Cleaned existing data');
  } catch (error) {
    console.error('❌ Error cleaning data:', error);
    throw error;
  }
}

/**
 * Import HYG stellar database
 */
async function importHYGData() {
  console.log('🌟 Importing nearby stars database...');

  const hygFile = path.join(CONFIG.DATA_DIR, 'hygdata_v3.csv');

  // Download if not exists
  if (!fs.existsSync(hygFile)) {
    await downloadFile(CONFIG.SOURCES.NEARBY_STARS, 'hygdata_v3.csv');
  }
  
  return new Promise((resolve, reject) => {
    const stars = [];
    let processed = 0;
    
    fs.createReadStream(hygFile)
      .pipe(csv())
      .on('data', (row) => {
        processed++;
        
        // Parse distance and filter by range
        const distance = parseFloat(row.dist);
        if (!distance || distance > CONFIG.MAX_DISTANCE_LY) return;
        
        // Parse coordinates
        const ra = parseFloat(row.ra);
        const dec = parseFloat(row.dec);
        if (isNaN(ra) || isNaN(dec)) return;
        
        // Calculate 3D coordinates
        const coords = celestialToCartesian(ra * 15, dec, distance); // RA in hours, convert to degrees
        
        const star = {
          src: 'hyg',
          src_key: row.id || row.hip || row.hd,
          name: row.proper || row.bf || `HYG ${row.id}`,
          catalog_name: row.bf || null,
          ra_deg: ra * 15, // Convert hours to degrees
          dec_deg: dec,
          distance_ly: distance,
          x_pc: coords.x,
          y_pc: coords.y,
          z_pc: coords.z,
          parallax_mas: parseFloat(row.parallax) || null,
          pm_ra_masyr: parseFloat(row.pmra) || null,
          pm_dec_masyr: parseFloat(row.pmdec) || null,
          mag_g: parseFloat(row.mag) || null,
          mag_v: parseFloat(row.mag) || null,
          color_bv: parseFloat(row.ci) || null,
          spectral_type: row.spect || null,
          absolute_magnitude: parseFloat(row.absmag) || null,
          luminosity_solar: row.lum ? Math.pow(10, parseFloat(row.lum)) : null,
          hipparcos_id: parseInt(row.hip) || null,
          henry_draper_id: parseInt(row.hd) || null,
          gliese_id: row.gl || null,
          proper_name: row.proper || null,
          discovery_status: 'known',
          is_colonizable: true,
          stellar_color: getSpectralColor(row.spect)
        };
        
        stars.push(star);
        
        if (processed % 1000 === 0) {
          console.log(`📊 Processed ${processed} HYG records, found ${stars.length} nearby stars`);
        }
      })
      .on('end', async () => {
        console.log(`✅ Processed ${processed} HYG records, importing ${stars.length} nearby stars`);
        
        try {
          // Batch insert stars
          for (const star of stars) {
            await pool.query(`
              INSERT INTO stars (
                src, src_key, name, catalog_name, ra_deg, dec_deg, distance_ly,
                x_pc, y_pc, z_pc, parallax_mas, pm_ra_masyr, pm_dec_masyr,
                mag_g, mag_v, color_bv, spectral_type, absolute_magnitude,
                luminosity_solar, hipparcos_id, henry_draper_id, gliese_id,
                proper_name, discovery_status, is_colonizable
              ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
                $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25
              ) ON CONFLICT (src, src_key) DO UPDATE SET
                name = EXCLUDED.name,
                ra_deg = EXCLUDED.ra_deg,
                dec_deg = EXCLUDED.dec_deg,
                distance_ly = EXCLUDED.distance_ly,
                x_pc = EXCLUDED.x_pc,
                y_pc = EXCLUDED.y_pc,
                z_pc = EXCLUDED.z_pc,
                updated_at = now()
            `, [
              star.src, star.src_key, star.name, star.catalog_name,
              star.ra_deg, star.dec_deg, star.distance_ly,
              star.x_pc, star.y_pc, star.z_pc,
              star.parallax_mas, star.pm_ra_masyr, star.pm_dec_masyr,
              star.mag_g, star.mag_v, star.color_bv, star.spectral_type,
              star.absolute_magnitude, star.luminosity_solar,
              star.hipparcos_id, star.henry_draper_id, star.gliese_id,
              star.proper_name, star.discovery_status, star.is_colonizable
            ]);
          }
          
          console.log(`✅ Imported ${stars.length} stars from HYG database`);
          resolve(stars.length);
        } catch (error) {
          console.error('❌ Error importing HYG data:', error);
          reject(error);
        }
      })
      .on('error', reject);
  });
}

/**
 * Import NASA exoplanet data
 */
async function importNASAExoplanets() {
  console.log('🪐 Importing NASA exoplanet data...');
  
  const exoFile = path.join(CONFIG.DATA_DIR, 'nasa_exoplanets.csv');
  
  // Download if not exists
  if (!fs.existsSync(exoFile)) {
    await downloadFile(CONFIG.SOURCES.NASA_EXOPLANETS, 'nasa_exoplanets.csv');
  }
  
  return new Promise((resolve, reject) => {
    const exoplanets = [];
    let processed = 0;
    
    fs.createReadStream(exoFile)
      .pipe(csv())
      .on('data', (row) => {
        processed++;
        
        // Filter by host star distance
        const distance = parseFloat(row.sy_dist);
        if (!distance || distance > CONFIG.MAX_DISTANCE_LY) return;
        
        const exoplanet = {
          nasa_name: row.pl_name,
          host_star: row.hostname,
          discovery_method: row.discoverymethod,
          discovery_year: parseInt(row.disc_year) || null,
          orbital_period_days: parseFloat(row.pl_orbper) || null,
          mass_earth: parseFloat(row.pl_masse) || null,
          radius_earth: parseFloat(row.pl_rade) || null,
          semi_major_axis_au: parseFloat(row.pl_orbsmax) || null,
          eccentricity: parseFloat(row.pl_orbeccen) || null,
          equilibrium_temp_k: parseFloat(row.pl_eqt) || null,
          host_distance_ly: distance,
          host_ra_deg: parseFloat(row.ra),
          host_dec_deg: parseFloat(row.dec),
          confirmed: row.pl_controv_flag !== '1'
        };
        
        exoplanets.push(exoplanet);
        
        if (processed % 100 === 0) {
          console.log(`📊 Processed ${processed} exoplanet records, found ${exoplanets.length} nearby`);
        }
      })
      .on('end', () => {
        console.log(`✅ Found ${exoplanets.length} exoplanets in nearby systems`);
        resolve(exoplanets.length);
      })
      .on('error', reject);
  });
}

/**
 * Main import function
 */
async function main() {
  console.log('🚀 Starting stellar data import pipeline...');
  console.log(`📏 Maximum distance: ${CONFIG.MAX_DISTANCE_LY} light years`);
  
  try {
    // Step 1: Clean existing data
    await cleanExistingData();
    
    // Step 2: Import HYG stellar database
    const starCount = await importHYGData();
    
    // Step 3: Import NASA exoplanets
    const exoplanetCount = await importNASAExoplanets();
    
    // Step 4: Update statistics
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total_stars,
        COUNT(*) FILTER (WHERE distance_ly <= 10) as stars_10ly,
        COUNT(*) FILTER (WHERE distance_ly <= 25) as stars_25ly,
        COUNT(*) FILTER (WHERE spectral_type LIKE 'G%') as g_type_stars,
        COUNT(*) FILTER (WHERE spectral_type LIKE 'M%') as m_type_stars,
        AVG(distance_ly) as avg_distance
      FROM stars 
      WHERE distance_ly <= $1
    `, [CONFIG.MAX_DISTANCE_LY]);
    
    console.log('\n🌌 STELLAR DATABASE IMPORT COMPLETE!');
    console.log('=====================================');
    console.log(`✅ Total stars imported: ${stats.rows[0].total_stars}`);
    console.log(`✅ Stars within 10 ly: ${stats.rows[0].stars_10ly}`);
    console.log(`✅ Stars within 25 ly: ${stats.rows[0].stars_25ly}`);
    console.log(`✅ G-type stars (Sun-like): ${stats.rows[0].g_type_stars}`);
    console.log(`✅ M-type stars (Red dwarfs): ${stats.rows[0].m_type_stars}`);
    console.log(`✅ Average distance: ${parseFloat(stats.rows[0].avg_distance).toFixed(2)} ly`);
    console.log(`✅ Exoplanets found: ${exoplanetCount}`);
    
  } catch (error) {
    console.error('❌ Import failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, celestialToCartesian, getSpectralColor };
