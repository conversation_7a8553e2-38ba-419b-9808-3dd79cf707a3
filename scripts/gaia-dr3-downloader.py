#!/usr/bin/env python3
"""
GAIA DR3 Data Downloader for Galactic Genesis
Downloads all stars within 100 light years from GAIA DR3 and NASA Exoplanet Archive
"""

import os
import sys
import time
import json
import requests
import psycopg2
import psycopg2.extras
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from astroquery.gaia import Gaia
from astroquery.ipac.nexsci.nasa_exoplanet_archive import NasaExoplanetArchive
import pandas as pd
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gaia_download.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class StellarData:
    """Data structure for stellar information from GAIA DR3"""
    source_id: str
    ra: float
    dec: float
    parallax: float
    distance_pc: float
    distance_ly: float
    pmra: Optional[float]
    pmdec: Optional[float]
    radial_velocity: Optional[float]
    phot_g_mean_mag: Optional[float]
    phot_bp_mean_mag: Optional[float]
    phot_rp_mean_mag: Optional[float]
    teff_gspphot: Optional[float]
    logg_gspphot: Optional[float]
    mh_gspphot: Optional[float]
    bp_rp: Optional[float]
    spectral_type: Optional[str]
    stellar_mass: Optional[float]
    stellar_radius: Optional[float]

@dataclass
class PlanetData:
    """Data structure for planetary information from NASA Exoplanet Archive"""
    pl_name: str
    hostname: str
    ra: float
    dec: float
    pl_masse: Optional[float]  # Earth masses
    pl_rade: Optional[float]   # Earth radii
    pl_orbsmax: Optional[float]  # Semi-major axis (AU)
    pl_orbper: Optional[float]   # Orbital period (days)
    pl_orbeccen: Optional[float]  # Eccentricity
    pl_orbincl: Optional[float]   # Inclination (degrees)
    pl_eqt: Optional[float]      # Equilibrium temperature (K)
    discoverymethod: Optional[str]
    disc_year: Optional[int]
    disc_facility: Optional[str]

class GaiaDataDownloader:
    """Downloads and processes GAIA DR3 stellar data"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.conn = None
        self.setup_database_connection()
        
    def setup_database_connection(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.autocommit = False
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
    
    def download_gaia_stars(self, max_distance_ly: float = 100.0) -> List[StellarData]:
        """Download all stars within specified distance from GAIA DR3"""
        logger.info(f"🌟 Starting GAIA DR3 download for stars within {max_distance_ly} light years")
        
        # Convert light years to parsecs (1 ly = 0.306601 pc)
        max_distance_pc = max_distance_ly * 0.306601
        min_parallax_mas = 1000.0 / max_distance_pc  # parallax in milliarcseconds
        
        # ADQL query for GAIA DR3
        query = f"""
        SELECT 
            source_id,
            ra, dec,
            parallax, parallax_error,
            pmra, pmdec,
            radial_velocity,
            phot_g_mean_mag,
            phot_bp_mean_mag, 
            phot_rp_mean_mag,
            bp_rp,
            teff_gspphot,
            logg_gspphot,
            mh_gspphot,
            mass_gspphot,
            radius_gspphot,
            ruwe
        FROM gaiadr3.gaia_source 
        WHERE parallax > {min_parallax_mas}
            AND parallax_over_error > 5
            AND ruwe < 1.4
            AND phot_g_mean_mag IS NOT NULL
        ORDER BY parallax DESC
        """
        
        try:
            logger.info("📡 Executing GAIA TAP query...")
            job = Gaia.launch_job_async(query)
            results = job.get_results()
            logger.info(f"✅ Downloaded {len(results)} stars from GAIA DR3")
            
            stellar_data = []
            for row in results:
                # Calculate distance
                if row['parallax'] and row['parallax'] > 0:
                    distance_pc = 1000.0 / row['parallax']  # Convert mas to pc
                    distance_ly = distance_pc / 0.306601    # Convert pc to ly
                    
                    # Estimate spectral type from BP-RP color
                    spectral_type = self.estimate_spectral_type(
                        row['bp_rp'], row['teff_gspphot']
                    )
                    
                    stellar_data.append(StellarData(
                        source_id=str(row['source_id']),
                        ra=float(row['ra']),
                        dec=float(row['dec']),
                        parallax=float(row['parallax']),
                        distance_pc=distance_pc,
                        distance_ly=distance_ly,
                        pmra=float(row['pmra']) if row['pmra'] else None,
                        pmdec=float(row['pmdec']) if row['pmdec'] else None,
                        radial_velocity=float(row['radial_velocity']) if row['radial_velocity'] else None,
                        phot_g_mean_mag=float(row['phot_g_mean_mag']) if row['phot_g_mean_mag'] else None,
                        phot_bp_mean_mag=float(row['phot_bp_mean_mag']) if row['phot_bp_mean_mag'] else None,
                        phot_rp_mean_mag=float(row['phot_rp_mean_mag']) if row['phot_rp_mean_mag'] else None,
                        teff_gspphot=float(row['teff_gspphot']) if row['teff_gspphot'] else None,
                        logg_gspphot=float(row['logg_gspphot']) if row['logg_gspphot'] else None,
                        mh_gspphot=float(row['mh_gspphot']) if row['mh_gspphot'] else None,
                        bp_rp=float(row['bp_rp']) if row['bp_rp'] else None,
                        spectral_type=spectral_type,
                        stellar_mass=float(row['mass_gspphot']) if row['mass_gspphot'] else None,
                        stellar_radius=float(row['radius_gspphot']) if row['radius_gspphot'] else None
                    ))
            
            logger.info(f"✅ Processed {len(stellar_data)} stellar objects")
            return stellar_data
            
        except Exception as e:
            logger.error(f"❌ GAIA download failed: {e}")
            raise
    
    def estimate_spectral_type(self, bp_rp: Optional[float], teff: Optional[float]) -> Optional[str]:
        """Estimate spectral type from BP-RP color and effective temperature"""
        if teff:
            if teff >= 30000:
                return "O5V"
            elif teff >= 10000:
                return "B5V"
            elif teff >= 7500:
                return "A5V"
            elif teff >= 6000:
                return "F5V"
            elif teff >= 5200:
                return "G5V"
            elif teff >= 3700:
                return "K5V"
            else:
                return "M5V"
        elif bp_rp:
            if bp_rp < 0.0:
                return "B5V"
            elif bp_rp < 0.5:
                return "A5V"
            elif bp_rp < 0.8:
                return "F5V"
            elif bp_rp < 1.2:
                return "G5V"
            elif bp_rp < 1.8:
                return "K5V"
            else:
                return "M5V"
        return None

class NasaExoplanetDownloader:
    """Downloads planetary data from NASA Exoplanet Archive"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        
    def download_exoplanets(self, max_distance_ly: float = 100.0) -> List[PlanetData]:
        """Download all confirmed exoplanets within specified distance"""
        logger.info(f"🪐 Starting NASA Exoplanet Archive download for planets within {max_distance_ly} ly")
        
        try:
            # Query NASA Exoplanet Archive for confirmed planets
            planets_table = NasaExoplanetArchive.query_criteria(
                table="pscomppars",
                select="pl_name,hostname,ra,dec,sy_dist,pl_masse,pl_rade,pl_orbsmax,pl_orbper,pl_orbeccen,pl_orbincl,pl_eqt,discoverymethod,disc_year,disc_facility",
                where=f"sy_dist<{max_distance_ly*3.26156}",  # Convert ly to pc
                order="sy_dist"
            )
            
            logger.info(f"✅ Downloaded {len(planets_table)} exoplanets from NASA Archive")
            
            planet_data = []
            for row in planets_table:
                planet_data.append(PlanetData(
                    pl_name=str(row['pl_name']),
                    hostname=str(row['hostname']),
                    ra=float(row['ra']),
                    dec=float(row['dec']),
                    pl_masse=float(row['pl_masse']) if row['pl_masse'] else None,
                    pl_rade=float(row['pl_rade']) if row['pl_rade'] else None,
                    pl_orbsmax=float(row['pl_orbsmax']) if row['pl_orbsmax'] else None,
                    pl_orbper=float(row['pl_orbper']) if row['pl_orbper'] else None,
                    pl_orbeccen=float(row['pl_orbeccen']) if row['pl_orbeccen'] else None,
                    pl_orbincl=float(row['pl_orbincl']) if row['pl_orbincl'] else None,
                    pl_eqt=float(row['pl_eqt']) if row['pl_eqt'] else None,
                    discoverymethod=str(row['discoverymethod']) if row['discoverymethod'] else None,
                    disc_year=int(row['disc_year']) if row['disc_year'] else None,
                    disc_facility=str(row['disc_facility']) if row['disc_facility'] else None
                ))
            
            return planet_data
            
        except Exception as e:
            logger.error(f"❌ NASA Exoplanet download failed: {e}")
            raise

def main():
    """Main execution function"""
    # Database configuration
    db_config = {
        'host': 'localhost',
        'port': 5433,
        'database': 'gg',
        'user': 'gg',
        'password': 'ggpassword'
    }
    
    try:
        # Download GAIA stellar data
        gaia_downloader = GaiaDataDownloader(db_config)
        stellar_data = gaia_downloader.download_gaia_stars(100.0)
        
        # Download NASA exoplanet data
        nasa_downloader = NasaExoplanetDownloader(db_config)
        planet_data = nasa_downloader.download_exoplanets(100.0)
        
        logger.info(f"🎯 Download complete: {len(stellar_data)} stars, {len(planet_data)} planets")
        
        # Save data to JSON for processing
        with open('gaia_stellar_data.json', 'w') as f:
            json.dump([vars(star) for star in stellar_data], f, indent=2, default=str)
            
        with open('nasa_planet_data.json', 'w') as f:
            json.dump([vars(planet) for planet in planet_data], f, indent=2, default=str)
            
        logger.info("✅ Data saved to JSON files for processing")
        
    except Exception as e:
        logger.error(f"❌ Download failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
