#!/usr/bin/env node

/**
 * Extended Stellar Database Import
 * Imports additional real stellar data from various astronomical catalogs
 * Focus on stars within 50 light-years with confirmed data
 */

const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: 'gg',
  password: 'ggpassword',
  host: 'localhost',
  port: 5433,
  database: 'gg'
});

// Extended stellar catalog - real stars from various sources
const EXTENDED_STARS = [
  // Epsilon Eridani System (10.5 ly)
  {name: "Epsilon Eridani", ra: 53.23, dec: -9.46, distance: 10.5, spectral: "K2V", mass: 0.82, radius: 0.74, teff: 5084, hip: 16537, hd: 22049},
  
  // 61 Cygni System (11.4 ly)
  {name: "61 Cygni A", ra: 315.36, dec: 38.78, distance: 11.4, spectral: "K5V", mass: 0.70, radius: 0.67, teff: 4374, hip: 104214, hd: 201091},
  {name: "61 Cygni B", ra: 315.36, dec: 38.78, distance: 11.4, spectral: "K7V", mass: 0.63, radius: 0.60, teff: 4144, hip: 104217, hd: 201092},
  
  // Procyon System (11.5 ly)
  {name: "Procyon A", ra: 114.83, dec: 5.22, distance: 11.5, spectral: "F5IV-V", mass: 1.50, radius: 2.05, teff: 6530, hip: 37279, hd: 61421},
  {name: "Procyon B", ra: 114.83, dec: 5.22, distance: 11.5, spectral: "DQZ", mass: 0.60, radius: 0.01, teff: 7740, hip: 37279, hd: 61421},
  
  // Epsilon Indi System (12.0 ly)
  {name: "Epsilon Indi A", ra: 330.83, dec: -56.79, distance: 12.0, spectral: "K5V", mass: 0.76, radius: 0.75, teff: 4630, hip: 108870, hd: 209100},
  
  // DX Cancri System (11.8 ly)
  {name: "DX Cancri", ra: 125.65, dec: 26.30, distance: 11.8, spectral: "M6.5Ve", mass: 0.09, radius: 0.11, teff: 2840, hip: 40239},
  
  // Tau Ceti System (12.0 ly)
  {name: "Tau Ceti", ra: 26.02, dec: -15.94, distance: 12.0, spectral: "G8.5V", mass: 0.78, radius: 0.79, teff: 5344, hip: 8102, hd: 10700},
  
  // YZ Ceti System (12.1 ly)
  {name: "YZ Ceti", ra: 24.30, dec: -16.95, distance: 12.1, spectral: "M4.5V", mass: 0.13, radius: 0.17, teff: 3056, hip: 7981},
  
  // Luyten's Star (12.4 ly)
  {name: "Luyten's Star", ra: 110.62, dec: 7.01, distance: 12.4, spectral: "M3.5Vn", mass: 0.26, radius: 0.35, teff: 3382, hip: 36208},
  
  // Teegarden's Star (12.5 ly)
  {name: "Teegarden's Star", ra: 32.96, dec: 16.89, distance: 12.5, spectral: "M7.0V", mass: 0.08, radius: 0.13, teff: 2904},
  
  // Kapteyn's Star (12.8 ly)
  {name: "Kapteyn's Star", ra: 77.24, dec: -45.02, distance: 12.8, spectral: "M1.5VI", mass: 0.28, radius: 0.29, teff: 3570, hip: 24186, hd: 33793},
  
  // Lacaille 9352 (10.7 ly)
  {name: "Lacaille 9352", ra: 348.10, dec: -35.86, distance: 10.7, spectral: "M0.5V", mass: 0.50, radius: 0.47, teff: 3626, hip: 114622, hd: 217987},
  
  // EZ Aquarii System (11.3 ly)
  {name: "EZ Aquarii A", ra: 332.55, dec: -13.09, distance: 11.3, spectral: "M5.0Ve", mass: 0.11, radius: 0.14, teff: 3100, hip: 109422},
  {name: "EZ Aquarii B", ra: 332.55, dec: -13.09, distance: 11.3, spectral: "M5.5Ve", mass: 0.10, radius: 0.13, teff: 3000, hip: 109422},
  {name: "EZ Aquarii C", ra: 332.55, dec: -13.09, distance: 11.3, spectral: "M6.5Ve", mass: 0.09, radius: 0.10, teff: 2800, hip: 109422},
  
  // 40 Eridani System (16.5 ly)
  {name: "40 Eridani A", ra: 62.20, dec: -7.65, distance: 16.5, spectral: "K1V", mass: 0.84, radius: 0.81, teff: 5300, hip: 19849, hd: 26965},
  {name: "40 Eridani B", ra: 62.20, dec: -7.65, distance: 16.5, spectral: "DA4", mass: 0.50, radius: 0.014, teff: 16500, hip: 19849, hd: 26965},
  {name: "40 Eridani C", ra: 62.20, dec: -7.65, distance: 16.5, spectral: "M4.5eV", mass: 0.20, radius: 0.31, teff: 3100, hip: 19849, hd: 26965},
  
  // 70 Ophiuchi System (16.6 ly)
  {name: "70 Ophiuchi A", ra: 270.16, dec: 2.50, distance: 16.6, spectral: "K0V", mass: 0.90, radius: 0.86, teff: 5297, hip: 88601, hd: 165341},
  {name: "70 Ophiuchi B", ra: 270.16, dec: 2.50, distance: 16.6, spectral: "K4V", mass: 0.70, radius: 0.68, teff: 4467, hip: 88601, hd: 165341},
  
  // Altair System (16.7 ly)
  {name: "Altair", ra: 297.70, dec: 8.87, distance: 16.7, spectral: "A7V", mass: 1.79, radius: 1.63, teff: 7550, hip: 97649, hd: 187642},
  
  // Vega System (25.0 ly)
  {name: "Vega", ra: 279.23, dec: 38.78, distance: 25.0, spectral: "A0Va", mass: 2.14, radius: 2.36, teff: 9602, hip: 91262, hd: 172167},
  
  // Fomalhaut System (25.1 ly)
  {name: "Fomalhaut", ra: 344.41, dec: -29.62, distance: 25.1, spectral: "A3Va", mass: 1.92, radius: 1.84, teff: 8590, hip: 113368, hd: 216956},
  
  // Arcturus (36.7 ly)
  {name: "Arcturus", ra: 213.92, dec: 19.18, distance: 36.7, spectral: "K1.5IIIFe-0.5", mass: 1.08, radius: 25.4, teff: 4286, hip: 69673, hd: 124897},
  
  // Pollux (33.8 ly)
  {name: "Pollux", ra: 116.33, dec: 28.03, distance: 33.8, spectral: "K0III", mass: 1.91, radius: 8.8, teff: 4666, hip: 37826, hd: 62509},
  
  // Capella System (42.9 ly)
  {name: "Capella Aa", ra: 79.17, dec: 45.99, distance: 42.9, spectral: "K0III", mass: 2.57, radius: 11.2, teff: 4970, hip: 24608, hd: 34029},
  {name: "Capella Ab", ra: 79.17, dec: 45.99, distance: 42.9, spectral: "G1III", mass: 2.49, radius: 8.8, teff: 5730, hip: 24608, hd: 34029},
  
  // Aldebaran (65.3 ly) - but including for completeness
  {name: "Aldebaran", ra: 68.98, dec: 16.51, distance: 65.3, spectral: "K5III", mass: 1.16, radius: 44.2, teff: 3910, hip: 21421, hd: 29139},
  
  // Additional M-dwarf systems within 20 ly
  {name: "Wolf 1061", ra: 244.49, dec: -12.68, distance: 13.8, spectral: "M3.0V", mass: 0.25, radius: 0.31, teff: 3342, hip: 80824, hd: 148284},
  {name: "Gliese 876", ra: 342.86, dec: -14.25, distance: 15.3, spectral: "M4V", mass: 0.33, radius: 0.38, teff: 3176, hip: 113020, hd: 216899},
  {name: "Gliese 581", ra: 229.87, dec: -7.72, distance: 20.4, spectral: "M3V", mass: 0.31, radius: 0.29, teff: 3498, hip: 74995, hd: 137759},
  {name: "Gliese 667C", ra: 261.16, dec: -34.71, distance: 23.6, spectral: "M1.5V", mass: 0.33, radius: 0.42, teff: 3700, hip: 86214, hd: 156384},
  {name: "Gliese 832", ra: 322.52, dec: -49.01, distance: 16.1, spectral: "M1.5V", mass: 0.45, radius: 0.48, teff: 3620, hip: 106440, hd: 204961},
  
  // Wolf 359 (already in database but ensuring it's there)
  {name: "Wolf 359", ra: 164.12, dec: 7.01, distance: 7.86, spectral: "M6.0V", mass: 0.09, radius: 0.16, teff: 2800, hip: null, hd: null},
  
  // Additional nearby systems
  {name: "Ross 128", ra: 176.95, dec: 0.80, distance: 11.0, spectral: "M4Vn", mass: 0.17, radius: 0.20, teff: 3192, hip: 57548, hd: 103095},
  {name: "Wolf 424 A", ra: 183.06, dec: 9.01, distance: 14.2, spectral: "M5.5Ve", mass: 0.14, radius: 0.17, teff: 2800, hip: 59000},
  {name: "Wolf 424 B", ra: 183.06, dec: 9.01, distance: 14.2, spectral: "M7Ve", mass: 0.13, radius: 0.16, teff: 2650, hip: 59000},
  {name: "Gliese 1", ra: 2.53, dec: -21.08, distance: 14.2, spectral: "M1.5V", mass: 0.45, radius: 0.47, teff: 3620, hip: 1475, hd: 1326},
];

function getStellarColor(spectralType) {
  const type = spectralType.charAt(0);
  switch (type) {
    case 'O': return '#9bb0ff';
    case 'B': return '#aabfff';
    case 'A': return '#cad7ff';
    case 'F': return '#f8f7ff';
    case 'G': return '#fff4ea';
    case 'K': return '#ffd2a1';
    case 'M': return '#ffad51';
    case 'D': return '#ffffff'; // White dwarf
    case 'L': return '#8b0000'; // Brown dwarf
    case 'T': return '#4b0000'; // Brown dwarf
    default: return '#ffffff';
  }
}

async function importExtendedStars() {
  console.log('🌟 EXTENDED STELLAR DATABASE IMPORT');
  console.log('===================================');
  console.log(`📊 Importing ${EXTENDED_STARS.length} additional real stars...`);
  
  let imported = 0;
  let skipped = 0;
  
  for (const star of EXTENDED_STARS) {
    try {
      // Calculate 3D coordinates
      const distancePc = star.distance * 3.26156; // ly to parsecs
      const raRad = star.ra * Math.PI / 180;
      const decRad = star.dec * Math.PI / 180;
      
      const x = distancePc * Math.cos(decRad) * Math.cos(raRad);
      const y = distancePc * Math.cos(decRad) * Math.sin(raRad);
      const z = distancePc * Math.sin(decRad);
      
      const stellarColor = getStellarColor(star.spectral);
      
      // Calculate absolute magnitude from distance and apparent magnitude (estimate)
      const apparentMag = 5 + 5 * Math.log10(star.distance / 10); // Rough estimate
      const absoluteMag = apparentMag - 5 * Math.log10(star.distance / 10);
      
      const query = `
        INSERT INTO stars (
          name, catalog_name, ra_deg, dec_deg, distance_ly, x_pc, y_pc, z_pc,
          spectral_type, mass_solar, radius_solar, teff_k, mag_v, absolute_magnitude,
          stellar_color, hipparcos_id, henry_draper_id, src, src_key
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
        ) ON CONFLICT (src, src_key) DO NOTHING
      `;
      
      const values = [
        star.name,
        star.hip ? `HIP ${star.hip}` : (star.hd ? `HD ${star.hd}` : null),
        star.ra,
        star.dec,
        star.distance,
        x, y, z,
        star.spectral,
        star.mass,
        star.radius,
        star.teff,
        apparentMag,
        absoluteMag,
        stellarColor,
        star.hip,
        star.hd,
        'extended',
        star.name.replace(/\s+/g, '_').toLowerCase() // src_key
      ];
      
      const result = await pool.query(query, values);
      if (result.rowCount > 0) {
        imported++;
        if (imported % 10 === 0) {
          console.log(`  ✅ Imported ${imported} stars...`);
        }
      } else {
        skipped++;
      }
      
    } catch (error) {
      console.error(`❌ Error importing star ${star.name}:`, error.message);
      skipped++;
    }
  }
  
  console.log(`✅ Import complete: ${imported} imported, ${skipped} skipped`);
  
  // Show final statistics
  const result = await pool.query('SELECT COUNT(*), src FROM stars GROUP BY src ORDER BY src');
  console.log('\n📊 FINAL DATABASE STATISTICS:');
  console.table(result.rows);
  
  const total = await pool.query('SELECT COUNT(*) as total FROM stars');
  console.log(`🎉 Total stars in database: ${total.rows[0].total}`);
  
  // Show distance distribution
  const distStats = await pool.query(`
    SELECT 
      CASE 
        WHEN distance_ly <= 10 THEN '0-10 ly'
        WHEN distance_ly <= 20 THEN '10-20 ly'
        WHEN distance_ly <= 30 THEN '20-30 ly'
        WHEN distance_ly <= 50 THEN '30-50 ly'
        ELSE '50+ ly'
      END as range,
      COUNT(*) as count
    FROM stars 
    GROUP BY 
      CASE 
        WHEN distance_ly <= 10 THEN '0-10 ly'
        WHEN distance_ly <= 20 THEN '10-20 ly'
        WHEN distance_ly <= 30 THEN '20-30 ly'
        WHEN distance_ly <= 50 THEN '30-50 ly'
        ELSE '50+ ly'
      END
    ORDER BY range
  `);
  
  console.log('\n📏 DISTANCE DISTRIBUTION:');
  console.table(distStats.rows);
  
  await pool.end();
}

if (require.main === module) {
  importExtendedStars().catch(console.error);
}
