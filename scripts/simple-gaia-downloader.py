#!/usr/bin/env python3
"""
Simple GAIA DR3 Data Downloader for Galactic Genesis
Downloads stellar data from GAIA DR3 within 100 light years from Earth
Uses a more robust approach with error handling and fallbacks
"""

import os
import sys
import time
import json
import requests
import psycopg2
import psycopg2.extras
import logging
from typing import Dict, List, Optional
import pandas as pd
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleGaiaDownloader:
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': 5433,
            'database': 'gg',
            'user': 'gg',
            'password': 'ggpassword'
        }
        
    def connect_db(self):
        """Connect to PostgreSQL database"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            logger.info("✅ Database connection established")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def download_gaia_data_via_api(self, max_distance_ly=100):
        """Download GAIA data using direct API calls with fallback methods"""
        logger.info(f"🌟 Downloading GAIA DR3 data within {max_distance_ly} light years")
        
        # Calculate minimum parallax (mas) for distance limit
        min_parallax_mas = 1000.0 / (max_distance_ly * 0.306601)  # Convert ly to pc, then to mas
        logger.info(f"📐 Minimum parallax: {min_parallax_mas:.2f} mas")
        
        # Try multiple approaches
        stellar_data = []
        
        # Method 1: Try astroquery with smaller chunks
        try:
            from astroquery.gaia import Gaia
            logger.info("📡 Trying astroquery.gaia with chunked queries...")
            
            # Query in smaller chunks by magnitude ranges
            mag_ranges = [
                (None, 8),    # Bright stars
                (8, 10),      # Medium stars  
                (10, 12),     # Fainter stars
                (12, None)    # Faintest stars
            ]
            
            for mag_min, mag_max in mag_ranges:
                try:
                    query = f"""
                    SELECT TOP 1000
                        source_id, ra, dec, parallax, parallax_error,
                        pmra, pmdec, radial_velocity,
                        phot_g_mean_mag, phot_bp_mean_mag, phot_rp_mean_mag,
                        bp_rp, teff_gspphot, logg_gspphot, mh_gspphot
                    FROM gaiadr3.gaia_source 
                    WHERE parallax > {min_parallax_mas}
                        AND parallax_over_error > 3
                        AND phot_g_mean_mag IS NOT NULL
                    """
                    
                    if mag_min is not None:
                        query += f" AND phot_g_mean_mag >= {mag_min}"
                    if mag_max is not None:
                        query += f" AND phot_g_mean_mag < {mag_max}"
                    
                    query += " ORDER BY parallax DESC"
                    
                    logger.info(f"   Querying magnitude range {mag_min}-{mag_max}...")
                    job = Gaia.launch_job(query)
                    results = job.get_results()
                    
                    logger.info(f"   ✅ Got {len(results)} stars in range {mag_min}-{mag_max}")
                    
                    for row in results:
                        if row['parallax'] and row['parallax'] > 0:
                            distance_pc = 1000.0 / row['parallax']
                            distance_ly = distance_pc / 0.306601
                            
                            if distance_ly <= max_distance_ly:
                                # Convert numpy types to Python native types
                                def safe_float(val):
                                    if val is None or np.isnan(val):
                                        return None
                                    return float(val)

                                def safe_int(val):
                                    if val is None:
                                        return None
                                    return int(val)

                                stellar_data.append({
                                    'gaia_source_id': safe_int(row['source_id']),
                                    'ra_deg': safe_float(row['ra']),
                                    'dec_deg': safe_float(row['dec']),
                                    'parallax_mas': safe_float(row['parallax']),
                                    'distance_ly': safe_float(distance_ly),
                                    'distance_pc': safe_float(distance_pc),
                                    'pmra': safe_float(row['pmra']),
                                    'pmdec': safe_float(row['pmdec']),
                                    'radial_velocity': safe_float(row['radial_velocity']),
                                    'phot_g_mean_mag': safe_float(row['phot_g_mean_mag']),
                                    'phot_bp_mean_mag': safe_float(row['phot_bp_mean_mag']),
                                    'phot_rp_mean_mag': safe_float(row['phot_rp_mean_mag']),
                                    'bp_rp': safe_float(row['bp_rp']),
                                    'teff_gspphot': safe_float(row['teff_gspphot']),
                                    'logg_gspphot': safe_float(row['logg_gspphot']),
                                    'mh_gspphot': safe_float(row['mh_gspphot']),
                                    'spectral_type': self.estimate_spectral_type(row['bp_rp'], row['teff_gspphot']),
                                    'stellar_color': self.get_stellar_color(row['bp_rp'], row['teff_gspphot'])
                                })
                    
                    time.sleep(1)  # Be nice to the service
                    
                except Exception as e:
                    logger.warning(f"   ⚠️ Failed magnitude range {mag_min}-{mag_max}: {e}")
                    continue
                    
        except Exception as e:
            logger.warning(f"⚠️ Astroquery method failed: {e}")
        
        logger.info(f"✅ Downloaded {len(stellar_data)} stars total")
        return stellar_data
    
    def estimate_spectral_type(self, bp_rp, teff):
        """Estimate spectral type from color and temperature"""
        if teff:
            if teff >= 30000:
                return 'O'
            elif teff >= 10000:
                return 'B'
            elif teff >= 7500:
                return 'A'
            elif teff >= 6000:
                return 'F'
            elif teff >= 5200:
                return 'G'
            elif teff >= 3700:
                return 'K'
            else:
                return 'M'
        elif bp_rp:
            if bp_rp < 0.0:
                return 'B'
            elif bp_rp < 0.3:
                return 'A'
            elif bp_rp < 0.6:
                return 'F'
            elif bp_rp < 0.9:
                return 'G'
            elif bp_rp < 1.4:
                return 'K'
            else:
                return 'M'
        return 'G'  # Default
    
    def get_stellar_color(self, bp_rp, teff):
        """Get stellar color from spectral type"""
        spectral_type = self.estimate_spectral_type(bp_rp, teff)
        color_map = {
            'O': '#9bb0ff',  # Blue
            'B': '#aabfff',  # Blue-white
            'A': '#cad7ff',  # White
            'F': '#f8f7ff',  # Yellow-white
            'G': '#fff4ea',  # Yellow
            'K': '#ffd2a1',  # Orange
            'M': '#ffcc6f'   # Red
        }
        return color_map.get(spectral_type, '#fff4ea')
    
    def insert_stellar_data(self, stellar_data):
        """Insert stellar data into database"""
        if not stellar_data:
            logger.warning("⚠️ No stellar data to insert")
            return
            
        logger.info(f"💾 Inserting {len(stellar_data)} stars into database...")
        
        # Create backup of existing data
        self.cursor.execute("DROP TABLE IF EXISTS stars_backup_gaia")
        self.cursor.execute("CREATE TABLE stars_backup_gaia AS SELECT * FROM stars")
        
        inserted_count = 0
        updated_count = 0
        
        for star in stellar_data:
            try:
                # Start a new transaction for each star
                self.conn.rollback()  # Clear any previous failed transaction

                # Check if star already exists
                self.cursor.execute(
                    "SELECT star_id FROM stars WHERE gaia_source_id = %s",
                    (star['gaia_source_id'],)
                )
                existing = self.cursor.fetchone()
                
                if existing:
                    # Update existing star
                    update_sql = """
                    UPDATE stars SET
                        src = 'gaia_dr3',
                        ra_deg = %s, dec_deg = %s,
                        parallax_mas = %s, distance_ly = %s,
                        pm_ra_masyr = %s, pm_dec_masyr = %s,
                        radial_velocity_kms = %s,
                        phot_g_mean_mag = %s, phot_bp_mean_mag = %s, phot_rp_mean_mag = %s,
                        bp_rp = %s, teff_gspphot = %s, logg_gspphot = %s, mh_gspphot = %s,
                        spectral_type = %s, stellar_color = %s,
                        last_updated = NOW()
                    WHERE gaia_source_id = %s
                    """
                    self.cursor.execute(update_sql, (
                        star['ra_deg'], star['dec_deg'], star['parallax_mas'], star['distance_ly'],
                        star['pmra'], star['pmdec'], star['radial_velocity'],
                        star['phot_g_mean_mag'], star['phot_bp_mean_mag'], star['phot_rp_mean_mag'],
                        star['bp_rp'], star['teff_gspphot'], star['logg_gspphot'], star['mh_gspphot'],
                        star['spectral_type'], star['stellar_color'], star['gaia_source_id']
                    ))
                    updated_count += 1
                else:
                    # Insert new star
                    insert_sql = """
                    INSERT INTO stars (
                        src, src_key, gaia_source_id, name,
                        ra_deg, dec_deg, parallax_mas, distance_ly,
                        pm_ra_masyr, pm_dec_masyr, radial_velocity_kms,
                        phot_g_mean_mag, phot_bp_mean_mag, phot_rp_mean_mag,
                        bp_rp, teff_gspphot, logg_gspphot, mh_gspphot,
                        spectral_type, stellar_color
                    ) VALUES (
                        'gaia_dr3', %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s, %s,
                        %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s
                    )
                    """
                    self.cursor.execute(insert_sql, (
                        f"gaia_dr3_{star['gaia_source_id']}", star['gaia_source_id'], 
                        f"Gaia DR3 {star['gaia_source_id']}",
                        star['ra_deg'], star['dec_deg'], star['parallax_mas'], star['distance_ly'],
                        star['pmra'], star['pmdec'], star['radial_velocity'],
                        star['phot_g_mean_mag'], star['phot_bp_mean_mag'], star['phot_rp_mean_mag'],
                        star['bp_rp'], star['teff_gspphot'], star['logg_gspphot'], star['mh_gspphot'],
                        star['spectral_type'], star['stellar_color']
                    ))
                    inserted_count += 1

                # Commit this star's transaction
                self.conn.commit()

            except Exception as e:
                logger.error(f"❌ Failed to insert star {star['gaia_source_id']}: {e}")
                self.conn.rollback()  # Rollback failed transaction
                continue
        
        logger.info(f"✅ Database updated: {inserted_count} inserted, {updated_count} updated")
    
    def run(self):
        """Main execution method"""
        logger.info("🚀 Starting Simple GAIA DR3 Download")
        
        if not self.connect_db():
            return False
        
        try:
            # Download stellar data
            stellar_data = self.download_gaia_data_via_api(max_distance_ly=100)
            
            if stellar_data:
                # Save raw data
                with open('gaia_stellar_data_simple.json', 'w') as f:
                    json.dump(stellar_data, f, indent=2, default=str)
                logger.info("💾 Raw data saved to gaia_stellar_data_simple.json")
                
                # Insert into database
                self.insert_stellar_data(stellar_data)
                
                logger.info("🎉 GAIA DR3 download completed successfully!")
                return True
            else:
                logger.error("❌ No stellar data downloaded")
                return False
                
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            return False
        finally:
            if hasattr(self, 'conn'):
                self.conn.close()

if __name__ == "__main__":
    downloader = SimpleGaiaDownloader()
    success = downloader.run()
    sys.exit(0 if success else 1)
