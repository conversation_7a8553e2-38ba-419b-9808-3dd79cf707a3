#!/bin/bash
# Comprehensive Test Suite for Galactic Genesis GAIA Integration
# Tests all components after GAIA DR3 integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log "🧪 Running test: $test_name"
    
    if result=$(eval "$test_command" 2>&1); then
        if [[ -z "$expected_pattern" ]] || echo "$result" | grep -q "$expected_pattern"; then
            success "✅ PASS: $test_name"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            return 0
        else
            error "❌ FAIL: $test_name - Expected pattern '$expected_pattern' not found"
            echo "Output: $result"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            return 1
        fi
    else
        error "❌ FAIL: $test_name - Command failed"
        echo "Output: $result"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "scripts" ]; then
    error "Please run this script from the project root directory"
    exit 1
fi

log "🌌 Starting Comprehensive Test Suite for Galactic Genesis"

# 1. Database Connectivity Tests
log "📊 Testing Database Connectivity..."
run_test "Database Connection" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c 'SELECT 1;'" \
    "1"

run_test "Database Schema Check" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c '\d stars' | grep 'gaia_source_id.*bigint'" \
    "gaia_source_id"

# 2. Data Integrity Tests
log "🔍 Testing Data Integrity..."
run_test "GAIA Stars Count" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3';\"" \
    "3977"

run_test "Stellar Colors Present" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars WHERE stellar_color IS NOT NULL AND src = 'gaia_dr3';\"" \
    "3977"

run_test "Distance Range Check" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars WHERE distance_ly <= 100 AND src = 'gaia_dr3';\"" \
    "3977"

run_test "Spectral Type Distribution" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(DISTINCT spectral_type) FROM stars WHERE src = 'gaia_dr3';\"" \
    "[1-9]"

# 3. API Gateway Tests
log "🌐 Testing API Gateway..."
run_test "API Health Check" \
    "curl -s http://localhost:19081/v1/health" \
    "ok"

run_test "Stellar API Response" \
    "curl -s 'http://localhost:19081/v1/stellar/stars?limit=5' | jq -r '.stars | length'" \
    "5"

run_test "GAIA Stars in API" \
    "curl -s 'http://localhost:19081/v1/stellar/stars?limit=100' | jq -r '.stars[] | select(.src == \"gaia_dr3\") | .name' | wc -l" \
    "[1-9]"

run_test "Stellar Colors in API" \
    "curl -s 'http://localhost:19081/v1/stellar/stars?limit=10' | jq -r '.stars[] | select(.stellar_color != null) | .stellar_color' | wc -l" \
    "[1-9]"

# 4. Frontend Tests
log "🎨 Testing Frontend..."
run_test "Frontend Accessibility" \
    "curl -s http://localhost:5174" \
    "Galactic Genesis"

run_test "Frontend Assets Loading" \
    "curl -s http://localhost:5174 | grep -c 'script\\|link'" \
    "[1-9]"

# 5. Build Tests
log "🔨 Testing Build Process..."
run_test "API Gateway Build" \
    "cd services/api-gateway && npm run build" \
    ""

run_test "Frontend Build" \
    "cd frontend && npm run build" \
    ""

# 6. Performance Tests
log "⚡ Testing Performance..."
run_test "API Response Time" \
    "time curl -s 'http://localhost:19081/v1/stellar/stars?limit=100' > /dev/null" \
    ""

run_test "Database Query Performance" \
    "time PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c \"SELECT * FROM stars WHERE distance_ly < 50 ORDER BY distance_ly LIMIT 100;\" > /dev/null" \
    ""

# 7. Data Quality Tests
log "📈 Testing Data Quality..."
run_test "No Null GAIA Source IDs" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3' AND gaia_source_id IS NULL;\"" \
    "0"

run_test "Valid Distance Values" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3' AND (distance_ly <= 0 OR distance_ly > 100);\"" \
    "0"

run_test "Valid Coordinates" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3' AND (ra_deg < 0 OR ra_deg > 360 OR dec_deg < -90 OR dec_deg > 90);\"" \
    "0"

# 8. Integration Tests
log "🔗 Testing Integration..."
run_test "Star Data Consistency" \
    "curl -s 'http://localhost:19081/v1/stellar/stars?limit=1' | jq -r '.stars[0] | [.name, .distance_ly, .spectral_type, .stellar_color] | @csv'" \
    ","

run_test "API-Database Consistency" \
    "API_COUNT=\$(curl -s 'http://localhost:19081/v1/stellar/stars?limit=1000' | jq '.stars | length'); DB_COUNT=\$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c 'SELECT COUNT(*) FROM stars LIMIT 1000;' | tr -d ' '); [ \$API_COUNT -eq \$DB_COUNT ]" \
    ""

# 9. Security Tests
log "🔒 Testing Security..."
run_test "SQL Injection Protection" \
    "curl -s \"http://localhost:19081/v1/stellar/stars?limit=1'; DROP TABLE stars; --\" | jq -r '.error // \"safe\"'" \
    "safe"

run_test "API Rate Limiting" \
    "for i in {1..5}; do curl -s http://localhost:19081/v1/health > /dev/null; done" \
    ""

# 10. Backup and Recovery Tests
log "💾 Testing Backup and Recovery..."
run_test "Backup Table Exists" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c '\d stars_backup_gaia'" \
    "stars_backup_gaia"

run_test "Backup Data Integrity" \
    "PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c \"SELECT COUNT(*) FROM stars_backup_gaia;\"" \
    "[1-9]"

# Generate Test Report
log "📊 Generating Test Report..."

cat > test_report.md << EOF
# Galactic Genesis GAIA Integration Test Report

## Test Summary
- **Date**: $(date)
- **Total Tests**: $TESTS_TOTAL
- **Passed**: $TESTS_PASSED
- **Failed**: $TESTS_FAILED
- **Success Rate**: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%

## Database Statistics
- **Total Stars**: $(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM stars;" | tr -d ' ')
- **GAIA DR3 Stars**: $(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3';" | tr -d ' ')
- **Stars with Colors**: $(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM stars WHERE stellar_color IS NOT NULL;" | tr -d ' ')

## Spectral Type Distribution
$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c "SELECT spectral_type, COUNT(*) as count FROM stars WHERE src = 'gaia_dr3' GROUP BY spectral_type ORDER BY count DESC;" | head -10)

## Nearest GAIA Stars
$(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -c "SELECT name, distance_ly, spectral_type, stellar_color FROM stars WHERE src = 'gaia_dr3' ORDER BY distance_ly LIMIT 5;" | head -10)

## Test Status
$(if [ $TESTS_FAILED -eq 0 ]; then echo "🎉 ALL TESTS PASSED"; else echo "⚠️ $TESTS_FAILED TESTS FAILED"; fi)

## Next Steps
1. Review any failed tests above
2. Test frontend galaxy view with new stellar data
3. Verify stellar colors display correctly in 3D view
4. Consider adding more detailed stellar properties
5. Plan for planetary and moon data integration

EOF

success "Test report generated: test_report.md"

# Final Results
echo ""
echo "🎯 TEST SUITE COMPLETE"
echo ""
echo "📊 Results:"
echo "   • Total Tests: $TESTS_TOTAL"
echo "   • Passed: $TESTS_PASSED"
echo "   • Failed: $TESTS_FAILED"
echo "   • Success Rate: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo "🎉 ALL TESTS PASSED!"
    echo "✅ Galactic Genesis GAIA integration is working perfectly!"
    echo ""
    echo "🌟 Database now contains $(PGPASSWORD=ggpassword psql -h localhost -p 5433 -U gg -d gg -t -c "SELECT COUNT(*) FROM stars WHERE src = 'gaia_dr3';" | tr -d ' ') real stars from GAIA DR3"
    echo "🎨 All stars have realistic colors based on spectral classification"
    echo "🚀 Frontend and API are fully operational"
    exit 0
else
    echo "⚠️ $TESTS_FAILED TESTS FAILED"
    echo "Please review the test output above and fix any issues"
    exit 1
fi
