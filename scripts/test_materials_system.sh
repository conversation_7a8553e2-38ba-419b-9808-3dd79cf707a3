#!/bin/bash

# Test script for Raw Materials and Trade Stations System
# This script validates the implementation of the materials system

set -e

echo "🧪 Testing Raw Materials and Trade Stations System"
echo "=================================================="

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:19081}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5433}"
DB_NAME="${DB_NAME:-galactic_genesis}"
DB_USER="${DB_USER:-postgres}"

echo "📋 Configuration:"
echo "  API Base URL: $API_BASE_URL"
echo "  Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo ""

# Test database connection
echo "🔌 Testing database connection..."
if command -v psql >/dev/null 2>&1; then
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ Database connection successful"
    else
        echo "❌ Database connection failed"
        exit 1
    fi
else
    echo "⚠️  psql not found, skipping database connection test"
fi

# Test API health
echo ""
echo "🏥 Testing API health..."
if curl -s "$API_BASE_URL/v1/health" >/dev/null; then
    echo "✅ API Gateway is healthy"
else
    echo "❌ API Gateway is not responding"
    exit 1
fi

# Test materials service health
echo ""
echo "🏥 Testing materials service health..."
MATERIALS_SVC_URL="${MATERIALS_SVC_URL:-http://localhost:8086}"
if curl -s "$MATERIALS_SVC_URL/health" >/dev/null; then
    echo "✅ Materials service is healthy"
else
    echo "⚠️  Materials service is not responding (may not be started)"
fi

# Test materials API endpoints
echo ""
echo "🧪 Testing Materials API Endpoints..."

# Test get materials
echo "  📦 Testing GET /v1/materials..."
MATERIALS_RESPONSE=$(curl -s "$API_BASE_URL/v1/materials" || echo "FAILED")
if echo "$MATERIALS_RESPONSE" | grep -q "materials"; then
    MATERIALS_COUNT=$(echo "$MATERIALS_RESPONSE" | grep -o '"id"' | wc -l)
    echo "  ✅ Materials endpoint working ($MATERIALS_COUNT materials found)"
else
    echo "  ❌ Materials endpoint failed"
fi

# Test get material deposits
echo "  🏔️  Testing GET /v1/material-deposits..."
DEPOSITS_RESPONSE=$(curl -s "$API_BASE_URL/v1/material-deposits" || echo "FAILED")
if echo "$DEPOSITS_RESPONSE" | grep -q "deposits"; then
    DEPOSITS_COUNT=$(echo "$DEPOSITS_RESPONSE" | grep -o '"id"' | wc -l)
    echo "  ✅ Material deposits endpoint working ($DEPOSITS_COUNT deposits found)"
else
    echo "  ❌ Material deposits endpoint failed"
fi

# Test get stations
echo "  🏭 Testing GET /v1/stations..."
STATIONS_RESPONSE=$(curl -s "$API_BASE_URL/v1/stations" || echo "FAILED")
if echo "$STATIONS_RESPONSE" | grep -q "stations"; then
    STATIONS_COUNT=$(echo "$STATIONS_RESPONSE" | grep -o '"id"' | wc -l)
    echo "  ✅ Stations endpoint working ($STATIONS_COUNT stations found)"
else
    echo "  ❌ Stations endpoint failed"
fi

# Test get station types
echo "  🏗️  Testing GET /v1/station-types..."
STATION_TYPES_RESPONSE=$(curl -s "$API_BASE_URL/v1/station-types" || echo "FAILED")
if echo "$STATION_TYPES_RESPONSE" | grep -q "station_types"; then
    STATION_TYPES_COUNT=$(echo "$STATION_TYPES_RESPONSE" | grep -o '"id"' | wc -l)
    echo "  ✅ Station types endpoint working ($STATION_TYPES_COUNT types found)"
else
    echo "  ❌ Station types endpoint failed"
fi

# Test get processing recipes
echo "  ⚗️  Testing GET /v1/processing-recipes..."
RECIPES_RESPONSE=$(curl -s "$API_BASE_URL/v1/processing-recipes" || echo "FAILED")
if echo "$RECIPES_RESPONSE" | grep -q "recipes"; then
    RECIPES_COUNT=$(echo "$RECIPES_RESPONSE" | grep -o '"id"' | wc -l)
    echo "  ✅ Processing recipes endpoint working ($RECIPES_COUNT recipes found)"
else
    echo "  ❌ Processing recipes endpoint failed"
fi

# Test database schema
echo ""
echo "🗄️  Testing Database Schema..."

if command -v psql >/dev/null 2>&1; then
    # Check if material_types table exists and has data
    MATERIAL_TYPES_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM material_types;" 2>/dev/null | tr -d ' ' || echo "0")
    if [ "$MATERIAL_TYPES_COUNT" -gt 0 ]; then
        echo "  ✅ material_types table exists with $MATERIAL_TYPES_COUNT materials"
    else
        echo "  ❌ material_types table is empty or doesn't exist"
    fi

    # Check if body_material_deposits table exists and has data
    DEPOSITS_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM body_material_deposits;" 2>/dev/null | tr -d ' ' || echo "0")
    if [ "$DEPOSITS_COUNT" -gt 0 ]; then
        echo "  ✅ body_material_deposits table exists with $DEPOSITS_COUNT deposits"
    else
        echo "  ❌ body_material_deposits table is empty or doesn't exist"
    fi

    # Check if station_types table exists and has data
    STATION_TYPES_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM station_types;" 2>/dev/null | tr -d ' ' || echo "0")
    if [ "$STATION_TYPES_COUNT" -gt 0 ]; then
        echo "  ✅ station_types table exists with $STATION_TYPES_COUNT types"
    else
        echo "  ❌ station_types table is empty or doesn't exist"
    fi

    # Check if material_processing table exists and has data
    PROCESSING_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM material_processing;" 2>/dev/null | tr -d ' ' || echo "0")
    if [ "$PROCESSING_COUNT" -gt 0 ]; then
        echo "  ✅ material_processing table exists with $PROCESSING_COUNT recipes"
    else
        echo "  ❌ material_processing table is empty or doesn't exist"
    fi
else
    echo "  ⚠️  psql not found, skipping database schema tests"
fi

# Test material distribution
echo ""
echo "🌍 Testing Material Distribution..."

if command -v psql >/dev/null 2>&1; then
    # Check material distribution by category
    echo "  📊 Material distribution by category:"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT mt.category, COUNT(*) as count
        FROM material_types mt
        GROUP BY mt.category
        ORDER BY count DESC;
    " 2>/dev/null || echo "  ❌ Failed to query material distribution"

    # Check deposit distribution by body type
    echo ""
    echo "  🏔️  Deposit distribution by body type:"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT body_type, COUNT(*) as deposit_count
        FROM body_material_deposits
        GROUP BY body_type
        ORDER BY deposit_count DESC;
    " 2>/dev/null || echo "  ❌ Failed to query deposit distribution"
else
    echo "  ⚠️  psql not found, skipping material distribution tests"
fi

# Test order creation
echo ""
echo "📝 Testing Order Creation..."

# Test survey order creation
echo "  🔍 Testing survey order creation..."
SURVEY_ORDER_DATA='{
    "empire_id": "emp-1",
    "target_body_type": "planet",
    "target_body_id": 1,
    "system_id": "sys-1",
    "survey_type": "basic"
}'

SURVEY_RESPONSE=$(curl -s -X POST "$API_BASE_URL/v1/survey-orders" \
    -H "Content-Type: application/json" \
    -d "$SURVEY_ORDER_DATA" || echo "FAILED")

if echo "$SURVEY_RESPONSE" | grep -q "survey_order"; then
    echo "  ✅ Survey order creation successful"
else
    echo "  ❌ Survey order creation failed: $SURVEY_RESPONSE"
fi

# Test station construction order creation
echo "  🏗️  Testing station construction order creation..."
CONSTRUCTION_ORDER_DATA='{
    "empire_id": "emp-1",
    "system_id": "sys-1",
    "station_type_id": "orbital_mining_platform",
    "orbiting_body_type": "moon",
    "orbiting_body_id": 1,
    "orbital_distance_km": 10000
}'

CONSTRUCTION_RESPONSE=$(curl -s -X POST "$API_BASE_URL/v1/station-construction-orders" \
    -H "Content-Type: application/json" \
    -d "$CONSTRUCTION_ORDER_DATA" || echo "FAILED")

if echo "$CONSTRUCTION_RESPONSE" | grep -q "construction_order"; then
    echo "  ✅ Station construction order creation successful"
else
    echo "  ❌ Station construction order creation failed: $CONSTRUCTION_RESPONSE"
fi

# Summary
echo ""
echo "📊 Test Summary"
echo "==============="

TOTAL_TESTS=12
PASSED_TESTS=0

# Count successful tests (this is a simplified count)
if echo "$MATERIALS_RESPONSE" | grep -q "materials"; then ((PASSED_TESTS++)); fi
if echo "$DEPOSITS_RESPONSE" | grep -q "deposits"; then ((PASSED_TESTS++)); fi
if echo "$STATIONS_RESPONSE" | grep -q "stations"; then ((PASSED_TESTS++)); fi
if echo "$STATION_TYPES_RESPONSE" | grep -q "station_types"; then ((PASSED_TESTS++)); fi
if echo "$RECIPES_RESPONSE" | grep -q "recipes"; then ((PASSED_TESTS++)); fi
if echo "$SURVEY_RESPONSE" | grep -q "survey_order"; then ((PASSED_TESTS++)); fi
if echo "$CONSTRUCTION_RESPONSE" | grep -q "construction_order"; then ((PASSED_TESTS++)); fi

echo "Tests passed: $PASSED_TESTS/$TOTAL_TESTS"

if [ "$PASSED_TESTS" -eq "$TOTAL_TESTS" ]; then
    echo "🎉 All tests passed! Raw materials system is working correctly."
    exit 0
elif [ "$PASSED_TESTS" -gt $((TOTAL_TESTS / 2)) ]; then
    echo "⚠️  Most tests passed, but some issues detected."
    exit 1
else
    echo "❌ Many tests failed. Please check the implementation."
    exit 1
fi
