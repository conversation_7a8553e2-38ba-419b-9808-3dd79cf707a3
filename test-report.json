{"timestamp": "2025-09-09T10:06:22.059Z", "summary": {"total": 4, "passed": 1, "failed": 3, "critical": 3, "criticalPassed": 1}, "duration": 27999, "results": [{"name": "Smoke Tests - Health Check", "success": false, "critical": true, "duration": 91, "command": "node tests/smoke/health-check.js"}, {"name": "Smoke Tests - Frontend", "success": true, "critical": true, "duration": 108, "command": "node tests/smoke/frontend-smoke.js"}, {"name": "Unit Tests - Frontend", "success": false, "critical": true, "duration": 1211, "command": "npm run test:run"}, {"name": "E2E Tests - Cypress", "success": false, "critical": false, "duration": 22572, "command": "npm run cypress:run"}]}