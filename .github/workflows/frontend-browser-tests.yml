name: Frontend Browser Tests

on:
  push:
    branches: [ main, develop, feat/* ]
    paths:
      - 'frontend/**'
      - 'services/**'
      - '.github/workflows/frontend-browser-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'frontend/**'
      - 'services/**'
      - '.github/workflows/frontend-browser-tests.yml'

jobs:
  browser-tests:
    name: Browser Tests (Playwright)
    runs-on: ubuntu-latest
    timeout-minutes: 15

    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_USER: gg
          POSTGRES_PASSWORD: ggpassword
          POSTGRES_DB: gg
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: |
            frontend/package-lock.json
            services/*/package-lock.json

      - name: Install frontend dependencies
        working-directory: frontend
        run: npm ci

      - name: Install API Gateway dependencies
        working-directory: services/api-gateway
        run: npm ci

      - name: Install Health Dashboard dependencies
        working-directory: services/health-dashboard
        run: npm ci

      - name: Install Playwright browsers
        working-directory: frontend
        run: npx playwright install chromium

      - name: Build frontend
        working-directory: frontend
        run: npm run build

      - name: Start API Gateway
        working-directory: services/api-gateway
        run: |
          PGHOST=localhost PGPORT=5433 PGUSER=gg PGPASSWORD=ggpassword PGDATABASE=gg PORT=19081 npm run dev &
          echo $! > api-gateway.pid
        env:
          NODE_ENV: test

      - name: Start Health Dashboard
        working-directory: services/health-dashboard
        run: |
          PGHOST=localhost PGPORT=5433 PGUSER=gg PGPASSWORD=ggpassword PGDATABASE=gg DASHBOARD_PORT=8086 npm run dev &
          echo $! > health-dashboard.pid
        env:
          NODE_ENV: test

      - name: Start frontend dev server
        working-directory: frontend
        run: |
          PORT=5174 npm run dev &
          echo $! > frontend.pid

      - name: Wait for services to be ready
        run: |
          echo "Waiting for services to start..."
          timeout 60 bash -c 'until curl -f http://localhost:5174/ > /dev/null 2>&1; do sleep 2; done'
          timeout 30 bash -c 'until curl -f http://localhost:19081/v1/health > /dev/null 2>&1; do sleep 2; done'
          timeout 30 bash -c 'until curl -f http://localhost:8086/api/ports > /dev/null 2>&1; do sleep 2; done'
          echo "All services are ready!"

      - name: Run import/module error tests
        working-directory: frontend
        run: npx playwright test import-errors.spec.ts --reporter=github

      - name: Run comprehensive frontend tests
        working-directory: frontend
        run: npx playwright test comprehensive-frontend.spec.ts --reporter=github

      - name: Run port monitor tests
        working-directory: frontend
        run: npx playwright test port-monitor.spec.ts --reporter=github

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: frontend/test-results/
          retention-days: 7

      - name: Upload screenshots on failure
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-screenshots
          path: frontend/test-results/**/*.png
          retention-days: 7

      - name: Cleanup processes
        if: always()
        run: |
          if [ -f services/api-gateway/api-gateway.pid ]; then
            kill $(cat services/api-gateway/api-gateway.pid) || true
          fi
          if [ -f services/health-dashboard/health-dashboard.pid ]; then
            kill $(cat services/health-dashboard/health-dashboard.pid) || true
          fi
          if [ -f frontend/frontend.pid ]; then
            kill $(cat frontend/frontend.pid) || true
          fi

  smoke-tests:
    name: Smoke Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: browser-tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Install Playwright browsers
        working-directory: frontend
        run: npx playwright install chromium

      - name: Build frontend
        working-directory: frontend
        run: npm run build

      - name: Preview built frontend
        working-directory: frontend
        run: |
          npm run preview &
          echo $! > preview.pid

      - name: Wait for preview server
        run: |
          timeout 30 bash -c 'until curl -f http://localhost:4173/ > /dev/null 2>&1; do sleep 2; done'

      - name: Run smoke tests on built frontend
        working-directory: frontend
        run: |
          npx playwright test import-errors.spec.ts -g "should successfully import PortMonitor component without import/module errors" --reporter=github
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:4173

      - name: Cleanup preview server
        if: always()
        run: |
          if [ -f frontend/preview.pid ]; then
            kill $(cat frontend/preview.pid) || true
          fi

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Run security audit
        working-directory: frontend
        run: npm audit --audit-level=high

      - name: Check for known vulnerabilities
        working-directory: frontend
        run: |
          echo "Checking for common security issues..."
          # Check for console.log statements in production build
          if npm run build 2>&1 | grep -i "console\." && [ "$NODE_ENV" = "production" ]; then
            echo "Warning: console statements found in production build"
            exit 1
          fi
          echo "Security checks passed!"
