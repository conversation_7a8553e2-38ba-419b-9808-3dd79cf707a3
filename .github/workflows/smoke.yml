name: Dev Compose Smoke Test

on:
  pull_request:
    branches: [main]
  workflow_dispatch:
  push:
    branches: [feat/*]

jobs:
  smoke:
    runs-on: ubuntu-latest
    timeout-minutes: 25
    steps:
      - uses: actions/checkout@v4

      - name: Install jq and websocat (for WS smoke)
        run: |
          sudo apt-get update -y
          sudo apt-get install -y jq websocat || sudo apt-get install -y jq

      - name: Start compose stack
        run: |
          docker compose -f deploy/docker-compose.yml -f deploy/docker-compose.override.yml -f deploy/docker-compose.override.local.yml up -d

      - name: Wait for gateway
        env:
          GATEWAY_PORT: 8080
        run: |
          for i in {1..60}; do
            code=$(curl -s -o /dev/null -w '%{http_code}' http://localhost:${GATEWAY_PORT}/v1/health || true)
            if [ "$code" = "200" ]; then echo 'gateway ready'; exit 0; fi
            sleep 3
          done
          echo 'gateway not ready' >&2
          docker compose -f deploy/docker-compose.yml -f deploy/docker-compose.override.yml -f deploy/docker-compose.override.local.yml logs || true
          exit 1

      - name: Run smoke checks (with WS assertion)
        run: bash scripts/smoke.sh --ws

      - name: Compose logs (always)
        if: always()
        run: docker compose -f deploy/docker-compose.yml -f deploy/docker-compose.override.yml -f deploy/docker-compose.override.local.yml logs || true

      - name: Archive compose logs (failure)
        if: ${{ failure() && github.event_name == 'pull_request' }}
        run: |
          mkdir -p artifacts
          docker compose -f deploy/docker-compose.yml -f deploy/docker-compose.override.yml -f deploy/docker-compose.override.local.yml logs > artifacts/compose.logs.txt || true
        shell: bash
      - uses: actions/upload-artifact@v4
        if: ${{ failure() && github.event_name == 'pull_request' }}
        with:
          name: compose-logs
          path: artifacts/compose.logs.txt

      - name: Archive compose logs (always)
        if: always()
        run: |
          mkdir -p artifacts
          docker compose -f deploy/docker-compose.yml -f deploy/docker-compose.override.yml -f deploy/docker-compose.override.local.yml ps > artifacts/compose.ps.txt || true
        shell: bash
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: compose-ps
          path: artifacts/compose.ps.txt

      - name: Shutdown
        if: always()
        run: docker compose -f deploy/docker-compose.yml -f deploy/docker-compose.override.yml -f deploy/docker-compose.override.local.yml down -v
