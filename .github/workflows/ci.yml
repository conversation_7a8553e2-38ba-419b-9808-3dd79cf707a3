name: CI

on:
  push:
    branches: [main, 'feat/**', 'fix/**']
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install, type-check and test api-gateway
        working-directory: services/api-gateway
        run: |
          npm ci || npm install
          npx -y tsc -p tsconfig.json --noEmit
          npm test --silent
      - name: Install, type-check and test orders-svc
        working-directory: services/orders-svc
        run: |
          npm ci || npm install
          npx -y tsc -p tsconfig.json --noEmit
          npm test --silent
      - name: Install, type-check and test fleets-svc
        working-directory: services/fleets-svc
        run: |
          npm ci || npm install
          npx -y tsc -p tsconfig.json --noEmit
          npm test --silent
      - name: Install, type-check and test event-dispatcher
        working-directory: services/event-dispatcher
        run: |
          npm ci || npm install
          npx -y tsc -p tsconfig.json --noEmit
          npm test --silent
