// Global setup for Playwright tests
const { chromium } = require('@playwright/test');

async function globalSetup(config) {
  console.log('🚀 Starting Playwright global setup...');
  
  // Wait for services to be ready
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for frontend to be ready
    console.log('⏳ Waiting for frontend to be ready...');
    await page.goto('http://localhost:5174', { waitUntil: 'networkidle' });
    console.log('✅ Frontend is ready');
    
    // Wait for API Gateway to be ready
    console.log('⏳ Waiting for API Gateway to be ready...');
    await page.goto('http://localhost:19081/v1/health');
    console.log('✅ API Gateway is ready');
    
    // Check if materials service is available
    try {
      await page.goto('http://localhost:8086/health');
      console.log('✅ Materials service is ready');
    } catch (error) {
      console.log('⚠️  Materials service not available - some tests may fail');
    }
    
  } catch (error) {
    console.error('❌ Global setup failed:', error.message);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('✅ Playwright global setup completed');
}

module.exports = globalSetup;
