const { test, expect } = require('@playwright/test');

test.describe('Materials System - Cross-Browser Tests', () => {
  const API_BASE_URL = 'http://localhost:19081';
  
  test.beforeEach(async ({ page }) => {
    // Set up API route interception
    await page.route(`${API_BASE_URL}/v1/materials`, async route => {
      const response = await route.fetch();
      await route.fulfill({ response });
    });
    
    await page.route(`${API_BASE_URL}/v1/material-deposits*`, async route => {
      const response = await route.fetch();
      await route.fulfill({ response });
    });
    
    await page.route(`${API_BASE_URL}/v1/stations*`, async route => {
      const response = await route.fetch();
      await route.fulfill({ response });
    });
    
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the app to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Materials Management Interface', () => {
    test('should display materials manager component', async ({ page }) => {
      // Look for materials manager component
      const materialsManager = page.locator('[data-testid="materials-manager"]');
      await expect(materialsManager).toBeVisible({ timeout: 10000 });
      
      // Check for tab navigation
      await expect(page.locator('[data-testid="materials-tab"]')).toBeVisible();
      await expect(page.locator('[data-testid="deposits-tab"]')).toBeVisible();
      await expect(page.locator('[data-testid="stations-tab"]')).toBeVisible();
      await expect(page.locator('[data-testid="construction-tab"]')).toBeVisible();
    });

    test('should load and display materials list', async ({ page }) => {
      // Click on materials tab
      await page.click('[data-testid="materials-tab"]');
      
      // Wait for materials to load
      await page.waitForResponse(`${API_BASE_URL}/v1/materials`);
      
      // Check that materials are displayed
      const materialCards = page.locator('[data-testid="material-card"]');
      await expect(materialCards.first()).toBeVisible({ timeout: 10000 });
      
      // Check material card content
      const firstCard = materialCards.first();
      await expect(firstCard.locator('[data-testid="material-name"]')).toBeVisible();
      await expect(firstCard.locator('[data-testid="material-category"]')).toBeVisible();
      await expect(firstCard.locator('[data-testid="material-description"]')).toBeVisible();
    });

    test('should display material categories with correct styling', async ({ page }) => {
      await page.click('[data-testid="materials-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/materials`);
      
      // Check for material category badges
      const categoryBadges = page.locator('[data-testid="material-category"]');
      await expect(categoryBadges.first()).toBeVisible();
      
      // Verify category colors are applied
      const metalCategory = page.locator('[data-testid="material-category"][data-category="metal"]');
      if (await metalCategory.count() > 0) {
        await expect(metalCategory.first()).toHaveClass(/bg-gray-600/);
      }
    });

    test('should load and display material deposits table', async ({ page }) => {
      // Click on deposits tab
      await page.click('[data-testid="deposits-tab"]');
      
      // Wait for deposits to load
      await page.waitForResponse(/\/v1\/material-deposits/);
      
      // Check that deposits table is displayed
      const depositsTable = page.locator('[data-testid="deposits-table"]');
      await expect(depositsTable).toBeVisible({ timeout: 10000 });
      
      // Check table headers
      await expect(page.locator('text=Material')).toBeVisible();
      await expect(page.locator('text=Body')).toBeVisible();
      await expect(page.locator('text=Richness')).toBeVisible();
      await expect(page.locator('text=Accessibility')).toBeVisible();
      await expect(page.locator('text=Status')).toBeVisible();
    });

    test('should display richness indicators with color coding', async ({ page }) => {
      await page.click('[data-testid="deposits-tab"]');
      await page.waitForResponse(/\/v1\/material-deposits/);
      
      // Check richness indicators
      const richnessIndicators = page.locator('[data-testid="richness-indicator"]');
      const count = await richnessIndicators.count();
      
      if (count > 0) {
        // Check that at least one richness indicator is visible
        await expect(richnessIndicators.first()).toBeVisible();
        
        // Verify color classes are applied based on richness values
        for (let i = 0; i < Math.min(count, 5); i++) {
          const indicator = richnessIndicators.nth(i);
          const text = await indicator.textContent();
          const richness = parseFloat(text);
          
          if (richness >= 80) {
            await expect(indicator).toHaveClass(/text-green-400/);
          } else if (richness >= 60) {
            await expect(indicator).toHaveClass(/text-yellow-400/);
          } else if (richness >= 40) {
            await expect(indicator).toHaveClass(/text-orange-400/);
          } else {
            await expect(indicator).toHaveClass(/text-red-400/);
          }
        }
      }
    });

    test('should load and display space stations', async ({ page }) => {
      // Click on stations tab
      await page.click('[data-testid="stations-tab"]');
      
      // Wait for stations to load
      await page.waitForResponse(/\/v1\/stations/);
      
      // Check if stations are displayed (may be empty)
      const stationCards = page.locator('[data-testid="station-card"]');
      const stationCount = await stationCards.count();
      
      if (stationCount > 0) {
        // If stations exist, check their content
        const firstStation = stationCards.first();
        await expect(firstStation.locator('[data-testid="station-name"]')).toBeVisible();
        await expect(firstStation.locator('[data-testid="station-type"]')).toBeVisible();
        await expect(firstStation.locator('[data-testid="station-status"]')).toBeVisible();
      } else {
        // If no stations, should show empty state or message
        console.log('No stations found - this is expected for a fresh installation');
      }
    });
  });

  test.describe('Station Construction Workflow', () => {
    test('should display construction form with all required fields', async ({ page }) => {
      // Click on construction tab
      await page.click('[data-testid="construction-tab"]');
      
      // Wait for station types to load
      await page.waitForResponse(`${API_BASE_URL}/v1/station-types`);
      
      // Check form elements
      await expect(page.locator('[data-testid="construction-form"]')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('[data-testid="station-type-select"]')).toBeVisible();
      await expect(page.locator('[data-testid="orbiting-body-type-select"]')).toBeVisible();
      await expect(page.locator('[data-testid="orbital-distance-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="construct-button"]')).toBeVisible();
    });

    test('should populate station type dropdown with options', async ({ page }) => {
      await page.click('[data-testid="construction-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/station-types`);
      
      // Click on station type select to open dropdown
      await page.click('[data-testid="station-type-select"]');
      
      // Check that options are available
      const options = page.locator('[data-testid="station-type-select"] option');
      const optionCount = await options.count();
      
      // Should have at least the default option plus station types
      expect(optionCount).toBeGreaterThan(1);
      
      // Check for expected station types
      await expect(page.locator('option:has-text("Mining")')).toBeVisible();
    });

    test('should show/hide body ID field based on orbiting body selection', async ({ page }) => {
      await page.click('[data-testid="construction-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/station-types`);
      
      // Initially, body ID should not be visible (deep space)
      await expect(page.locator('[data-testid="body-id-input"]')).not.toBeVisible();
      
      // Select planet as orbiting body
      await page.selectOption('[data-testid="orbiting-body-type-select"]', 'planet');
      
      // Body ID field should now be visible
      await expect(page.locator('[data-testid="body-id-input"]')).toBeVisible();
      
      // Select deep space again
      await page.selectOption('[data-testid="orbiting-body-type-select"]', '');
      
      // Body ID field should be hidden again
      await expect(page.locator('[data-testid="body-id-input"]')).not.toBeVisible();
    });

    test('should validate form before submission', async ({ page }) => {
      await page.click('[data-testid="construction-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/station-types`);
      
      // Try to submit without required fields
      await page.click('[data-testid="construct-button"]');
      
      // Check for HTML5 validation
      const stationTypeSelect = page.locator('[data-testid="station-type-select"]');
      const isInvalid = await stationTypeSelect.evaluate(el => !el.checkValidity());
      expect(isInvalid).toBe(true);
    });

    test('should submit construction order successfully', async ({ page }) => {
      await page.click('[data-testid="construction-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/station-types`);
      
      // Fill out the form
      await page.selectOption('[data-testid="station-type-select"]', 'orbital_mining_platform');
      await page.selectOption('[data-testid="orbiting-body-type-select"]', 'moon');
      await page.fill('[data-testid="body-id-input"]', '1');
      await page.fill('[data-testid="orbital-distance-input"]', '15000');
      
      // Set up response interception for construction order
      await page.route(`${API_BASE_URL}/v1/station-construction-orders`, async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            construction_order: {
              id: 'test-order-123',
              empire_id: 'test-empire-1',
              station_type_id: 'orbital_mining_platform'
            }
          })
        });
      });
      
      // Submit the form
      await page.click('[data-testid="construct-button"]');
      
      // Wait for API call
      await page.waitForResponse(`${API_BASE_URL}/v1/station-construction-orders`);
      
      // Check for success message
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible({ timeout: 5000 });
      
      // Form should be reset
      const stationTypeValue = await page.locator('[data-testid="station-type-select"]').inputValue();
      expect(stationTypeValue).toBe('');
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Intercept materials API to return error
      await page.route(`${API_BASE_URL}/v1/materials`, async route => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });
      
      // Navigate to materials tab
      await page.click('[data-testid="materials-tab"]');
      
      // Wait for error response
      await page.waitForResponse(`${API_BASE_URL}/v1/materials`);
      
      // Check that error message is displayed
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible({ timeout: 5000 });
    });

    test('should show loading states', async ({ page }) => {
      // Intercept API to add delay
      await page.route(`${API_BASE_URL}/v1/materials`, async route => {
        // Add 2 second delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        const response = await route.fetch();
        await route.fulfill({ response });
      });
      
      // Navigate to materials tab
      await page.click('[data-testid="materials-tab"]');
      
      // Check that loading indicator is shown
      await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible();
      
      // Wait for loading to complete
      await page.waitForResponse(`${API_BASE_URL}/v1/materials`);
      
      // Loading indicator should be hidden
      await expect(page.locator('[data-testid="loading-indicator"]')).not.toBeVisible();
    });
  });

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page, browserName }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Check that materials manager is still functional
      await expect(page.locator('[data-testid="materials-manager"]')).toBeVisible();
      
      // Check that tabs are accessible on mobile
      await page.click('[data-testid="materials-tab"]');
      await page.click('[data-testid="deposits-tab"]');
      await page.click('[data-testid="stations-tab"]');
      await page.click('[data-testid="construction-tab"]');
      
      // All tabs should be clickable and visible
      await expect(page.locator('[data-testid="construction-form"]')).toBeVisible();
    });

    test('should work on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Check that layout adapts properly
      await expect(page.locator('[data-testid="materials-manager"]')).toBeVisible();
      
      // Check grid layouts
      await page.click('[data-testid="materials-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/materials`);
      
      const materialCards = page.locator('[data-testid="material-card"]');
      const cardCount = await materialCards.count();
      
      if (cardCount > 0) {
        await expect(materialCards.first()).toBeVisible();
      }
    });
  });

  test.describe('Performance Tests', () => {
    test('should load materials data within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.click('[data-testid="materials-tab"]');
      await page.waitForResponse(`${API_BASE_URL}/v1/materials`);
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(5000); // 5 seconds max for Playwright
    });

    test('should handle scrolling in large datasets', async ({ page }) => {
      // Mock large dataset for deposits
      await page.route(/\/v1\/material-deposits/, async route => {
        const largeDataset = {
          deposits: Array.from({ length: 1000 }, (_, i) => ({
            id: `deposit-${i}`,
            material_name: `Material ${i}`,
            body_type: 'planet',
            body_id: i,
            richness: Math.random(),
            accessibility: Math.random(),
            discovered: true
          }))
        };
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataset)
        });
      });
      
      await page.click('[data-testid="deposits-tab"]');
      await page.waitForResponse(/\/v1\/material-deposits/);
      
      // Check that table is displayed
      await expect(page.locator('[data-testid="deposits-table"]')).toBeVisible();
      
      // Test scrolling performance
      const table = page.locator('[data-testid="deposits-table"]');
      await table.scrollIntoViewIfNeeded();
      
      // Scroll to bottom and back to top
      await page.keyboard.press('End');
      await page.keyboard.press('Home');
      
      // Table should still be responsive
      await expect(table).toBeVisible();
    });
  });
});
