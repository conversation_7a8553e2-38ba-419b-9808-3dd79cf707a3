import { test, expect } from '@playwright/test';

test.describe('Stellar Database Comprehensive Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the galaxy view
    await page.goto('http://localhost:5174');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for the galaxy view to initialize
    await page.waitForSelector('canvas', { timeout: 10000 });
    
    // Wait a bit more for 3D scene to render
    await page.waitForTimeout(3000);
  });

  test('should have both widgets visible and draggable', async ({ page }) => {
    // Check that search widget is visible
    const searchWidget = page.locator('text=🔍 Star Search');
    await expect(searchWidget).toBeVisible();
    
    // Check that optimization widget is visible
    const optimizationWidget = page.locator('text=🎮 Graphics Optimization');
    await expect(optimizationWidget).toBeVisible();
    
    // Check that both widgets have drag handles
    const searchDragHandle = page.locator('text=🔍 Star Search').locator('..').locator('text=⋮⋮');
    const optimizationDragHandle = page.locator('text=🎮 Graphics Optimization').locator('..').locator('text=⋮⋮');
    
    await expect(searchDragHandle).toBeVisible();
    await expect(optimizationDragHandle).toBeVisible();
  });

  test('should have correct distance range slider (10-100ly)', async ({ page }) => {
    // Find the distance slider
    const distanceSlider = page.locator('input[type="range"]').first();
    await expect(distanceSlider).toBeVisible();
    
    // Check slider attributes
    const minValue = await distanceSlider.getAttribute('min');
    const maxValue = await distanceSlider.getAttribute('max');
    const currentValue = await distanceSlider.getAttribute('value');
    
    expect(minValue).toBe('10');
    expect(maxValue).toBe('100');
    expect(parseInt(currentValue || '0')).toBeGreaterThanOrEqual(10);
    expect(parseInt(currentValue || '0')).toBeLessThanOrEqual(100);
    
    // Check range labels
    await expect(page.locator('text=10ly')).toBeVisible();
    await expect(page.locator('text=100ly')).toBeVisible();
  });

  test('should load stellar data successfully', async ({ page }) => {
    // Wait for stellar data to load
    await page.waitForTimeout(5000);
    
    // Check console logs for successful data loading
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'log') {
        logs.push(msg.text());
      }
    });
    
    // Refresh to capture logs
    await page.reload();
    await page.waitForTimeout(5000);
    
    // Look for successful stellar data loading
    const stellarDataLoaded = logs.some(log => 
      log.includes('SUCCESS! Loaded REAL stellar database') ||
      log.includes('Loaded stellar data')
    );
    
    expect(stellarDataLoaded).toBeTruthy();
  });

  test('should have performance and quality preset buttons working', async ({ page }) => {
    // Find performance mode button
    const performanceButton = page.locator('text=⚡ Performance Mode');
    await expect(performanceButton).toBeVisible();
    
    // Find quality mode button  
    const qualityButton = page.locator('text=🎨 Quality Mode');
    await expect(qualityButton).toBeVisible();
    
    // Click performance mode
    await performanceButton.click();
    await page.waitForTimeout(1000);
    
    // Check that distance slider value changed to 30ly
    const distanceSlider = page.locator('input[type="range"]').first();
    const performanceValue = await distanceSlider.getAttribute('value');
    expect(parseInt(performanceValue || '0')).toBe(30);
    
    // Click quality mode
    await qualityButton.click();
    await page.waitForTimeout(1000);
    
    // Check that distance slider value changed to 100ly
    const qualityValue = await distanceSlider.getAttribute('value');
    expect(parseInt(qualityValue || '0')).toBe(100);
  });

  test('should be able to drag widgets around', async ({ page }) => {
    // Get initial position of search widget
    const searchWidget = page.locator('text=🔍 Star Search').locator('..');
    const initialSearchBox = await searchWidget.boundingBox();
    
    // Get initial position of optimization widget
    const optimizationWidget = page.locator('text=🎮 Graphics Optimization').locator('..');
    const initialOptimizationBox = await optimizationWidget.boundingBox();
    
    expect(initialSearchBox).toBeTruthy();
    expect(initialOptimizationBox).toBeTruthy();
    
    // Drag search widget
    const searchDragHandle = page.locator('text=🔍 Star Search');
    await searchDragHandle.dragTo(page.locator('body'), {
      targetPosition: { x: 300, y: 100 }
    });
    
    await page.waitForTimeout(500);
    
    // Check that search widget moved
    const newSearchBox = await searchWidget.boundingBox();
    expect(newSearchBox?.x).not.toBe(initialSearchBox?.x);
    
    // Drag optimization widget
    const optimizationDragHandle = page.locator('text=🎮 Graphics Optimization');
    await optimizationDragHandle.dragTo(page.locator('body'), {
      targetPosition: { x: 400, y: 200 }
    });
    
    await page.waitForTimeout(500);
    
    // Check that optimization widget moved
    const newOptimizationBox = await optimizationWidget.boundingBox();
    expect(newOptimizationBox?.x).not.toBe(initialOptimizationBox?.x);
  });

  test('should display far galaxies in background', async ({ page }) => {
    // Check that far galaxies toggle exists and is enabled by default
    const farGalaxiesToggle = page.locator('input[type="checkbox"]').filter({ hasText: /far galaxies/i });
    
    // If toggle exists, it should be checked by default
    if (await farGalaxiesToggle.count() > 0) {
      await expect(farGalaxiesToggle).toBeChecked();
    }
    
    // Check for WebGL canvas (where galaxies would be rendered)
    const canvas = page.locator('canvas');
    await expect(canvas).toBeVisible();
    
    // Verify no WebGL context lost errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });
    
    await page.waitForTimeout(3000);
    
    const webglErrors = logs.filter(log => 
      log.includes('WebGL') || log.includes('Context Lost')
    );
    
    // Should have minimal WebGL errors
    expect(webglErrors.length).toBeLessThan(3);
  });

  test('should handle star search functionality', async ({ page }) => {
    // Find search input
    const searchInput = page.locator('input[type="text"]').first();
    await expect(searchInput).toBeVisible();
    
    // Search for a well-known star
    await searchInput.fill('Sol');
    await page.waitForTimeout(1000);
    
    // Should show search results
    const searchResults = page.locator('text=Sol').first();
    await expect(searchResults).toBeVisible();
    
    // Clear search
    await searchInput.fill('');
    await page.waitForTimeout(500);
  });

  test('should not have requestAnimationFrame violations', async ({ page }) => {
    const violations = [];
    
    page.on('console', msg => {
      if (msg.type() === 'warning' && msg.text().includes('requestAnimationFrame')) {
        violations.push(msg.text());
      }
    });
    
    // Wait and interact with the page
    await page.waitForTimeout(5000);
    
    // Drag a widget to trigger animation
    const searchWidget = page.locator('text=🔍 Star Search');
    await searchWidget.dragTo(page.locator('body'), {
      targetPosition: { x: 200, y: 150 }
    });
    
    await page.waitForTimeout(2000);
    
    // Should have minimal or no requestAnimationFrame violations
    expect(violations.length).toBeLessThan(5);
  });

  test('should verify stellar database quality', async ({ page }) => {
    // Navigate to a test endpoint that shows database stats
    await page.goto('http://localhost:19081/v1/stellar/stars?max_distance=100&limit=10');
    
    // Parse JSON response
    const content = await page.textContent('body');
    expect(content).toBeTruthy();
    
    const data = JSON.parse(content || '{}');
    
    // Verify we have stars
    expect(data.stars).toBeTruthy();
    expect(Array.isArray(data.stars)).toBeTruthy();
    expect(data.stars.length).toBeGreaterThan(0);
    
    // Check that stars have required fields
    const firstStar = data.stars[0];
    expect(firstStar.star_id).toBeTruthy();
    expect(firstStar.name).toBeTruthy();
    expect(typeof firstStar.distance_ly).toBe('number');
    expect(typeof firstStar.ra_deg).toBe('number');
    expect(typeof firstStar.dec_deg).toBe('number');
    
    // Check for 3D coordinates (should be present after our fix)
    expect(typeof firstStar.x_pc).toBe('number');
    expect(typeof firstStar.y_pc).toBe('number');
    expect(typeof firstStar.z_pc).toBe('number');
    
    // Check for stellar color (should be present)
    expect(firstStar.stellar_color).toBeTruthy();
  });

  test('should have realistic distance distribution', async ({ page }) => {
    // Test different distance ranges
    const testRanges = [
      { distance: 10, expectedMinStars: 20 },
      { distance: 50, expectedMinStars: 1000 },
      { distance: 100, expectedMinStars: 5000 }
    ];
    
    for (const range of testRanges) {
      await page.goto(`http://localhost:19081/v1/stellar/stars?max_distance=${range.distance}&limit=10000`);
      
      const content = await page.textContent('body');
      const data = JSON.parse(content || '{}');
      
      expect(data.stars.length).toBeGreaterThanOrEqual(range.expectedMinStars);
    }
  });
});
