#!/usr/bin/env node

/**
 * Game Startup Diagnostic Tool
 * Diagnoses why Galactic Genesis won't start and provides solutions
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

function makeRequest(url, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.on('error', reject);
  });
}

async function checkService(name, url, description) {
  try {
    const result = await makeRequest(url, 3000);
    if (result.status >= 200 && result.status < 400) {
      log(colors.green, `✅ ${name}: ${description} - Working`);
      return { name, status: 'working', details: `HTTP ${result.status}` };
    } else {
      log(colors.yellow, `⚠️  ${name}: ${description} - HTTP ${result.status}`);
      return { name, status: 'warning', details: `HTTP ${result.status}` };
    }
  } catch (error) {
    log(colors.red, `❌ ${name}: ${description} - ${error.message}`);
    return { name, status: 'failed', details: error.message };
  }
}

function checkFile(filePath, description) {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      log(colors.green, `✅ ${description}: Found (${stats.size} bytes)`);
      return { status: 'found', size: stats.size };
    } else {
      log(colors.red, `❌ ${description}: Not found`);
      return { status: 'missing' };
    }
  } catch (error) {
    log(colors.red, `❌ ${description}: Error - ${error.message}`);
    return { status: 'error', details: error.message };
  }
}

async function diagnoseStartup() {
  log(colors.bold + colors.magenta, '🔍 GALACTIC GENESIS - STARTUP DIAGNOSTIC');
  log(colors.magenta, '=' .repeat(60));
  
  const issues = [];
  const solutions = [];
  
  // 1. Check Frontend Development Server
  log(colors.bold + colors.blue, '\n1️⃣  Frontend Development Server');
  const frontend = await checkService(
    'Frontend',
    'http://localhost:5174',
    'Vite development server'
  );
  
  if (frontend.status !== 'working') {
    issues.push('Frontend development server not running');
    solutions.push('Start frontend: cd frontend && npm run dev');
  }
  
  // 2. Check Critical Frontend Files
  log(colors.bold + colors.blue, '\n2️⃣  Critical Frontend Files');
  const frontendFiles = [
    { path: '/var/www/star/frontend/src/App.tsx', desc: 'Main App component' },
    { path: '/var/www/star/frontend/src/main.tsx', desc: 'App entry point' },
    { path: '/var/www/star/frontend/src/components/Galaxy3D.tsx', desc: '3D Galaxy component' },
    { path: '/var/www/star/frontend/src/services/stellarApi.ts', desc: 'Stellar API client' },
    { path: '/var/www/star/frontend/src/store/gameStore.ts', desc: 'Game state store' }
  ];
  
  let missingFiles = 0;
  frontendFiles.forEach(file => {
    const result = checkFile(file.path, file.desc);
    if (result.status !== 'found') {
      missingFiles++;
    }
  });
  
  if (missingFiles > 0) {
    issues.push(`${missingFiles} critical frontend files missing`);
    solutions.push('Restore missing files from repository');
  }
  
  // 3. Check Backend Services (Optional)
  log(colors.bold + colors.blue, '\n3️⃣  Backend Services (Optional)');
  const backendServices = [
    { name: 'API Gateway', url: 'http://localhost:19080/v1/health', desc: 'Main API gateway' },
    { name: 'Stellar API', url: 'http://localhost:19080/v1/stellar/stars?limit=1', desc: 'Stellar database' }
  ];
  
  let backendWorking = 0;
  for (const service of backendServices) {
    const result = await checkService(service.name, service.url, service.desc);
    if (result.status === 'working') {
      backendWorking++;
    }
  }
  
  if (backendWorking === 0) {
    log(colors.yellow, '\n⚠️  Backend services not running (this is OK for development)');
    solutions.push('Optional: Start backend with "cd deploy && docker compose up -d"');
  }
  
  // 4. Check Node.js Dependencies
  log(colors.bold + colors.blue, '\n4️⃣  Node.js Dependencies');
  const packageJson = checkFile('/var/www/star/frontend/package.json', 'Package configuration');
  const nodeModules = checkFile('/var/www/star/frontend/node_modules', 'Node modules');
  
  if (packageJson.status !== 'found' || nodeModules.status !== 'found') {
    issues.push('Frontend dependencies not installed');
    solutions.push('Install dependencies: cd frontend && npm install');
  }
  
  // 5. Check Browser Compatibility
  log(colors.bold + colors.blue, '\n5️⃣  Browser Compatibility');
  log(colors.cyan, '   Modern browser with WebGL support required');
  log(colors.cyan, '   Supported: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+');
  
  // 6. Generate Diagnosis Report
  log(colors.bold + colors.blue, '\n📋 DIAGNOSIS REPORT');
  log(colors.blue, '=' .repeat(40));
  
  if (issues.length === 0) {
    log(colors.bold + colors.green, '\n🎉 NO CRITICAL ISSUES FOUND!');
    log(colors.green, 'Galactic Genesis should be working properly.');
    
    if (frontend.status === 'working') {
      log(colors.cyan, '\n🚀 Game should be accessible at: http://localhost:5174');
      log(colors.cyan, '   • 3D galaxy map with real stellar data');
      log(colors.cyan, '   • Interactive fleet management');
      log(colors.cyan, '   • Educational astronomical content');
    }
  } else {
    log(colors.bold + colors.red, '\n💥 ISSUES DETECTED:');
    issues.forEach((issue, index) => {
      log(colors.red, `   ${index + 1}. ${issue}`);
    });
    
    log(colors.bold + colors.yellow, '\n🔧 RECOMMENDED SOLUTIONS:');
    solutions.forEach((solution, index) => {
      log(colors.yellow, `   ${index + 1}. ${solution}`);
    });
  }
  
  // 7. Quick Start Guide
  log(colors.bold + colors.cyan, '\n🚀 QUICK START GUIDE');
  log(colors.cyan, '=' .repeat(30));
  log(colors.cyan, '1. Start frontend: cd frontend && npm run dev');
  log(colors.cyan, '2. Open browser: http://localhost:5174');
  log(colors.cyan, '3. Wait for stellar data to load');
  log(colors.cyan, '4. Explore the 3D galaxy map!');
  
  log(colors.bold + colors.magenta, '\n🎮 GAME FEATURES (Working without backend):');
  log(colors.magenta, '• 3D visualization of 29 real nearby stars');
  log(colors.magenta, '• Scientific stellar data from Gaia DR3 & NASA');
  log(colors.magenta, '• Interactive galaxy exploration');
  log(colors.magenta, '• Educational astronomical content');
  log(colors.magenta, '• Fleet management interface');
  
  log(colors.bold + colors.blue, '\n🛠️  DEVELOPMENT UTILITIES:');
  log(colors.blue, '• Browser console: disableWebSocket() - Disable real-time features');
  log(colors.blue, '• Browser console: showTutorial() - Show game tutorial');
  log(colors.blue, '• Environment: VITE_DISABLE_WS=true - Disable WebSocket entirely');
  
  // 8. Save diagnostic report
  const report = {
    timestamp: new Date().toISOString(),
    frontend: frontend,
    backend_services: backendWorking,
    issues: issues,
    solutions: solutions,
    status: issues.length === 0 ? 'healthy' : 'needs_attention'
  };
  
  try {
    fs.writeFileSync('/var/www/star/startup-diagnosis.json', JSON.stringify(report, null, 2));
    log(colors.cyan, '\n📄 Diagnostic report saved to: startup-diagnosis.json');
  } catch (error) {
    log(colors.yellow, `⚠️  Could not save diagnostic report: ${error.message}`);
  }
  
  return issues.length === 0;
}

// Run diagnostic
if (require.main === module) {
  diagnoseStartup().then(success => {
    if (success) {
      log(colors.bold + colors.green, '\n✨ Galactic Genesis is ready to launch! ✨');
      process.exit(0);
    } else {
      log(colors.bold + colors.red, '\n🔧 Please fix the issues above before launching.');
      process.exit(1);
    }
  }).catch(error => {
    log(colors.red, `💥 Diagnostic failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { diagnoseStartup };
