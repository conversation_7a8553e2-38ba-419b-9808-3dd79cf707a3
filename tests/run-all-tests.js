#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Galactic Genesis
 * Runs all test suites and generates a comprehensive report
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, args, cwd, description) {
  return new Promise((resolve, reject) => {
    log(colors.blue, `\n🔄 ${description}...`);
    log(colors.cyan, `   Command: ${command} ${args.join(' ')}`);
    log(colors.cyan, `   Directory: ${cwd}`);
    
    const startTime = Date.now();
    const child = spawn(command, args, {
      cwd,
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      const duration = Date.now() - startTime;
      const result = {
        command: `${command} ${args.join(' ')}`,
        description,
        code,
        stdout,
        stderr,
        duration,
        success: code === 0
      };

      if (code === 0) {
        log(colors.green, `✅ ${description} completed successfully (${duration}ms)`);
      } else {
        log(colors.red, `❌ ${description} failed with code ${code} (${duration}ms)`);
      }

      resolve(result);
    });

    child.on('error', (error) => {
      log(colors.red, `💥 ${description} failed to start: ${error.message}`);
      reject(error);
    });
  });
}

async function runTestSuite() {
  const results = [];
  const startTime = Date.now();
  
  log(colors.bold + colors.magenta, '🚀 GALACTIC GENESIS - COMPREHENSIVE TEST SUITE');
  log(colors.magenta, '=' .repeat(60));
  
  // Test configuration
  const tests = [
    {
      name: 'Smoke Tests - Health Check',
      command: 'node',
      args: ['tests/smoke/health-check.js'],
      cwd: '/var/www/star',
      critical: true
    },
    {
      name: 'Smoke Tests - Frontend',
      command: 'node',
      args: ['tests/smoke/frontend-smoke.js'],
      cwd: '/var/www/star',
      critical: true
    },
    {
      name: 'Unit Tests - Frontend',
      command: 'npm',
      args: ['run', 'test:run'],
      cwd: '/var/www/star/frontend',
      critical: true
    },
    {
      name: 'E2E Tests - Cypress',
      command: 'npm',
      args: ['run', 'cypress:run'],
      cwd: '/var/www/star/frontend',
      critical: false // Non-critical due to WebGL issues in headless mode
    }
  ];

  // Run each test suite
  for (const test of tests) {
    try {
      const result = await runCommand(
        test.command,
        test.args,
        test.cwd,
        test.name
      );
      
      results.push({
        ...result,
        critical: test.critical,
        name: test.name
      });
      
      // Brief pause between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      results.push({
        name: test.name,
        success: false,
        error: error.message,
        critical: test.critical
      });
    }
  }

  // Generate comprehensive report
  const totalDuration = Date.now() - startTime;
  generateReport(results, totalDuration);
  
  // Determine overall success
  const criticalFailures = results.filter(r => r.critical && !r.success);
  const overallSuccess = criticalFailures.length === 0;
  
  if (overallSuccess) {
    log(colors.bold + colors.green, '\n🎉 ALL CRITICAL TESTS PASSED!');
    log(colors.green, 'Galactic Genesis is ready for development and testing.');
    process.exit(0);
  } else {
    log(colors.bold + colors.red, '\n💥 CRITICAL TEST FAILURES DETECTED');
    log(colors.red, 'Please fix the issues before proceeding.');
    process.exit(1);
  }
}

function generateReport(results, totalDuration) {
  log(colors.bold + colors.blue, '\n📊 COMPREHENSIVE TEST REPORT');
  log(colors.blue, '=' .repeat(60));
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const critical = results.filter(r => r.critical).length;
  const criticalPassed = results.filter(r => r.critical && r.success).length;
  
  log(colors.bold, `\n📈 Summary:`);
  log(colors.cyan, `   Total Tests: ${results.length}`);
  log(colors.green, `   Passed: ${passed}`);
  log(colors.red, `   Failed: ${failed}`);
  log(colors.yellow, `   Critical Tests: ${critical}`);
  log(colors.green, `   Critical Passed: ${criticalPassed}`);
  log(colors.cyan, `   Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);
  
  log(colors.bold, `\n📋 Detailed Results:`);
  
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const critical = result.critical ? '🔥' : '📝';
    const duration = result.duration ? `(${result.duration}ms)` : '';
    
    log(colors.cyan, `\n${index + 1}. ${critical} ${result.name}`);
    log(result.success ? colors.green : colors.red, `   ${status} ${result.success ? 'PASSED' : 'FAILED'} ${duration}`);
    
    if (!result.success && result.stderr) {
      const errorLines = result.stderr.split('\n').slice(0, 3);
      errorLines.forEach(line => {
        if (line.trim()) {
          log(colors.red, `   📄 ${line.trim()}`);
        }
      });
    }
  });
  
  // Recommendations
  log(colors.bold + colors.yellow, '\n💡 Recommendations:');
  
  const smokeFailures = results.filter(r => r.name.includes('Smoke') && !r.success);
  const unitFailures = results.filter(r => r.name.includes('Unit') && !r.success);
  const e2eFailures = results.filter(r => r.name.includes('E2E') && !r.success);
  
  if (smokeFailures.length > 0) {
    log(colors.yellow, '   🔧 Fix smoke test failures first - these indicate basic setup issues');
  }
  
  if (unitFailures.length > 0) {
    log(colors.yellow, '   🧪 Address unit test failures - these indicate code logic issues');
  }
  
  if (e2eFailures.length > 0) {
    log(colors.yellow, '   🎭 E2E failures may be due to missing backend services (non-critical)');
  }
  
  if (passed === results.length) {
    log(colors.green, '   🚀 All tests passing! Ready for production deployment');
  }
  
  // Save report to file
  const reportPath = '/var/www/star/test-report.json';
  const report = {
    timestamp: new Date().toISOString(),
    summary: { total: results.length, passed, failed, critical, criticalPassed },
    duration: totalDuration,
    results: results.map(r => ({
      name: r.name,
      success: r.success,
      critical: r.critical,
      duration: r.duration,
      command: r.command
    }))
  };
  
  try {
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(colors.cyan, `\n📄 Detailed report saved to: ${reportPath}`);
  } catch (error) {
    log(colors.yellow, `⚠️  Could not save report: ${error.message}`);
  }
}

// Run the test suite
if (require.main === module) {
  runTestSuite().catch(error => {
    log(colors.red, `💥 Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTestSuite };
