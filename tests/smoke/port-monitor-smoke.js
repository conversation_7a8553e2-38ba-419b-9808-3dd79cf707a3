#!/usr/bin/env node

/**
 * Port Monitor Smoke Tests
 * 
 * Tests the port monitoring functionality of the health dashboard
 */

const http = require('http');
const WebSocket = require('ws');

const HEALTH_DASHBOARD_URL = process.env.HEALTH_DASHBOARD_URL || 'http://localhost:8086';
const WS_URL = HEALTH_DASHBOARD_URL.replace('http', 'ws') + '/ws';

let testResults = [];
let totalTests = 0;
let passedTests = 0;

function log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`);
}

function logTest(testName, passed, details = '') {
    totalTests++;
    if (passed) {
        passedTests++;
        log(`✅ ${testName}`);
    } else {
        log(`❌ ${testName}: ${details}`);
    }
    testResults.push({ testName, passed, details });
}

async function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const req = http.request(url, options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(data);
                    resolve({ status: res.statusCode, data: parsed });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });
        
        req.on('error', reject);
        
        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

async function testPortScanEndpoint() {
    try {
        const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports`);
        
        if (response.status !== 200) {
            logTest('Port scan endpoint returns 200', false, `Got status ${response.status}`);
            return;
        }
        
        logTest('Port scan endpoint returns 200', true);
        
        // Validate response structure
        const data = response.data;
        const hasRequiredFields = data.timestamp && data.ports && data.summary;
        logTest('Port scan response has required fields', hasRequiredFields, 
            hasRequiredFields ? '' : 'Missing timestamp, ports, or summary');
        
        // Validate summary structure
        const summary = data.summary;
        const summaryValid = typeof summary.total === 'number' && 
                           typeof summary.listening === 'number' &&
                           typeof summary.expected === 'number' &&
                           typeof summary.unexpected === 'number' &&
                           typeof summary.missing === 'number';
        logTest('Port scan summary structure valid', summaryValid);
        
        // Validate ports array
        const portsValid = Array.isArray(data.ports) && data.ports.length > 0;
        logTest('Port scan returns ports array', portsValid);
        
        if (portsValid && data.ports.length > 0) {
            const firstPort = data.ports[0];
            const portStructureValid = typeof firstPort.port === 'number' &&
                                     typeof firstPort.protocol === 'string' &&
                                     typeof firstPort.state === 'string' &&
                                     typeof firstPort.isExpected === 'boolean';
            logTest('Port object structure valid', portStructureValid);
        }
        
        log(`📊 Port scan summary: ${summary.total} total, ${summary.listening} listening, ${summary.expected} expected`);
        
    } catch (error) {
        logTest('Port scan endpoint accessible', false, error.message);
    }
}

async function testExpectedPortsEndpoint() {
    try {
        const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/expected`);
        
        if (response.status !== 200) {
            logTest('Expected ports endpoint returns 200', false, `Got status ${response.status}`);
            return;
        }
        
        logTest('Expected ports endpoint returns 200', true);
        
        const data = response.data;
        const isArray = Array.isArray(data);
        logTest('Expected ports returns array', isArray);
        
        if (isArray && data.length > 0) {
            const firstPort = data[0];
            const structureValid = typeof firstPort.port === 'number' &&
                                 typeof firstPort.service === 'string' &&
                                 typeof firstPort.description === 'string' &&
                                 typeof firstPort.protocol === 'string';
            logTest('Expected port object structure valid', structureValid);
            
            // Check for known Galactic Genesis ports
            const hasApiGateway = data.some(p => p.port === 19081 && p.service === 'API Gateway');
            const hasHealthDashboard = data.some(p => p.port === 8086 && p.service === 'Health Dashboard');
            logTest('Expected ports include API Gateway (19081)', hasApiGateway);
            logTest('Expected ports include Health Dashboard (8086)', hasHealthDashboard);
        }
        
    } catch (error) {
        logTest('Expected ports endpoint accessible', false, error.message);
    }
}

async function testPortCheckEndpoint() {
    try {
        // Test checking the health dashboard port (should be occupied)
        const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/8086/check`);
        
        if (response.status !== 200) {
            logTest('Port check endpoint returns 200', false, `Got status ${response.status}`);
            return;
        }
        
        logTest('Port check endpoint returns 200', true);
        
        const data = response.data;
        const structureValid = typeof data.port === 'number' && typeof data.available === 'boolean';
        logTest('Port check response structure valid', structureValid);
        
        // Health dashboard port should not be available (it's running)
        const correctAvailability = data.port === 8086 && data.available === false;
        logTest('Port check correctly identifies occupied port', correctAvailability);
        
    } catch (error) {
        logTest('Port check endpoint accessible', false, error.message);
    }
}

async function testFindFreePortEndpoint() {
    try {
        const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/find-free?start=9000&max=10`);
        
        if (response.status !== 200 && response.status !== 404) {
            logTest('Find free port endpoint returns valid status', false, `Got status ${response.status}`);
            return;
        }
        
        logTest('Find free port endpoint returns valid status', true);
        
        if (response.status === 200) {
            const data = response.data;
            const structureValid = typeof data.port === 'number' && typeof data.available === 'boolean';
            logTest('Find free port response structure valid', structureValid);
            
            const portInRange = data.port >= 9000 && data.port < 9010;
            logTest('Found port is in requested range', portInRange);
        }
        
    } catch (error) {
        logTest('Find free port endpoint accessible', false, error.message);
    }
}

async function testWebSocketPortUpdates() {
    return new Promise((resolve) => {
        try {
            const ws = new WebSocket(WS_URL);
            let receivedPortData = false;
            
            const timeout = setTimeout(() => {
                ws.close();
                logTest('WebSocket port updates received', false, 'Timeout waiting for port-scan message');
                resolve();
            }, 35000); // Wait up to 35 seconds for port scan update
            
            ws.on('open', () => {
                logTest('WebSocket connection established', true);
            });
            
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.type === 'port-scan') {
                        receivedPortData = true;
                        clearTimeout(timeout);
                        
                        const hasValidData = message.data && message.data.ports && message.data.summary;
                        logTest('WebSocket port updates received', hasValidData);
                        
                        ws.close();
                        resolve();
                    }
                } catch (e) {
                    // Ignore invalid JSON
                }
            });
            
            ws.on('error', (error) => {
                clearTimeout(timeout);
                logTest('WebSocket connection established', false, error.message);
                resolve();
            });
            
        } catch (error) {
            logTest('WebSocket connection established', false, error.message);
            resolve();
        }
    });
}

async function testPortKillEndpoint() {
    try {
        // Test with a non-existent port (should fail gracefully)
        const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/99999/kill`, {
            method: 'POST'
        });
        
        // Should return 400 or 500, but not crash
        const validStatus = response.status === 400 || response.status === 500;
        logTest('Port kill endpoint handles invalid port gracefully', validStatus);
        
        if (response.data && typeof response.data === 'object') {
            const hasErrorMessage = response.data.error || response.data.message;
            logTest('Port kill endpoint returns error message', !!hasErrorMessage);
        }
        
    } catch (error) {
        logTest('Port kill endpoint accessible', false, error.message);
    }
}

async function testDashboardUI() {
    try {
        const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/`);
        
        if (response.status !== 200) {
            logTest('Dashboard UI accessible', false, `Got status ${response.status}`);
            return;
        }
        
        logTest('Dashboard UI accessible', true);
        
        const htmlContent = response.data;
        const hasPortMonitorButton = htmlContent.includes('Port Monitor') || htmlContent.includes('network-wired');
        logTest('Dashboard UI includes port monitor elements', hasPortMonitorButton);
        
    } catch (error) {
        logTest('Dashboard UI accessible', false, error.message);
    }
}

async function runAllTests() {
    log('🚀 Starting Port Monitor Smoke Tests...');
    log(`📍 Testing against: ${HEALTH_DASHBOARD_URL}`);
    
    await testPortScanEndpoint();
    await testExpectedPortsEndpoint();
    await testPortCheckEndpoint();
    await testFindFreePortEndpoint();
    await testPortKillEndpoint();
    await testDashboardUI();
    await testWebSocketPortUpdates();
    
    log('\n📊 Test Results Summary:');
    log(`✅ Passed: ${passedTests}/${totalTests}`);
    log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
        log('🎉 All port monitor smoke tests passed!');
        process.exit(0);
    } else {
        log('💥 Some port monitor smoke tests failed!');
        
        // Show failed tests
        const failedTests = testResults.filter(t => !t.passed);
        if (failedTests.length > 0) {
            log('\n❌ Failed Tests:');
            failedTests.forEach(test => {
                log(`   - ${test.testName}: ${test.details}`);
            });
        }
        
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    log('\n🛑 Tests interrupted by user');
    process.exit(1);
});

process.on('SIGTERM', () => {
    log('\n🛑 Tests terminated');
    process.exit(1);
});

// Run tests
runAllTests().catch(error => {
    log(`💥 Test suite failed: ${error.message}`);
    process.exit(1);
});
