#!/usr/bin/env node

/**
 * Frontend Port Monitor Smoke Test
 * Tests the port monitoring integration in the frontend using real data
 */

const { execSync } = require('child_process');

// Test configuration
const FRONTEND_URL = 'http://localhost:5176';
const HEALTH_DASHBOARD_URL = 'http://localhost:8086';
const TIMEOUT = 10000;

let testsPassed = 0;
let testsFailed = 0;

function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
  testsPassed++;
}

function logError(message) {
  console.error(`❌ ${message}`);
  testsFailed++;
}

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const curlCmd = `curl -s -w "HTTPSTATUS:%{http_code}" "${url}"`;
      const result = execSync(curlCmd, { timeout: TIMEOUT, encoding: 'utf8' });

      const parts = result.split('HTTPSTATUS:');
      const body = parts[0];
      const status = parseInt(parts[1] || '0');

      resolve({
        ok: status >= 200 && status < 300,
        status: status,
        json: async () => JSON.parse(body),
        text: async () => body
      });
    } catch (error) {
      reject(error);
    }
  });
}

async function testHealthDashboardAvailable() {
  log('Testing health dashboard availability...');
  try {
    const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports`);
    if (response.ok) {
      logSuccess('Health dashboard is available');
      return true;
    } else {
      logError(`Health dashboard returned status ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Health dashboard is not available: ${error.message}`);
    return false;
  }
}

async function testPortDataStructure() {
  log('Testing port data structure...');
  try {
    const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports`);
    const data = await response.json();
    
    // Check required fields
    if (!data.timestamp) {
      logError('Port data missing timestamp');
      return false;
    }
    
    if (!Array.isArray(data.ports)) {
      logError('Port data missing ports array');
      return false;
    }
    
    if (!data.summary || typeof data.summary !== 'object') {
      logError('Port data missing summary object');
      return false;
    }
    
    // Check summary fields
    const requiredSummaryFields = ['total', 'listening', 'expected', 'unexpected', 'missing'];
    for (const field of requiredSummaryFields) {
      if (typeof data.summary[field] !== 'number') {
        logError(`Port summary missing or invalid field: ${field}`);
        return false;
      }
    }
    
    // Check port structure
    if (data.ports.length > 0) {
      const port = data.ports[0];
      const requiredPortFields = ['port', 'protocol', 'state', 'isExpected', 'lastChecked'];
      for (const field of requiredPortFields) {
        if (port[field] === undefined) {
          logError(`Port object missing field: ${field}`);
          return false;
        }
      }
    }
    
    logSuccess(`Port data structure is valid (${data.ports.length} ports, ${data.summary.total} total)`);
    return true;
  } catch (error) {
    logError(`Failed to validate port data structure: ${error.message}`);
    return false;
  }
}

async function testExpectedPortsEndpoint() {
  log('Testing expected ports endpoint...');
  try {
    const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/expected`);
    const data = await response.json();
    
    if (!Array.isArray(data)) {
      logError('Expected ports endpoint should return an array');
      return false;
    }
    
    if (data.length > 0) {
      const expectedPort = data[0];
      const requiredFields = ['port', 'service', 'description', 'protocol'];
      for (const field of requiredFields) {
        if (expectedPort[field] === undefined) {
          logError(`Expected port object missing field: ${field}`);
          return false;
        }
      }
    }
    
    logSuccess(`Expected ports endpoint is working (${data.length} expected ports)`);
    return true;
  } catch (error) {
    logError(`Failed to test expected ports endpoint: ${error.message}`);
    return false;
  }
}

async function testPortCheckEndpoint() {
  log('Testing port check endpoint...');
  try {
    // Test with a common port
    const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/8086/check`);
    const data = await response.json();
    
    if (typeof data.port !== 'number' || typeof data.available !== 'boolean') {
      logError('Port check endpoint returned invalid data structure');
      return false;
    }
    
    if (data.port !== 8086) {
      logError('Port check endpoint returned wrong port number');
      return false;
    }
    
    // Port 8086 should not be available since health dashboard is using it
    if (data.available) {
      logError('Port 8086 should not be available (health dashboard is using it)');
      return false;
    }
    
    logSuccess('Port check endpoint is working correctly');
    return true;
  } catch (error) {
    logError(`Failed to test port check endpoint: ${error.message}`);
    return false;
  }
}

async function testFindFreePortEndpoint() {
  log('Testing find free port endpoint...');
  try {
    const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports/find-free?start=9000&max=10`);
    
    if (response.status === 404) {
      logSuccess('Find free port endpoint correctly returned 404 (no free ports in range)');
      return true;
    }
    
    if (response.ok) {
      const data = await response.json();
      
      if (typeof data.port !== 'number' || typeof data.available !== 'boolean') {
        logError('Find free port endpoint returned invalid data structure');
        return false;
      }
      
      if (!data.available) {
        logError('Find free port endpoint returned a port that is not available');
        return false;
      }
      
      logSuccess(`Find free port endpoint found port ${data.port}`);
      return true;
    }
    
    logError(`Find free port endpoint returned unexpected status: ${response.status}`);
    return false;
  } catch (error) {
    logError(`Failed to test find free port endpoint: ${error.message}`);
    return false;
  }
}

async function testFrontendAvailable() {
  log('Testing frontend availability...');
  try {
    const response = await makeRequest(FRONTEND_URL);
    if (response.ok) {
      const html = await response.text();
      if (html.includes('Galactic Genesis') || html.includes('GALACTIC GENESIS')) {
        logSuccess('Frontend is available and contains expected content');
        return true;
      } else {
        logError('Frontend is available but missing expected content');
        return false;
      }
    } else {
      logError(`Frontend returned status ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Frontend is not available: ${error.message}`);
    return false;
  }
}

async function testPortMonitorService() {
  log('Testing port monitor service integration...');
  try {
    // Test that the port monitor service can connect to health dashboard
    const response = await makeRequest(`${HEALTH_DASHBOARD_URL}/api/ports`);
    if (!response.ok) {
      logError('Port monitor service cannot connect to health dashboard');
      return false;
    }
    
    const data = await response.json();
    
    // Verify we have comprehensive port data
    if (data.summary.total < 10) {
      logError(`Expected more comprehensive port data, got only ${data.summary.total} ports`);
      return false;
    }
    
    // Verify we have both expected and unexpected ports
    if (data.summary.expected === 0) {
      logError('No expected ports found - configuration may be missing');
      return false;
    }
    
    if (data.summary.unexpected === 0) {
      logError('No unexpected ports found - this is unusual for a real system');
      return false;
    }
    
    logSuccess(`Port monitor service is working with comprehensive data (${data.summary.total} total ports)`);
    return true;
  } catch (error) {
    logError(`Port monitor service test failed: ${error.message}`);
    return false;
  }
}

async function runSmokeTests() {
  log('🚀 Starting Frontend Port Monitor Smoke Tests');
  log('================================================');
  
  const tests = [
    testHealthDashboardAvailable,
    testPortDataStructure,
    testExpectedPortsEndpoint,
    testPortCheckEndpoint,
    testFindFreePortEndpoint,
    testFrontendAvailable,
    testPortMonitorService
  ];
  
  for (const test of tests) {
    try {
      await test();
    } catch (error) {
      logError(`Test ${test.name} threw an exception: ${error.message}`);
    }
    log(''); // Empty line for readability
  }
  
  log('================================================');
  log(`📊 Test Results: ${testsPassed} passed, ${testsFailed} failed`);
  
  if (testsFailed === 0) {
    log('🎉 All frontend port monitor smoke tests passed!');
    process.exit(0);
  } else {
    log('💥 Some frontend port monitor smoke tests failed!');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run the tests
runSmokeTests().catch(error => {
  logError(`Smoke test runner failed: ${error.message}`);
  process.exit(1);
});
