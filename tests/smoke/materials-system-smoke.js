#!/usr/bin/env node

/**
 * Materials System Smoke Tests for Galactic Genesis
 * Validates raw materials, trade stations, and mining functionality
 */

const http = require('http');
const { Pool } = require('pg');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:19081';
const MATERIALS_SVC_URL = process.env.MATERIALS_SVC_URL || 'http://localhost:8086';

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433'),
  database: process.env.DB_NAME || 'galactic_genesis',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 5000
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

// Database connection helper
async function testDatabase() {
  const pool = new Pool(DB_CONFIG);
  try {
    await pool.query('SELECT 1');
    return pool;
  } catch (error) {
    throw new Error(`Database connection failed: ${error.message}`);
  }
}

// Test materials service health
async function testMaterialsServiceHealth() {
  log(colors.blue, '\n🏥 Testing Materials Service Health...');
  
  try {
    const result = await makeRequest(`${MATERIALS_SVC_URL}/health`);
    if (result.status === 200) {
      log(colors.green, '✅ Materials service is healthy');
      return true;
    } else {
      log(colors.red, `❌ Materials service health check failed: HTTP ${result.status}`);
      return false;
    }
  } catch (error) {
    log(colors.red, `❌ Materials service not responding: ${error.message}`);
    return false;
  }
}

// Test API Gateway materials endpoints
async function testApiGatewayEndpoints() {
  log(colors.blue, '\n🌐 Testing API Gateway Materials Endpoints...');
  
  const endpoints = [
    { path: '/v1/materials', name: 'Materials List' },
    { path: '/v1/material-deposits', name: 'Material Deposits' },
    { path: '/v1/stations', name: 'Space Stations' },
    { path: '/v1/station-types', name: 'Station Types' },
    { path: '/v1/processing-recipes', name: 'Processing Recipes' },
    { path: '/v1/mining-operations', name: 'Mining Operations' }
  ];

  let passCount = 0;
  
  for (const endpoint of endpoints) {
    try {
      const result = await makeRequest(`${API_BASE_URL}${endpoint.path}`);
      if (result.status === 200) {
        const data = JSON.parse(result.body);
        log(colors.green, `✅ ${endpoint.name}: HTTP 200 - Data loaded`);
        
        // Log data counts for verification
        const keys = Object.keys(data);
        if (keys.length > 0 && Array.isArray(data[keys[0]])) {
          log(colors.cyan, `   📊 ${data[keys[0]].length} items found`);
        }
        passCount++;
      } else {
        log(colors.red, `❌ ${endpoint.name}: HTTP ${result.status}`);
      }
    } catch (error) {
      log(colors.red, `❌ ${endpoint.name}: ${error.message}`);
    }
  }
  
  return passCount === endpoints.length;
}

// Test database schema
async function testDatabaseSchema() {
  log(colors.blue, '\n🗄️  Testing Database Schema...');
  
  try {
    const pool = await testDatabase();
    
    const tables = [
      { name: 'material_types', description: 'Material Types Catalog' },
      { name: 'body_material_deposits', description: 'Material Deposits' },
      { name: 'space_stations', description: 'Space Stations' },
      { name: 'station_types', description: 'Station Types' },
      { name: 'material_processing', description: 'Processing Recipes' },
      { name: 'mining_operations', description: 'Mining Operations' }
    ];

    let passCount = 0;
    
    for (const table of tables) {
      try {
        const result = await pool.query(`SELECT COUNT(*) FROM ${table.name}`);
        const count = parseInt(result.rows[0].count);
        
        if (count > 0) {
          log(colors.green, `✅ ${table.description}: ${count} records`);
          passCount++;
        } else {
          log(colors.yellow, `⚠️  ${table.description}: Table exists but empty`);
        }
      } catch (error) {
        log(colors.red, `❌ ${table.description}: Table missing or error`);
      }
    }
    
    await pool.end();
    return passCount >= tables.length * 0.8; // Allow some tables to be empty
  } catch (error) {
    log(colors.red, `❌ Database schema test failed: ${error.message}`);
    return false;
  }
}

// Test material distribution
async function testMaterialDistribution() {
  log(colors.blue, '\n🌍 Testing Material Distribution...');
  
  try {
    const pool = await testDatabase();
    
    // Test material categories
    const categoryResult = await pool.query(`
      SELECT category, COUNT(*) as count
      FROM material_types
      GROUP BY category
      ORDER BY count DESC
    `);
    
    if (categoryResult.rows.length > 0) {
      log(colors.green, '✅ Material categories found:');
      categoryResult.rows.forEach(row => {
        log(colors.cyan, `   • ${row.category}: ${row.count} materials`);
      });
    } else {
      log(colors.red, '❌ No material categories found');
      await pool.end();
      return false;
    }
    
    // Test deposit distribution
    const depositResult = await pool.query(`
      SELECT body_type, COUNT(*) as deposit_count
      FROM body_material_deposits
      GROUP BY body_type
      ORDER BY deposit_count DESC
    `);
    
    if (depositResult.rows.length > 0) {
      log(colors.green, '✅ Material deposits distributed across body types:');
      depositResult.rows.forEach(row => {
        log(colors.cyan, `   • ${row.body_type}: ${row.deposit_count} deposits`);
      });
    } else {
      log(colors.yellow, '⚠️  No material deposits found (may need population)');
    }
    
    await pool.end();
    return true;
  } catch (error) {
    log(colors.red, `❌ Material distribution test failed: ${error.message}`);
    return false;
  }
}

// Test order creation
async function testOrderCreation() {
  log(colors.blue, '\n📝 Testing Order Creation...');
  
  let passCount = 0;
  
  // Test survey order creation
  try {
    const surveyOrderData = {
      empire_id: 'test-empire-1',
      target_body_type: 'planet',
      target_body_id: 1,
      system_id: 'test-system-1',
      survey_type: 'basic'
    };
    
    const result = await makeRequest(`${API_BASE_URL}/v1/survey-orders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(surveyOrderData)
    });
    
    if (result.status === 200) {
      const data = JSON.parse(result.body);
      if (data.survey_order && data.survey_order.id) {
        log(colors.green, '✅ Survey order creation successful');
        passCount++;
      } else {
        log(colors.red, '❌ Survey order creation returned invalid data');
      }
    } else {
      log(colors.yellow, `⚠️  Survey order creation: HTTP ${result.status} (may be expected if validation fails)`);
    }
  } catch (error) {
    log(colors.yellow, `⚠️  Survey order creation failed: ${error.message} (may be expected)`);
  }
  
  // Test station construction order creation
  try {
    const constructionOrderData = {
      empire_id: 'test-empire-1',
      system_id: 'test-system-1',
      station_type_id: 'orbital_mining_platform',
      orbiting_body_type: 'moon',
      orbiting_body_id: 1,
      orbital_distance_km: 10000
    };
    
    const result = await makeRequest(`${API_BASE_URL}/v1/station-construction-orders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(constructionOrderData)
    });
    
    if (result.status === 200) {
      const data = JSON.parse(result.body);
      if (data.construction_order && data.construction_order.id) {
        log(colors.green, '✅ Station construction order creation successful');
        passCount++;
      } else {
        log(colors.red, '❌ Station construction order creation returned invalid data');
      }
    } else {
      log(colors.yellow, `⚠️  Station construction order creation: HTTP ${result.status} (may be expected if validation fails)`);
    }
  } catch (error) {
    log(colors.yellow, `⚠️  Station construction order creation failed: ${error.message} (may be expected)`);
  }
  
  return passCount > 0; // At least one order type should work
}

// Main test runner
async function runMaterialsSmokeTests() {
  log(colors.bold + colors.blue, '🧪 Galactic Genesis - Materials System Smoke Tests');
  log(colors.blue, '=' .repeat(60));
  
  const tests = [
    { name: 'Materials Service Health', fn: testMaterialsServiceHealth },
    { name: 'API Gateway Endpoints', fn: testApiGatewayEndpoints },
    { name: 'Database Schema', fn: testDatabaseSchema },
    { name: 'Material Distribution', fn: testMaterialDistribution },
    { name: 'Order Creation', fn: testOrderCreation }
  ];
  
  let passCount = 0;
  const results = {};
  
  for (const test of tests) {
    try {
      const passed = await test.fn();
      results[test.name] = passed;
      if (passed) passCount++;
    } catch (error) {
      log(colors.red, `❌ ${test.name} failed with error: ${error.message}`);
      results[test.name] = false;
    }
  }
  
  // Summary
  log(colors.blue, '\n' + '=' .repeat(60));
  log(colors.bold, `📊 Materials System Test Results: ${passCount}/${tests.length} passed`);
  
  if (passCount === tests.length) {
    log(colors.green, '🎉 All materials system smoke tests passed!');
    return true;
  } else if (passCount >= tests.length * 0.7) {
    log(colors.yellow, '⚠️  Most materials system tests passed, minor issues detected.');
    
    // Recommendations
    log(colors.yellow, '\n💡 Troubleshooting:');
    if (!results['Materials Service Health']) {
      log(colors.yellow, '   • Start materials service: cd services/materials-svc && npm start');
    }
    if (!results['Database Schema']) {
      log(colors.yellow, '   • Run database migrations: psql -f db/sql/015_raw_materials_system.sql');
    }
    if (!results['Material Distribution']) {
      log(colors.yellow, '   • Populate material deposits: psql -f db/sql/016_populate_material_deposits.sql');
    }
    
    return true;
  } else {
    log(colors.red, '💥 Materials system tests failed. Check the implementation.');
    return false;
  }
}

// Run tests
if (require.main === module) {
  runMaterialsSmokeTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    log(colors.red, `💥 Materials smoke test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runMaterialsSmokeTests, makeRequest, testDatabase };
