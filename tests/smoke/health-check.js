#!/usr/bin/env node

/**
 * Smoke Tests for Galactic Genesis
 * Quick health checks to verify basic functionality
 */

const http = require('http');
const https = require('https');

// Test configuration
const TESTS = {
  frontend: {
    name: 'Frontend Dev Server',
    url: 'http://localhost:5174',
    timeout: 5000
  },
  apiGateway: {
    name: 'API Gateway',
    url: 'http://localhost:19080/v1/health',
    timeout: 5000
  },
  stellarApi: {
    name: 'Stellar API',
    url: 'http://localhost:19080/v1/stellar/stars?limit=1',
    timeout: 5000
  },
  database: {
    name: 'PostgreSQL Database',
    url: 'http://localhost:19080/v1/health',
    timeout: 5000
  },
  websocket: {
    name: 'WebSocket Service',
    url: 'ws://localhost:19080/v1/stream',
    timeout: 5000
  }
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

// HTTP request helper
function makeRequest(url, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    const req = client.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.on('error', reject);
  });
}

// WebSocket test helper
function testWebSocket(url, timeout = 5000) {
  return new Promise((resolve, reject) => {
    try {
      const WebSocket = require('ws');
      const ws = new WebSocket(url);
      
      const timer = setTimeout(() => {
        ws.close();
        reject(new Error('WebSocket timeout'));
      }, timeout);

      ws.on('open', () => {
        clearTimeout(timer);
        ws.close();
        resolve({ status: 'connected' });
      });

      ws.on('error', (error) => {
        clearTimeout(timer);
        reject(error);
      });
    } catch (error) {
      reject(error);
    }
  });
}

// Individual test runner
async function runTest(testName, testConfig) {
  log(colors.blue, `\n🧪 Testing ${testConfig.name}...`);
  
  try {
    let result;
    
    if (testConfig.url.startsWith('ws://')) {
      result = await testWebSocket(testConfig.url, testConfig.timeout);
      log(colors.green, `✅ ${testConfig.name}: WebSocket connection successful`);
    } else {
      result = await makeRequest(testConfig.url, testConfig.timeout);
      
      if (result.status >= 200 && result.status < 400) {
        log(colors.green, `✅ ${testConfig.name}: HTTP ${result.status} - OK`);
        
        // Additional checks for specific endpoints
        if (testConfig.url.includes('/stellar/stars')) {
          try {
            const data = JSON.parse(result.body);
            if (data.stars && Array.isArray(data.stars)) {
              log(colors.green, `   📊 Stellar data: ${data.stars.length} stars loaded`);
            }
          } catch (e) {
            log(colors.yellow, `   ⚠️  Response not JSON: ${result.body.substring(0, 100)}...`);
          }
        }
      } else {
        log(colors.red, `❌ ${testConfig.name}: HTTP ${result.status} - Error`);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    log(colors.red, `❌ ${testConfig.name}: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runSmokeTests() {
  log(colors.bold + colors.blue, '🚀 Galactic Genesis - Smoke Tests');
  log(colors.blue, '=' .repeat(50));
  
  const results = {};
  let passCount = 0;
  let totalCount = 0;
  
  for (const [testName, testConfig] of Object.entries(TESTS)) {
    totalCount++;
    const passed = await runTest(testName, testConfig);
    results[testName] = passed;
    if (passed) passCount++;
  }
  
  // Summary
  log(colors.blue, '\n' + '=' .repeat(50));
  log(colors.bold, `📊 Test Results: ${passCount}/${totalCount} passed`);
  
  if (passCount === totalCount) {
    log(colors.green, '🎉 All smoke tests passed!');
    process.exit(0);
  } else {
    log(colors.red, '💥 Some tests failed. Check the output above.');
    
    // Recommendations
    log(colors.yellow, '\n💡 Troubleshooting:');
    if (!results.frontend) {
      log(colors.yellow, '   • Start frontend: cd frontend && npm run dev');
    }
    if (!results.apiGateway || !results.stellarApi) {
      log(colors.yellow, '   • Start backend: cd deploy && docker compose up -d');
    }
    if (!results.database) {
      log(colors.yellow, '   • Check PostgreSQL: docker ps | grep postgres');
    }
    if (!results.websocket) {
      log(colors.yellow, '   • WebSocket issues are non-critical for basic functionality');
    }
    
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  runSmokeTests().catch(error => {
    log(colors.red, `💥 Smoke test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runSmokeTests, makeRequest, testWebSocket };
