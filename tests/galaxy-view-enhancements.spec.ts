import { test, expect } from '@playwright/test';

test.describe('Galaxy View Enhancements', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5174');
    
    // Wait for the application to load
    await page.waitForSelector('canvas', { timeout: 30000 });
    
    // Wait for stellar data to load
    await page.waitForFunction(() => {
      return window.console.log.toString().includes('Loaded stellar data') || 
             document.querySelector('[data-testid="star-count"]');
    }, { timeout: 30000 });
  });

  test('should display optimization widget by default', async ({ page }) => {
    // Check if the Graphics Settings button is visible
    const settingsButton = page.locator('button:has-text("⚙️ Graphics Settings")');
    await expect(settingsButton).toBeVisible();
    
    // Check if the optimization widget is visible (should be open by default)
    const optimizationWidget = page.locator('text=🎮 Graphics Optimization');
    await expect(optimizationWidget).toBeVisible();
    
    // Check if distance control is present
    const distanceControl = page.locator('text=🌌 Visibility Range');
    await expect(distanceControl).toBeVisible();
    
    // Check if quality presets are present
    const lowQuality = page.locator('button:has-text("Low")');
    const mediumQuality = page.locator('button:has-text("Medium")');
    const highQuality = page.locator('button:has-text("High")');
    const ultraQuality = page.locator('button:has-text("Ultra")');
    
    await expect(lowQuality).toBeVisible();
    await expect(mediumQuality).toBeVisible();
    await expect(highQuality).toBeVisible();
    await expect(ultraQuality).toBeVisible();
  });

  test('should not have overlapping widgets', async ({ page }) => {
    // Check that search widget and optimization widget don't overlap
    const searchWidget = page.locator('input[placeholder="Search stars..."]');
    const optimizationWidget = page.locator('text=🎮 Graphics Optimization');
    
    await expect(searchWidget).toBeVisible();
    await expect(optimizationWidget).toBeVisible();
    
    // Get bounding boxes to ensure they don't overlap
    const searchBox = await searchWidget.boundingBox();
    const optimizationBox = await optimizationWidget.boundingBox();
    
    expect(searchBox).toBeTruthy();
    expect(optimizationBox).toBeTruthy();
    
    if (searchBox && optimizationBox) {
      // Check that widgets don't overlap horizontally
      const searchRight = searchBox.x + searchBox.width;
      const optimizationLeft = optimizationBox.x;
      
      expect(searchRight).toBeLessThan(optimizationLeft);
    }
  });

  test('should load all stars by default', async ({ page }) => {
    // Wait for console log indicating star count
    await page.waitForFunction(() => {
      const logs = window.performance?.getEntriesByType?.('navigation') || [];
      return true; // Simplified check
    });
    
    // Check that the distance slider is set to maximum (1000ly)
    const distanceSlider = page.locator('input[type="range"][min="20"][max="1000"]');
    await expect(distanceSlider).toBeVisible();
    
    const sliderValue = await distanceSlider.getAttribute('value');
    expect(sliderValue).toBe('1000');
  });

  test('should allow distance control adjustment', async ({ page }) => {
    // Find the distance slider
    const distanceSlider = page.locator('input[type="range"][min="20"][max="1000"]');
    await expect(distanceSlider).toBeVisible();
    
    // Change the distance to 50 light-years
    await distanceSlider.fill('50');
    
    // Verify the value changed
    const newValue = await distanceSlider.getAttribute('value');
    expect(newValue).toBe('50');
    
    // Check that the display text updated
    const distanceDisplay = page.locator('text=🌌 Visibility Range: 50 light-years');
    await expect(distanceDisplay).toBeVisible();
  });

  test('should allow quality preset changes', async ({ page }) => {
    // Click on Low quality preset
    const lowQualityButton = page.locator('button:has-text("Low")');
    await lowQualityButton.click();
    
    // Verify the button appears selected (should have different styling)
    await expect(lowQualityButton).toHaveClass(/bg-cyan-600/);
    
    // Click on Ultra quality preset
    const ultraQualityButton = page.locator('button:has-text("Ultra")');
    await ultraQualityButton.click();
    
    // Verify the Ultra button is now selected
    await expect(ultraQualityButton).toHaveClass(/bg-cyan-600/);
  });

  test('should toggle far galaxies', async ({ page }) => {
    // Find the far galaxies checkbox
    const farGalaxiesCheckbox = page.locator('input[type="checkbox"]').nth(1); // Second checkbox
    await expect(farGalaxiesCheckbox).toBeVisible();
    
    // Toggle it off
    await farGalaxiesCheckbox.uncheck();
    expect(await farGalaxiesCheckbox.isChecked()).toBe(false);
    
    // Toggle it back on
    await farGalaxiesCheckbox.check();
    expect(await farGalaxiesCheckbox.isChecked()).toBe(true);
  });

  test('should have working search functionality', async ({ page }) => {
    // Find the search input
    const searchInput = page.locator('input[placeholder="Search stars..."]');
    await expect(searchInput).toBeVisible();
    
    // Search for "Sol"
    await searchInput.fill('Sol');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Check if search results appear
    const searchResults = page.locator('text=Sol').first();
    await expect(searchResults).toBeVisible();
  });

  test('should navigate to solar system view', async ({ page }) => {
    // Search for Sol
    const searchInput = page.locator('input[placeholder="Search stars..."]');
    await searchInput.fill('Sol');
    
    // Wait for search results and click on Sol
    await page.waitForTimeout(1000);
    const solResult = page.locator('text=Sol').first();
    await solResult.click();
    
    // Wait for solar system view to load
    await page.waitForTimeout(3000);
    
    // Check for solar system elements
    const backButton = page.locator('button:has-text("← Back to Galaxy")');
    await expect(backButton).toBeVisible();
    
    // Check that planets are visible (look for planet names or controls)
    const timeControls = page.locator('text=Time Speed');
    await expect(timeControls).toBeVisible();
  });

  test('should not have WebGL errors', async ({ page }) => {
    // Listen for console errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Wait for the scene to render
    await page.waitForTimeout(5000);
    
    // Check that there are no WebGL shader errors
    const webglErrors = errors.filter(error => 
      error.includes('WebGL') || 
      error.includes('shader') || 
      error.includes('INVALID_OPERATION')
    );
    
    expect(webglErrors.length).toBe(0);
  });

  test('should have performance info display', async ({ page }) => {
    // Check that performance info is visible in the optimization widget
    const performanceInfo = page.locator('text=Stars:');
    await expect(performanceInfo).toBeVisible();
    
    const qualityInfo = page.locator('text=Quality:');
    await expect(qualityInfo).toBeVisible();
    
    const rangeInfo = page.locator('text=Range:');
    await expect(rangeInfo).toBeVisible();
  });

  test('should have working quick action buttons', async ({ page }) => {
    // Test Performance Mode button
    const performanceModeButton = page.locator('button:has-text("⚡ Performance Mode")');
    await expect(performanceModeButton).toBeVisible();
    await performanceModeButton.click();
    
    // Verify distance changed to 50
    await page.waitForTimeout(500);
    const distanceSlider = page.locator('input[type="range"][min="20"][max="1000"]');
    const performanceValue = await distanceSlider.getAttribute('value');
    expect(performanceValue).toBe('50');
    
    // Test Quality Mode button
    const qualityModeButton = page.locator('button:has-text("🎨 Quality Mode")');
    await qualityModeButton.click();
    
    // Verify distance changed to 200
    await page.waitForTimeout(500);
    const qualityValue = await distanceSlider.getAttribute('value');
    expect(qualityValue).toBe('200');
  });
});
