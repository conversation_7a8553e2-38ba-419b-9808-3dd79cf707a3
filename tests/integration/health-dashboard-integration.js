#!/usr/bin/env node

/**
 * Health Dashboard Integration Test
 * 
 * Tests the health dashboard API endpoints and functionality
 */

import { execSync } from 'child_process';

const DASHBOARD_URL = process.env.DASHBOARD_URL || 'http://localhost:8086';
const TIMEOUT = 10000; // 10 seconds

let testsPassed = 0;
let testsFailed = 0;

function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

function success(message) {
  console.log(`✅ ${message}`);
  testsPassed++;
}

function failure(message) {
  console.log(`❌ ${message}`);
  testsFailed++;
}

async function testWithTimeout(testFn, timeoutMs = TIMEOUT) {
  return Promise.race([
    testFn(),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Test timeout')), timeoutMs)
    )
  ]);
}

async function testHealthEndpoint() {
  log('Testing health endpoint...');
  
  try {
    const response = await fetch(`${DASHBOARD_URL}/health`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.ok && data.service === 'health-dashboard') {
        success('Health endpoint returns correct data');
      } else {
        failure('Health endpoint returned invalid data');
      }
    } else {
      failure(`Health endpoint returned ${response.status}`);
    }
  } catch (error) {
    failure(`Health endpoint test failed: ${error.message}`);
  }
}

async function testHealthDataAPI() {
  log('Testing health data API...');
  
  try {
    const response = await fetch(`${DASHBOARD_URL}/api/health-data`);
    
    if (response.ok) {
      const data = await response.json();
      
      // Validate required fields
      const requiredFields = ['timestamp', 'services', 'databases', 'system', 'overall'];
      const missingFields = requiredFields.filter(field => !data[field]);
      
      if (missingFields.length === 0) {
        success('Health data API has all required fields');
      } else {
        failure(`Health data API missing fields: ${missingFields.join(', ')}`);
      }
      
      // Validate services array
      if (Array.isArray(data.services) && data.services.length > 0) {
        success(`Health data includes ${data.services.length} services`);
        
        // Check service structure
        const service = data.services[0];
        const serviceFields = ['name', 'status', 'url', 'responseTime', 'lastChecked'];
        const missingServiceFields = serviceFields.filter(field => service[field] === undefined);
        
        if (missingServiceFields.length === 0) {
          success('Service objects have correct structure');
        } else {
          failure(`Service objects missing fields: ${missingServiceFields.join(', ')}`);
        }
      } else {
        failure('Health data contains no services');
      }
      
      // Validate system metrics
      if (data.system && data.system.cpu && data.system.memory && data.system.disk) {
        success('System metrics are present');
        
        // Check metric values are reasonable
        if (data.system.cpu.usage >= 0 && data.system.cpu.usage <= 100) {
          success('CPU usage is within valid range');
        } else {
          failure(`CPU usage out of range: ${data.system.cpu.usage}%`);
        }
        
        if (data.system.memory.usage >= 0 && data.system.memory.usage <= 100) {
          success('Memory usage is within valid range');
        } else {
          failure(`Memory usage out of range: ${data.system.memory.usage}%`);
        }
      } else {
        failure('System metrics are incomplete');
      }
      
      // Validate overall status
      const validStatuses = ['healthy', 'degraded', 'unhealthy'];
      if (validStatuses.includes(data.overall.status)) {
        success(`Overall status is valid: ${data.overall.status}`);
      } else {
        failure(`Invalid overall status: ${data.overall.status}`);
      }
      
    } else {
      failure(`Health data API returned ${response.status}`);
    }
  } catch (error) {
    failure(`Health data API test failed: ${error.message}`);
  }
}

async function testAlertsAPI() {
  log('Testing alerts API...');
  
  try {
    const response = await fetch(`${DASHBOARD_URL}/api/alerts`);
    
    if (response.ok) {
      const alerts = await response.json();
      
      if (Array.isArray(alerts)) {
        success(`Alerts API returns array with ${alerts.length} alerts`);
        
        // If there are alerts, validate structure
        if (alerts.length > 0) {
          const alert = alerts[0];
          const alertFields = ['id', 'type', 'service', 'message', 'timestamp'];
          const missingAlertFields = alertFields.filter(field => alert[field] === undefined);
          
          if (missingAlertFields.length === 0) {
            success('Alert objects have correct structure');
          } else {
            failure(`Alert objects missing fields: ${missingAlertFields.join(', ')}`);
          }
          
          // Validate alert types
          const validTypes = ['error', 'warning', 'info'];
          if (validTypes.includes(alert.type)) {
            success('Alert type is valid');
          } else {
            failure(`Invalid alert type: ${alert.type}`);
          }
        }
      } else {
        failure('Alerts API did not return an array');
      }
    } else {
      failure(`Alerts API returned ${response.status}`);
    }
  } catch (error) {
    failure(`Alerts API test failed: ${error.message}`);
  }
}

async function testServiceControlAPI() {
  log('Testing service control API...');
  
  try {
    // Test restart status endpoint
    const statusResponse = await fetch(`${DASHBOARD_URL}/api/services/restart-status`);
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      
      if (Array.isArray(statusData.inProgress) && statusData.timestamp) {
        success('Service restart status API works');
      } else {
        failure('Service restart status API returned invalid data');
      }
    } else {
      failure(`Service restart status API returned ${statusResponse.status}`);
    }
    
  } catch (error) {
    failure(`Service control API test failed: ${error.message}`);
  }
}

async function testReadinessProbe() {
  log('Testing readiness probe...');
  
  try {
    const response = await fetch(`${DASHBOARD_URL}/readyz`);
    
    if (response.status === 200 || response.status === 503) {
      const data = await response.json();
      
      if (typeof data.ready === 'boolean' && data.timestamp) {
        success(`Readiness probe works (ready: ${data.ready})`);
      } else {
        failure('Readiness probe returned invalid data');
      }
    } else {
      failure(`Readiness probe returned unexpected status ${response.status}`);
    }
  } catch (error) {
    failure(`Readiness probe test failed: ${error.message}`);
  }
}

async function testDashboardUI() {
  log('Testing dashboard UI...');
  
  try {
    const response = await fetch(DASHBOARD_URL);
    
    if (response.ok) {
      const html = await response.text();
      
      // Check for key UI elements
      if (html.includes('Galactic Genesis Health Dashboard')) {
        success('Dashboard UI loads with correct title');
      } else {
        failure('Dashboard UI missing title');
      }
      
      if (html.includes('data-testid="dashboard-title"')) {
        success('Dashboard UI includes test IDs');
      } else {
        failure('Dashboard UI missing test IDs');
      }
      
      if (html.includes('React') && html.includes('ReactDOM')) {
        success('Dashboard UI includes React dependencies');
      } else {
        failure('Dashboard UI missing React dependencies');
      }
      
    } else {
      failure(`Dashboard UI returned ${response.status}`);
    }
  } catch (error) {
    failure(`Dashboard UI test failed: ${error.message}`);
  }
}

async function testAPIPerformance() {
  log('Testing API performance...');
  
  try {
    const startTime = Date.now();
    const response = await fetch(`${DASHBOARD_URL}/api/health-data`);
    const endTime = Date.now();
    
    const responseTime = endTime - startTime;
    
    if (response.ok) {
      if (responseTime < 1000) {
        success(`Health data API responds quickly (${responseTime}ms)`);
      } else {
        failure(`Health data API is slow (${responseTime}ms)`);
      }
    } else {
      failure('Health data API failed during performance test');
    }
  } catch (error) {
    failure(`API performance test failed: ${error.message}`);
  }
}

async function runIntegrationTests() {
  log('🏥 Starting Health Dashboard Integration Tests...');
  log(`Dashboard URL: ${DASHBOARD_URL}`);
  
  const tests = [
    testHealthEndpoint,
    testHealthDataAPI,
    testAlertsAPI,
    testServiceControlAPI,
    testReadinessProbe,
    testDashboardUI,
    testAPIPerformance
  ];
  
  for (const test of tests) {
    try {
      await testWithTimeout(test);
    } catch (error) {
      failure(`Test failed with error: ${error.message}`);
    }
  }
  
  log('\n📊 Test Results:');
  log(`✅ Passed: ${testsPassed}`);
  log(`❌ Failed: ${testsFailed}`);
  log(`📈 Success Rate: ${((testsPassed / (testsPassed + testsFailed)) * 100).toFixed(1)}%`);
  
  if (testsFailed === 0) {
    log('🎉 All health dashboard integration tests passed!');
    process.exit(0);
  } else {
    log('💥 Some health dashboard integration tests failed!');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  failure(`Unhandled promise rejection: ${reason}`);
  process.exit(1);
});

// Run the tests
runIntegrationTests().catch((error) => {
  failure(`Integration test suite failed: ${error.message}`);
  process.exit(1);
});
