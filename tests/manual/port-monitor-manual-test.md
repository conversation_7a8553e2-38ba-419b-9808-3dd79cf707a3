# Port Monitor Manual Test

## Test Environment
- Frontend: http://localhost:5176
- Health Dashboard: http://localhost:8086

## Test Steps

### 1. Frontend Port Monitor Integration
1. Open http://localhost:5176 in browser
2. Look for "🔌 Ports" button in the navigation
3. Click the "🔌 Ports" button
4. Verify port monitor modal opens
5. Check that real port data is displayed (not mock data)

### 2. Health Dashboard Standalone
1. Open http://localhost:8086 in browser
2. Verify health dashboard loads
3. Click on "Port Monitor" tab
4. Verify comprehensive port overview is displayed

### 3. Real Data Verification
1. In port monitor, verify summary shows:
   - Total ports: ~40+
   - Listening ports: ~30+
   - Expected ports: ~10+
   - Unexpected ports: ~20+
   - Missing ports: ~5+

2. Verify port table shows:
   - Real port numbers (not 8080, 3000, 5000 mock data)
   - Process names and PIDs
   - Status indicators (Running/Closed)
   - Service descriptions

### 4. Real-time Updates
1. Keep port monitor open
2. Wait 30 seconds for automatic refresh
3. Verify "Last scan" timestamp updates
4. Verify port data refreshes

### 5. Interactive Features
1. Find a non-critical listening port
2. Click "Kill" button (if available)
3. Verify port status changes
4. Test "Find Free Port" functionality

## Expected Results

✅ **All data should be REAL system data, not mock data**
✅ **Port monitor should show comprehensive overview of all system ports**
✅ **Real-time updates should work via WebSocket**
✅ **Integration between frontend and health dashboard should be seamless**

## API Verification Commands

```bash
# Test health dashboard API
curl -s http://localhost:8086/api/ports | jq '.summary'

# Test expected ports
curl -s http://localhost:8086/api/ports/expected | jq '.[0:3]'

# Test port check
curl -s http://localhost:8086/api/ports/8086/check

# Test find free port
curl -s http://localhost:8086/api/ports/find-free
```

## Smoke Test Results

Run the automated smoke test:
```bash
node tests/smoke/frontend-port-monitor-smoke.js
```

Expected: All tests should pass with real data validation.
